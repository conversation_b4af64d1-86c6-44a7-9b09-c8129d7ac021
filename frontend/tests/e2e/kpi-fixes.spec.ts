import { test, expect } from '@playwright/test';

test.describe('KPI Component Fixes - End-to-End Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Monitor console for React errors
    const errors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        const text = msg.text();
        if (text.includes('Maximum update depth exceeded') || 
            text.includes('Cannot read properties of null') ||
            text.includes('reading \'direction\'')) {
          errors.push(text);
        }
      }
    });
    
    // Store errors for later assertions
    await page.addInitScript(() => {
      (window as any).reactErrors = [];
    });
  });

  test('should load application without React errors', async ({ page }) => {
    await page.goto('/');
    
    // Wait for app to load
    await expect(page.locator('h1')).toContainText('مرحباً بك في نمو');
    
    // Check for React errors in console
    const errors = await page.evaluate(() => {
      const logs: string[] = [];
      const originalError = console.error;
      console.error = (...args) => {
        const message = args.join(' ');
        if (message.includes('Maximum update depth exceeded') || 
            message.includes('Cannot read properties of null')) {
          logs.push(message);
        }
        originalError(...args);
      };
      return logs;
    });
    
    expect(errors).toHaveLength(0);
  });

  test('should handle login form without infinite re-renders', async ({ page }) => {
    await page.goto('/');
    
    // Fill login form
    await page.fill('#username', '<EMAIL>');
    await page.fill('#password', 'password123');
    
    // Monitor for maximum update depth errors
    let maxDepthError = false;
    page.on('console', msg => {
      if (msg.type() === 'error' && msg.text().includes('Maximum update depth exceeded')) {
        maxDepthError = true;
      }
    });
    
    // Click login button
    await page.click('button[type="submit"]');
    
    // Wait a bit to see if errors occur
    await page.waitForTimeout(3000);
    
    expect(maxDepthError).toBe(false);
  });

  test('should navigate to KPI dashboard without crashes', async ({ page }) => {
    await page.goto('/#/kpi/dashboard');
    
    // Should redirect to login but not crash
    await expect(page.locator('h1')).toContainText('مرحباً بك في نمو');
    
    // Check that no React error boundaries are triggered
    const errorBoundaries = await page.locator('.error-boundary').count();
    expect(errorBoundaries).toBe(0);
  });

  test('should handle KPI data with null trends safely', async ({ page }) => {
    await page.goto('/');
    
    // Inject test KPI data with null trends
    await page.evaluate(() => {
      // Simulate the fixed getTrendIcon function
      const testKPIData = [
        { id: '1', name: 'Test KPI 1', trend: null },
        { id: '2', name: 'Test KPI 2', trend: undefined },
        { id: '3', name: 'Test KPI 3', trend: { direction: null } }
      ];
      
      // Test our fixed logic
      testKPIData.forEach(kpi => {
        // This should not throw errors with our fixes
        const trend = typeof kpi.trend === 'object' && kpi.trend !== null 
          ? kpi.trend.direction 
          : kpi.trend;
        
        const icon = !kpi.trend ? 'stable' : 
                    trend === 'up' ? 'up' : 
                    trend === 'down' ? 'down' : 'stable';
      });
      
      return true;
    });
    
    // If we get here without errors, the test passes
    expect(true).toBe(true);
  });

  test('should handle KPI conversion without errors', async ({ page }) => {
    await page.goto('/');
    
    const conversionResult = await page.evaluate(() => {
      // Test KPI conversion with problematic data
      const problematicKPIs = [
        { id: '1', name: 'KPI 1', current_value: null, trend: null },
        { id: '2', name: 'KPI 2', current_value: undefined, trend: undefined },
        { id: '3', name: 'KPI 3', current_value: { value: 100 }, trend: { invalidProp: 'test' } }
      ];
      
      try {
        const converted = problematicKPIs.map(kpi => {
          // Simulate our fixed conversion logic
          let safeTrend: 'up' | 'down' | 'stable' = 'stable';
          if (kpi.trend) {
            if (typeof kpi.trend === 'object' && kpi.trend !== null && 'direction' in kpi.trend) {
              safeTrend = (kpi.trend as any).direction;
            } else if (typeof kpi.trend === 'string') {
              safeTrend = kpi.trend as 'up' | 'down' | 'stable';
            }
          }
          
          return {
            ...kpi,
            current_value: typeof kpi.current_value === 'object' ? 
              (kpi.current_value as any)?.value : kpi.current_value,
            trend: safeTrend
          };
        });
        
        return { success: true, count: converted.length };
      } catch (error) {
        return { success: false, error: (error as Error).message };
      }
    });
    
    expect(conversionResult.success).toBe(true);
    expect(conversionResult.count).toBe(3);
  });

  test('should not have memory leaks or infinite loops', async ({ page }) => {
    await page.goto('/');
    
    // Monitor memory usage
    const initialMemory = await page.evaluate(() => {
      return (performance as any).memory ? (performance as any).memory.usedJSHeapSize : 0;
    });
    
    // Interact with the page for a while
    for (let i = 0; i < 5; i++) {
      await page.fill('#username', `test${i}@example.com`);
      await page.waitForTimeout(500);
    }
    
    const finalMemory = await page.evaluate(() => {
      return (performance as any).memory ? (performance as any).memory.usedJSHeapSize : 0;
    });
    
    // Memory shouldn't grow excessively (allow for some normal growth)
    if (initialMemory > 0 && finalMemory > 0) {
      const memoryGrowth = (finalMemory - initialMemory) / initialMemory;
      expect(memoryGrowth).toBeLessThan(2); // Less than 200% growth
    }
  });

  test('should render components without crashing', async ({ page }) => {
    await page.goto('/');
    
    // Check that main components are rendered
    await expect(page.locator('form')).toBeVisible();
    await expect(page.locator('input[type="text"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
    
    // No error boundaries should be visible
    const errorElements = await page.locator('[class*="error"], .error-boundary').count();
    expect(errorElements).toBe(0);
  });

});
