import { test, expect } from '@playwright/test';

test.describe('KPI Component Stress Tests - Edge Cases', () => {
  
  test('should handle rapid state changes without infinite re-renders', async ({ page }) => {
    await page.goto('/');
    
    let maxDepthErrors = 0;
    let nullDirectionErrors = 0;
    
    // Monitor for specific errors we fixed
    page.on('console', msg => {
      if (msg.type() === 'error') {
        const text = msg.text();
        if (text.includes('Maximum update depth exceeded')) {
          maxDepthErrors++;
        }
        if (text.includes('Cannot read properties of null') && text.includes('direction')) {
          nullDirectionErrors++;
        }
      }
    });
    
    // Simulate rapid form interactions that could trigger re-renders
    for (let i = 0; i < 10; i++) {
      await page.fill('#username', `user${i}@test.com`);
      await page.fill('#password', `password${i}`);
      await page.waitForTimeout(100);
    }
    
    // Wait for any potential errors to surface
    await page.waitForTimeout(2000);
    
    expect(maxDepthErrors).toBe(0);
    expect(nullDirectionErrors).toBe(0);
  });

  test('should handle malformed KPI data injection', async ({ page }) => {
    await page.goto('/');
    
    const testResult = await page.evaluate(() => {
      // Inject extremely problematic KPI data
      const malformedKPIs = [
        { id: null, name: null, trend: null, current_value: null },
        { id: undefined, name: undefined, trend: undefined, current_value: undefined },
        { id: '1', name: 'Test', trend: { direction: null }, current_value: { value: null } },
        { id: '2', name: 'Test', trend: { invalidProp: 'test' }, current_value: 'invalid' },
        { id: '3', name: 'Test', trend: 42, current_value: [] },
        { id: '4', name: 'Test', trend: 'invalid_trend', current_value: {} }
      ];
      
      let errors = 0;
      let successes = 0;
      
      malformedKPIs.forEach(kpi => {
        try {
          // Test our fixed conversion logic
          let safeTrend: 'up' | 'down' | 'stable' = 'stable';
          if (kpi.trend) {
            if (typeof kpi.trend === 'object' && kpi.trend !== null && 'direction' in kpi.trend) {
              safeTrend = (kpi.trend as any).direction || 'stable';
            } else if (typeof kpi.trend === 'string') {
              safeTrend = ['up', 'down', 'stable'].includes(kpi.trend) ? 
                kpi.trend as 'up' | 'down' | 'stable' : 'stable';
            }
          }
          
          // Test getTrendIcon logic
          const iconResult = !kpi.trend ? 'stable-icon' : 
                            safeTrend === 'up' ? 'up-icon' : 
                            safeTrend === 'down' ? 'down-icon' : 'stable-icon';
          
          successes++;
        } catch (error) {
          errors++;
        }
      });
      
      return { errors, successes, total: malformedKPIs.length };
    });
    
    expect(testResult.errors).toBe(0);
    expect(testResult.successes).toBe(testResult.total);
  });

  test('should maintain performance under load', async ({ page }) => {
    await page.goto('/');
    
    // Measure initial performance
    const initialMetrics = await page.evaluate(() => {
      const timing = performance.timing;
      return {
        domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
        loadComplete: timing.loadEventEnd - timing.navigationStart,
        memory: (performance as any).memory ? (performance as any).memory.usedJSHeapSize : 0
      };
    });
    
    // Simulate heavy interaction
    for (let i = 0; i < 20; i++) {
      await page.fill('#username', `stress_test_${i}@example.com`);
      await page.fill('#password', `password_${i}_with_long_string_to_test_memory`);
      if (i % 5 === 0) {
        await page.click('button[type="submit"]');
        await page.waitForTimeout(500);
      }
    }
    
    const finalMetrics = await page.evaluate(() => {
      return {
        memory: (performance as any).memory ? (performance as any).memory.usedJSHeapSize : 0,
        timestamp: Date.now()
      };
    });
    
    // Performance assertions
    expect(initialMetrics.domContentLoaded).toBeLessThan(5000); // Less than 5 seconds
    
    if (initialMetrics.memory > 0 && finalMetrics.memory > 0) {
      const memoryIncrease = finalMetrics.memory - initialMetrics.memory;
      const memoryIncreasePercent = (memoryIncrease / initialMetrics.memory) * 100;
      expect(memoryIncreasePercent).toBeLessThan(300); // Less than 300% increase
    }
  });

  test('should handle concurrent navigation attempts', async ({ page }) => {
    await page.goto('/');
    
    let navigationErrors = 0;
    page.on('console', msg => {
      if (msg.type() === 'error' && 
          (msg.text().includes('navigation') || msg.text().includes('router'))) {
        navigationErrors++;
      }
    });
    
    // Attempt rapid navigation
    const navigationPromises = [
      page.goto('/#/kpi/dashboard'),
      page.goto('/#/login'),
      page.goto('/#/kpi/dashboard'),
      page.goto('/'),
      page.goto('/#/kpi/dashboard')
    ];
    
    await Promise.allSettled(navigationPromises);
    await page.waitForTimeout(1000);
    
    // Should end up on login page due to auth redirect
    await expect(page.locator('h1')).toContainText('مرحباً بك في نمو');
    expect(navigationErrors).toBe(0);
  });

  test('should recover from component errors gracefully', async ({ page }) => {
    await page.goto('/');
    
    // Inject a potential error scenario and verify recovery
    const recoveryTest = await page.evaluate(() => {
      try {
        // Simulate error conditions that our fixes should handle
        const errorScenarios = [
          () => {
            // Test null trend handling
            const trend = null;
            return typeof trend === 'object' && trend !== null ? trend.direction : trend;
          },
          () => {
            // Test undefined trend handling  
            const trend = undefined;
            return !trend ? 'stable' : trend;
          },
          () => {
            // Test malformed object handling
            const trend = { invalidProp: 'test' };
            return typeof trend === 'object' && trend !== null && 'direction' in trend ? 
              trend.direction : 'stable';
          }
        ];
        
        let passedScenarios = 0;
        errorScenarios.forEach(scenario => {
          try {
            const result = scenario();
            passedScenarios++;
          } catch (error) {
            // Should not reach here with our fixes
          }
        });
        
        return { passed: passedScenarios, total: errorScenarios.length };
      } catch (error) {
        return { passed: 0, total: 3, error: (error as Error).message };
      }
    });
    
    expect(recoveryTest.passed).toBe(recoveryTest.total);
    expect(recoveryTest.error).toBeUndefined();
  });

});
