import { test, expect } from '@playwright/test';

/**
 * HR Module Comprehensive Tests
 * 
 * Tests all HR-related functionality:
 * - Employee management
 * - Attendance tracking
 * - Leave management
 * - Performance reviews
 * - Payroll processing
 * - Department management
 */

test.describe('HR Module Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Try to login as HR manager
    await page.goto('/');
    await page.fill('#username', '<EMAIL>');
    await page.fill('#password', 'hr123');
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
  });

  test('should access employee management module', async ({ page }) => {
    await page.goto('/#/employees');
    await page.waitForTimeout(2000);
    
    // Check if employees page loads
    const hasEmployeeList = await page.locator('table, .employee-list, .grid-view').count() > 0;
    const hasAddButton = await page.locator('button:has-text("Add"), button:has-text("New Employee")').count() > 0;
    
    // Should be able to view employees (even if empty)
    expect(await page.locator('h1, h2, .page-title').count()).toBeGreaterThan(0);
    
    console.log(`Employee Management - List: ${hasEmployeeList}, Add Button: ${hasAddButton}`);
  });

  test('should test employee creation flow', async ({ page }) => {
    await page.goto('/#/employees');
    await page.waitForTimeout(2000);
    
    // Look for add employee button
    const addButton = page.locator('button:has-text("Add"), button:has-text("New"), button:has-text("Create")').first();
    
    if (await addButton.count() > 0) {
      await addButton.click();
      await page.waitForTimeout(1000);
      
      // Should open employee form
      const hasForm = await page.locator('form, .modal, .dialog').count() > 0;
      const hasNameField = await page.locator('input[name*="name"], input[placeholder*="name"]').count() > 0;
      const hasEmailField = await page.locator('input[type="email"], input[name*="email"]').count() > 0;
      
      expect(hasForm || hasNameField || hasEmailField).toBe(true);
      
      console.log(`Employee Form - Form: ${hasForm}, Name: ${hasNameField}, Email: ${hasEmailField}`);
    }
  });

  test('should access attendance module', async ({ page }) => {
    await page.goto('/#/attendance');
    await page.waitForTimeout(2000);
    
    // Check attendance page elements
    const hasAttendanceData = await page.locator('table, .attendance-list, .calendar').count() > 0;
    const hasDatePicker = await page.locator('input[type="date"], .date-picker').count() > 0;
    const hasFilterOptions = await page.locator('select, .filter, .dropdown').count() > 0;
    
    // Should load attendance interface
    expect(await page.locator('body').count()).toBe(1);
    
    console.log(`Attendance - Data: ${hasAttendanceData}, Date: ${hasDatePicker}, Filters: ${hasFilterOptions}`);
  });

  test('should test leave management functionality', async ({ page }) => {
    await page.goto('/#/leave-management');
    await page.waitForTimeout(2000);
    
    // Check leave management elements
    const hasLeaveRequests = await page.locator('table, .leave-list').count() > 0;
    const hasApprovalButtons = await page.locator('button:has-text("Approve"), button:has-text("Reject")').count() > 0;
    const hasNewLeaveButton = await page.locator('button:has-text("New Leave"), button:has-text("Request")').count() > 0;
    
    console.log(`Leave Management - Requests: ${hasLeaveRequests}, Approval: ${hasApprovalButtons}, New: ${hasNewLeaveButton}`);
    
    // Test leave request creation if button exists
    if (hasNewLeaveButton) {
      await page.click('button:has-text("New Leave"), button:has-text("Request")');
      await page.waitForTimeout(1000);
      
      const hasLeaveForm = await page.locator('form, .modal').count() > 0;
      expect(hasLeaveForm).toBeTruthy();
    }
  });

  test('should access performance management', async ({ page }) => {
    await page.goto('/#/performance');
    await page.waitForTimeout(2000);
    
    // Check performance page elements
    const hasPerformanceData = await page.locator('table, .performance-list, .chart').count() > 0;
    const hasReviewButtons = await page.locator('button:has-text("Review"), button:has-text("Evaluate")').count() > 0;
    
    console.log(`Performance - Data: ${hasPerformanceData}, Reviews: ${hasReviewButtons}`);
    
    // Should load without errors
    const hasErrorBoundary = await page.locator('.error-boundary').count();
    expect(hasErrorBoundary).toBe(0);
  });

  test('should test payroll module access', async ({ page }) => {
    await page.goto('/#/payroll');
    await page.waitForTimeout(2000);
    
    // Check payroll elements
    const hasPayrollData = await page.locator('table, .payroll-list').count() > 0;
    const hasProcessButton = await page.locator('button:has-text("Process"), button:has-text("Generate")').count() > 0;
    const hasSalaryInfo = await page.locator('.salary, .wage, .payment').count() > 0;
    
    console.log(`Payroll - Data: ${hasPayrollData}, Process: ${hasProcessButton}, Salary: ${hasSalaryInfo}`);
    
    // Should not crash
    const pageTitle = await page.title();
    expect(pageTitle).toBeTruthy();
  });

  test('should test department management', async ({ page }) => {
    await page.goto('/#/departments');
    await page.waitForTimeout(2000);
    
    // Check department elements
    const hasDepartmentList = await page.locator('table, .department-list, .grid').count() > 0;
    const hasAddDeptButton = await page.locator('button:has-text("Add"), button:has-text("New Department")').count() > 0;
    
    console.log(`Departments - List: ${hasDepartmentList}, Add: ${hasAddDeptButton}`);
    
    // Test department creation if possible
    if (hasAddDeptButton) {
      await page.click('button:has-text("Add"), button:has-text("New Department")');
      await page.waitForTimeout(1000);
      
      const hasDeptForm = await page.locator('form, .modal').count() > 0;
      const hasNameField = await page.locator('input[name*="name"], input[placeholder*="name"]').count() > 0;
      
      expect(hasDeptForm || hasNameField).toBeTruthy();
    }
  });

  test('should test HR reports and analytics', async ({ page }) => {
    await page.goto('/#/reports');
    await page.waitForTimeout(2000);
    
    // Check reports elements
    const hasReportList = await page.locator('table, .report-list, .grid').count() > 0;
    const hasGenerateButton = await page.locator('button:has-text("Generate"), button:has-text("Create Report")').count() > 0;
    const hasCharts = await page.locator('canvas, .chart, svg').count() > 0;
    
    console.log(`Reports - List: ${hasReportList}, Generate: ${hasGenerateButton}, Charts: ${hasCharts}`);
    
    // Test report generation if possible
    if (hasGenerateButton) {
      await page.click('button:has-text("Generate"), button:has-text("Create Report")');
      await page.waitForTimeout(2000);
      
      // Should either show a form or generate a report
      const hasReportForm = await page.locator('form, .modal, .report-config').count() > 0;
      const hasReportData = await page.locator('table, .report-data, .chart').count() > 0;
      
      expect(hasReportForm || hasReportData).toBeTruthy();
    }
  });

  test('should handle HR data export functionality', async ({ page }) => {
    const exportTests = [
      { path: '/#/employees', buttonText: 'Export' },
      { path: '/#/attendance', buttonText: 'Export' },
      { path: '/#/reports', buttonText: 'Download' }
    ];
    
    for (const exportTest of exportTests) {
      await page.goto(exportTest.path);
      await page.waitForTimeout(2000);
      
      const hasExportButton = await page.locator(`button:has-text("${exportTest.buttonText}")`).count() > 0;
      
      if (hasExportButton) {
        // Don't actually click to avoid downloads, just verify button exists
        console.log(`Export button found on ${exportTest.path}`);
        expect(hasExportButton).toBe(true);
      }
    }
  });

  test('should test HR module error handling', async ({ page }) => {
    const errorScenarios = [
      '/#/employees/invalid-id',
      '/#/departments/999999',
      '/#/attendance/nonexistent'
    ];
    
    for (const scenario of errorScenarios) {
      await page.goto(scenario);
      await page.waitForTimeout(2000);
      
      // Should handle errors gracefully
      const hasErrorBoundary = await page.locator('.error-boundary').count();
      const hasErrorMessage = await page.locator('text=Error, text=Not Found, text=404').count() > 0;
      const isRedirected = !page.url().includes(scenario.replace('/#', ''));
      
      // Should either show error message, redirect, or use error boundary
      expect(hasErrorBoundary === 0 || hasErrorMessage || isRedirected).toBe(true);
    }
  });

});
