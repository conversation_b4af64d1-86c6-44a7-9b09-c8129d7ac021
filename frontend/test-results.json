{"config": {"configFile": "/Users/<USER>/Desktop/EMS/frontend/playwright.config.ts", "rootDir": "/Users/<USER>/Desktop/EMS/frontend/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 4}, "preserveOutput": "always", "reporter": [["json"]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Desktop/EMS/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Desktop/EMS/frontend/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 4, "webServer": {"command": "npm run dev", "url": "http://localhost:5175", "reuseExistingServer": true}}, "suites": [{"title": "kpi-component-stress-test.spec.ts", "file": "kpi-component-stress-test.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "KPI Component Stress Tests - Edge Cases", "file": "kpi-component-stress-test.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should handle rapid state changes without infinite re-renders", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 6141, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-22T16:33:27.644Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "644c0dee8bd814e34695-723e607ce7542a2f0f08", "file": "kpi-component-stress-test.spec.ts", "line": 5, "column": 3}, {"title": "should handle malformed KPI data injection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 2469, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-22T16:33:27.645Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "644c0dee8bd814e34695-e6ca54f0cb5eb6ee3245", "file": "kpi-component-stress-test.spec.ts", "line": 38, "column": 3}, {"title": "should maintain performance under load", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "timedOut", "duration": 30266, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Desktop/EMS/frontend/tests/e2e/kpi-component-stress-test.spec.ts", "column": 18, "line": 101}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#username')\u001b[22m\n\u001b[2m    - locator resolved to <input disabled value=\"\" required=\"\" type=\"text\" id=\"username\" data-slot=\"input\" placeholder=\"أدخل اسم المستخدم\" class=\"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-n…/>\u001b[22m\n\u001b[2m    - fill(\"<EMAIL>\")\u001b[22m\n\u001b[2m  - attempting fill action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and editable\u001b[22m\n\u001b[2m      - element is not enabled\u001b[22m\n\u001b[2m    - retrying fill action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and editable\u001b[22m\n\u001b[2m      - element is not enabled\u001b[22m\n\u001b[2m    - retrying fill action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    6 × waiting for element to be visible, enabled and editable\u001b[22m\n\u001b[2m      - element is not enabled\u001b[22m\n\u001b[2m    - retrying fill action\u001b[22m\n\u001b[2m      - waiting 500ms\u001b[22m\n\n\n   99 |     // Simulate heavy interaction\n  100 |     for (let i = 0; i < 20; i++) {\n> 101 |       await page.fill('#username', `stress_test_${i}@example.com`);\n      |                  ^\n  102 |       await page.fill('#password', `password_${i}_with_long_string_to_test_memory`);\n  103 |       if (i % 5 === 0) {\n  104 |         await page.click('button[type=\"submit\"]');\n    at /Users/<USER>/Desktop/EMS/frontend/tests/e2e/kpi-component-stress-test.spec.ts:101:18"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-22T16:33:27.648Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/kpi-component-stress-test--c4913-tain-performance-under-load-chromium/test-failed-1.png"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/kpi-component-stress-test--c4913-tain-performance-under-load-chromium/error-context.md"}]}], "status": "unexpected"}], "id": "644c0dee8bd814e34695-f5ede30473775504cb94", "file": "kpi-component-stress-test.spec.ts", "line": 86, "column": 3}, {"title": "should handle concurrent navigation attempts", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 2973, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-22T16:33:27.649Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "644c0dee8bd814e34695-8947309d978416427157", "file": "kpi-component-stress-test.spec.ts", "line": 126, "column": 3}, {"title": "should recover from component errors gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 1431, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-22T16:33:30.597Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "644c0dee8bd814e34695-30637acfa79e5f73b21a", "file": "kpi-component-stress-test.spec.ts", "line": 154, "column": 3}]}]}, {"title": "kpi-fixes.spec.ts", "file": "kpi-fixes.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "KPI Component Fixes - End-to-End Tests", "file": "kpi-fixes.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should load application without React errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 1823, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-22T16:33:31.106Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "cb8b6c892aa14bdacad0-a770d80262dc04ace9f3", "file": "kpi-fixes.spec.ts", "line": 25, "column": 3}, {"title": "should handle login form without infinite re-renders", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 5089, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-22T16:33:32.043Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "cb8b6c892aa14bdacad0-4af0fb0a8cd7b5024d10", "file": "kpi-fixes.spec.ts", "line": 49, "column": 3}, {"title": "should navigate to KPI dashboard without crashes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 1680, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-22T16:33:32.934Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "cb8b6c892aa14bdacad0-b196ac59de18f885b113", "file": "kpi-fixes.spec.ts", "line": 73, "column": 3}, {"title": "should handle KPI data with null trends safely", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 1010, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-22T16:33:34.269Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "cb8b6c892aa14bdacad0-260a34ce63367ed09b70", "file": "kpi-fixes.spec.ts", "line": 84, "column": 3}, {"title": "should handle KPI conversion without errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 1566, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-22T16:33:34.621Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "cb8b6c892aa14bdacad0-eedfee6f7a5451a2f0a5", "file": "kpi-fixes.spec.ts", "line": 115, "column": 3}, {"title": "should not have memory leaks or infinite loops", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 4280, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-22T16:33:35.284Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "cb8b6c892aa14bdacad0-4919b0381f04a53ac0dc", "file": "kpi-fixes.spec.ts", "line": 156, "column": 3}, {"title": "should render components without crashing", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 1508, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-22T16:33:36.192Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "cb8b6c892aa14bdacad0-000a870b39cfdc657578", "file": "kpi-fixes.spec.ts", "line": 181, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-07-22T16:33:27.053Z", "duration": 31356.811, "expected": 11, "skipped": 0, "unexpected": 1, "flaky": 0}}