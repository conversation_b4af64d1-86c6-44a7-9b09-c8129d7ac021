/**
 * Real-time KPI Updates Hook
 * 
 * This hook provides real-time KPI updates via WebSocket connection.
 * It automatically manages connection lifecycle and provides reactive
 * KPI data updates for enterprise dashboards.
 * 
 * Features:
 * - Automatic WebSocket connection management
 * - Real-time KPI value updates
 * - KPI alert notifications
 * - Connection status monitoring
 * - Automatic reconnection handling
 * - Performance optimized updates
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { enhancedWebSocketService, KPIUpdateMessage, KPIAlertMessage } from '@/services/websocket'

export interface KPI {
  id: string
  name: string
  current_value?: number
  last_updated?: string
  status: string
  target_value?: number
  warning_threshold?: number
  critical_threshold?: number
  unit?: string
  trend?: 'up' | 'down' | 'stable'
  achievement_percentage?: number
}

export interface KPIAlert {
  id: string
  kpi_id: string
  alert_type: string
  message: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  timestamp: string
  acknowledged?: boolean
}

export interface UseRealTimeKPIsOptions {
  autoConnect?: boolean
  subscribeToAll?: boolean
  specificKPIs?: string[]
  enableAlerts?: boolean
}

export interface UseRealTimeKPIsReturn {
  kpis: KPI[]
  alerts: KPIAlert[]
  isConnected: boolean
  connectionStatus: any
  lastUpdate: Date | null
  subscribeToKPI: (kpiId: string) => void
  acknowledgeAlert: (alertId: string) => void
  refreshConnection: () => void
}

export const useRealTimeKPIs = (
  initialKPIs: KPI[] = [],
  options: UseRealTimeKPIsOptions = {}
): UseRealTimeKPIsReturn => {
  const {
    autoConnect = true,
    subscribeToAll = false,
    specificKPIs = [],
    enableAlerts = true
  } = options

  // State management
  const [kpis, setKPIs] = useState<KPI[]>(initialKPIs)
  const [alerts, setAlerts] = useState<KPIAlert[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState(enhancedWebSocketService.getConnectionStatus())
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)

  // Refs for stable references
  const kpisRef = useRef<KPI[]>(initialKPIs)
  const alertsRef = useRef<KPIAlert[]>([])

  // Update refs when state changes
  useEffect(() => {
    kpisRef.current = kpis
  }, [kpis])

  useEffect(() => {
    alertsRef.current = alerts
  }, [alerts])

  // KPI update handler
  const handleKPIUpdate = useCallback((message: KPIUpdateMessage) => {
    console.log('📊 KPI Update received:', message)
    
    setKPIs(prevKPIs => {
      const updatedKPIs = prevKPIs.map(kpi => {
        if (kpi.id === message.kpi_id) {
          const updatedKPI = {
            ...kpi,
            current_value: message.value,
            last_updated: message.timestamp
          }

          // Calculate achievement percentage if target exists
          if (kpi.target_value && kpi.target_value > 0) {
            updatedKPI.achievement_percentage = (message.value / kpi.target_value) * 100
          }

          // Determine trend (simplified logic)
          if (kpi.current_value !== undefined) {
            if (message.value > kpi.current_value) {
              updatedKPI.trend = 'up'
            } else if (message.value < kpi.current_value) {
              updatedKPI.trend = 'down'
            } else {
              updatedKPI.trend = 'stable'
            }
          }

          return updatedKPI
        }
        return kpi
      })

      return updatedKPIs
    })

    setLastUpdate(new Date())
  }, [])

  // KPI alert handler
  const handleKPIAlert = useCallback((message: KPIAlertMessage) => {
    if (!enableAlerts) return

    console.log('🚨 KPI Alert received:', message)
    
    const newAlert: KPIAlert = {
      id: `${message.kpi_id}-${Date.now()}`,
      kpi_id: message.kpi_id,
      alert_type: message.alert_type,
      message: message.message,
      severity: message.severity,
      timestamp: message.timestamp,
      acknowledged: false
    }

    setAlerts(prevAlerts => [newAlert, ...prevAlerts.slice(0, 49)]) // Keep last 50 alerts
  }, [enableAlerts])

  // Connection status handler
  const handleConnectionChange = useCallback(() => {
    const status = enhancedWebSocketService.getConnectionStatus()
    setConnectionStatus(status)
    setIsConnected(status.connected)
  }, [])

  // Subscribe to specific KPI
  const subscribeToKPI = useCallback((kpiId: string) => {
    if (isConnected) {
      enhancedWebSocketService.subscribeToKPI(kpiId)
      console.log(`📡 Subscribed to KPI: ${kpiId}`)
    }
  }, [isConnected])

  // Acknowledge alert
  const acknowledgeAlert = useCallback((alertId: string) => {
    setAlerts(prevAlerts => 
      prevAlerts.map(alert => 
        alert.id === alertId 
          ? { ...alert, acknowledged: true }
          : alert
      )
    )
  }, [])

  // Refresh connection - DISABLED until WebSocket backend is properly configured
  const refreshConnection = useCallback(() => {
    console.log('🚫 WebSocket connection disabled - using REST API only')
    // enhancedWebSocketService.disconnect()
    // setTimeout(() => {
    //   enhancedWebSocketService.connect()
    // }, 1000)
  }, [])

  // Setup WebSocket connection and event listeners
  useEffect(() => {
    if (!autoConnect) return

    // Connect to WebSocket
    enhancedWebSocketService.connect()

    // Set up event listeners
    enhancedWebSocketService.on('kpi_update', handleKPIUpdate)
    enhancedWebSocketService.on('kpi_alert', handleKPIAlert)
    enhancedWebSocketService.on('connected', handleConnectionChange)
    enhancedWebSocketService.on('disconnected', handleConnectionChange)
    enhancedWebSocketService.on('connection_failed', handleConnectionChange)

    // Initial connection status
    handleConnectionChange()

    // Cleanup function
    return () => {
      enhancedWebSocketService.off('kpi_update', handleKPIUpdate)
      enhancedWebSocketService.off('kpi_alert', handleKPIAlert)
      enhancedWebSocketService.off('connected', handleConnectionChange)
      enhancedWebSocketService.off('disconnected', handleConnectionChange)
      enhancedWebSocketService.off('connection_failed', handleConnectionChange)
    }
  }, [autoConnect, handleKPIUpdate, handleKPIAlert, handleConnectionChange])

  // Subscribe to specific KPIs when connected
  useEffect(() => {
    if (isConnected && subscribeToAll) {
      // Subscribe to all KPIs
      kpis.forEach(kpi => {
        enhancedWebSocketService.subscribeToKPI(kpi.id)
      })
    } else if (isConnected && specificKPIs.length > 0) {
      // Subscribe to specific KPIs
      specificKPIs.forEach(kpiId => {
        enhancedWebSocketService.subscribeToKPI(kpiId)
      })
    }
  }, [isConnected, subscribeToAll, specificKPIs, kpis])

  // Update KPIs when initialKPIs change - use deep comparison to prevent infinite re-renders
  useEffect(() => {
    // Only update if the KPIs have actually changed (deep comparison by ID and values)
    const hasChanged = initialKPIs.length !== kpis.length ||
      initialKPIs.some((newKPI, index) => {
        const existingKPI = kpis[index]
        return !existingKPI ||
               newKPI.id !== existingKPI.id ||
               newKPI.current_value !== existingKPI.current_value ||
               newKPI.name !== existingKPI.name
      })

    if (hasChanged) {
      setKPIs(initialKPIs)
    }
  }, [initialKPIs, kpis])

  return {
    kpis,
    alerts,
    isConnected,
    connectionStatus,
    lastUpdate,
    subscribeToKPI,
    acknowledgeAlert,
    refreshConnection
  }
}
