/**
 * Role Mapping Utilities
 * Handles mapping between backend role names and frontend role names
 */

// Backend to Frontend role mapping
export const BACKEND_TO_FRONTEND_ROLES: Record<string, string> = {
  'SUPERADMIN': 'superAdmin',
  'ADMIN': 'admin', 
  'HR_MANAGER': 'hrManager',
  'FINANCE_MANAGER': 'financeManager',
  'SALES_MANAGER': 'salesManager',
  'DEPARTMENT_MANAGER': 'departmentManager',
  'PROJECT_MANAGER': 'projectManager',
  'EMPLOYEE': 'employee',
  'INTERN': 'intern'
}

// Frontend to Backend role mapping (reverse)
export const FRONTEND_TO_BACKEND_ROLES: Record<string, string> = {
  'superAdmin': 'SUPERADMIN',
  'admin': 'ADMIN',
  'hrManager': 'HR_MANAGER', 
  'financeManager': 'FINANCE_MANAGER',
  'salesManager': 'SALES_MANAGER',
  'departmentManager': 'DEPARTMENT_MANAGER',
  'projectManager': 'PROJECT_MANAGER',
  'employee': 'EMPLOYEE',
  'intern': 'INTERN'
}

// Role display names for translations
export const ROLE_TRANSLATIONS = {
  ar: {
    superAdmin: 'مدير النظام الأعلى',
    admin: 'مدير النظام',
    hrManager: 'مدير الموارد البشرية',
    financeManager: 'مدير المالية',
    salesManager: 'مدير المبيعات',
    departmentManager: 'مدير القسم',
    projectManager: 'مدير المشروع',
    employee: 'موظف',
    intern: 'متدرب'
  },
  en: {
    superAdmin: 'Super Administrator',
    admin: 'Administrator',
    hrManager: 'HR Manager',
    financeManager: 'Finance Manager',
    salesManager: 'Sales Manager',
    departmentManager: 'Department Manager',
    projectManager: 'Project Manager',
    employee: 'Employee',
    intern: 'Intern'
  }
}

/**
 * Convert backend role name to frontend role name
 */
export function mapBackendRoleToFrontend(backendRole: string): string {
  return BACKEND_TO_FRONTEND_ROLES[backendRole] || 'employee'
}

/**
 * Convert frontend role name to backend role name
 */
export function mapFrontendRoleToBackend(frontendRole: string): string {
  return FRONTEND_TO_BACKEND_ROLES[frontendRole] || 'EMPLOYEE'
}

/**
 * Get role display name for given language (expects backend role name)
 */
export function getRoleDisplayName(role: string, language: 'ar' | 'en'): string {
  const frontendRole = mapBackendRoleToFrontend(role)
  return ROLE_TRANSLATIONS[language][frontendRole as keyof typeof ROLE_TRANSLATIONS[typeof language]] || role
}

/**
 * Get role display name from frontend role name
 */
export function getFrontendRoleDisplayName(frontendRole: string, language: 'ar' | 'en'): string {
  return ROLE_TRANSLATIONS[language][frontendRole as keyof typeof ROLE_TRANSLATIONS[typeof language]] || frontendRole
}

/**
 * Check if a role has admin privileges
 */
export function isAdminRole(role: string): boolean {
  const frontendRole = mapBackendRoleToFrontend(role)
  return ['superAdmin', 'admin'].includes(frontendRole)
}

/**
 * Check if a role has super admin privileges
 */
export function isSuperAdminRole(role: string): boolean {
  const frontendRole = mapBackendRoleToFrontend(role)
  return frontendRole === 'superAdmin'
}

/**
 * Get role hierarchy level (lower number = higher privilege)
 */
export function getRoleLevel(role: string): number {
  const frontendRole = mapBackendRoleToFrontend(role)
  const levels: Record<string, number> = {
    superAdmin: 1,
    admin: 2,
    hrManager: 3,
    financeManager: 3,
    salesManager: 3,
    departmentManager: 4,
    projectManager: 4,
    employee: 5,
    intern: 6
  }
  return levels[frontendRole] || 5
}

/**
 * Role permissions configuration
 */
export const ROLE_PERMISSIONS = {
  // Roles that can create new employees
  canCreateEmployees: ['superAdmin', 'admin', 'hrManager'],

  // Roles that can edit any employee
  canEditAnyEmployee: ['superAdmin', 'admin'],

  // Roles that can delete employees
  canDeleteEmployees: ['superAdmin', 'admin'],

  // Roles that can view all employees
  canViewAllEmployees: ['superAdmin', 'admin', 'hrManager', 'financeManager', 'salesManager'],

  // Roles that can export employee data
  canExportEmployees: ['superAdmin', 'admin', 'hrManager'],

  // Roles that can manage roles/permissions
  canManageRoles: ['superAdmin'],

  // Roles that can view sensitive information (salary, etc.)
  canViewSensitiveInfo: ['superAdmin', 'admin', 'hrManager', 'financeManager']
}

/**
 * Check if a role has a specific permission
 */
export function hasPermission(userRole: string, permission: keyof typeof ROLE_PERMISSIONS): boolean {
  const frontendRole = mapBackendRoleToFrontend(userRole)
  return ROLE_PERMISSIONS[permission].includes(frontendRole)
}

/**
 * Check if user can create employees
 */
export function canCreateEmployees(userRole: string): boolean {
  return hasPermission(userRole, 'canCreateEmployees')
}

/**
 * Check if user can edit any employee
 */
export function canEditAnyEmployee(userRole: string): boolean {
  return hasPermission(userRole, 'canEditAnyEmployee')
}

/**
 * Check if user can delete employees
 */
export function canDeleteEmployees(userRole: string): boolean {
  return hasPermission(userRole, 'canDeleteEmployees')
}

/**
 * Check if user can view all employees
 */
export function canViewAllEmployees(userRole: string): boolean {
  return hasPermission(userRole, 'canViewAllEmployees')
}

/**
 * Get roles that can create employees (for display purposes)
 */
export function getRolesWithCreatePermission(): string[] {
  return ROLE_PERMISSIONS.canCreateEmployees
}
