/**
 * KPI Test Utilities
 * 
 * Utilities to test the KPI component fixes and verify they handle
 * edge cases properly without crashing.
 */

import { RealTimeKPIData } from '@/components/kpi/RealTimeKPICard'

// Test data that would previously cause errors
export const problematicKPIData: RealTimeKPIData[] = [
  // K<PERSON> with null trend (would cause "Cannot read properties of null" error)
  {
    id: 'test-1',
    name: 'Revenue Growth',
    current_value: 150000,
    target_value: 200000,
    unit: '$',
    trend: null as any, // This would crash the component before our fix
    status: 'good'
  },
  
  // KPI with undefined trend
  {
    id: 'test-2', 
    name: 'Customer Satisfaction',
    current_value: 85,
    target_value: 90,
    unit: '%',
    trend: undefined, // This would also crash
    status: 'warning'
  },
  
  // KPI with malformed trend object
  {
    id: 'test-3',
    name: 'Employee Productivity',
    current_value: 75,
    target_value: 80,
    unit: '%',
    trend: { invalidProperty: 'test' } as any, // Malformed object
    status: 'critical'
  },
  
  // KPI with null current_value
  {
    id: 'test-4',
    name: 'Sales Volume',
    current_value: null as any,
    target_value: 1000,
    unit: 'units',
    trend: 'up',
    status: 'good'
  },
  
  // Valid KPI with object trend (should work fine)
  {
    id: 'test-5',
    name: 'Market Share',
    current_value: 25,
    target_value: 30,
    unit: '%',
    trend: {
      direction: 'up' as const,
      change_percentage: 5.2
    },
    status: 'good'
  },
  
  // Valid KPI with string trend (should work fine)
  {
    id: 'test-6',
    name: 'Cost Reduction',
    current_value: 12,
    target_value: 15,
    unit: '%',
    trend: 'down' as const,
    status: 'good'
  }
]

/**
 * Test function to verify getTrendIcon handles null/undefined safely
 */
export const testGetTrendIcon = (trend: any) => {
  // Simulate the fixed getTrendIcon logic
  if (!trend) {
    return 'stable-icon' // Would return <Minus> component
  }

  const trendDirection = typeof trend === 'object' && trend !== null 
    ? trend.direction 
    : trend

  switch (trendDirection) {
    case 'up':
      return 'up-icon'
    case 'down':
      return 'down-icon'
    default:
      return 'stable-icon'
  }
}

/**
 * Test function to verify KPI conversion handles edge cases
 */
export const testKPIConversion = (kpis: any[]) => {
  return kpis.map(kpi => {
    try {
      // Safe trend extraction with null handling (our fix)
      let safeTrend: 'up' | 'down' | 'stable' = 'stable'
      if (kpi.trend) {
        if (typeof kpi.trend === 'object' && kpi.trend !== null && 'direction' in kpi.trend) {
          safeTrend = kpi.trend.direction as 'up' | 'down' | 'stable'
        } else if (typeof kpi.trend === 'string') {
          safeTrend = kpi.trend as 'up' | 'down' | 'stable'
        }
      }

      return {
        ...kpi,
        current_value: typeof kpi.current_value === 'object' ? kpi.current_value?.value : kpi.current_value,
        trend: safeTrend
      }
    } catch (error) {
      console.error('❌ Error converting KPI:', kpi, error)
      return {
        ...kpi,
        current_value: 0,
        trend: 'stable' as 'up' | 'down' | 'stable'
      }
    }
  })
}

/**
 * Run all tests to verify fixes work
 */
export const runKPIFixTests = () => {
  console.log('🧪 Running KPI Fix Tests...')
  
  // Test 1: getTrendIcon with problematic data
  console.log('Test 1: getTrendIcon with null/undefined trends')
  const trendTests = [null, undefined, { invalidProperty: 'test' }, 'up', { direction: 'down' }]
  trendTests.forEach((trend, index) => {
    try {
      const result = testGetTrendIcon(trend)
      console.log(`✅ Test 1.${index + 1}: ${JSON.stringify(trend)} -> ${result}`)
    } catch (error) {
      console.error(`❌ Test 1.${index + 1} failed:`, error)
    }
  })
  
  // Test 2: KPI conversion with problematic data
  console.log('Test 2: KPI conversion with edge cases')
  try {
    const convertedKPIs = testKPIConversion(problematicKPIData)
    console.log(`✅ Test 2: Successfully converted ${convertedKPIs.length} KPIs`)
    convertedKPIs.forEach((kpi, index) => {
      console.log(`  KPI ${index + 1}: ${kpi.name} - trend: ${kpi.trend}`)
    })
  } catch (error) {
    console.error('❌ Test 2 failed:', error)
  }
  
  console.log('🎉 All KPI fix tests completed!')
  return true
}

// Export for use in components or tests
export default {
  problematicKPIData,
  testGetTrendIcon,
  testKPIConversion,
  runKPIFixTests
}
