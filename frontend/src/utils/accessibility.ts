/**
 * WCAG 2.1 AA Accessibility Compliance System
 * Comprehensive accessibility features and compliance checking
 */

import { log } from './logger'

interface AccessibilityConfig {
  enableScreenReader: boolean
  enableKeyboardNavigation: boolean
  enableHighContrast: boolean
  enableReducedMotion: boolean
  enableFocusManagement: boolean
  announcePageChanges: boolean
  announceFormErrors: boolean
  announceStatusUpdates: boolean
}

interface AccessibilityViolation {
  type: 'color_contrast' | 'missing_alt' | 'missing_label' | 'keyboard_trap' | 'focus_order' | 'aria_missing'
  element: HTMLElement
  description: string
  severity: 'error' | 'warning' | 'info'
  wcagCriterion: string
}

class AccessibilityManager {
  private config: AccessibilityConfig
  private announcer: HTMLElement | null = null
  private focusHistory: HTMLElement[] = []
  private keyboardTrapStack: HTMLElement[] = []

  constructor(config: Partial<AccessibilityConfig> = {}) {
    this.config = {
      enableScreenReader: true,
      enableKeyboardNavigation: true,
      enableHighContrast: false,
      enableReducedMotion: false,
      enableFocusManagement: true,
      announcePageChanges: true,
      announceFormErrors: true,
      announceStatusUpdates: true,
      ...config
    }

    this.initialize()
  }

  private initialize(): void {
    this.createScreenReaderAnnouncer()
    this.setupKeyboardNavigation()
    this.setupFocusManagement()
    this.detectUserPreferences()
    this.setupAccessibilityObserver()

    log.info('accessibility', 'Accessibility system initialized', this.config)
  }

  /**
   * Create screen reader announcer element
   */
  private createScreenReaderAnnouncer(): void {
    if (!this.config.enableScreenReader) return

    this.announcer = document.createElement('div')
    this.announcer.setAttribute('aria-live', 'polite')
    this.announcer.setAttribute('aria-atomic', 'true')
    this.announcer.setAttribute('class', 'sr-only')
    this.announcer.style.cssText = `
      position: absolute !important;
      width: 1px !important;
      height: 1px !important;
      padding: 0 !important;
      margin: -1px !important;
      overflow: hidden !important;
      clip: rect(0, 0, 0, 0) !important;
      white-space: nowrap !important;
      border: 0 !important;
    `

    document.body.appendChild(this.announcer)
  }

  /**
   * Announce message to screen readers
   */
  public announce(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
    if (!this.announcer || !this.config.enableScreenReader) return

    this.announcer.setAttribute('aria-live', priority)
    this.announcer.textContent = message

    log.debug('accessibility', 'Screen reader announcement', { message, priority })

    // Clear after announcement
    setTimeout(() => {
      if (this.announcer) {
        this.announcer.textContent = ''
      }
    }, 1000)
  }

  /**
   * Setup keyboard navigation
   */
  private setupKeyboardNavigation(): void {
    if (!this.config.enableKeyboardNavigation) return

    document.addEventListener('keydown', (e) => {
      this.handleKeyboardNavigation(e)
    })

    // Add skip links
    this.addSkipLinks()
  }

  private handleKeyboardNavigation(e: KeyboardEvent): void {
    switch (e.key) {
      case 'Tab':
        this.handleTabNavigation(e)
        break
      case 'Escape':
        this.handleEscapeKey(e)
        break
      case 'Enter':
      case ' ':
        this.handleActivation(e)
        break
      case 'ArrowUp':
      case 'ArrowDown':
      case 'ArrowLeft':
      case 'ArrowRight':
        this.handleArrowNavigation(e)
        break
    }
  }

  private handleTabNavigation(e: KeyboardEvent): void {
    const focusableElements = this.getFocusableElements()
    const currentIndex = focusableElements.indexOf(document.activeElement as HTMLElement)

    // Check for keyboard traps
    if (this.keyboardTrapStack.length > 0) {
      const trapContainer = this.keyboardTrapStack[this.keyboardTrapStack.length - 1]
      const trapFocusable = this.getFocusableElements(trapContainer)
      
      if (trapFocusable.length > 0) {
        e.preventDefault()
        const trapIndex = trapFocusable.indexOf(document.activeElement as HTMLElement)
        const nextIndex = e.shiftKey 
          ? (trapIndex - 1 + trapFocusable.length) % trapFocusable.length
          : (trapIndex + 1) % trapFocusable.length
        
        trapFocusable[nextIndex].focus()
      }
    }
  }

  private handleEscapeKey(e: KeyboardEvent): void {
    // Close modals, dropdowns, etc.
    const activeModal = document.querySelector('[role="dialog"][aria-hidden="false"]')
    if (activeModal) {
      this.closeModal(activeModal as HTMLElement)
      e.preventDefault()
    }

    // Exit keyboard trap
    if (this.keyboardTrapStack.length > 0) {
      this.exitKeyboardTrap()
      e.preventDefault()
    }
  }

  private handleActivation(e: KeyboardEvent): void {
    const target = e.target as HTMLElement
    
    // Handle custom interactive elements
    if (target.getAttribute('role') === 'button' && !target.hasAttribute('disabled')) {
      target.click()
      e.preventDefault()
    }
  }

  private handleArrowNavigation(e: KeyboardEvent): void {
    const target = e.target as HTMLElement
    const role = target.getAttribute('role')

    // Handle menu navigation
    if (role === 'menuitem' || target.closest('[role="menu"]')) {
      this.handleMenuNavigation(e)
    }

    // Handle tab navigation
    if (role === 'tab' || target.closest('[role="tablist"]')) {
      this.handleTabListNavigation(e)
    }
  }

  private handleMenuNavigation(e: KeyboardEvent): void {
    const menu = (e.target as HTMLElement).closest('[role="menu"]')
    if (!menu) return

    const menuItems = Array.from(menu.querySelectorAll('[role="menuitem"]')) as HTMLElement[]
    const currentIndex = menuItems.indexOf(e.target as HTMLElement)

    let nextIndex = currentIndex
    if (e.key === 'ArrowDown') {
      nextIndex = (currentIndex + 1) % menuItems.length
    } else if (e.key === 'ArrowUp') {
      nextIndex = (currentIndex - 1 + menuItems.length) % menuItems.length
    }

    if (nextIndex !== currentIndex) {
      menuItems[nextIndex].focus()
      e.preventDefault()
    }
  }

  private handleTabListNavigation(e: KeyboardEvent): void {
    const tabList = (e.target as HTMLElement).closest('[role="tablist"]')
    if (!tabList) return

    const tabs = Array.from(tabList.querySelectorAll('[role="tab"]')) as HTMLElement[]
    const currentIndex = tabs.indexOf(e.target as HTMLElement)

    let nextIndex = currentIndex
    if (e.key === 'ArrowRight') {
      nextIndex = (currentIndex + 1) % tabs.length
    } else if (e.key === 'ArrowLeft') {
      nextIndex = (currentIndex - 1 + tabs.length) % tabs.length
    }

    if (nextIndex !== currentIndex) {
      tabs[nextIndex].focus()
      tabs[nextIndex].click() // Activate the tab
      e.preventDefault()
    }
  }

  /**
   * Add skip links for keyboard navigation
   */
  private addSkipLinks(): void {
    const skipLinks = document.createElement('div')
    skipLinks.className = 'skip-links'
    skipLinks.innerHTML = `
      <a href="#main-content" class="skip-link">تخطي إلى المحتوى الرئيسي</a>
      <a href="#navigation" class="skip-link">تخطي إلى التنقل</a>
      <a href="#search" class="skip-link">تخطي إلى البحث</a>
    `

    // Add CSS for skip links
    const style = document.createElement('style')
    style.textContent = `
      .skip-links {
        position: absolute;
        top: -40px;
        left: 6px;
        z-index: 1000;
      }
      .skip-link {
        position: absolute;
        top: -40px;
        left: 6px;
        background: #000;
        color: #fff;
        padding: 8px;
        text-decoration: none;
        border-radius: 4px;
        font-size: 14px;
        transition: top 0.3s;
      }
      .skip-link:focus {
        top: 6px;
      }
    `

    document.head.appendChild(style)
    document.body.insertBefore(skipLinks, document.body.firstChild)
  }

  /**
   * Setup focus management
   */
  private setupFocusManagement(): void {
    if (!this.config.enableFocusManagement) return

    // Track focus history
    document.addEventListener('focusin', (e) => {
      const target = e.target as HTMLElement
      if (target && target !== document.body) {
        this.focusHistory.push(target)
        // Keep only last 10 focused elements
        if (this.focusHistory.length > 10) {
          this.focusHistory.shift()
        }
      }
    })

    // Add focus indicators
    this.addFocusIndicators()
  }

  private addFocusIndicators(): void {
    const style = document.createElement('style')
    style.textContent = `
      /* Enhanced focus indicators */
      *:focus {
        outline: 2px solid #0066cc !important;
        outline-offset: 2px !important;
      }
      
      /* High contrast focus for buttons */
      button:focus,
      [role="button"]:focus {
        outline: 3px solid #0066cc !important;
        outline-offset: 2px !important;
        box-shadow: 0 0 0 1px #fff, 0 0 0 4px #0066cc !important;
      }
      
      /* Focus for form elements */
      input:focus,
      textarea:focus,
      select:focus {
        outline: 2px solid #0066cc !important;
        outline-offset: 1px !important;
        border-color: #0066cc !important;
      }
    `
    document.head.appendChild(style)
  }

  /**
   * Detect user accessibility preferences
   */
  private detectUserPreferences(): void {
    // Detect reduced motion preference
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      this.config.enableReducedMotion = true
      this.applyReducedMotion()
    }

    // Detect high contrast preference
    if (window.matchMedia('(prefers-contrast: high)').matches) {
      this.config.enableHighContrast = true
      this.applyHighContrast()
    }

    // Listen for preference changes
    window.matchMedia('(prefers-reduced-motion: reduce)').addEventListener('change', (e) => {
      this.config.enableReducedMotion = e.matches
      if (e.matches) {
        this.applyReducedMotion()
      }
    })

    window.matchMedia('(prefers-contrast: high)').addEventListener('change', (e) => {
      this.config.enableHighContrast = e.matches
      if (e.matches) {
        this.applyHighContrast()
      }
    })
  }

  private applyReducedMotion(): void {
    const style = document.createElement('style')
    style.textContent = `
      *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
      }
    `
    document.head.appendChild(style)
    
    this.announce('تم تطبيق تقليل الحركة للمساعدة في إمكانية الوصول')
  }

  private applyHighContrast(): void {
    document.body.classList.add('high-contrast')
    
    const style = document.createElement('style')
    style.textContent = `
      .high-contrast {
        filter: contrast(150%) !important;
      }
      .high-contrast button,
      .high-contrast [role="button"] {
        border: 2px solid #000 !important;
        background: #fff !important;
        color: #000 !important;
      }
      .high-contrast a {
        color: #0000ff !important;
        text-decoration: underline !important;
      }
    `
    document.head.appendChild(style)
    
    this.announce('تم تطبيق التباين العالي للمساعدة في إمكانية الوصول')
  }

  /**
   * Get all focusable elements
   */
  private getFocusableElements(container: HTMLElement = document.body): HTMLElement[] {
    const focusableSelectors = [
      'a[href]',
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
      '[role="button"]:not([disabled])',
      '[role="link"]',
      '[role="menuitem"]',
      '[role="tab"]'
    ].join(', ')

    return Array.from(container.querySelectorAll(focusableSelectors)) as HTMLElement[]
  }

  /**
   * Keyboard trap management
   */
  public trapKeyboard(container: HTMLElement): void {
    this.keyboardTrapStack.push(container)
    
    // Focus first focusable element in container
    const focusableElements = this.getFocusableElements(container)
    if (focusableElements.length > 0) {
      focusableElements[0].focus()
    }
  }

  public exitKeyboardTrap(): HTMLElement | null {
    const exitedTrap = this.keyboardTrapStack.pop()
    
    // Return focus to previous element
    if (this.focusHistory.length > 0) {
      const previousFocus = this.focusHistory[this.focusHistory.length - 1]
      if (previousFocus && document.contains(previousFocus)) {
        previousFocus.focus()
      }
    }
    
    return exitedTrap || null
  }

  /**
   * Modal management
   */
  public openModal(modal: HTMLElement): void {
    modal.setAttribute('aria-hidden', 'false')
    modal.setAttribute('role', 'dialog')
    
    // Trap keyboard focus
    this.trapKeyboard(modal)
    
    // Announce modal opening
    const modalTitle = modal.querySelector('h1, h2, h3, [role="heading"]')?.textContent
    if (modalTitle) {
      this.announce(`تم فتح نافذة حوار: ${modalTitle}`)
    }
  }

  public closeModal(modal: HTMLElement): void {
    modal.setAttribute('aria-hidden', 'true')
    
    // Exit keyboard trap
    this.exitKeyboardTrap()
    
    // Announce modal closing
    this.announce('تم إغلاق النافذة')
  }

  /**
   * Setup accessibility observer for dynamic content
   */
  private setupAccessibilityObserver(): void {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              this.checkNewElementAccessibility(node as HTMLElement)
            }
          })
        }
      })
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true
    })
  }

  private checkNewElementAccessibility(element: HTMLElement): void {
    // Check for missing alt attributes on images
    const images = element.querySelectorAll('img:not([alt])')
    images.forEach((img) => {
      log.warn('accessibility', 'Image missing alt attribute', img)
    })

    // Check for missing labels on form elements
    const formElements = element.querySelectorAll('input, textarea, select')
    formElements.forEach((input) => {
      // PERFORMANCE FIX: Skip hidden elements (like Radix UI internal elements)
      const isHidden = input.hasAttribute('aria-hidden') && input.getAttribute('aria-hidden') === 'true'
      const isNotFocusable = input.hasAttribute('tabindex') && input.getAttribute('tabindex') === '-1'
      const isVisuallyHidden = input.style.position === 'absolute' &&
                              (input.style.width === '1px' || input.style.height === '1px')

      // Skip accessibility check for intentionally hidden elements
      if (isHidden || (isNotFocusable && isVisuallyHidden)) {
        return
      }

      const hasLabel = input.hasAttribute('aria-label') ||
                      input.hasAttribute('aria-labelledby') ||
                      document.querySelector(`label[for="${input.id}"]`)

      if (!hasLabel) {
        log.warn('accessibility', 'Form element missing label', input)
      }
    })
  }

  /**
   * Page change announcement
   */
  public announcePageChange(pageName: string): void {
    if (!this.config.announcePageChanges) return
    
    this.announce(`انتقلت إلى صفحة: ${pageName}`, 'assertive')
  }

  /**
   * Form error announcement
   */
  public announceFormErrors(errors: string[]): void {
    if (!this.config.announceFormErrors || errors.length === 0) return
    
    const errorMessage = errors.length === 1 
      ? `خطأ في النموذج: ${errors[0]}`
      : `${errors.length} أخطاء في النموذج: ${errors.join(', ')}`
    
    this.announce(errorMessage, 'assertive')
  }

  /**
   * Status update announcement
   */
  public announceStatus(message: string, type: 'success' | 'error' | 'info' = 'info'): void {
    if (!this.config.announceStatusUpdates) return
    
    const prefix = {
      success: 'نجح:',
      error: 'خطأ:',
      info: 'معلومات:'
    }[type]
    
    this.announce(`${prefix} ${message}`, type === 'error' ? 'assertive' : 'polite')
  }

  /**
   * Get accessibility configuration
   */
  public getConfig(): AccessibilityConfig {
    return { ...this.config }
  }

  /**
   * Update accessibility configuration
   */
  public updateConfig(newConfig: Partial<AccessibilityConfig>): void {
    this.config = { ...this.config, ...newConfig }
    log.info('accessibility', 'Configuration updated', this.config)
  }
}

// Singleton instance
export const accessibility = new AccessibilityManager()

// Convenience functions
export const announce = (message: string, priority?: 'polite' | 'assertive') => 
  accessibility.announce(message, priority)

export const announcePageChange = (pageName: string) => 
  accessibility.announcePageChange(pageName)

export const announceFormErrors = (errors: string[]) => 
  accessibility.announceFormErrors(errors)

export const announceStatus = (message: string, type?: 'success' | 'error' | 'info') => 
  accessibility.announceStatus(message, type)

export const trapKeyboard = (container: HTMLElement) => 
  accessibility.trapKeyboard(container)

export const exitKeyboardTrap = () => 
  accessibility.exitKeyboardTrap()

export const openModal = (modal: HTMLElement) => 
  accessibility.openModal(modal)

export const closeModal = (modal: HTMLElement) => 
  accessibility.closeModal(modal)

export default accessibility
