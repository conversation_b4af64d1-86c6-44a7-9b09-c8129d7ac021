/**
 * Tests for RealTimeKPICard Component
 * 
 * These tests verify that the component handles null/undefined trend values
 * safely and doesn't crash when receiving malformed data.
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import RealTimeKPICard, { RealTimeKPIData } from '../RealTimeKPICard'

describe('RealTimeKPICard', () => {
  const baseKPI: RealTimeKPIData = {
    id: 'test-kpi-1',
    name: 'Test KPI',
    current_value: 100,
    target_value: 150,
    unit: '%',
    status: 'good'
  }

  it('renders without crashing with null trend', () => {
    const kpiWithNullTrend = {
      ...baseKPI,
      trend: null as any
    }

    render(<RealTimeKPICard kpi={kpiWithNullTrend} />)
    
    expect(screen.getByText('Test KPI')).toBeInTheDocument()
    expect(screen.getByText('100%')).toBeInTheDocument()
  })

  it('renders without crashing with undefined trend', () => {
    const kpiWithUndefinedTrend = {
      ...baseKPI,
      trend: undefined
    }

    render(<RealTimeKPICard kpi={kpiWithUndefinedTrend} />)
    
    expect(screen.getByText('Test KPI')).toBeInTheDocument()
    expect(screen.getByText('100%')).toBeInTheDocument()
  })

  it('renders correctly with object trend', () => {
    const kpiWithObjectTrend = {
      ...baseKPI,
      trend: {
        direction: 'up' as const,
        change_percentage: 15
      }
    }

    render(<RealTimeKPICard kpi={kpiWithObjectTrend} />)
    
    expect(screen.getByText('Test KPI')).toBeInTheDocument()
    expect(screen.getByText('100%')).toBeInTheDocument()
  })

  it('renders correctly with string trend', () => {
    const kpiWithStringTrend = {
      ...baseKPI,
      trend: 'down' as const
    }

    render(<RealTimeKPICard kpi={kpiWithStringTrend} />)
    
    expect(screen.getByText('Test KPI')).toBeInTheDocument()
    expect(screen.getByText('100%')).toBeInTheDocument()
  })

  it('handles malformed trend object safely', () => {
    const kpiWithMalformedTrend = {
      ...baseKPI,
      trend: { invalidProperty: 'test' } as any
    }

    render(<RealTimeKPICard kpi={kpiWithMalformedTrend} />)
    
    expect(screen.getByText('Test KPI')).toBeInTheDocument()
    expect(screen.getByText('100%')).toBeInTheDocument()
  })

  it('handles null current_value safely', () => {
    const kpiWithNullValue = {
      ...baseKPI,
      current_value: null as any
    }

    render(<RealTimeKPICard kpi={kpiWithNullValue} />)
    
    expect(screen.getByText('Test KPI')).toBeInTheDocument()
    expect(screen.getByText('--')).toBeInTheDocument()
  })

  it('handles object current_value safely', () => {
    const kpiWithObjectValue = {
      ...baseKPI,
      current_value: {
        value: 85,
        period_start: '2024-01-01',
        period_end: '2024-01-31',
        recorded_at: '2024-01-31T23:59:59Z'
      }
    }

    render(<RealTimeKPICard kpi={kpiWithObjectValue} />)
    
    expect(screen.getByText('Test KPI')).toBeInTheDocument()
    expect(screen.getByText('85%')).toBeInTheDocument()
  })
})
