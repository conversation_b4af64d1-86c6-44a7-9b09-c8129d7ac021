/**
 * Real-time KPI Card Component
 * 
 * This component displays KPI data with real-time updates and visual indicators.
 * It provides enterprise-grade visualization with automatic refresh capabilities.
 * 
 * Features:
 * - Real-time value updates via WebSocket
 * - Visual trend indicators
 * - Achievement percentage display
 * - Alert status indicators
 * - Professional animations
 * - Responsive design
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  TrendingUp,
  TrendingDown,
  Minus,
  Target,
  AlertTriangle,
  CheckCircle,
  Activity,
  Zap,
  Clock
} from 'lucide-react'

export interface RealTimeKPIData {
  id: string
  name: string
  current_value?: number | { value: number; period_start: string; period_end: string; recorded_at: string }
  target_value?: number
  warning_threshold?: number
  critical_threshold?: number
  unit?: string
  trend?: 'up' | 'down' | 'stable' | { direction: 'up' | 'down' | 'stable'; change_percentage: number }
  achievement_percentage?: number
  last_updated?: string
  status?: string
  category?: string
}

interface RealTimeKPICardProps {
  kpi: RealTimeKPIData
  isLive?: boolean
  showTrend?: boolean
  showProgress?: boolean
  showLastUpdate?: boolean
  compact?: boolean
  language?: 'en' | 'ar'
  onClick?: () => void
}

const RealTimeKPICard: React.FC<RealTimeKPICardProps> = ({
  kpi,
  isLive = false,
  showTrend = true,
  showProgress = true,
  showLastUpdate = true,
  compact = false,
  onClick
}) => {
  const [isUpdating, setIsUpdating] = useState(false)
  const [previousValue, setPreviousValue] = useState<number | undefined>(kpi.current_value)

  // Extract numeric value from current_value
  const getCurrentValue = (): number | undefined => {
    if (typeof kpi.current_value === 'number') {
      return kpi.current_value
    } else if (typeof kpi.current_value === 'object' && kpi.current_value?.value) {
      return kpi.current_value.value
    }
    return undefined
  }

  const currentNumericValue = getCurrentValue()

  // Detect value changes for animation
  useEffect(() => {
    if (currentNumericValue !== previousValue) {
      setIsUpdating(true)
      setPreviousValue(currentNumericValue)

      // Reset animation after 1 second
      const timer = setTimeout(() => {
        setIsUpdating(false)
      }, 1000)

      return () => clearTimeout(timer)
    }
  }, [currentNumericValue, previousValue])

  // Calculate status based on thresholds
  const getStatus = () => {
    const value = getCurrentValue()
    if (!value) return 'unknown'

    if (kpi.critical_threshold && value <= kpi.critical_threshold) {
      return 'critical'
    } else if (kpi.warning_threshold && value <= kpi.warning_threshold) {
      return 'warning'
    } else if (kpi.target_value && value >= kpi.target_value) {
      return 'excellent'
    } else {
      return 'good'
    }
  }

  const status = getStatus()

  // Get status colors
  const getStatusColors = () => {
    switch (status) {
      case 'critical':
        return {
          border: 'border-red-500/30',
          bg: 'bg-red-500/10',
          text: 'text-red-400',
          icon: AlertTriangle
        }
      case 'warning':
        return {
          border: 'border-yellow-500/30',
          bg: 'bg-yellow-500/10',
          text: 'text-yellow-400',
          icon: AlertTriangle
        }
      case 'excellent':
        return {
          border: 'border-green-500/30',
          bg: 'bg-green-500/10',
          text: 'text-green-400',
          icon: CheckCircle
        }
      default:
        return {
          border: 'border-blue-500/30',
          bg: 'bg-blue-500/10',
          text: 'text-blue-400',
          icon: Target
        }
    }
  }

  const colors = getStatusColors()
  const StatusIcon = colors.icon

  // Get trend icon
  const getTrendIcon = () => {
    // Handle null/undefined trend safely
    if (!kpi.trend) {
      return <Minus className="h-4 w-4 text-white/50" />
    }

    const trend = typeof kpi.trend === 'object' && kpi.trend !== null
      ? kpi.trend.direction
      : kpi.trend

    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-400" />
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-400" />
      default:
        return <Minus className="h-4 w-4 text-white/50" />
    }
  }

  // Format value
  const formatValue = (value?: number) => {
    if (value === undefined || value === null || typeof value !== 'number') return '--'

    // Format based on unit
    if (kpi.unit === '%') {
      return `${value.toFixed(1)}%`
    } else if (kpi.unit === '$' || kpi.unit === 'USD') {
      return `$${value.toLocaleString()}`
    } else {
      return value.toLocaleString()
    }
  }

  // Format last update time
  const formatLastUpdate = (timestamp?: string) => {
    if (!timestamp) return ''
    
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    
    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`
    return date.toLocaleDateString()
  }

  return (
    <Card 
      className={`glass-card ${colors.border} ${colors.bg} transition-all duration-300 hover:scale-105 cursor-pointer ${
        isUpdating ? 'animate-pulse' : ''
      } ${compact ? 'p-2' : 'p-4'}`}
      onClick={onClick}
    >
      <CardHeader className={compact ? 'pb-2' : 'pb-3'}>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className={`text-white ${compact ? 'text-sm' : 'text-base'} font-medium truncate`}>
              {kpi.name}
            </span>
            {isLive && (
              <Badge className="bg-green-500/20 text-green-400 border-green-500/30 animate-pulse">
                <Activity className="h-3 w-3 mr-1" />
                Live
              </Badge>
            )}
          </div>
          <StatusIcon className={`h-5 w-5 ${colors.text}`} />
        </CardTitle>
      </CardHeader>
      
      <CardContent className={compact ? 'pt-0' : 'pt-2'}>
        {/* Main Value */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <span className={`${compact ? 'text-xl' : 'text-2xl'} font-bold text-white ${
              isUpdating ? 'animate-bounce' : ''
            }`}>
              {formatValue(getCurrentValue())}
            </span>
            {showTrend && getTrendIcon()}
          </div>
          
          {kpi.target_value && (
            <div className="text-right">
              <p className="text-xs text-white/50">Target</p>
              <p className="text-sm text-white/70">{formatValue(kpi.target_value)}</p>
            </div>
          )}
        </div>

        {/* Progress Bar */}
        {showProgress && kpi.achievement_percentage !== undefined && (
          <div className="mb-3">
            <div className="flex justify-between items-center mb-1">
              <span className="text-xs text-white/70">Achievement</span>
              <span className="text-xs text-white/70">{kpi.achievement_percentage.toFixed(1)}%</span>
            </div>
            <Progress 
              value={Math.min(kpi.achievement_percentage, 100)} 
              className="h-2"
            />
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between text-xs text-white/50">
          {showLastUpdate && (
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>{formatLastUpdate(kpi.last_updated)}</span>
            </div>
          )}
          
          {isLive && (
            <div className="flex items-center gap-1">
              <Zap className="h-3 w-3 text-yellow-400" />
              <span className="text-yellow-400">Auto-calculated</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export default RealTimeKPICard
