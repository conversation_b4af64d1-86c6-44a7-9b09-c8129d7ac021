import React from 'react'
import { useSelector } from 'react-redux'
import type { RootState } from '../store'

/**
 * Debug component to show login button state
 * Remove this component after fixing the loading issue
 */
export const LoginButtonDebug: React.FC = () => {
  const { isLoading, isVerifying, isAuthenticated, error, loginAttempts } = useSelector((state: RootState) => state.auth)

  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <div className="fixed top-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs z-50">
      <h3 className="font-bold mb-2">🐛 Auth Debug</h3>
      <div className="space-y-1">
        <div>isLoading: <span className={isLoading ? 'text-red-400' : 'text-green-400'}>{String(isLoading)}</span></div>
        <div>isVerifying: <span className={isVerifying ? 'text-yellow-400' : 'text-green-400'}>{String(isVerifying)}</span></div>
        <div>isAuthenticated: <span className={isAuthenticated ? 'text-green-400' : 'text-red-400'}>{String(isAuthenticated)}</span></div>
        <div>loginAttempts: <span className="text-blue-400">{loginAttempts}</span></div>
        <div>error: <span className={error ? 'text-red-400' : 'text-green-400'}>{error || 'null'}</span></div>
      </div>
    </div>
  )
}

export default LoginButtonDebug
