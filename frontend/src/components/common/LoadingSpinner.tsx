/**
 * Enhanced Loading Spinner Component
 * Provides various loading states with accessibility and performance optimizations
 */

import React from 'react'
import { Loader2, RefreshCw, Zap } from 'lucide-react'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'default' | 'minimal' | 'pulse' | 'dots' | 'skeleton'
  message?: string
  className?: string
  fullScreen?: boolean
  color?: 'primary' | 'secondary' | 'accent'
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  variant = 'default',
  message,
  className = '',
  fullScreen = false,
  color = 'primary'
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  }

  const colorClasses = {
    primary: 'text-blue-500',
    secondary: 'text-purple-500',
    accent: 'text-green-500'
  }

  const renderSpinner = () => {
    switch (variant) {
      case 'minimal':
        return (
          <div className={`${sizeClasses[size]} ${colorClasses[color]} animate-spin`}>
            <RefreshCw className="w-full h-full" />
          </div>
        )

      case 'pulse':
        return (
          <div className={`${sizeClasses[size]} ${colorClasses[color]} animate-pulse`}>
            <Zap className="w-full h-full" />
          </div>
        )

      case 'dots':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={`w-2 h-2 ${colorClasses[color]} bg-current rounded-full animate-bounce`}
                style={{ animationDelay: `${i * 0.1}s` }}
              />
            ))}
          </div>
        )

      case 'skeleton':
        return (
          <div className="space-y-3 animate-pulse">
            <div className="h-4 bg-gray-300 rounded w-3/4"></div>
            <div className="h-4 bg-gray-300 rounded w-1/2"></div>
            <div className="h-4 bg-gray-300 rounded w-5/6"></div>
          </div>
        )

      default:
        return (
          <div className={`${sizeClasses[size]} ${colorClasses[color]} animate-spin`}>
            <Loader2 className="w-full h-full" />
          </div>
        )
    }
  }

  const content = (
    <div className={`flex flex-col items-center justify-center space-y-3 ${className}`}>
      {renderSpinner()}
      {message && (
        <p className="text-sm text-gray-600 dark:text-gray-400 text-center max-w-xs">
          {message}
        </p>
      )}
      {/* Accessibility: Screen reader announcement */}
      <span className="sr-only" aria-live="polite">
        {message || 'جاري التحميل...'}
      </span>
    </div>
  )

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm z-50 flex items-center justify-center">
        {content}
      </div>
    )
  }

  return content
}

// Skeleton loading components for specific use cases
export const TableSkeleton: React.FC<{ rows?: number; columns?: number }> = ({ 
  rows = 5, 
  columns = 4 
}) => (
  <div className="animate-pulse space-y-4">
    {/* Header skeleton */}
    <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
      {Array.from({ length: columns }).map((_, i) => (
        <div key={i} className="h-4 bg-gray-300 rounded"></div>
      ))}
    </div>
    
    {/* Rows skeleton */}
    {Array.from({ length: rows }).map((_, rowIndex) => (
      <div key={rowIndex} className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
        {Array.from({ length: columns }).map((_, colIndex) => (
          <div key={colIndex} className="h-4 bg-gray-200 rounded"></div>
        ))}
      </div>
    ))}
  </div>
)

export const CardSkeleton: React.FC<{ count?: number }> = ({ count = 3 }) => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    {Array.from({ length: count }).map((_, i) => (
      <div key={i} className="animate-pulse">
        <div className="bg-white rounded-lg shadow p-6 space-y-4">
          <div className="h-4 bg-gray-300 rounded w-3/4"></div>
          <div className="h-4 bg-gray-300 rounded w-1/2"></div>
          <div className="h-20 bg-gray-200 rounded"></div>
          <div className="flex space-x-2">
            <div className="h-8 bg-gray-300 rounded w-20"></div>
            <div className="h-8 bg-gray-300 rounded w-20"></div>
          </div>
        </div>
      </div>
    ))}
  </div>
)

export const DashboardSkeleton: React.FC = () => (
  <div className="space-y-6 animate-pulse">
    {/* Header skeleton */}
    <div className="flex justify-between items-center">
      <div className="h-8 bg-gray-300 rounded w-48"></div>
      <div className="h-10 bg-gray-300 rounded w-32"></div>
    </div>
    
    {/* Stats cards skeleton */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {Array.from({ length: 4 }).map((_, i) => (
        <div key={i} className="bg-white rounded-lg shadow p-6 space-y-3">
          <div className="h-4 bg-gray-300 rounded w-2/3"></div>
          <div className="h-8 bg-gray-300 rounded w-1/2"></div>
          <div className="h-3 bg-gray-200 rounded w-full"></div>
        </div>
      ))}
    </div>
    
    {/* Chart skeleton */}
    <div className="bg-white rounded-lg shadow p-6">
      <div className="h-6 bg-gray-300 rounded w-1/3 mb-4"></div>
      <div className="h-64 bg-gray-200 rounded"></div>
    </div>
  </div>
)

// Performance optimized loading states
export const LazyComponentLoader: React.FC<{ 
  componentName?: string 
  estimatedLoadTime?: number 
}> = ({ 
  componentName = 'المكون', 
  estimatedLoadTime = 2000 
}) => {
  const [showDetailedMessage, setShowDetailedMessage] = React.useState(false)

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setShowDetailedMessage(true)
    }, estimatedLoadTime / 2)

    return () => clearTimeout(timer)
  }, [estimatedLoadTime])

  return (
    <LoadingSpinner
      size="lg"
      variant="default"
      message={
        showDetailedMessage 
          ? `جاري تحميل ${componentName}... قد يستغرق هذا بضع ثوانٍ`
          : `جاري تحميل ${componentName}...`
      }
      fullScreen
    />
  )
}

// Page loader component for full-screen loading
export const PageLoader: React.FC<{ message?: string }> = ({ message }) => (
  <LoadingSpinner
    size="lg"
    variant="default"
    message={message || 'جاري التحميل...'}
    fullScreen
  />
)

export default LoadingSpinner
