/**
 * KPI Domain Types
 * Comprehensive interfaces for KPI management
 */

import { BaseEntity, LocalizedContent, AuditTrail, ID, Timestamp, Status } from './index'

// KPI measurement types
export type MeasurementType = 'NUMBER' | 'PERCENTAGE' | 'CURRENCY' | 'RATIO' | 'SCORE' | 'TIME'

// KPI frequency types
export type KPIFrequency = 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'YEARLY'

// KPI trend direction
export type TrendDirection = 'UP' | 'DOWN' | 'TARGET'

// KPI status
export type KPIStatus = 'ACTIVE' | 'INACTIVE' | 'DRAFT' | 'ARCHIVED'

// Priority levels
export type Priority = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'

// Core KPI interface
export interface KPI extends BaseEntity, LocalizedContent, AuditTrail {
  category: string
  category_name: string
  category_name_ar: string
  measurement_type: MeasurementType
  unit: string
  unit_ar: string
  frequency: KPIFrequency
  trend_direction: TrendDirection
  formula: string
  data_source: string
  calculation_method: string
  target_value: number | null
  warning_threshold: number | null
  critical_threshold: number | null
  visible_to_roles: string[]
  owner: string | null
  owner_name: string
  status: KPIStatus
  is_automated: boolean
  automation_config: Record<string, any>
  created_by: string
  updated_by: string
  current_value?: number
  achievement_percentage?: number
  trend?: 'up' | 'down' | 'stable'
  change_percentage?: number
  last_updated?: Timestamp
}

// KPI Category interface
export interface KPICategory extends BaseEntity, LocalizedContent {
  color: string
  icon?: string
  sort_order?: number
  parent_category_id?: ID
  is_default?: boolean
}

// KPI Value interface
export interface KPIValue extends BaseEntity {
  kpi_id: ID
  value: number
  period_start: Timestamp
  period_end: Timestamp
  notes?: string
  data_source?: string
  is_manual: boolean
  validated_by?: ID
  validation_date?: Timestamp
}

// KPI Target interface
export interface KPITarget extends BaseEntity {
  kpi_id: ID
  target_value: number
  warning_threshold?: number
  critical_threshold?: number
  period_start: Timestamp
  period_end: Timestamp
  priority: Priority
  notes?: string
  assigned_to?: ID
}

// KPI Alert interface
export interface KPIAlert extends BaseEntity {
  kpi_id: ID
  severity: Priority
  message: string
  message_ar: string
  status: 'ACTIVE' | 'ACKNOWLEDGED' | 'RESOLVED'
  triggered_at: Timestamp
  acknowledged_by?: ID
  acknowledged_at?: Timestamp
  resolved_by?: ID
  resolved_at?: Timestamp
  threshold_type: 'WARNING' | 'CRITICAL'
  threshold_value: number
  actual_value: number
}

// Create and Update types DEPRECATED - Manual KPI entry disabled for enterprise automation
// All KPIs are now automatically calculated from operational data
// These interfaces are kept for backward compatibility only

export interface CreateKPIData {
  // DEPRECATED: Manual KPI creation disabled
  readonly _deprecated: 'Manual KPI creation has been disabled for enterprise automation'
}

export interface UpdateKPIData {
  // DEPRECATED: Manual KPI updates disabled
  readonly _deprecated: 'Manual KPI updates have been disabled for enterprise automation'
}

export interface CreateKPICategoryData {
  name: string
  name_ar: string
  description?: string
  description_ar?: string
  color: string
  icon?: string
  sort_order?: number
  parent_category_id?: ID
}

export interface UpdateKPICategoryData extends Partial<CreateKPICategoryData> {
  id?: ID
}

export interface CreateKPIValueData {
  kpi_id: ID
  value: number
  period_start: string
  period_end: string
  notes?: string
  data_source?: string
  is_manual?: boolean
}

export interface UpdateKPIValueData extends Partial<CreateKPIValueData> {
  id?: ID
}

export interface CreateKPITargetData {
  kpi_id: ID
  target_value: number
  warning_threshold?: number
  critical_threshold?: number
  period_start: string
  period_end: string
  priority: Priority
  notes?: string
  assigned_to?: ID
}

export interface UpdateKPITargetData extends Partial<CreateKPITargetData> {
  id?: ID
}

// Filter interfaces
export interface KPIFilters {
  search?: string
  category?: string
  status?: KPIStatus
  measurement_type?: MeasurementType
  frequency?: KPIFrequency
  owner?: string
  is_automated?: boolean
  page?: number
  page_size?: number
  ordering?: string
}

export interface KPICategoryFilters {
  search?: string
  is_active?: boolean
  parent_category_id?: ID
  page?: number
  page_size?: number
  ordering?: string
}

export interface KPIValueFilters {
  kpi_id?: ID
  period_start?: string
  period_end?: string
  is_manual?: boolean
  page?: number
  page_size?: number
  ordering?: string
}

// Dashboard and analytics interfaces
export interface KPIDashboard {
  categories: KPICategory[]
  recent_alerts: KPIAlert[]
  top_performing_kpis: KPI[]
  underperforming_kpis: KPI[]
  kpi_summary: {
    total_kpis: number
    active_kpis: number
    kpis_on_target: number
    kpis_above_target: number
    kpis_below_target: number
    active_alerts: number
    critical_alerts: number
    categories_count: number
    last_updated: string
  }
}

export interface KPITrendData {
  kpi_id: string
  kpi_name: string
  data_points: Array<{
    date: string
    value: number
    period_start: string
    period_end: string
  }>
  trend_direction: 'up' | 'down' | 'stable'
  change_percentage: number
}

// Statistics interfaces
export interface KPIStats {
  total: number
  by_status: Record<string, number>
  by_category: Record<string, number>
  by_measurement_type: Record<string, number>
  average_achievement: number
  alerts_count: number
}
