// FIXED: Asset interface to match backend API exactly
export interface Asset {
  id: number
  asset_id: string // FIXED: Backend uses asset_id
  name: string
  name_ar?: string
  category: number // FIXED: Backend sends category ID as number
  category_name: string // FIXED: Backend provides category_name for display
  description?: string
  description_ar?: string
  serial_number?: string
  model?: string
  manufacturer?: string
  purchase_date: string
  purchase_price: number // FIXED: Backend uses purchase_price
  current_value?: number
  warranty_expiry?: string
  location: string
  assigned_to?: number
  assigned_to_name?: string // FIXED: Backend provides assigned_to_name for display
  status: 'AVAILABLE' | 'IN_USE' | 'MAINTENANCE' | 'RETIRED' | 'LOST' | 'DAMAGED' // FIXED: Match backend status choices
  notes?: string
  created_at: string
  updated_at: string
}

// FIXED: CreateAssetData interface to match backend API exactly
export interface CreateAssetData {
  asset_id: string // FIXED: Backend expects asset_id
  name: string
  name_ar?: string
  category: number // FIXED: Backend expects category ID as number
  description?: string
  description_ar?: string
  serial_number?: string
  model?: string
  manufacturer?: string
  purchase_date: string
  purchase_price: number // FIXED: Backend expects purchase_price
  current_value?: number
  warranty_expiry?: string
  location: string
  assigned_to?: number
  status?: 'AVAILABLE' | 'IN_USE' | 'MAINTENANCE' | 'RETIRED' | 'LOST' | 'DAMAGED' // FIXED: Match backend status choices
  notes?: string
}

export interface AssetFilters {
  search?: string
  category?: string
  status?: Asset['status']
  condition?: Asset['condition']
  assigned_to?: string
  location?: string
  page?: number
  page_size?: number
}

export interface AssetStats {
  total_assets: number
  by_status: Record<string, number>
  by_condition: Record<string, number>
  total_value: number
  maintenance_due: number
}
