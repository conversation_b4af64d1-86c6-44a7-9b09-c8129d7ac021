/**
 * Cash Flow Statement Report
 * Comprehensive cash flow analysis with operating, investing, and financing activities
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Calendar,
  Download,
  Printer,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Activity,
  Building2,
  Banknote,
  FileText,
  RefreshCw,
  ArrowUpCircle,
  ArrowDownCircle
} from 'lucide-react'

interface CashFlowReportProps {
  language: 'ar' | 'en'
}

interface CFLineItem {
  account_code: string
  account_name: string
  current_period: number
  previous_period: number
  variance: number
  variance_percent: number
  is_subtotal?: boolean
  is_total?: boolean
  level: number
}

interface CFReport {
  company_name: string
  report_period: string
  comparison_period: string
  currency: string
  // Operating Activities
  operating_activities: CFLineItem[]
  net_cash_from_operating: CFLineItem
  // Investing Activities
  investing_activities: CFLineItem[]
  net_cash_from_investing: CFLineItem
  // Financing Activities
  financing_activities: CFLineItem[]
  net_cash_from_financing: CFLineItem
  // Summary
  net_change_in_cash: CFLineItem
  cash_beginning: CFLineItem
  cash_ending: CFLineItem
  generated_at: string
}

const translations = {
  ar: {
    cashFlowStatement: 'قائمة التدفقات النقدية',
    statementOfCashFlows: 'بيان التدفقات النقدية',
    forPeriod: 'للفترة',
    comparedTo: 'مقارنة بـ',
    operatingActivities: 'الأنشطة التشغيلية',
    investingActivities: 'الأنشطة الاستثمارية',
    financingActivities: 'الأنشطة التمويلية',
    netCashFromOperating: 'صافي النقد من الأنشطة التشغيلية',
    netCashFromInvesting: 'صافي النقد من الأنشطة الاستثمارية',
    netCashFromFinancing: 'صافي النقد من الأنشطة التمويلية',
    netChangeInCash: 'صافي التغيير في النقد',
    cashBeginning: 'النقد في بداية الفترة',
    cashEnding: 'النقد في نهاية الفترة',
    currentPeriod: 'الفترة الحالية',
    previousPeriod: 'الفترة السابقة',
    variance: 'التغيير',
    variancePercent: 'نسبة التغيير',
    accountCode: 'رمز الحساب',
    accountName: 'اسم الحساب',
    amount: 'المبلغ',
    // Actions
    downloadPDF: 'تحميل PDF',
    print: 'طباعة',
    export: 'تصدير',
    refresh: 'تحديث',
    selectPeriod: 'اختيار الفترة',
    // Periods
    thisMonth: 'هذا الشهر',
    lastMonth: 'الشهر الماضي',
    thisQuarter: 'هذا الربع',
    lastQuarter: 'الربع الماضي',
    thisYear: 'هذا العام',
    lastYear: 'العام الماضي',
    // Status
    loading: 'جاري التحميل...',
    noData: 'لا توجد بيانات',
    generatedAt: 'تم إنشاؤه في',
    // Cash flow items
    netIncome: 'صافي الدخل',
    depreciation: 'الاستهلاك',
    accountsReceivableChange: 'التغيير في الذمم المدينة',
    inventoryChange: 'التغيير في المخزون',
    accountsPayableChange: 'التغيير في الذمم الدائنة',
    purchaseOfEquipment: 'شراء معدات',
    saleOfAssets: 'بيع أصول',
    capitalContributions: 'مساهمات رأس المال',
    dividendsPaid: 'أرباح مدفوعة',
    loanProceeds: 'متحصلات قروض',
    loanRepayments: 'سداد قروض',
    // Metrics
    operatingCashRatio: 'نسبة النقد التشغيلي',
    freeCashFlow: 'التدفق النقدي الحر',
    cashConversionCycle: 'دورة تحويل النقد',
    cashFlowMargin: 'هامش التدفق النقدي',
    // Indicators
    positiveFlow: 'تدفق إيجابي',
    negativeFlow: 'تدفق سلبي',
    cashInflow: 'تدفق نقدي داخل',
    cashOutflow: 'تدفق نقدي خارج'
  },
  en: {
    cashFlowStatement: 'Cash Flow Statement',
    statementOfCashFlows: 'Statement of Cash Flows',
    forPeriod: 'For Period',
    comparedTo: 'Compared to',
    operatingActivities: 'Operating Activities',
    investingActivities: 'Investing Activities',
    financingActivities: 'Financing Activities',
    netCashFromOperating: 'Net Cash from Operating Activities',
    netCashFromInvesting: 'Net Cash from Investing Activities',
    netCashFromFinancing: 'Net Cash from Financing Activities',
    netChangeInCash: 'Net Change in Cash',
    cashBeginning: 'Cash at Beginning of Period',
    cashEnding: 'Cash at End of Period',
    currentPeriod: 'Current Period',
    previousPeriod: 'Previous Period',
    variance: 'Variance',
    variancePercent: 'Variance %',
    accountCode: 'Account Code',
    accountName: 'Account Name',
    amount: 'Amount',
    // Actions
    downloadPDF: 'Download PDF',
    print: 'Print',
    export: 'Export',
    refresh: 'Refresh',
    selectPeriod: 'Select Period',
    // Periods
    thisMonth: 'This Month',
    lastMonth: 'Last Month',
    thisQuarter: 'This Quarter',
    lastQuarter: 'Last Quarter',
    thisYear: 'This Year',
    lastYear: 'Last Year',
    // Status
    loading: 'Loading...',
    noData: 'No data available',
    generatedAt: 'Generated at',
    // Cash flow items
    netIncome: 'Net Income',
    depreciation: 'Depreciation',
    accountsReceivableChange: 'Change in Accounts Receivable',
    inventoryChange: 'Change in Inventory',
    accountsPayableChange: 'Change in Accounts Payable',
    purchaseOfEquipment: 'Purchase of Equipment',
    saleOfAssets: 'Sale of Assets',
    capitalContributions: 'Capital Contributions',
    dividendsPaid: 'Dividends Paid',
    loanProceeds: 'Loan Proceeds',
    loanRepayments: 'Loan Repayments',
    // Metrics
    operatingCashRatio: 'Operating Cash Ratio',
    freeCashFlow: 'Free Cash Flow',
    cashConversionCycle: 'Cash Conversion Cycle',
    cashFlowMargin: 'Cash Flow Margin',
    // Indicators
    positiveFlow: 'Positive Flow',
    negativeFlow: 'Negative Flow',
    cashInflow: 'Cash Inflow',
    cashOutflow: 'Cash Outflow'
  }
}

const CashFlowReport: React.FC<CashFlowReportProps> = ({ language }) => {
  const t = translations[language]
  const [report, setReport] = useState<CFReport | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState('thisMonth')
  const [comparisonPeriod, setComparisonPeriod] = useState('lastMonth')

  useEffect(() => {
    fetchReport()
  }, [selectedPeriod, comparisonPeriod])

  const fetchReport = async () => {
    try {
      setLoading(true)
      const token = localStorage.getItem('token')
      const response = await fetch(`/api/financial-reports/cash-flow/?period=${selectedPeriod}&comparison=${comparisonPeriod}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })
      
      if (response.ok) {
        const data = await response.json()
        setReport(data)
      }
    } catch (error) {
      console.error('Error fetching cash flow report:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2,
    }).format(amount)
  }

  const formatPercent = (percent: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 1,
    }).format(percent / 100)
  }

  const getVarianceColor = (variance: number) => {
    if (variance === 0) return 'text-gray-500'
    return variance > 0 ? 'text-green-600' : 'text-red-600'
  }

  const getVarianceIcon = (variance: number) => {
    if (variance === 0) return null
    return variance > 0 ? 
      <TrendingUp className="h-4 w-4 inline ml-1" /> : 
      <TrendingDown className="h-4 w-4 inline ml-1" />
  }

  const getCashFlowIcon = (amount: number) => {
    if (amount === 0) return null
    return amount > 0 ? 
      <ArrowUpCircle className="h-4 w-4 inline mr-2 text-green-600" /> : 
      <ArrowDownCircle className="h-4 w-4 inline mr-2 text-red-600" />
  }

  const renderLineItem = (item: CFLineItem) => {
    const indentClass = `pl-${item.level * 4}`
    const fontWeight = item.is_total ? 'font-bold' : item.is_subtotal ? 'font-semibold' : 'font-normal'
    const borderClass = item.is_total ? 'border-t-2 border-b-2 border-gray-800' : 
                       item.is_subtotal ? 'border-t border-gray-400' : ''

    return (
      <tr key={item.account_code} className={`${borderClass} hover:bg-gray-50`}>
        <td className={`py-2 px-4 ${indentClass} ${fontWeight}`}>
          {getCashFlowIcon(item.current_period)}
          {item.account_code && (
            <span className="text-gray-500 text-sm mr-2">{item.account_code}</span>
          )}
          {item.account_name}
        </td>
        <td className={`py-2 px-4 text-right font-mono ${fontWeight}`}>
          {formatCurrency(item.current_period)}
        </td>
        <td className={`py-2 px-4 text-right font-mono ${fontWeight}`}>
          {formatCurrency(item.previous_period)}
        </td>
        <td className={`py-2 px-4 text-right font-mono ${fontWeight} ${getVarianceColor(item.variance)}`}>
          {formatCurrency(Math.abs(item.variance))}
          {getVarianceIcon(item.variance)}
        </td>
        <td className={`py-2 px-4 text-right ${fontWeight} ${getVarianceColor(item.variance)}`}>
          {item.variance_percent !== 0 && formatPercent(Math.abs(item.variance_percent))}
        </td>
      </tr>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!report) {
    return (
      <div className="text-center py-8">
        <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500">{t.noData}</p>
      </div>
    )
  }

  // Calculate key metrics
  const operatingCashFlow = report.net_cash_from_operating.current_period
  const investingCashFlow = report.net_cash_from_investing.current_period
  const financingCashFlow = report.net_cash_from_financing.current_period
  const freeCashFlow = operatingCashFlow + investingCashFlow
  const netCashChange = report.net_change_in_cash.current_period

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{t.cashFlowStatement}</h1>
          <p className="text-gray-600 mt-1">{t.statementOfCashFlows}</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={fetchReport}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {t.refresh}
          </Button>
          <Button variant="outline">
            <Printer className="h-4 w-4 mr-2" />
            {t.print}
          </Button>
          <Button>
            <Download className="h-4 w-4 mr-2" />
            {t.downloadPDF}
          </Button>
        </div>
      </div>

      {/* Period Selection */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4 items-center">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="font-medium">{t.selectPeriod}:</span>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="thisMonth">{t.thisMonth}</option>
                <option value="lastMonth">{t.lastMonth}</option>
                <option value="thisQuarter">{t.thisQuarter}</option>
                <option value="lastQuarter">{t.lastQuarter}</option>
                <option value="thisYear">{t.thisYear}</option>
                <option value="lastYear">{t.lastYear}</option>
              </select>
              <span className="self-center text-gray-500">{t.comparedTo}</span>
              <select
                value={comparisonPeriod}
                onChange={(e) => setComparisonPeriod(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="lastMonth">{t.lastMonth}</option>
                <option value="lastQuarter">{t.lastQuarter}</option>
                <option value="lastYear">{t.lastYear}</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Cash Flow Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t.netCashFromOperating}</p>
                <p className={`text-2xl font-bold mt-1 ${operatingCashFlow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatCurrency(operatingCashFlow)}
                </p>
              </div>
              <Activity className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-orange-50 to-orange-100 border-orange-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t.netCashFromInvesting}</p>
                <p className={`text-2xl font-bold mt-1 ${investingCashFlow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatCurrency(investingCashFlow)}
                </p>
              </div>
              <Building2 className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t.netCashFromFinancing}</p>
                <p className={`text-2xl font-bold mt-1 ${financingCashFlow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatCurrency(financingCashFlow)}
                </p>
              </div>
              <Banknote className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t.freeCashFlow}</p>
                <p className={`text-2xl font-bold mt-1 ${freeCashFlow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatCurrency(freeCashFlow)}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Net Cash Change Summary */}
      <Card className={netCashChange >= 0 ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{t.netChangeInCash}</h3>
              <p className={`text-3xl font-bold mt-2 ${netCashChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {formatCurrency(netCashChange)}
              </p>
              <p className="text-sm text-gray-600 mt-1">
                {netCashChange >= 0 ? t.positiveFlow : t.negativeFlow}
              </p>
            </div>
            <div className="text-right">
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <p className="text-sm text-gray-600">{t.cashEnding}</p>
                <p className="text-xl font-bold text-blue-600">{formatCurrency(report.cash_ending.current_period)}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Cash Flow Statement */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>{report.company_name}</CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                {t.cashFlowStatement} - {report.report_period}
              </p>
            </div>
            <div className="text-right text-sm text-gray-500">
              {t.generatedAt}: {new Date(report.generated_at).toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US')}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b-2 border-gray-800">
                  <th className="text-left py-3 px-4 font-bold">{t.accountName}</th>
                  <th className="text-right py-3 px-4 font-bold">{t.currentPeriod}</th>
                  <th className="text-right py-3 px-4 font-bold">{t.previousPeriod}</th>
                  <th className="text-right py-3 px-4 font-bold">{t.variance}</th>
                  <th className="text-right py-3 px-4 font-bold">{t.variancePercent}</th>
                </tr>
              </thead>
              <tbody>
                {/* Operating Activities */}
                <tr className="bg-blue-50">
                  <td colSpan={5} className="py-2 px-4 font-bold text-blue-800 uppercase">
                    {t.operatingActivities}
                  </td>
                </tr>
                {report.operating_activities.map(item => renderLineItem(item))}
                {renderLineItem(report.net_cash_from_operating)}
                
                {/* Investing Activities */}
                <tr className="bg-orange-50">
                  <td colSpan={5} className="py-2 px-4 font-bold text-orange-800 uppercase">
                    {t.investingActivities}
                  </td>
                </tr>
                {report.investing_activities.map(item => renderLineItem(item))}
                {renderLineItem(report.net_cash_from_investing)}
                
                {/* Financing Activities */}
                <tr className="bg-purple-50">
                  <td colSpan={5} className="py-2 px-4 font-bold text-purple-800 uppercase">
                    {t.financingActivities}
                  </td>
                </tr>
                {report.financing_activities.map(item => renderLineItem(item))}
                {renderLineItem(report.net_cash_from_financing)}
                
                {/* Net Change in Cash */}
                {renderLineItem(report.net_change_in_cash)}
                
                {/* Cash Beginning and Ending */}
                {renderLineItem(report.cash_beginning)}
                {renderLineItem(report.cash_ending)}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default CashFlowReport