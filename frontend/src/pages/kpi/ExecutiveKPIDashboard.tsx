/**
 * Executive KPI Dashboard Page
 * Enterprise-grade executive dashboard with comprehensive organizational KPIs
 */

import React from 'react'
import HierarchicalKPIDashboard from '../../components/kpi/HierarchicalKPIDashboard'

const ExecutiveKPIDashboard: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-6">
      <HierarchicalKPIDashboard
        dashboardType="executive"
        className="max-w-7xl mx-auto"
      />
    </div>
  )
}

export default ExecutiveKPIDashboard
