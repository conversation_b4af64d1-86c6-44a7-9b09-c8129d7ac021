/**
 * Project Analytics Dashboard Page
 * Project-focused KPI dashboard with project performance and timeline metrics
 */

import React from 'react'
import HierarchicalKPIDashboard from '../../components/kpi/HierarchicalKPIDashboard'

const ProjectAnalyticsDashboard: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-indigo-900 to-purple-900 p-6">
      <HierarchicalKPIDashboard
        dashboardType="project"
        className="max-w-7xl mx-auto"
      />
    </div>
  )
}

export default ProjectAnalyticsDashboard
