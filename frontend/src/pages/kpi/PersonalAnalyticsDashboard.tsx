/**
 * Personal Analytics Dashboard Page
 * Employee-focused KPI dashboard with personal performance metrics
 */

import React from 'react'
import HierarchicalKPIDashboard from '../../components/kpi/HierarchicalKPIDashboard'

const PersonalAnalyticsDashboard: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-pink-900 to-rose-900 p-6">
      <HierarchicalKPIDashboard
        dashboardType="personal"
        className="max-w-7xl mx-auto"
      />
    </div>
  )
}

export default PersonalAnalyticsDashboard
