import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { 
  FileText, 
  Plus, 
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  TrendingUp,
  Calendar,
  Search,
  Eye,
  Settings
} from 'lucide-react';
// Language is passed as prop, not from context

interface ComplianceFramework {
  id: number;
  name: string;
  name_ar: string;
  framework_type: string;
  description: string;
  description_ar: string;
  version: string;
  effective_date: string;
  is_active: boolean;
  implementation_status: string;
  compliance_score: number;
  control_count: number;
  implemented_controls: number;
  last_assessment_date: string | null;
  next_assessment_due: string | null;
  assessment_overdue: boolean;
  assessment_frequency_months: number;
  created_by_name: string;
  created_at: string;
}

interface ComplianceControl {
  id: number;
  framework: number;
  framework_name: string;
  control_id: string;
  name: string;
  name_ar: string;
  description: string;
  control_type: string;
  status: string;
  risk_level: string;
  priority: number;
  last_tested_date: string | null;
  test_frequency_months: number;
  test_overdue: boolean;
  control_owner_name: string;
  department_name: string;
  created_at: string;
}

interface ComplianceStats {
  total_frameworks: number;
  active_frameworks: number;
  average_compliance_score: number;
  overdue_assessments: number;
  status_breakdown: Array<{
    implementation_status: string;
    count: number;
  }>;
  top_frameworks: Array<{
    name: string;
    framework_type: string;
    compliance_score: number;
  }>;
}

interface ControlStats {
  total_controls: number;
  implemented_controls: number;
  implementation_rate: number;
  partially_implemented: number;
  not_implemented: number;
  high_risk_controls: number;
  critical_risk_controls: number;
  overdue_tests: number;
  status_breakdown: Array<{
    status: string;
    count: number;
  }>;
}

interface ComplianceManagementProps {
  language: 'ar' | 'en';
}

const ComplianceManagement: React.FC<ComplianceManagementProps> = ({ language }) => {
  const [frameworks, setFrameworks] = useState<ComplianceFramework[]>([]);
  const [controls, setControls] = useState<ComplianceControl[]>([]);
  const [complianceStats, setComplianceStats] = useState<ComplianceStats | null>(null);
  const [controlStats, setControlStats] = useState<ControlStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'frameworks' | 'controls'>('frameworks');
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [selectedFramework, setSelectedFramework] = useState<ComplianceFramework | null>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);

  useEffect(() => {
    fetchComplianceData();
  }, []);

  const fetchComplianceData = async () => {
    try {
      const [
        frameworksResponse,
        controlsResponse,
        complianceStatsResponse,
        controlStatsResponse
      ] = await Promise.all([
        fetch('/api/compliance-frameworks/'),
        fetch('/api/compliance-controls/'),
        fetch('/api/compliance-frameworks/compliance_dashboard/'),
        fetch('/api/compliance-controls/control_stats/')
      ]);

      if (frameworksResponse.ok) {
        const data = await frameworksResponse.json();
        setFrameworks(data.results || data);
      }

      if (controlsResponse.ok) {
        const data = await controlsResponse.json();
        setControls(data.results || data);
      }

      if (complianceStatsResponse.ok) {
        const data = await complianceStatsResponse.json();
        setComplianceStats(data);
      }

      if (controlStatsResponse.ok) {
        const data = await controlStatsResponse.json();
        setControlStats(data);
      }

    } catch (error) {
      console.error('Error fetching compliance data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateComplianceScore = async (frameworkId: number) => {
    try {
      const response = await fetch(`/api/compliance-frameworks/${frameworkId}/update_compliance_score/`, {
        method: 'POST',
      });

      if (response.ok) {
        fetchComplianceData();
        alert(language === 'ar' ? 'تم تحديث نقاط الامتثال بنجاح' : 'Compliance score updated successfully');
      }
    } catch (error) {
      console.error('Error updating compliance score:', error);
    }
  };

  const handleUpdateControlStatus = async (controlId: number, newStatus: string) => {
    try {
      const response = await fetch(`/api/compliance-controls/${controlId}/update_status/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus,
          implementation_notes: `Status updated to ${newStatus}`
        }),
      });

      if (response.ok) {
        fetchComplianceData();
        alert(language === 'ar' ? 'تم تحديث حالة التحكم بنجاح' : 'Control status updated successfully');
      }
    } catch (error) {
      console.error('Error updating control status:', error);
    }
  };

  const getComplianceScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 75) return 'text-blue-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStatusColor = (status: string) => {
    const colors = {
      'IMPLEMENTED': 'bg-green-100 text-green-800',
      'PARTIALLY_IMPLEMENTED': 'bg-yellow-100 text-yellow-800',
      'NOT_IMPLEMENTED': 'bg-red-100 text-red-800',
      'NEEDS_IMPROVEMENT': 'bg-orange-100 text-orange-800',
      'NOT_APPLICABLE': 'bg-gray-100 text-gray-800',
      'PLANNING': 'bg-blue-100 text-blue-800',
      'IN_PROGRESS': 'bg-yellow-100 text-yellow-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getRiskLevelColor = (riskLevel: string) => {
    const colors = {
      'CRITICAL': 'bg-red-100 text-red-800',
      'HIGH': 'bg-orange-100 text-orange-800',
      'MEDIUM': 'bg-yellow-100 text-yellow-800',
      'LOW': 'bg-blue-100 text-blue-800'
    };
    return colors[riskLevel as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString();
  };

  const filteredFrameworks = frameworks.filter(framework => {
    const matchesSearch = !searchTerm || 
      framework.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      framework.framework_type.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = !typeFilter || framework.framework_type === typeFilter;
    const matchesStatus = !statusFilter || framework.implementation_status === statusFilter;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  const filteredControls = controls.filter(control => {
    const matchesSearch = !searchTerm || 
      control.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      control.control_id.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = !statusFilter || control.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'إدارة الامتثال' : 'Compliance Management'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'مراقبة وإدارة أطر الامتثال والضوابط'
              : 'Monitor and manage compliance frameworks and controls'
            }
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Settings className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'الإعدادات' : 'Settings'}
          </Button>
          <Button onClick={fetchComplianceData}>
            <TrendingUp className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تحديث' : 'Refresh'}
          </Button>
        </div>
      </div>

      {/* Statistics Overview */}
      {complianceStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'أطر العمل' : 'Frameworks'}
              </CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{complianceStats.total_frameworks}</div>
              <p className="text-xs text-muted-foreground">
                {complianceStats.active_frameworks} {language === 'ar' ? 'نشط' : 'active'}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'متوسط الامتثال' : 'Avg Compliance'}
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getComplianceScoreColor(complianceStats.average_compliance_score)}`}>
                {complianceStats.average_compliance_score.toFixed(1)}%
              </div>
              <Progress value={complianceStats.average_compliance_score} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'تقييمات متأخرة' : 'Overdue Assessments'}
              </CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {complianceStats.overdue_assessments}
              </div>
              <p className="text-xs text-muted-foreground">
                {language === 'ar' ? 'تتطلب انتباه' : 'require attention'}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'الضوابط المنفذة' : 'Implemented Controls'}
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {controlStats?.implemented_controls || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                {controlStats?.implementation_rate.toFixed(1) || 0}% {language === 'ar' ? 'معدل التنفيذ' : 'implementation rate'}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Tabs */}
      <div className="flex space-x-1 bg-muted p-1 rounded-lg w-fit">
        <Button
          variant={activeTab === 'frameworks' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('frameworks')}
        >
          <FileText className="h-4 w-4 mr-2" />
          {language === 'ar' ? 'أطر العمل' : 'Frameworks'}
        </Button>
        <Button
          variant={activeTab === 'controls' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('controls')}
        >
          <CheckCircle className="h-4 w-4 mr-2" />
          {language === 'ar' ? 'الضوابط' : 'Controls'}
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={language === 'ar' ? 'البحث...' : 'Search...'}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            {activeTab === 'frameworks' && (
              <>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder={language === 'ar' ? 'جميع الأنواع' : 'All types'} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">
                      {language === 'ar' ? 'جميع الأنواع' : 'All types'}
                    </SelectItem>
                    <SelectItem value="GDPR">GDPR</SelectItem>
                    <SelectItem value="SOX">SOX</SelectItem>
                    <SelectItem value="ISO_27001">ISO 27001</SelectItem>
                    <SelectItem value="SAUDI_DPA">Saudi DPA</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder={language === 'ar' ? 'جميع الحالات' : 'All statuses'} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">
                      {language === 'ar' ? 'جميع الحالات' : 'All statuses'}
                    </SelectItem>
                    <SelectItem value="PLANNING">
                      {language === 'ar' ? 'تخطيط' : 'Planning'}
                    </SelectItem>
                    <SelectItem value="IN_PROGRESS">
                      {language === 'ar' ? 'قيد التنفيذ' : 'In Progress'}
                    </SelectItem>
                    <SelectItem value="IMPLEMENTED">
                      {language === 'ar' ? 'منفذ' : 'Implemented'}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </>
            )}
            {activeTab === 'controls' && (
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder={language === 'ar' ? 'جميع الحالات' : 'All statuses'} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">
                    {language === 'ar' ? 'جميع الحالات' : 'All statuses'}
                  </SelectItem>
                  <SelectItem value="IMPLEMENTED">
                    {language === 'ar' ? 'منفذ' : 'Implemented'}
                  </SelectItem>
                  <SelectItem value="PARTIALLY_IMPLEMENTED">
                    {language === 'ar' ? 'منفذ جزئياً' : 'Partially Implemented'}
                  </SelectItem>
                  <SelectItem value="NOT_IMPLEMENTED">
                    {language === 'ar' ? 'غير منفذ' : 'Not Implemented'}
                  </SelectItem>
                  <SelectItem value="NEEDS_IMPROVEMENT">
                    {language === 'ar' ? 'يحتاج تحسين' : 'Needs Improvement'}
                  </SelectItem>
                </SelectContent>
              </Select>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Content based on active tab */}
      {activeTab === 'frameworks' ? (
        /* Frameworks Table */
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {language === 'ar' ? 'أطر الامتثال' : 'Compliance Frameworks'}
              <Badge variant="secondary" className="ml-2">
                {filteredFrameworks.length}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{language === 'ar' ? 'اسم الإطار' : 'Framework Name'}</TableHead>
                    <TableHead>{language === 'ar' ? 'النوع' : 'Type'}</TableHead>
                    <TableHead>{language === 'ar' ? 'نقاط الامتثال' : 'Compliance Score'}</TableHead>
                    <TableHead>{language === 'ar' ? 'الضوابط' : 'Controls'}</TableHead>
                    <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                    <TableHead>{language === 'ar' ? 'التقييم التالي' : 'Next Assessment'}</TableHead>
                    <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredFrameworks.map((framework) => (
                    <TableRow key={framework.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {language === 'ar' && framework.name_ar ? framework.name_ar : framework.name}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {language === 'ar' ? 'الإصدار:' : 'Version:'} {framework.version}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {framework.framework_type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className={`text-lg font-bold ${getComplianceScoreColor(framework.compliance_score)}`}>
                            {framework.compliance_score.toFixed(1)}%
                          </div>
                          <Progress value={framework.compliance_score} className="w-20" />
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className="font-medium">
                            {framework.implemented_controls}/{framework.control_count}
                          </div>
                          <div className="text-muted-foreground">
                            {language === 'ar' ? 'منفذ' : 'implemented'}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(framework.implementation_status)}>
                          {language === 'ar' 
                            ? framework.implementation_status === 'IMPLEMENTED' ? 'منفذ' 
                              : framework.implementation_status === 'IN_PROGRESS' ? 'قيد التنفيذ'
                              : framework.implementation_status === 'PLANNING' ? 'تخطيط'
                              : framework.implementation_status
                            : framework.implementation_status
                          }
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className={framework.assessment_overdue ? 'text-red-600 font-medium' : ''}>
                            {formatDate(framework.next_assessment_due)}
                          </div>
                          {framework.assessment_overdue && (
                            <div className="flex items-center gap-1 text-red-600">
                              <AlertTriangle className="h-3 w-3" />
                              <span className="text-xs">
                                {language === 'ar' ? 'متأخر' : 'Overdue'}
                              </span>
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => {
                              setSelectedFramework(framework);
                              setIsDetailsDialogOpen(true);
                            }}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleUpdateComplianceScore(framework.id)}
                          >
                            <TrendingUp className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      ) : (
        /* Controls Table */
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              {language === 'ar' ? 'ضوابط الامتثال' : 'Compliance Controls'}
              <Badge variant="secondary" className="ml-2">
                {filteredControls.length}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{language === 'ar' ? 'معرف الضابط' : 'Control ID'}</TableHead>
                    <TableHead>{language === 'ar' ? 'اسم الضابط' : 'Control Name'}</TableHead>
                    <TableHead>{language === 'ar' ? 'الإطار' : 'Framework'}</TableHead>
                    <TableHead>{language === 'ar' ? 'النوع' : 'Type'}</TableHead>
                    <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                    <TableHead>{language === 'ar' ? 'مستوى المخاطر' : 'Risk Level'}</TableHead>
                    <TableHead>{language === 'ar' ? 'آخر اختبار' : 'Last Test'}</TableHead>
                    <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredControls.map((control) => (
                    <TableRow key={control.id}>
                      <TableCell>
                        <code className="text-sm bg-muted px-2 py-1 rounded">
                          {control.control_id}
                        </code>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {language === 'ar' && control.name_ar ? control.name_ar : control.name}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {language === 'ar' ? 'الأولوية:' : 'Priority:'} {control.priority}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">{control.framework_name}</div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {control.control_type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(control.status)}>
                          {language === 'ar' 
                            ? control.status === 'IMPLEMENTED' ? 'منفذ' 
                              : control.status === 'PARTIALLY_IMPLEMENTED' ? 'منفذ جزئياً'
                              : control.status === 'NOT_IMPLEMENTED' ? 'غير منفذ'
                              : control.status === 'NEEDS_IMPROVEMENT' ? 'يحتاج تحسين'
                              : control.status
                            : control.status
                          }
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getRiskLevelColor(control.risk_level)}>
                          {language === 'ar' 
                            ? control.risk_level === 'CRITICAL' ? 'حرج' 
                              : control.risk_level === 'HIGH' ? 'عالي'
                              : control.risk_level === 'MEDIUM' ? 'متوسط'
                              : control.risk_level === 'LOW' ? 'منخفض'
                              : control.risk_level
                            : control.risk_level
                          }
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className={control.test_overdue ? 'text-red-600 font-medium' : ''}>
                            {formatDate(control.last_tested_date)}
                          </div>
                          {control.test_overdue && (
                            <div className="flex items-center gap-1 text-red-600">
                              <AlertTriangle className="h-3 w-3" />
                              <span className="text-xs">
                                {language === 'ar' ? 'متأخر' : 'Overdue'}
                              </span>
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          {control.status !== 'IMPLEMENTED' && (
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleUpdateControlStatus(control.id, 'IMPLEMENTED')}
                            >
                              <CheckCircle className="h-4 w-4" />
                            </Button>
                          )}
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Framework Details Dialog */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>
              {language === 'ar' ? 'تفاصيل إطار الامتثال' : 'Compliance Framework Details'}
            </DialogTitle>
          </DialogHeader>
          {selectedFramework && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'اسم الإطار' : 'Framework Name'}
                  </Label>
                  <div className="text-sm">
                    {language === 'ar' && selectedFramework.name_ar ? selectedFramework.name_ar : selectedFramework.name}
                  </div>
                </div>
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'النوع' : 'Type'}
                  </Label>
                  <div className="text-sm">{selectedFramework.framework_type}</div>
                </div>
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'نقاط الامتثال' : 'Compliance Score'}
                  </Label>
                  <div className="flex items-center gap-2">
                    <div className={`text-lg font-bold ${getComplianceScoreColor(selectedFramework.compliance_score)}`}>
                      {selectedFramework.compliance_score.toFixed(1)}%
                    </div>
                    <Progress value={selectedFramework.compliance_score} className="w-32" />
                  </div>
                </div>
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'حالة التنفيذ' : 'Implementation Status'}
                  </Label>
                  <div className="mt-1">
                    <Badge className={getStatusColor(selectedFramework.implementation_status)}>
                      {selectedFramework.implementation_status}
                    </Badge>
                  </div>
                </div>
              </div>

              <div>
                <Label className="font-medium">
                  {language === 'ar' ? 'الوصف' : 'Description'}
                </Label>
                <div className="text-sm mt-1 p-3 bg-muted rounded">
                  {language === 'ar' && selectedFramework.description_ar 
                    ? selectedFramework.description_ar 
                    : selectedFramework.description
                  }
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'تاريخ السريان' : 'Effective Date'}
                  </Label>
                  <div>{formatDate(selectedFramework.effective_date)}</div>
                </div>
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'آخر تقييم' : 'Last Assessment'}
                  </Label>
                  <div>{formatDate(selectedFramework.last_assessment_date)}</div>
                </div>
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'التقييم التالي' : 'Next Assessment'}
                  </Label>
                  <div className={selectedFramework.assessment_overdue ? 'text-red-600 font-medium' : ''}>
                    {formatDate(selectedFramework.next_assessment_due)}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'إجمالي الضوابط' : 'Total Controls'}
                  </Label>
                  <div className="text-2xl font-bold">{selectedFramework.control_count}</div>
                </div>
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'الضوابط المنفذة' : 'Implemented Controls'}
                  </Label>
                  <div className="text-2xl font-bold text-green-600">{selectedFramework.implemented_controls}</div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ComplianceManagement;
