import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON>Content, 
  Card<PERSON>eader, 
  CardTitle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { 
  Globe, 
  Plus, 
  Activity, 
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Settings,
  Play,
  Search,
  CreditCard,
  Truck,
  Mail,
  MessageSquare,
  Cloud,
  BarChart3
} from 'lucide-react';
// Language is passed as prop, not from context

interface ExternalService {
  id: number;
  name: string;
  service_type: string;
  description: string;
  base_url: string;
  api_version: string;
  authentication_type: string;
  status: string;
  is_healthy: boolean;
  success_rate: number;
  total_requests: number;
  successful_requests: number;
  failed_requests: number;
  last_request_at: string | null;
  health_status: string;
  created_by_name: string;
  created_at: string;
}

interface ServiceStats {
  total_services: number;
  active_services: number;
  healthy_services: number;
  total_requests: number;
  average_success_rate: number;
  service_type_breakdown: Array<{
    service_type: string;
    count: number;
    healthy_count: number;
  }>;
}

interface ExternalServicesProps {
  language?: 'ar' | 'en';
}

const ExternalServices: React.FC<ExternalServicesProps> = ({ language = 'en' }) => {
  const [services, setServices] = useState<ExternalService[]>([]);
  const [serviceStats, setServiceStats] = useState<ServiceStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [testingService, setTestingService] = useState<number | null>(null);

  // Form state
  const [serviceForm, setServiceForm] = useState({
    name: '',
    service_type: 'CUSTOM',
    description: '',
    base_url: '',
    api_version: '',
    authentication_type: 'API_KEY',
    timeout: 30,
    health_check_url: ''
  });

  useEffect(() => {
    fetchServices();
    fetchServiceStats();
  }, []);

  const fetchServices = async () => {
    try {
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (typeFilter) params.append('service_type', typeFilter);
      if (statusFilter) params.append('status', statusFilter);
      
      const response = await fetch(`/api/external-services/?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setServices(data.results || data);
      }
    } catch (error) {
      console.error('Error fetching services:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchServiceStats = async () => {
    try {
      const response = await fetch('/api/external-services/service_stats/');
      if (response.ok) {
        const data = await response.json();
        setServiceStats(data);
      }
    } catch (error) {
      console.error('Error fetching service stats:', error);
    }
  };

  const handleCreateService = async () => {
    try {
      const response = await fetch('/api/external-services/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(serviceForm),
      });

      if (response.ok) {
        fetchServices();
        fetchServiceStats();
        setIsAddDialogOpen(false);
        resetForm();
      }
    } catch (error) {
      console.error('Error creating service:', error);
    }
  };

  const handleHealthCheck = async (serviceId: number) => {
    setTestingService(serviceId);
    try {
      const response = await fetch(`/api/external-services/${serviceId}/health_check/`, {
        method: 'POST',
      });

      if (response.ok) {
        const data = await response.json();
        fetchServices(); // Refresh to get updated health status
        alert(
          language === 'ar' 
            ? `فحص الصحة: ${data.is_healthy ? 'صحي' : 'غير صحي'} (${data.response_time_ms}ms)`
            : `Health check: ${data.is_healthy ? 'Healthy' : 'Unhealthy'} (${data.response_time_ms}ms)`
        );
      }
    } catch (error) {
      console.error('Error performing health check:', error);
    } finally {
      setTestingService(null);
    }
  };

  const handleTestConnection = async (serviceId: number) => {
    setTestingService(serviceId);
    try {
      const response = await fetch(`/api/external-services/${serviceId}/test_connection/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ endpoint: '' }),
      });

      if (response.ok) {
        const data = await response.json();
        alert(
          language === 'ar' 
            ? `اختبار الاتصال: ${data.success ? 'نجح' : 'فشل'} (${data.response_time_ms || 0}ms)`
            : `Connection test: ${data.success ? 'Success' : 'Failed'} (${data.response_time_ms || 0}ms)`
        );
      }
    } catch (error) {
      console.error('Error testing connection:', error);
    } finally {
      setTestingService(null);
    }
  };

  const resetForm = () => {
    setServiceForm({
      name: '',
      service_type: 'CUSTOM',
      description: '',
      base_url: '',
      api_version: '',
      authentication_type: 'API_KEY',
      timeout: 30,
      health_check_url: ''
    });
  };

  const getServiceTypeIcon = (type: string) => {
    switch (type) {
      case 'PAYMENT':
        return <CreditCard className="h-4 w-4 text-green-600" />;
      case 'SHIPPING':
        return <Truck className="h-4 w-4 text-blue-600" />;
      case 'EMAIL':
        return <Mail className="h-4 w-4 text-purple-600" />;
      case 'SMS':
        return <MessageSquare className="h-4 w-4 text-orange-600" />;
      case 'STORAGE':
        return <Cloud className="h-4 w-4 text-cyan-600" />;
      case 'ANALYTICS':
        return <BarChart3 className="h-4 w-4 text-indigo-600" />;
      default:
        return <Globe className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      'ACTIVE': 'bg-green-100 text-green-800',
      'INACTIVE': 'bg-gray-100 text-gray-800',
      'TESTING': 'bg-blue-100 text-blue-800',
      'ERROR': 'bg-red-100 text-red-800',
      'MAINTENANCE': 'bg-yellow-100 text-yellow-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getHealthIcon = (healthStatus: string, isHealthy: boolean) => {
    if (healthStatus === 'UNKNOWN') {
      return <Clock className="h-4 w-4 text-gray-500" />;
    } else if (healthStatus === 'STALE') {
      return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    } else if (isHealthy) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    } else {
      return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString();
  };

  const filteredServices = services.filter(service => {
    const matchesSearch = !searchTerm || 
      service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.base_url.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = !typeFilter || service.service_type === typeFilter;
    const matchesStatus = !statusFilter || service.status === statusFilter;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'الخدمات الخارجية' : 'External Services'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'إدارة ومراقبة التكامل مع الخدمات الخارجية'
              : 'Manage and monitor external service integrations'
            }
          </p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'إضافة خدمة' : 'Add Service'}
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {language === 'ar' ? 'إضافة خدمة خارجية جديدة' : 'Add New External Service'}
              </DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>{language === 'ar' ? 'اسم الخدمة' : 'Service Name'}</Label>
                <Input
                  value={serviceForm.name}
                  onChange={(e) => setServiceForm({...serviceForm, name: e.target.value})}
                  placeholder={language === 'ar' ? 'أدخل اسم الخدمة' : 'Enter service name'}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'نوع الخدمة' : 'Service Type'}</Label>
                <Select value={serviceForm.service_type} onValueChange={(value) => setServiceForm({...serviceForm, service_type: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PAYMENT">
                      {language === 'ar' ? 'دفع' : 'Payment'}
                    </SelectItem>
                    <SelectItem value="SHIPPING">
                      {language === 'ar' ? 'شحن' : 'Shipping'}
                    </SelectItem>
                    <SelectItem value="EMAIL">
                      {language === 'ar' ? 'بريد إلكتروني' : 'Email'}
                    </SelectItem>
                    <SelectItem value="SMS">
                      {language === 'ar' ? 'رسائل نصية' : 'SMS'}
                    </SelectItem>
                    <SelectItem value="STORAGE">
                      {language === 'ar' ? 'تخزين' : 'Storage'}
                    </SelectItem>
                    <SelectItem value="ANALYTICS">
                      {language === 'ar' ? 'تحليلات' : 'Analytics'}
                    </SelectItem>
                    <SelectItem value="CUSTOM">
                      {language === 'ar' ? 'مخصص' : 'Custom'}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="md:col-span-2">
                <Label>{language === 'ar' ? 'الوصف' : 'Description'}</Label>
                <Textarea
                  value={serviceForm.description}
                  onChange={(e) => setServiceForm({...serviceForm, description: e.target.value})}
                  placeholder={language === 'ar' ? 'أدخل وصف الخدمة' : 'Enter service description'}
                  rows={3}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'الرابط الأساسي' : 'Base URL'}</Label>
                <Input
                  value={serviceForm.base_url}
                  onChange={(e) => setServiceForm({...serviceForm, base_url: e.target.value})}
                  placeholder="https://api.example.com"
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'إصدار API' : 'API Version'}</Label>
                <Input
                  value={serviceForm.api_version}
                  onChange={(e) => setServiceForm({...serviceForm, api_version: e.target.value})}
                  placeholder="v1, v2, etc."
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'نوع المصادقة' : 'Authentication Type'}</Label>
                <Select value={serviceForm.authentication_type} onValueChange={(value) => setServiceForm({...serviceForm, authentication_type: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="API_KEY">API Key</SelectItem>
                    <SelectItem value="OAUTH2">OAuth 2.0</SelectItem>
                    <SelectItem value="BASIC_AUTH">Basic Auth</SelectItem>
                    <SelectItem value="BEARER_TOKEN">Bearer Token</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>{language === 'ar' ? 'مهلة الاتصال (ثانية)' : 'Timeout (seconds)'}</Label>
                <Input
                  type="number"
                  value={serviceForm.timeout}
                  onChange={(e) => setServiceForm({...serviceForm, timeout: parseInt(e.target.value) || 30})}
                />
              </div>
              <div className="md:col-span-2">
                <Label>{language === 'ar' ? 'رابط فحص الصحة' : 'Health Check URL'}</Label>
                <Input
                  value={serviceForm.health_check_url}
                  onChange={(e) => setServiceForm({...serviceForm, health_check_url: e.target.value})}
                  placeholder="https://api.example.com/health"
                />
              </div>
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                {language === 'ar' ? 'إلغاء' : 'Cancel'}
              </Button>
              <Button onClick={handleCreateService}>
                {language === 'ar' ? 'إضافة الخدمة' : 'Add Service'}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Statistics */}
      {serviceStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'إجمالي الخدمات' : 'Total Services'}
              </CardTitle>
              <Globe className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{serviceStats.total_services}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'الخدمات النشطة' : 'Active Services'}
              </CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{serviceStats.active_services}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'الخدمات الصحية' : 'Healthy Services'}
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{serviceStats.healthy_services}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'إجمالي الطلبات' : 'Total Requests'}
              </CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{serviceStats.total_requests.toLocaleString()}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'معدل النجاح' : 'Success Rate'}
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{serviceStats.average_success_rate.toFixed(1)}%</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={language === 'ar' ? 'البحث في الخدمات...' : 'Search services...'}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الأنواع' : 'All types'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">
                  {language === 'ar' ? 'جميع الأنواع' : 'All types'}
                </SelectItem>
                <SelectItem value="PAYMENT">
                  {language === 'ar' ? 'دفع' : 'Payment'}
                </SelectItem>
                <SelectItem value="SHIPPING">
                  {language === 'ar' ? 'شحن' : 'Shipping'}
                </SelectItem>
                <SelectItem value="EMAIL">
                  {language === 'ar' ? 'بريد إلكتروني' : 'Email'}
                </SelectItem>
                <SelectItem value="SMS">
                  {language === 'ar' ? 'رسائل نصية' : 'SMS'}
                </SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الحالات' : 'All statuses'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">
                  {language === 'ar' ? 'جميع الحالات' : 'All statuses'}
                </SelectItem>
                <SelectItem value="ACTIVE">
                  {language === 'ar' ? 'نشط' : 'Active'}
                </SelectItem>
                <SelectItem value="INACTIVE">
                  {language === 'ar' ? 'غير نشط' : 'Inactive'}
                </SelectItem>
                <SelectItem value="ERROR">
                  {language === 'ar' ? 'خطأ' : 'Error'}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Services Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            {language === 'ar' ? 'الخدمات الخارجية' : 'External Services'}
            <Badge variant="secondary" className="ml-2">
              {filteredServices.length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{language === 'ar' ? 'اسم الخدمة' : 'Service Name'}</TableHead>
                  <TableHead>{language === 'ar' ? 'النوع' : 'Type'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الصحة' : 'Health'}</TableHead>
                  <TableHead>{language === 'ar' ? 'معدل النجاح' : 'Success Rate'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الطلبات' : 'Requests'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredServices.map((service) => (
                  <TableRow key={service.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{service.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {service.base_url}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getServiceTypeIcon(service.service_type)}
                        <span className="text-sm">
                          {language === 'ar' 
                            ? service.service_type === 'PAYMENT' ? 'دفع' 
                              : service.service_type === 'SHIPPING' ? 'شحن'
                              : service.service_type === 'EMAIL' ? 'بريد إلكتروني'
                              : service.service_type === 'SMS' ? 'رسائل نصية'
                              : service.service_type === 'STORAGE' ? 'تخزين'
                              : service.service_type === 'ANALYTICS' ? 'تحليلات'
                              : service.service_type
                            : service.service_type
                          }
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(service.status)}>
                        {language === 'ar' 
                          ? service.status === 'ACTIVE' ? 'نشط' 
                            : service.status === 'INACTIVE' ? 'غير نشط'
                            : service.status === 'ERROR' ? 'خطأ'
                            : service.status === 'TESTING' ? 'اختبار'
                            : service.status
                          : service.status
                        }
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getHealthIcon(service.health_status, service.is_healthy)}
                        <span className="text-sm">
                          {language === 'ar' 
                            ? service.health_status === 'HEALTHY' ? 'صحي' 
                              : service.health_status === 'UNHEALTHY' ? 'غير صحي'
                              : service.health_status === 'STALE' ? 'قديم'
                              : 'غير معروف'
                            : service.health_status
                          }
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <span className={service.success_rate >= 95 ? 'text-green-600' : service.success_rate >= 80 ? 'text-yellow-600' : 'text-red-600'}>
                          {service.success_rate.toFixed(1)}%
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="text-sm font-medium">{service.total_requests.toLocaleString()}</div>
                        <div className="text-xs text-muted-foreground">
                          {language === 'ar' ? 'آخر طلب:' : 'Last:'} {formatDate(service.last_request_at)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleHealthCheck(service.id)}
                          disabled={testingService === service.id}
                        >
                          {testingService === service.id ? (
                            <Clock className="h-4 w-4 animate-spin" />
                          ) : (
                            <Activity className="h-4 w-4" />
                          )}
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleTestConnection(service.id)}
                          disabled={testingService === service.id}
                        >
                          <Play className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Settings className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ExternalServices;
