/**
 * Data Service
 * Handles all data-related API calls for the EMS application
 */

import { apiClient, ApiResponse } from './api'

// Data Types
export interface Department {
  id: number
  name: string
  name_ar: string
  description?: string
  description_ar?: string
  manager?: Employee
  budget_amount?: number
  location?: string
  phone?: string
  email?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Employee {
  id: number
  user: {
    id: number
    username: string
    email: string
    first_name: string
    last_name: string
  }
  employee_id: string
  department?: Department
  position: string
  position_ar: string
  phone?: string
  gender: 'M' | 'F'
  hire_date: string
  salary?: number
  employment_status: string
  manager?: Employee
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Project {
  id: number
  name: string
  name_ar: string
  description: string
  description_ar?: string
  project_manager?: Employee
  department?: Department
  team_members: Employee[]
  client?: string
  budget_amount?: number
  start_date: string
  end_date: string
  status: string
  priority: string
  progress_percentage: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Task {
  id: number
  project: Project
  title: string
  title_ar?: string
  description: string
  description_ar?: string
  assigned_to?: Employee
  created_by: Employee
  due_date: string
  status: string
  priority: string
  progress_percentage: number
  created_at: string
  updated_at: string
}

export interface LeaveRequest {
  id: number
  employee: Employee
  leave_type: {
    id: number
    name: string
    name_ar: string
  }
  start_date: string
  end_date: string
  days_requested: number
  reason: string
  status: string
  approved_by?: Employee
  approval_date?: string
  created_at: string
  updated_at: string
}

export interface Announcement {
  id: number
  title: string
  title_ar?: string
  content: string
  content_ar?: string
  author: Employee
  priority: string
  is_published: boolean
  publish_date?: string
  expiry_date?: string
  created_at: string
  updated_at: string
}

// Pagination Response
export interface PaginatedResponse<T> {
  count: number
  next?: string
  previous?: string
  results: T[]
}

// Dashboard Stats
export interface DashboardStats {
  total_employees: number
  total_departments: number
  active_projects: number
  pending_tasks: number
  pending_leave_requests: number
  monthly_expenses: number
  system_health: {
    cpu_usage: number
    memory_usage: number
    disk_usage: number
  }
}

export class DataService {
  // Dashboard
  async getDashboardStats(): Promise<DashboardStats> {
    const response = await apiClient.get<DashboardStats>('/dashboard/stats/')
    return response.data
  }

  // Departments
  async getDepartments(params?: { page?: number; search?: string }): Promise<PaginatedResponse<Department>> {
    const queryParams = new URLSearchParams()
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.search) queryParams.append('search', params.search)
    
    const response = await apiClient.get<PaginatedResponse<Department>>(`/departments/?${queryParams}`)
    return response.data
  }

  async getDepartment(id: number): Promise<Department> {
    const response = await apiClient.get<Department>(`/departments/${id}/`)
    return response.data
  }

  async createDepartment(data: Partial<Department>): Promise<Department> {
    const response = await apiClient.post<Department>('/departments/', data)
    return response.data
  }

  async updateDepartment(id: number, data: Partial<Department>): Promise<Department> {
    const response = await apiClient.patch<Department>(`/departments/${id}/`, data)
    return response.data
  }

  async deleteDepartment(id: number): Promise<void> {
    await apiClient.delete(`/departments/${id}/`)
  }

  // Employees
  async getEmployees(params?: { page?: number; search?: string; department?: number }): Promise<PaginatedResponse<Employee>> {
    const queryParams = new URLSearchParams()
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.search) queryParams.append('search', params.search)
    if (params?.department) queryParams.append('department', params.department.toString())
    
    const response = await apiClient.get<PaginatedResponse<Employee>>(`/employees/?${queryParams}`)
    return response.data
  }

  async getEmployee(id: number): Promise<Employee> {
    const response = await apiClient.get<Employee>(`/employees/${id}/`)
    return response.data
  }

  async createEmployee(data: Partial<Employee>): Promise<Employee> {
    const response = await apiClient.post<Employee>('/employees/', data)
    return response.data
  }

  async updateEmployee(id: number, data: Partial<Employee>): Promise<Employee> {
    const response = await apiClient.patch<Employee>(`/employees/${id}/`, data)
    return response.data
  }

  async deleteEmployee(id: number): Promise<void> {
    await apiClient.delete(`/employees/${id}/`)
  }

  // Projects
  async getProjects(params?: { page?: number; search?: string; status?: string }): Promise<PaginatedResponse<Project>> {
    const queryParams = new URLSearchParams()
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.search) queryParams.append('search', params.search)
    if (params?.status) queryParams.append('status', params.status)
    
    const response = await apiClient.get<PaginatedResponse<Project>>(`/projects/?${queryParams}`)
    return response.data
  }

  async getProject(id: number): Promise<Project> {
    const response = await apiClient.get<Project>(`/projects/${id}/`)
    return response.data
  }

  async createProject(data: Partial<Project>): Promise<Project> {
    const response = await apiClient.post<Project>('/projects/', data)
    return response.data
  }

  async updateProject(id: number, data: Partial<Project>): Promise<Project> {
    const response = await apiClient.patch<Project>(`/projects/${id}/`, data)
    return response.data
  }

  async deleteProject(id: number): Promise<void> {
    await apiClient.delete(`/projects/${id}/`)
  }

  // Tasks
  async getTasks(params?: { page?: number; project?: number; assigned_to?: number; status?: string }): Promise<PaginatedResponse<Task>> {
    const queryParams = new URLSearchParams()
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.project) queryParams.append('project', params.project.toString())
    if (params?.assigned_to) queryParams.append('assigned_to', params.assigned_to.toString())
    if (params?.status) queryParams.append('status', params.status)
    
    const response = await apiClient.get<PaginatedResponse<Task>>(`/tasks/?${queryParams}`)
    return response.data
  }

  async getTask(id: number): Promise<Task> {
    const response = await apiClient.get<Task>(`/tasks/${id}/`)
    return response.data
  }

  async createTask(data: Partial<Task>): Promise<Task> {
    const response = await apiClient.post<Task>('/tasks/', data)
    return response.data
  }

  async updateTask(id: number, data: Partial<Task>): Promise<Task> {
    const response = await apiClient.patch<Task>(`/tasks/${id}/`, data)
    return response.data
  }

  async deleteTask(id: number): Promise<void> {
    await apiClient.delete(`/tasks/${id}/`)
  }

  // Leave Requests
  async getLeaveRequests(params?: { page?: number; employee?: number; status?: string }): Promise<PaginatedResponse<LeaveRequest>> {
    const queryParams = new URLSearchParams()
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.employee) queryParams.append('employee', params.employee.toString())
    if (params?.status) queryParams.append('status', params.status)
    
    const response = await apiClient.get<PaginatedResponse<LeaveRequest>>(`/leave-requests/?${queryParams}`)
    return response.data
  }

  async createLeaveRequest(data: Partial<LeaveRequest>): Promise<LeaveRequest> {
    const response = await apiClient.post<LeaveRequest>('/leave-requests/', data)
    return response.data
  }

  async updateLeaveRequest(id: number, data: Partial<LeaveRequest>): Promise<LeaveRequest> {
    const response = await apiClient.patch<LeaveRequest>(`/leave-requests/${id}/`, data)
    return response.data
  }

  // Announcements
  async getAnnouncements(params?: { page?: number; priority?: string }): Promise<PaginatedResponse<Announcement>> {
    const queryParams = new URLSearchParams()
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.priority) queryParams.append('priority', params.priority)
    
    const response = await apiClient.get<PaginatedResponse<Announcement>>(`/announcements/?${queryParams}`)
    return response.data
  }

  async createAnnouncement(data: Partial<Announcement>): Promise<Announcement> {
    const response = await apiClient.post<Announcement>('/announcements/', data)
    return response.data
  }
}

// Create and export data service instance
export const dataService = new DataService()
export default dataService
