/**
 * Enhanced API Service with Backend Integration
 * Provides comprehensive API integration with proper error handling and validation
 */

import { toast } from 'react-hot-toast'
import { apiClient, ApiError } from './api'
import { employeeAPI } from './employeeAPI'
import { Employee, EmployeeFormData } from '../types/employee'

// Common filter types
export interface ApiFilters {
  search?: string
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  [key: string]: string | number | boolean | undefined
}

// Department types
export interface Department {
  id: number
  name: string
  nameAr: string
  description?: string
  manager?: Employee
  employeeCount?: number
}

export interface DepartmentFormData {
  name: string
  nameAr?: string
  description?: string
  manager?: number
}

// Enhanced API response types
export interface ApiResponse<T = unknown> {
  data: T
  message?: string
  status: number
  success: boolean
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

export interface ApiErrorResponse {
  message: string
  errors?: Record<string, string[]>
  status: number
}

// Loading state management
class LoadingManager {
  private loadingStates = new Map<string, boolean>()
  private listeners = new Set<(states: Record<string, boolean>) => void>()

  setLoading(key: string, loading: boolean) {
    this.loadingStates.set(key, loading)
    this.notifyListeners()
  }

  isLoading(key: string): boolean {
    return this.loadingStates.get(key) || false
  }

  subscribe(listener: (states: Record<string, boolean>) => void) {
    this.listeners.add(listener)
    return () => this.listeners.delete(listener)
  }

  private notifyListeners() {
    const states = Object.fromEntries(this.loadingStates)
    this.listeners.forEach(listener => listener(states))
  }
}

export const loadingManager = new LoadingManager()

// Enhanced API service class
class EnhancedAPIService {
  private handleError(error: unknown, operation: string, language: 'ar' | 'en' = 'en'): never {
    console.error(`API Error in ${operation}:`, error)
    
    let message = language === 'ar' 
      ? 'حدث خطأ غير متوقع' 
      : 'An unexpected error occurred'
    
    if (error instanceof ApiError) {
      message = error.message
    } else if ((error as any).response?.data?.message) {
      message = (error as any).response.data.message
    } else if ((error as any).message) {
      message = (error as any).message
    }

    toast.error(message)
    throw new ApiError({ message, status: (error as any).status || 500, details: error as Record<string, unknown> })
  }

  private async executeWithLoading<T>(
    key: string,
    operation: () => Promise<T>,
    operationName: string,
    language: 'ar' | 'en' = 'en'
  ): Promise<T> {
    try {
      loadingManager.setLoading(key, true)
      const result = await operation()
      return result
    } catch (error) {
      this.handleError(error, operationName, language)
    } finally {
      loadingManager.setLoading(key, false)
    }
  }

  // Employee API with enhanced error handling
  async getEmployees(filters?: ApiFilters, language: 'ar' | 'en' = 'en'): Promise<Employee[]> {
    return this.executeWithLoading(
      'employees-list',
      async () => {
        const response = await apiClient.get('/employees/', { params: filters })
        return response.data as Employee[]
      },
      'getEmployees',
      language
    )
  }

  async createEmployee(data: EmployeeFormData, language: 'ar' | 'en' = 'en'): Promise<Employee> {
    return this.executeWithLoading(
      'employee-create',
      async () => {
        const response = await apiClient.post('/employees/', data)
        toast.success(
          language === 'ar'
            ? 'تم إنشاء الموظف بنجاح'
            : 'Employee created successfully'
        )
        return response.data as Employee
      },
      'createEmployee',
      language
    )
  }

  async updateEmployee(id: number, data: Partial<EmployeeFormData>, language: 'ar' | 'en' = 'en'): Promise<Employee> {
    return this.executeWithLoading(
      `employee-update-${id}`,
      async () => {
        const response = await apiClient.patch(`/employees/${id}/`, data)
        toast.success(
          language === 'ar'
            ? 'تم تحديث الموظف بنجاح'
            : 'Employee updated successfully'
        )
        return response.data as Employee
      },
      'updateEmployee',
      language
    )
  }

  async deleteEmployee(id: number, language: 'ar' | 'en' = 'en'): Promise<void> {
    return this.executeWithLoading(
      `employee-delete-${id}`,
      async () => {
        await apiClient.delete(`/employees/${id}/`)
        toast.success(
          language === 'ar' 
            ? 'تم حذف الموظف بنجاح' 
            : 'Employee deleted successfully'
        )
      },
      'deleteEmployee',
      language
    )
  }

  // Department API with enhanced error handling
  async getDepartments(language: 'ar' | 'en' = 'en'): Promise<Department[]> {
    return this.executeWithLoading(
      'departments-list',
      async () => {
        const response = await apiClient.get('/departments/')
        return response.data as Department[]
      },
      'getDepartments',
      language
    )
  }

  async createDepartment(data: DepartmentFormData, language: 'ar' | 'en' = 'en'): Promise<Department> {
    return this.executeWithLoading(
      'department-create',
      async () => {
        const response = await apiClient.post('/departments/', data)
        toast.success(
          language === 'ar'
            ? 'تم إنشاء القسم بنجاح'
            : 'Department created successfully'
        )
        return response.data as Department
      },
      'createDepartment',
      language
    )
  }

  async updateDepartment(id: number, data: Partial<DepartmentFormData>, language: 'ar' | 'en' = 'en'): Promise<Department> {
    return this.executeWithLoading(
      `department-update-${id}`,
      async () => {
        const response = await apiClient.patch(`/departments/${id}/`, data)
        toast.success(
          language === 'ar'
            ? 'تم تحديث القسم بنجاح'
            : 'Department updated successfully'
        )
        return response.data as Department
      },
      'updateDepartment',
      language
    )
  }

  async deleteDepartment(id: number, language: 'ar' | 'en' = 'en'): Promise<void> {
    return this.executeWithLoading(
      `department-delete-${id}`,
      async () => {
        await apiClient.delete(`/departments/${id}/`)
        toast.success(
          language === 'ar' 
            ? 'تم حذف القسم بنجاح' 
            : 'Department deleted successfully'
        )
      },
      'deleteDepartment',
      language
    )
  }

  // System Administration API
  async getDatabaseStats(language: 'ar' | 'en' = 'en'): Promise<any> {
    return this.executeWithLoading(
      'database-stats',
      async () => {
        const response = await apiClient.get('/system/database-stats/')
        return response.data as any
      },
      'getDatabaseStats',
      language
    )
  }

  async optimizeDatabase(language: 'ar' | 'en' = 'en'): Promise<void> {
    return this.executeWithLoading(
      'database-optimize',
      async () => {
        await apiClient.post('/system/optimize-database/')
        toast.success(
          language === 'ar' 
            ? 'تم تحسين قاعدة البيانات بنجاح' 
            : 'Database optimized successfully'
        )
      },
      'optimizeDatabase',
      language
    )
  }

  async rebuildIndexes(language: 'ar' | 'en' = 'en'): Promise<void> {
    return this.executeWithLoading(
      'rebuild-indexes',
      async () => {
        await apiClient.post('/system/rebuild-indexes/')
        toast.success(
          language === 'ar' 
            ? 'تم إعادة بناء الفهارس بنجاح' 
            : 'Database indexes rebuilt successfully'
        )
      },
      'rebuildIndexes',
      language
    )
  }

  async createBackup(language: 'ar' | 'en' = 'en'): Promise<any> {
    return this.executeWithLoading(
      'create-backup',
      async () => {
        const response = await apiClient.post('/system/create-backup/')
        toast.success(
          language === 'ar' 
            ? 'تم إنشاء النسخة الاحتياطية بنجاح' 
            : 'Backup created successfully'
        )
        return response.data as any
      },
      'createBackup',
      language
    )
  }

  async restoreBackup(file: File, language: 'ar' | 'en' = 'en'): Promise<void> {
    return this.executeWithLoading(
      'restore-backup',
      async () => {
        const formData = new FormData()
        formData.append('backup_file', file)
        
        await apiClient.post('/system/restore-backup/', formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        })
        
        toast.success(
          language === 'ar' 
            ? 'تم استعادة النسخة الاحتياطية بنجاح' 
            : 'Backup restored successfully'
        )
      },
      'restoreBackup',
      language
    )
  }

  // Export functionality
  async exportData(
    endpoint: string,
    format: 'pdf' | 'excel' | 'csv',
    filters?: ApiFilters,
    language: 'ar' | 'en' = 'en'
  ): Promise<Blob> {
    return this.executeWithLoading(
      `export-${endpoint}-${format}`,
      async () => {
        const response = await apiClient.get(`/export/${endpoint}/`, {
          params: { format, ...filters }
        } as any)

        toast.success(
          language === 'ar'
            ? 'تم تصدير البيانات بنجاح'
            : 'Data exported successfully'
        )

        return response.data as Blob
      },
      'exportData',
      language
    )
  }

  // Generic CRUD operations
  async getList<T>(endpoint: string, filters?: ApiFilters, language: 'ar' | 'en' = 'en'): Promise<T[]> {
    return this.executeWithLoading(
      `${endpoint}-list`,
      async () => {
        const response = await apiClient.get(`/${endpoint}/`, { params: filters })
        return response.data as T[]
      },
      `get${endpoint}`,
      language
    )
  }

  async create<T>(endpoint: string, data: Record<string, unknown>, language: 'ar' | 'en' = 'en'): Promise<T> {
    return this.executeWithLoading(
      `${endpoint}-create`,
      async () => {
        const response = await apiClient.post(`/${endpoint}/`, data)
        toast.success(
          language === 'ar'
            ? 'تم الإنشاء بنجاح'
            : 'Created successfully'
        )
        return response.data as T
      },
      `create${endpoint}`,
      language
    )
  }

  async update<T>(endpoint: string, id: number, data: Record<string, unknown>, language: 'ar' | 'en' = 'en'): Promise<T> {
    return this.executeWithLoading(
      `${endpoint}-update-${id}`,
      async () => {
        const response = await apiClient.patch(`/${endpoint}/${id}/`, data)
        toast.success(
          language === 'ar'
            ? 'تم التحديث بنجاح'
            : 'Updated successfully'
        )
        return response.data as T
      },
      `update${endpoint}`,
      language
    )
  }

  async delete(endpoint: string, id: number, language: 'ar' | 'en' = 'en'): Promise<void> {
    return this.executeWithLoading(
      `${endpoint}-delete-${id}`,
      async () => {
        await apiClient.delete(`/${endpoint}/${id}/`)
        toast.success(
          language === 'ar' 
            ? 'تم الحذف بنجاح' 
            : 'Deleted successfully'
        )
      },
      `delete${endpoint}`,
      language
    )
  }
}

export const enhancedAPI = new EnhancedAPIService()
export default enhancedAPI
