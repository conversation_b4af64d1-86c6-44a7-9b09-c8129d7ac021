/**
 * User Management Service
 * Dedicated service for user management with proper role mapping and data transformation
 */

import { apiClient } from './api'
import { mapBackendRoleToFrontend } from '../utils/roleMapping'

export interface User {
  id: number
  fullName: string
  fullNameAr: string
  email: string
  phone: string
  role: string // Frontend role name (superAdmin, admin, hrManager, etc.)
  roleAr: string
  department: string
  departmentAr: string
  status: 'active' | 'inactive' | 'pending' | 'suspended'
  lastLogin: string
  joinDate: string
  position: string
  positionAr: string
  permissions: string[]
  profilePicture?: string
  address?: string
  addressAr?: string
  emergencyContact?: string
  emergencyContactAr?: string
  // Creator information
  createdBy?: {
    id: number
    name: string
    nameAr: string
    role: string
    roleAr: string
  }
  createdAt: string
}

export interface BackendEmployee {
  id: number
  user: {
    id: number
    username: string
    email: string
    first_name: string
    last_name: string
    is_active: boolean
    last_login: string | null
    date_joined: string
  }
  employee_id: string
  department: {
    id: number
    name: string
    name_ar: string
  } | null
  position: string
  position_ar: string
  phone: string
  address: string
  address_ar: string
  emergency_contact_name: string
  emergency_contact_name_ar: string
  profile_picture: string | null
  created_at: string
  updated_at: string
  user_profile?: {
    role: {
      id: number
      name: string // Backend role name (SUPERADMIN, ADMIN, HR_MANAGER, etc.)
      name_ar: string
      permissions: Record<string, any>
    } | null
  }
  created_by_info?: {
    id: number
    name: string
    nameAr: string
    role: string
    roleAr: string
  } | null
}

export interface CreateUserData {
  fullName: string
  fullNameAr: string
  email: string
  phone: string
  role: string
  department: string
  position: string
  positionAr: string
  status: string
  joinDate: string
  address?: string
  addressAr?: string
  emergencyContact?: string
  emergencyContactAr?: string
}

class UserManagementService {
  private baseUrl = '/employees'

  /**
   * Transform backend employee data to frontend user format
   */
  private transformEmployeeToUser(employee: BackendEmployee): User {
    const backendRole = employee.user_profile?.role?.name || 'EMPLOYEE'
    const frontendRole = mapBackendRoleToFrontend(backendRole)
    
    return {
      id: employee.id,
      fullName: `${employee.user.first_name} ${employee.user.last_name}`.trim(),
      fullNameAr: `${employee.user.first_name} ${employee.user.last_name}`.trim(), // TODO: Add Arabic name fields
      email: employee.user.email,
      phone: employee.phone || '',
      role: frontendRole,
      roleAr: employee.user_profile?.role?.name_ar || 'موظف',
      department: employee.department?.name || 'N/A',
      departmentAr: employee.department?.name_ar || 'غير محدد',
      status: employee.user.is_active ? 'active' : 'inactive',
      lastLogin: employee.user.last_login || 'Never',
      joinDate: employee.user.date_joined,
      position: employee.position || 'N/A',
      positionAr: employee.position_ar || 'غير محدد',
      permissions: [], // TODO: Extract from role permissions
      profilePicture: employee.profile_picture || undefined,
      address: employee.address || undefined,
      addressAr: employee.address_ar || undefined,
      emergencyContact: employee.emergency_contact_name || undefined,
      emergencyContactAr: employee.emergency_contact_name_ar || undefined,
      // Creator information
      createdBy: employee.created_by_info ? {
        id: employee.created_by_info.id,
        name: employee.created_by_info.name,
        nameAr: employee.created_by_info.nameAr,
        role: mapBackendRoleToFrontend(employee.created_by_info.role),
        roleAr: employee.created_by_info.roleAr
      } : undefined,
      createdAt: employee.created_at || employee.user.date_joined
    }
  }

  /**
   * Get all users with proper transformation
   */
  async getUsers(params?: {
    search?: string
    department?: string
    role?: string
    status?: string
    page?: number
    page_size?: number
  }): Promise<{ data: User[], total: number, page: number, totalPages: number }> {
    try {
      const response = await apiClient.get<{
        results: BackendEmployee[]
        count: number
        next: string | null
        previous: string | null
      }>(this.baseUrl, { params })

      const transformedUsers = response.data.results.map(employee => 
        this.transformEmployeeToUser(employee)
      )

      const pageSize = params?.page_size || 20
      const currentPage = params?.page || 1
      const totalPages = Math.ceil(response.data.count / pageSize)

      return {
        data: transformedUsers,
        total: response.data.count,
        page: currentPage,
        totalPages
      }
    } catch (error) {
      console.error('Error fetching users:', error)
      throw error
    }
  }

  /**
   * Get single user by ID
   */
  async getUser(id: number): Promise<User> {
    try {
      const response = await apiClient.get<BackendEmployee>(`${this.baseUrl}/${id}/`)
      return this.transformEmployeeToUser(response.data)
    } catch (error) {
      console.error('Error fetching user:', error)
      throw error
    }
  }

  /**
   * Create new user
   */
  async createUser(userData: CreateUserData): Promise<User> {
    try {
      // Transform frontend data to backend format
      const backendData = {
        user: {
          first_name: userData.fullName.split(' ')[0] || '',
          last_name: userData.fullName.split(' ').slice(1).join(' ') || '',
          email: userData.email,
          username: userData.email, // Use email as username
        },
        phone: userData.phone,
        position: userData.position,
        position_ar: userData.positionAr,
        address: userData.address || '',
        address_ar: userData.addressAr || '',
        emergency_contact_name: userData.emergencyContact || '',
        emergency_contact_name_ar: userData.emergencyContactAr || '',
        // TODO: Handle department and role assignment
      }

      const response = await apiClient.post<BackendEmployee>(this.baseUrl, backendData)
      return this.transformEmployeeToUser(response.data)
    } catch (error) {
      console.error('Error creating user:', error)
      throw error
    }
  }

  /**
   * Update user
   */
  async updateUser(id: number, userData: Partial<CreateUserData>): Promise<User> {
    try {
      // Transform frontend data to backend format
      const backendData: any = {}
      
      if (userData.fullName) {
        backendData.user = {
          first_name: userData.fullName.split(' ')[0] || '',
          last_name: userData.fullName.split(' ').slice(1).join(' ') || '',
        }
      }
      
      if (userData.email) {
        backendData.user = { ...backendData.user, email: userData.email }
      }
      
      if (userData.phone) backendData.phone = userData.phone
      if (userData.position) backendData.position = userData.position
      if (userData.positionAr) backendData.position_ar = userData.positionAr
      if (userData.address) backendData.address = userData.address
      if (userData.addressAr) backendData.address_ar = userData.addressAr
      if (userData.emergencyContact) backendData.emergency_contact_name = userData.emergencyContact
      if (userData.emergencyContactAr) backendData.emergency_contact_name_ar = userData.emergencyContactAr

      const response = await apiClient.patch<BackendEmployee>(`${this.baseUrl}/${id}/`, backendData)
      return this.transformEmployeeToUser(response.data)
    } catch (error) {
      console.error('Error updating user:', error)
      throw error
    }
  }

  /**
   * Delete user
   */
  async deleteUser(id: number): Promise<void> {
    try {
      await apiClient.delete(`${this.baseUrl}/${id}/`)
    } catch (error) {
      console.error('Error deleting user:', error)
      throw error
    }
  }

  /**
   * Export users data
   */
  async exportUsers(format: 'csv' | 'excel' = 'csv'): Promise<Blob> {
    try {
      const response = await fetch(`${this.baseUrl}/export/?format=${format}`, {
        method: 'GET',
        credentials: 'include',
      })
      
      if (!response.ok) {
        throw new Error('Export failed')
      }
      
      return await response.blob()
    } catch (error) {
      console.error('Error exporting users:', error)
      throw error
    }
  }
}

/**
 * CRUD-compatible wrapper for UserManagementService
 * Adapts the service to work with the existing useCrud hook
 */
class CrudCompatibleUserService {
  private service = new UserManagementService()

  async getAll(options: any = {}) {
    const { search, filters, page = 1, pageSize = 20 } = options

    const params: any = {
      page,
      page_size: pageSize
    }

    if (search) params.search = search
    if (filters?.department) params.department = filters.department
    if (filters?.role) params.role = filters.role
    if (filters?.status) params.status = filters.status

    const result = await this.service.getUsers(params)

    // Return in format expected by useCrud
    return {
      data: result.data,
      total: result.total,
      page: result.page,
      totalPages: result.totalPages
    }
  }

  async getById(id: number) {
    return await this.service.getUser(id)
  }

  async create(data: CreateUserData) {
    return await this.service.createUser(data)
  }

  async update(id: number, data: Partial<CreateUserData>) {
    return await this.service.updateUser(id, data)
  }

  async delete(id: number) {
    return await this.service.deleteUser(id)
  }

  async export(format: 'csv' | 'excel' = 'csv') {
    return await this.service.exportUsers(format)
  }
}

export const userManagementService = new CrudCompatibleUserService()
export default userManagementService
