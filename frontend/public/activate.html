<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفعيل حساب الموظف - نمو</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #666;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .password-input {
            position: relative;
        }
        
        .toggle-password {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            color: #666;
        }
        
        .btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .employee-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-right: 4px solid #667eea;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">نمو</div>
            <h1>تفعيل حساب الموظف</h1>
            <p class="subtitle">أكمل إعداد حسابك لبدء استخدام النظام</p>
        </div>
        
        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p>جاري التحقق من رابط التفعيل...</p>
        </div>
        
        <div id="error-container" class="hidden">
            <div class="alert alert-error" id="error-message"></div>
        </div>
        
        <div id="success-container" class="hidden">
            <div class="alert alert-success" id="success-message"></div>
        </div>
        
        <div id="activation-form" class="hidden">
            <div class="employee-info" id="employee-info"></div>
            
            <form id="activationForm">
                <div class="form-group">
                    <label for="password">كلمة المرور الجديدة</label>
                    <div class="password-input">
                        <input type="password" id="password" placeholder="أدخل كلمة مرور قوية (8 أحرف على الأقل)" required>
                        <button type="button" class="toggle-password" onclick="togglePassword('password')">👁️</button>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="confirmPassword">تأكيد كلمة المرور</label>
                    <div class="password-input">
                        <input type="password" id="confirmPassword" placeholder="أعد إدخال كلمة المرور" required>
                        <button type="button" class="toggle-password" onclick="togglePassword('confirmPassword')">👁️</button>
                    </div>
                </div>
                
                <button type="submit" class="btn" id="submitBtn">تفعيل الحساب</button>
            </form>
        </div>
        
        <div id="completed-container" class="hidden">
            <div style="text-align: center;">
                <div style="font-size: 64px; margin-bottom: 20px;">✅</div>
                <h2 style="color: #28a745; margin-bottom: 10px;">تم تفعيل الحساب بنجاح!</h2>
                <p style="margin-bottom: 20px;">يمكنك الآن تسجيل الدخول إلى النظام</p>
                <button class="btn" onclick="goToLogin()">الذهاب إلى تسجيل الدخول</button>
            </div>
        </div>
    </div>

    <script>
        // Get token from URL
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token') || window.location.pathname.split('/').pop();
        
        let employeeData = null;
        
        // Check activation token on page load
        window.addEventListener('load', checkActivationToken);
        
        async function checkActivationToken() {
            try {
                const response = await fetch(`http://localhost:8000/api/auth/activate/${token}/`);
                const data = await response.json();
                
                document.getElementById('loading').classList.add('hidden');
                
                if (!response.ok) {
                    showError(data.message || 'رابط التفعيل غير صحيح');
                    return;
                }
                
                if (data.expired) {
                    showError('تم انتهاء صلاحية رابط التفعيل. يرجى التواصل مع المدير.');
                    return;
                }
                
                if (data.message.includes('already activated')) {
                    showSuccess('تم تفعيل الحساب مسبقاً. يمكنك تسجيل الدخول الآن.');
                    setTimeout(goToLogin, 3000);
                    return;
                }
                
                employeeData = data.employee;
                showActivationForm();
                
            } catch (error) {
                document.getElementById('loading').classList.add('hidden');
                showError('حدث خطأ أثناء التحقق من رابط التفعيل');
            }
        }
        
        function showError(message) {
            document.getElementById('error-message').textContent = message;
            document.getElementById('error-container').classList.remove('hidden');
        }
        
        function showSuccess(message) {
            document.getElementById('success-message').textContent = message;
            document.getElementById('success-container').classList.remove('hidden');
        }
        
        function showActivationForm() {
            const employeeInfo = document.getElementById('employee-info');
            employeeInfo.innerHTML = `
                <h3 style="margin-bottom: 15px;">معلومات الموظف</h3>
                <p><strong>الاسم:</strong> ${employeeData.name}</p>
                <p><strong>البريد الإلكتروني:</strong> ${employeeData.email}</p>
                <p><strong>المنصب:</strong> ${employeeData.position}</p>
                ${employeeData.department ? `<p><strong>القسم:</strong> ${employeeData.department}</p>` : ''}
            `;
            document.getElementById('activation-form').classList.remove('hidden');
        }
        
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            field.type = field.type === 'password' ? 'text' : 'password';
        }
        
        function goToLogin() {
            window.location.href = 'http://localhost:5173/login';
        }
        
        // Handle form submission
        document.getElementById('activationForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const submitBtn = document.getElementById('submitBtn');
            
            // Validation
            if (password.length < 8) {
                showError('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
                return;
            }
            
            if (password !== confirmPassword) {
                showError('كلمات المرور غير متطابقة');
                return;
            }
            
            // Clear previous errors
            document.getElementById('error-container').classList.add('hidden');
            
            // Disable button and show loading
            submitBtn.disabled = true;
            submitBtn.textContent = 'جاري التفعيل...';
            
            try {
                const response = await fetch(`http://localhost:8000/api/auth/activate/${token}/complete/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        password: password,
                        confirm_password: confirmPassword
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('activation-form').classList.add('hidden');
                    document.getElementById('completed-container').classList.remove('hidden');
                    setTimeout(goToLogin, 3000);
                } else {
                    showError(data.message || 'حدث خطأ أثناء تفعيل الحساب');
                }
                
            } catch (error) {
                showError('حدث خطأ أثناء تفعيل الحساب');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = 'تفعيل الحساب';
            }
        });
    </script>
</body>
</html>
