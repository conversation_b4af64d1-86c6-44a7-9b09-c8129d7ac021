Metadata-Version: 2.1
Name: typeguard
Version: 4.3.0
Summary: Run-time type checker for Python
Author-email: <PERSON> <alex.g<PERSON><EMAIL>>
License: MIT
Project-URL: Documentation, https://typeguard.readthedocs.io/en/latest/
Project-URL: Change log, https://typeguard.readthedocs.io/en/latest/versionhistory.html
Project-URL: Source code, https://github.com/agronholm/typeguard
Project-URL: Issue tracker, https://github.com/agronholm/typeguard/issues
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Requires-Python: >=3.8
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: typing-extensions >=4.10.0
Requires-Dist: importlib-metadata >=3.6 ; python_version < "3.10"
Provides-Extra: doc
Requires-Dist: packaging ; extra == 'doc'
Requires-Dist: Sphinx >=7 ; extra == 'doc'
Requires-Dist: sphinx-autodoc-typehints >=1.2.0 ; extra == 'doc'
Requires-Dist: sphinx-rtd-theme >=1.3.0 ; extra == 'doc'
Provides-Extra: test
Requires-Dist: coverage[toml] >=7 ; extra == 'test'
Requires-Dist: pytest >=7 ; extra == 'test'
Requires-Dist: mypy >=1.2.0 ; (platform_python_implementation != "PyPy") and extra == 'test'

.. image:: https://github.com/agronholm/typeguard/actions/workflows/test.yml/badge.svg
  :target: https://github.com/agronholm/typeguard/actions/workflows/test.yml
  :alt: Build Status
.. image:: https://coveralls.io/repos/agronholm/typeguard/badge.svg?branch=master&service=github
  :target: https://coveralls.io/github/agronholm/typeguard?branch=master
  :alt: Code Coverage
.. image:: https://readthedocs.org/projects/typeguard/badge/?version=latest
  :target: https://typeguard.readthedocs.io/en/latest/?badge=latest
  :alt: Documentation

This library provides run-time type checking for functions defined with
`PEP 484 <https://www.python.org/dev/peps/pep-0484/>`_ argument (and return) type
annotations, and any arbitrary objects. It can be used together with static type
checkers as an additional layer of type safety, to catch type violations that could only
be detected at run time.

Two principal ways to do type checking are provided:

#. The ``check_type`` function:

   * like ``isinstance()``, but supports arbitrary type annotations (within limits)
   * can be used as a ``cast()`` replacement, but with actual checking of the value
#. Code instrumentation:

   * entire modules, or individual functions (via ``@typechecked``) are recompiled, with
     type checking code injected into them
   * automatically checks function arguments, return values and assignments to annotated
     local variables
   * for generator functions (regular and async), checks yield and send values
   * requires the original source code of the instrumented module(s) to be accessible

Two options are provided for code instrumentation:

#. the ``@typechecked`` function:

   * can be applied to functions individually
#. the import hook (``typeguard.install_import_hook()``):

   * automatically instruments targeted modules on import
   * no manual code changes required in the target modules
   * requires the import hook to be installed before the targeted modules are imported
   * may clash with other import hooks

See the documentation_ for further information.

.. _documentation: https://typeguard.readthedocs.io/en/latest/
