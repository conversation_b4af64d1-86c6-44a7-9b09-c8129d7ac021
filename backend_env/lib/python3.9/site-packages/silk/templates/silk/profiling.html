{% extends 'silk/base/root_base.html' %}
{% load static %}
{% load silk_inclusion %}

{% block pagetitle %}Silky - Profiling - {{ silk_request.path }}{% endblock %}

{% block menu %}
    {% if silk_request %}
        {% request_menu request silk_request %}
    {% else %}
        {% root_menu request %}
    {% endif %}
{% endblock %}

{% block style %}
    {{ block.super }}
    <link rel="stylesheet" href="{% static 'silk/css/pages/profiling.css' %}"/>
{% endblock %}

{% block filter %}
    <form id="filter-form" action="." method="get"></form>

    <div class="menu-item">
        <div class="menu-item-outer">
            <div class="menu-item-inner">
                <label>Show:
                    <select name="show" form="filter-form" onchange="this.form.submit();">
                        {% for option in options_show %}
                            <option value="{{ option }}"
                                    {% if option == show %}selected{% endif %}>{{ option }}</option>
                        {% endfor %}
                    </select>
                </label>
            </div>
        </div>
    </div>
    <div class="menu-item">
        <div class="menu-item-outer">
            <div class="menu-item-inner">
                <label>Order:
                    <select name="order_by" form="filter-form" onchange="this.form.submit();">
                        {% for option in options_order_by %}
                            <option value="{{ option }}"
                                    {% if option == order_by %}selected{% endif %}>{{ option }}</option>
                        {% endfor %}
                    </select>
                </label>
            </div>
        </div>
    </div>
    {{ block.super }}
{% endblock %}

{% block js %}
    {{ block.super }}
    <script src="{% static 'silk/js/pages/profiling.js' %}"></script>
{% endblock %}

{% block filters %}
    <h4>Profile</h4>

    <div class="filter-section">Took longer than

        <div class="resizing-input">
            <input form="filter-form2"
                   class="typ"
                   type="hidden"
                   value="OverallTimeFilter"
                   name="filter-overalltime-typ"/>
            <input type="text"
                   placeholder="seconds"
                   form="filter-form2"
                   name="filter-overalltime-value"
                   value="{{ filters.overalltime.value }}"/>
            <span style="display:none"></span>
        </div>
        seconds,
        executed more than
        <div class="resizing-input">
            <input form="filter-form2"
                   class="typ"
                   type="hidden"
                   value="NumQueriesFilter"
                   name="filter-numqueriesfilter-typ"/>
            <input
                    type="text"
                    placeholder="n"
                    form="filter-form2"
                    name="filter-numqueriesfilter-value"
                    value="{{ filters.numqueriesfilter.value }}"/>
            <span style="display:none"></span>
        </div>
        queries,
        and spent longer than
        <div class="resizing-input">

            <input form="filter-form2"
                   class="typ"
                   type="hidden"
                   value="TimeSpentOnQueriesFilter"
                   name="filter-timespentfilter-typ"/>
            <input
                    type="text"
                    placeholder="seconds"
                    form="filter-form2"
                    name="filter-timespentfilter-value"
                    value="{{ filters.timespentfilter.value }}"/>
            <span style="display:none"></span>
        </div>
        seconds executing queries.
    </div>
    <h4>Date Range</h4>
    <div class="filter-section">
        Executed
        <div class="resizing-input">
            <input form="filter-form2"
                   class="typ"
                   type="hidden"
                   value="SecondsFilter"
                   name="filter-seconds-typ">
            <input type="text"
                   placeholder="seconds"
                   form="filter-form2"
                   name="filter-seconds-value"
                   value="{{ filters.seconds.value }}"
                    >
            <span style="display:none"></span>
        </div>
        seconds ago, before
        <div class="resizing-input">
            <input form="filter-form2"
                   class="typ"
                   type="hidden"
                   value="BeforeDateFilter"
                   name="filter-beforedate-typ">
            <input class="datetimepicker"
                   type="text"
                   placeholder="date"
                   form="filter-form2"
                   name="filter-beforedate-value"
                   value="{{ filters.beforedate.value }}"
                    />
            <span style="display:none"></span>
        </div>
        , and after
        <div class="resizing-input">

            <input form="filter-form2"
                   class="typ"
                   type="hidden"
                   value="AfterDateFilter"
                   name="filter-afterdate-typ">
            <input class="datetimepicker"
                   type="text"
                   placeholder="date"
                   form="filter-form2"
                   name="filter-afterdate-value"
                   value="{{ filters.afterdate.value }}"
                    >
            <span style="display:none"></span>
        </div>
        .
    </div>
    <h4>Function</h4>
    <div class="filter-section">
        <div>
            <input form="filter-form2"
                   class="typ"
                   type="hidden"
                   value="NameFilter"
                   name="filter-name-typ">
            <label>Name:
                <select form="filter-form2" name="filter-name-value">
                    <option value="" {% if not filters.name.value %}selected{% endif %}></option>
                    {% for name in options_names %}
                        <option {% if name == filters.name.value %}selected{% endif %}>{{ name }}</option>
                    {% endfor %}
                </select>
            </label>

        </div>
        <div>
            <input form="filter-form2"
                   class="typ"
                   type="hidden"
                   value="FunctionNameFilter"
                   name="filter-functionname-typ">
            <label>Function:
                <select form="filter-form2" name="filter-functionname-value">
                    <option value="" {% if not filters.functionname.value %}selected{% endif %}></option>
                    {% for func_name in options_func_names %}
                        <option {% if func_name == filters.functionname.value %}selected{% endif %}>{{ func_name }}</option>
                    {% endfor %}
                </select>
            </label>

        </div>


    </div>
{% endblock %}

{% block data %}

    {% if results %}
        <div class="container">
            <h2>Silk Profiler</h2>
        </div>
        {% for profile in results %}
            <a href="
            {% if silk_request %}
                {% url "silk:request_profile_detail" request_id=silk_request.pk profile_id=profile.pk %}
            {% else %}
                {% url "silk:profile_detail" profile_id=profile.pk %}
            {% endif %}
            ">
                {% profile_summary profile %}
            </a>
        {% endfor %}
    {% else %}
        <div class="container">
            <h2>Silk Profiler</h2>

            <div class="description">
                No Silk profiling was performed for this request. Please check that:
                <ul>
                    <li>you use the <code>@silk_profile</code> decorator or <code>with silk_profile():</code> context manager on the correct view</li>
                    <li>you have <code>"silk"</code> in <code>INSTALLED_APPS</code></li>
                    <li>you have <code>"silk.middleware.SilkyMiddleware"</code> in <code>MIDDLEWARE</code></li>
                    <li>you have <code>SILKY_PYTHON_PROFILER</code> set to <code>True</code></li>
                </ul>
            </div>
        </div>
    {% endif %}
{% endblock %}
