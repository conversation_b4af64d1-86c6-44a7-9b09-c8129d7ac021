{% extends "silk/base/detail_base.html" %}
{% load silk_filters %}
{% load silk_nav %}
{% load silk_inclusion %}
{% load static %}

{% block pagetitle %}Silky - Profile Detail - {{ silk_request.path }}{% endblock %}

{% block js %}
    <script type="text/javascript" src="{% static 'silk/lib/viz-lite.js' %}"></script>
    <script type="text/javascript" src="{% static 'silk/lib/svg-pan-zoom.min.js' %}"></script>
    {{ block.super }}
{% endblock %}

{% block style %}
    {{ block.super }}
    <link rel="stylesheet" href="{% static 'silk/css/components/summary.css' %}">
    <link rel="stylesheet" href="{% static 'silk/css/pages/profile_detail.css' %}">
{% endblock %}

{% block menu %}
    {% profile_menu request profile silk_request %}
{% endblock %}

{% block data %}
    <div class="wrapper">
        <div id="query-div">
            <div id="query-info-div">
                {% profile_summary profile %}
            </div>
            <div class="heading">
                <div class="inner-heading">
                    {% if profile.file_path and profile.line_num %}
                        {{ profile.file_path }}:{{ profile.line_num }}{% if profile.end_line_num %}:{{ profile.end_line_num }}{% endif %}
                    {% else %}
                        Location
                    {% endif %}
                </div>
            </div>
            <div class="description">
                Below shows where in your code this profile was defined. If your profile was defined dynamically (i.e in your settings.py),
                then this will show the range of lines that are covered by the profiling.
            </div>
            {% if code %}
                <pre id="code"><code>{% code code actual_line %}</code></pre>
            {% elif code_error %}
                <div id="error-div">
                    {{ code_error }}
                </div>
            {% endif %}

            {% if silk_request.prof_file %}
            <div class="heading">
                <div class="inner-heading">Profile graph</div>
            </div>
            <div class="description">
                Below is a graph of the profile, with the nodes coloured by the time taken (red is more time). This should give a good indication of the slowest path through the profiled code.</div>

                <span>Prune nodes taking up less than </span>
                <input id='percent' type="text" value='5'
                       onkeypress='return event.charCode >= 48 && event.charCode <= 57 && $("#percent").val().length < 2'
                       oninput="createViz()"
                >

                </input>
                <span>% of the total time</span>

            </div>
            <div id="graph-div">
            </div>
            {% url 'silk:request_profile_dot' request_id=silk_request.pk as profile_dot_url %}
            {{ profile_dot_url|json_script:'profileDotURL' }}
            <script src="{% static 'silk/js/pages/profile_detail.js' %}"></script>
            {% endif %}

            {% if silk_request.pyprofile %}
            <div id="pyprofile-div">
                <div class="heading">
                    <div class="inner-heading">Python Profiler</div>
                </div>
                <div class="description">
                    The below is a dump from the cPython profiler.
                </div>
                {% if silk_request.prof_file %}
                Click <a href="{% url 'silk:request_profile_download' request_id=silk_request.pk %}">here</a> to download profile.
                {% endif %}
                <pre class="pyprofile">{{ silk_request.pyprofile }}</pre>
                {% endif %}
            </div>

        </div>
    </div>

{% endblock %}
