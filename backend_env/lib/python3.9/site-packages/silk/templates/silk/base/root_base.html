{% extends "silk/base/base.html" %}
{% load silk_nav %}
{% load silk_inclusion %}
{% load static %}

{% block body_class %}
    cbp-spmenu-push
{% endblock %}

{% block style %}
    <link rel="stylesheet" href="{% static 'silk/css/components/cell.css' %}"/>
    <link rel="stylesheet" href="{% static 'silk/css/components/row.css' %}"/>
    <link rel="stylesheet" href="{% static 'silk/css/components/numeric.css' %}"/>
    <link rel="stylesheet" href="{% static "silk/lib/jquery.datetimepicker.css" %}"/>
    <link rel="icon" type="image/png" href="{% static 'silk/favicon-32x32.png' %}" sizes="32x32">
    <link rel="icon" type="image/png" href="{% static 'silk/favicon-16x16.png' %}" sizes="16x16">
    <!-- Begin Custom Styles -->
    <link rel="stylesheet" href="{% static 'silk/css/pages/root_base.css' %}"/>
    <!-- End Custom Styles -->
{% endblock %}

{% block filter %}
    <div id="filter-button" class="menu-item selectable-menu-item">
        <div class="menu-item-outer">
            <div class="menu-item-inner">
                <div id="filter-item"><span id="num-filters"><sub>{{ filters | length }}</sub></span></div>
            </div>
        </div>
    </div>
{% endblock %}

{% block top %}
    <nav class="cbp-spmenu cbp-spmenu-vertical cbp-spmenu-right" id="cbp-spmenu-s2">
        <h3>Filters</h3>

        <form id="filter-form2" action="" method="post">
            {% csrf_token %}
            {% block filters %}{% endblock %}
            <input type="submit" style="display: none">
        </form>

        <div class="button-div">
            <div class="apply-div" onclick="submitEmptyFilters()">Clear all filters</div>
        </div>
        <div class="button-div">
            <div class="apply-div" onclick="submitFilters()">Apply</div>
        </div>

    </nav>

{% endblock %}

{% block js %}
    <script src="{% static 'silk/js/components/cell.js' %}"></script>
    <script src="{% static 'silk/lib/jquery.datetimepicker.js' %}"></script>
    <script src="{% static 'silk/js/components/filters.js' %}"></script>
    <script src="{% static 'silk/js/pages/root_base.js' %}"></script>
{% endblock %}
