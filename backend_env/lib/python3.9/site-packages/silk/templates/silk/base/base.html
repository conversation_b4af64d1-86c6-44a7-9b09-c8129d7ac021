{% load static %}
<!DOCTYPE html>
<html>
<head>
    <title>{% block pagetitle %}Silky{% endblock %}</title>
    <link rel="stylesheet" href="{% static "silk/css/components/fonts.css" %}"/>
    <link rel="stylesheet" href="{% static "silk/css/components/colors.css" %}"/>
    <link rel="stylesheet" href="{% static "silk/lib/jquery-ui-1.13.1.min.css" %}"/>
    <link rel="icon" type="image/png" href="{% static 'silk/favicon-32x32.png' %}" sizes="32x32">
    <link rel="icon" type="image/png" href="{% static 'silk/favicon-16x16.png' %}" sizes="16x16">
    <link rel="stylesheet" href="{% static "silk/css/pages/base.css" %}"/>
    {% block style %}
    {% endblock %}
    <script src="{% static 'silk/lib/jquery-3.6.0.min.js' %}"></script>
    <script src="{% static 'silk/lib/jquery-ui-1.13.1.min.js' %}"></script>
    <script src="{% static 'silk/js/components/cell.js' %}"></script>
    <script src="{% static 'silk/js/pages/base.js' %}"></script>
    {% block js %}
    {% endblock %}

</head>

<body onload="{% block onload %}{% endblock %}" class="{% block body_class %}{% endblock %}">
{% block top %}

{% endblock %}
<div id="content">
    <div id="header">
        <div class="menu">
            {% block menu %}
            {% endblock %}
        </div>
        <div id="filter" class="menu">
            {% block filter %}
            {% endblock %}
        </div>
    </div>
    <div id="data">
        {% block data %}
        {% endblock %}
    </div>
</div>

</body>
</html>
