{% load silk_filters %}
<div class="cell">
    <div class="timestamp-div">{{ silk_request.start_time | silk_date_time }}</div>
    <div class="method-div">{% if silk_request.response.status_code %}{{ silk_request.response.status_code }} {% endif %}{{ silk_request.method }}</div>
    <div class="path-div">{{ silk_request.path }}</div>
    <div class="time-taken-div">
        <span class="numeric">{{ silk_request.time_taken|floatformat:"0" }}<span class="unit">ms</span></span>
        <span class="appendage">overall<span class="meta">{% if silk_request.total_meta_time %} +{{ silk_request.total_meta_time | floatformat:"0" }}<span class="unit">ms</span>{% endif %}</span></span>
    </div>
    <div class="time-taken-queries-div">
        <span class="numeric">{{ silk_request.time_spent_on_sql_queries|floatformat:"0" }}<span class="unit">ms</span></span>
        <span class="appenage">on queries<span class="meta">{% if silk_request.meta_time_spent_queries %} +{{ silk_request.meta_time_spent_queries | floatformat:"0" }}<span class="unit">ms</span>{% endif %}</span></div>
    <div class="num-queries-div">
        <span class="numeric">{{ silk_request.num_sql_queries }}</span>
        <span class="appendage">queries<span class="meta">{% if silk_request.meta_num_queries %} +{{ silk_request.meta_num_queries  }}{% endif %}</span>
    </div>
</div>
