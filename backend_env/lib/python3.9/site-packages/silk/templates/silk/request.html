{% extends "silk/base/base.html" %}
{% load silk_filters %}
{% load silk_inclusion %}
{% load static %}
{% block pagetitle %}Silky - Request - {{ silk_request.path }}{% endblock %}

{% block style %}
    <link rel="stylesheet" href="{% static 'silk/css/components/cell.css' %}"/>
    <link rel="stylesheet" href="{% static 'silk/css/components/numeric.css' %}"/>
    <link rel="stylesheet" href="{% static 'silk/css/components/heading.css' %}"/>
    <link rel="stylesheet" href="{% static 'silk/css/pages/request.css' %}"/>
    <link rel="stylesheet" href="{% static 'silk/lib/highlight/foundation.css' %}"/>
{% endblock %}
{% block js %}
    <script src="{% static 'silk/lib/highlight/highlight.pack.js' %}"></script>
    <script src="{% static 'silk/js/pages/request.js' %}"></script>
    <script src="{% static 'silk/js/components/cell.js' %}"></script>
{% endblock %}
{% block onload %}
{% endblock %}

{% block menu %}
    {% request_menu request silk_request %}
{% endblock %}

{% block data %}
    <div class="wrapper">
        <div id="request-summary">
            {% request_summary silk_request %}
        </div>
        <div id="request-info">
            {% if query_params %}
                {% heading 'Query Parameters' %}
                <pre><code>{{ query_params }}</code></pre>
            {% endif %}
            {% heading 'Request Headers' %}
            <table class="headers">
                {% for k,v in silk_request.headers.items %}
                    <tr>
                        <td class="key">{{ k.upper }}</td>
                        <td class="value">{{ v }}</td>
                    </tr>
                {% endfor %}
            </table>
            {% if silk_request.raw_body %}
                {% heading 'Raw Request Body' %}
                {% if silk_request.raw_body|length > 1000 %}
                    The raw request body is <b>{{ silk_request.raw_body|length }}</b> characters long
                    and hence is <b>too big</b> to show here.
                    Click <a href="{% url "silk:raw" silk_request.pk %}?typ=request&subtyp=raw">here</a> to view the raw request body.
                {% else %}
                    <pre>{{ silk_request.raw_body }}</pre>
                {% endif %}
            {% endif %}
            {% if silk_request.body %}
                {% heading 'Request Body' %}
                {% if silk_request.body|length > 1000 %}
                    The processed request body is <b>{{ silk_request.body|length }}</b> characters long
                    and hence is <b>too big</b> to show here.
                    Click <a href="{% url "silk:raw" silk_request.pk %}?typ=request&subtyp=processed">here</a> to view the request body.
                {% else %}
                    <div class="description">
                        This is the body of the HTTP request represented as JSON for easier reading.
                    </div>
                    <pre><code>{{ silk_request.body }}</code></pre>
                {% endif %}
            {% endif %}
            {% if silk_request.response.headers %}
                {% heading 'Response Headers' %}
                <table class="headers">
                    {% for k, v in silk_request.response.headers.items %}
                        <tr>
                            <td class="key">{{ k.upper }}</td>
                            <td class="value">{{ v }}</td>
                        </tr>
                    {% endfor %}
                </table>
            {% endif %}
            {% if silk_request.response.raw_body %}
                {% heading 'Raw Response Body' %}
                {% with raw_body=silk_request.response.raw_body_decoded %}
                    {% if raw_body|length > 1000 %}
                        The raw response body is <b>{{ raw_body|length }}</b> characters long
                        and hence is <b>too big</b> to show here.
                        Click <a href="{% url "silk:raw" silk_request.pk %}?typ=response&subtyp=raw">here</a> to view the raw response body.
                    {% else %}
                        <pre>{{ raw_body }}</pre>
                    {% endif %}
                {% endwith %}
            {% endif %}
            {% if silk_request.response.body %}
                {% heading 'Response Body' %}
                <div class="description">
                    This is the body of the HTTP response represented as JSON for easier reading.
                </div>
                {% if silk_request.response.body|length > 1000 %}
                    The response body is <b>{{ silk_request.response.body|length }}</b> characters long
                    and hence is <b>too big</b> to show here.
                    Click <a href="{% url "silk:raw" silk_request.pk %}?typ=response&subtyp=processed">here</a> to view the response body.
                {% else %}
                    <pre><code>{{ silk_request.response.body }}</code></pre>
                {% endif %}
            {% endif %}
            {% if curl %}
                {% heading 'Curl' %}
                <div class="description">
                    Curl is a command-line utility for transferring data from servers. Paste the following into
                    a terminal to repeat this request via command line.
                </div>
                <pre id="pre-curl"><code>{{ curl.strip }}</code></pre>
            {% endif %}
            {% if client %}
                {% heading 'Django Test Client' %}
                <div class="description">
                    The following is working python code that makes use of the Django test client. It can be used to
                    replicate this request from within a Django unit test, or simply as standalone Python.
                </div>
                <pre id="pre-curl"><code>{{ client.strip }}</code></pre>
            {% endif %}
        </div>
    </div>
{% endblock %}
