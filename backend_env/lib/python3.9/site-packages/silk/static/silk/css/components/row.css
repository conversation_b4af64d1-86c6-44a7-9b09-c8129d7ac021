.row-wrapper {
  display: table;
  margin: 2rem;
  width: 100%;
  width: -moz-available;
  width: -webkit-fill-available;
  width: fill-available;
}
.row-wrapper .row {
  display: table-row;
  transition: background-color 0.15s ease, color 0.15s ease;
}
.row-wrapper .row div {
  padding: 1rem;
}
.row-wrapper .row .col {
  font-size: 20px;
  display: table-cell;
}
.row-wrapper .row .timestamp-div {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  margin-bottom: 15px;
  font-size: 13px;
}
.row-wrapper .row .meta {
  font-size: 12px;
  color: #be5b43;
}
.row-wrapper .row .meta .unit {
  font-size: 9px;
  font-weight: lighter !important;
}
.row-wrapper .row .method-div {
  font-weight: bold;
  font-size: 20px;
}
.row-wrapper .row .path-div {
  font-size: 18px;
  margin-bottom: 15px;
}
.row-wrapper .row .num-queries-div {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.row-wrapper .row .spacing .numeric {
  padding: 0 0.3rem;
}
.row-wrapper .row .spacing .meta {
  padding: 0 0.3rem;
}
.row-wrapper .row:hover {
  background-color: rgb(51, 51, 68);
  color: white;
  cursor: pointer;
}
