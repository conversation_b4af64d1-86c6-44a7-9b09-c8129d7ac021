from copy import copy

from silk.singleton import Singleton


def default_permissions(user):
    if user:
        return user.is_staff
    return False


class SilkyConfig(metaclass=Singleton):
    defaults = {
        'SILKY_DYNAMIC_PROFILING': [],
        'SILKY_IGNORE_PATHS': [],
        'SIL<PERSON>Y_HIDE_COOKIES': True,
        'SILKY_IGNORE_QUERIES': [],
        'SILKY_META': False,
        'SILKY_AUTHENTICATION': False,
        'SILKY_AUTHORISATION': False,
        'SILKY_PERMISSIONS': default_permissions,
        'SILKY_MAX_RECORDED_REQUESTS': 10**4,
        'SILKY_MAX_RECORDED_REQUESTS_CHECK_PERCENT': 10,
        'SILKY_MAX_REQUEST_BODY_SIZE': -1,
        'SILKY_MAX_RESPONSE_BODY_SIZE': -1,
        'SILKY_INTERCEPT_PERCENT': 100,
        'SILKY_INTERCEPT_FUNC': None,
        'SILKY_PYTHON_PROFILER': False,
        'S<PERSON><PERSON>Y_PYTHON_PROFILER_FUNC': None,
        'SILKY_STORAGE_CLASS': 'silk.storage.ProfilerResultStorage',
        'SILKY_PYTHON_PROFILER_EXTENDED_FILE_NAME': False,
        'SILKY_MIDDLEWARE_CLASS': 'silk.middleware.SilkyMiddleware',
        'SILKY_JSON_ENSURE_ASCII': True,
        'SILKY_ANALYZE_QUERIES': False,
        'SILKY_EXPLAIN_FLAGS': None,
        'SILKY_SENSITIVE_KEYS': {'username', 'api', 'token', 'key', 'secret', 'password', 'signature'}
    }

    def _setup(self):
        from django.conf import settings

        options = {option: getattr(settings, option) for option in dir(settings) if option.startswith('SILKY')}
        self.attrs = copy(self.defaults)
        self.attrs['SILKY_PYTHON_PROFILER_RESULT_PATH'] = settings.MEDIA_ROOT
        self.attrs.update(options)

    def __init__(self):
        super().__init__()
        self._setup()

    def __getattr__(self, item):
        return self.attrs.get(item, None)

    def __setattribute__(self, key, value):
        self.attrs[key] = value
