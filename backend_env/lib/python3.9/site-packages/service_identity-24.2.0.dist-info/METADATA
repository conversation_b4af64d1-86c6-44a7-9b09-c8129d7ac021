Metadata-Version: 2.3
Name: service-identity
Version: 24.2.0
Summary: Service identity verification for pyOpenSSL & cryptography.
Project-URL: Documentation, https://service-identity.readthedocs.io/
Project-URL: Changelog, https://service-identity.readthedocs.io/en/stable/changelog.html
Project-URL: GitHub, https://github.com/pyca/service-identity
Project-URL: Funding, https://github.com/sponsors/hynek
Project-URL: Tidelift, https://tidelift.com/subscription/pkg/pypi-service-identity?utm_source=pypi-service-identity&utm_medium=pypi
Project-URL: Mastodon, https://mastodon.social/@hynek
Project-URL: Twitter, https://twitter.com/hynek
Author-email: Hynek <PERSON>awack <<EMAIL>>
License-Expression: MIT
License-File: LICENSE
Keywords: cryptography,openssl,pyopenssl
Classifier: Development Status :: 5 - Production/Stable
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Security :: Cryptography
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Typing :: Typed
Requires-Python: >=3.8
Requires-Dist: attrs>=19.1.0
Requires-Dist: cryptography
Requires-Dist: pyasn1
Requires-Dist: pyasn1-modules
Provides-Extra: dev
Requires-Dist: coverage[toml]>=5.0.2; extra == 'dev'
Requires-Dist: idna; extra == 'dev'
Requires-Dist: mypy; extra == 'dev'
Requires-Dist: pyopenssl; extra == 'dev'
Requires-Dist: pytest; extra == 'dev'
Requires-Dist: types-pyopenssl; extra == 'dev'
Provides-Extra: docs
Requires-Dist: furo; extra == 'docs'
Requires-Dist: myst-parser; extra == 'docs'
Requires-Dist: pyopenssl; extra == 'docs'
Requires-Dist: sphinx; extra == 'docs'
Requires-Dist: sphinx-notfound-page; extra == 'docs'
Provides-Extra: idna
Requires-Dist: idna; extra == 'idna'
Provides-Extra: mypy
Requires-Dist: idna; extra == 'mypy'
Requires-Dist: mypy; extra == 'mypy'
Requires-Dist: types-pyopenssl; extra == 'mypy'
Provides-Extra: tests
Requires-Dist: coverage[toml]>=5.0.2; extra == 'tests'
Requires-Dist: pytest; extra == 'tests'
Description-Content-Type: text/markdown

# Service Identity Verification for pyOpenSSL & *cryptography*

Use this package if:

- you want to **verify** that a [PyCA *cryptography*](https://cryptography.io/) certificate is valid for a certain hostname or IP address,
- or if you use [pyOpenSSL](https://pypi.org/project/pyOpenSSL/) and don’t want to be [**MITM**](https://en.wikipedia.org/wiki/Man-in-the-middle_attack)ed,
- or if you want to **inspect** certificates from either for service IDs.

*service-identity* aspires to give you all the tools you need for verifying whether a certificate is valid for the intended purposes.
In the simplest case, this means *host name verification*.
However, *service-identity* implements [RFC 6125](https://datatracker.ietf.org/doc/html/rfc6125.html) fully.

Also check out [*pem*](https://github.com/hynek/pem) that makes loading certificates from all kinds of PEM-encoded files a breeze!


## Project Information

*service-identity* is released under the [MIT](https://github.com/pyca/service-identity/blob/main/LICENSE) license, its documentation lives at [Read the Docs](https://service-identity.readthedocs.io/), the code on [GitHub](https://github.com/pyca/service-identity), and the latest release on [PyPI](https://pypi.org/project/service-identity/).


### Credits

*service-identity* is written and maintained by [Hynek Schlawack](https://hynek.me/).

The development is kindly supported by my employer [Variomedia AG](https://www.variomedia.de/), *service-identity*'s [Tidelift subscribers](https://tidelift.com/lifter/search/pypi/service-identity), and all my amazing [GitHub Sponsors](https://github.com/sponsors/hynek).


### *service-identity* for Enterprise

Available as part of the [Tidelift Subscription](https://tidelift.com/?utm_source=lifter&utm_medium=referral&utm_campaign=hynek).

The maintainers of *service-identity* and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open-source packages you use to build your applications.
Save time, reduce risk, and improve code health, while paying the maintainers of the exact packages you use.


## Release Information

### Added

- Python 3.13 is now officially supported.
  [#74](https://github.com/pyca/service-identity/pull/74)


### Changed

- pyOpenSSL's identity extraction has been reimplemented using *cryptography*'s primitives instead of deprecated pyOpenSSL APIs.
  As a result, the oldest supported pyOpenSSL version is now 17.1.0.
  [#70](https://github.com/pyca/service-identity/pull/70)


----

[Complete Changelog →](https://service-identity.readthedocs.io/en/stable/changelog.html)
