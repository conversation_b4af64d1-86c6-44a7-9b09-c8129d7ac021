# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2019.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-02-22 17:30+0100\n"
"PO-Revision-Date: \n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: ru_RU\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"X-Generator: Poedit 2.2.1\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr ""
"Заголовок авторизации должен содержать два значения, разделенных пробелом"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "Данный токен недействителен для любого типа токена"

#: authentication.py:127 authentication.py:162
msgid "Token contained no recognizable user identification"
msgstr "Токен не содержит идентификатор пользователя"

#: authentication.py:132
msgid "User not found"
msgstr "Пользователь не найден"

#: authentication.py:135
msgid "User is inactive"
msgstr "Пользователь неактивен"

#: authentication.py:142
msgid "The user's password has been changed."
msgstr ""

#: backends.py:90
msgid "Unrecognized algorithm type '{}'"
msgstr "Нераспознанный тип алгоритма '{}'"

#: backends.py:96
msgid "You must have cryptography installed to use {}."
msgstr ""

#: backends.py:111
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""

#: backends.py:125 backends.py:177 tokens.py:68
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is invalid"
msgstr "Токен недействителен или просрочен"

#: backends.py:173
msgid "Invalid algorithm specified"
msgstr ""

#: backends.py:175 tokens.py:66
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is expired"
msgstr "Токен недействителен или просрочен"

#: exceptions.py:55
msgid "Token is invalid or expired"
msgstr "Токен недействителен или просрочен"

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "Не найдено активной учетной записи с указанными данными"

#: serializers.py:108
#, fuzzy
#| msgid "No active account found with the given credentials"
msgid "No active account found for the given token."
msgstr "Не найдено активной учетной записи с указанными данными"

#: settings.py:74
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr ""
"Параметр '{}' был удален. Пожалуйста, обратитесь к '{}' для просмотра "
"доступных настроек."

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "пользователь"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "создан"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "истекает"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "Token Blacklist"

#: tokens.py:52
msgid "Cannot create token with no type or lifetime"
msgstr "Невозможно создать токен без типа или времени жизни"

#: tokens.py:126
msgid "Token has no id"
msgstr "У токена нет идентификатора"

#: tokens.py:138
msgid "Token has no type"
msgstr "Токен не имеет типа"

#: tokens.py:141
msgid "Token has wrong type"
msgstr "Токен имеет неправильный тип"

#: tokens.py:200
msgid "Token has no '{}' claim"
msgstr "Токен не содержит '{}'"

#: tokens.py:205
msgid "Token '{}' claim has expired"
msgstr "Токен имеет просроченное значение '{}'"

#: tokens.py:292
msgid "Token is blacklisted"
msgstr "Токен занесен в черный список"
