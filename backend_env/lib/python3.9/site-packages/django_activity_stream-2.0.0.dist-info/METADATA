Metadata-Version: 2.1
Name: django-activity-stream
Version: 2.0.0
Summary: Generate generic activity streams from the actions on your site. Users can follow any actors' activities for personalized streams.
Home-page: http://github.com/justquick/django-activity-stream
Author: <PERSON><PERSON>, <PERSON> <<EMAIL>>
Author-email: <EMAIL>
License: BSD 3-Clause
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Framework :: Django
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Topic :: Utilities
License-File: LICENSE.txt
License-File: AUTHORS.txt
Requires-Dist: Django (>=3.2)
Provides-Extra: drf
Requires-Dist: django-rest-framework ; extra == 'drf'
Requires-Dist: rest-framework-generic-relations ; extra == 'drf'

Django Activity Stream
======================

.. image:: https://github.com/justquick/django-activity-stream/actions/workflows/workflow.yaml/badge.svg
    :target: https://github.com/justquick/django-activity-stream/actions/workflows/workflow.yaml

.. image:: https://badges.gitter.im/django-activity-stream/Lobby.svg
   :alt: Join the chat at https://gitter.im/django-activity-stream/Lobby
   :target: https://gitter.im/django-activity-stream/Lobby?utm_source=badge&utm_medium=badge&utm_campaign=pr-badge&utm_content=badge

.. image:: https://coveralls.io/repos/github/justquick/django-activity-stream/badge.svg?branch=master
    :target: https://coveralls.io/github/justquick/django-activity-stream?branch=master

.. image:: https://scrutinizer-ci.com/g/justquick/django-activity-stream/badges/quality-score.png?b=master
    :target: https://scrutinizer-ci.com/g/justquick/django-activity-stream/

.. image:: https://img.shields.io/pypi/v/django-activity-stream.svg
    :target: https://pypi.python.org/pypi/django-activity-stream



What is Django Activity Stream?
===============================

Django Activity Stream is a way of creating activities generated by the actions on your site.

It is designed for generating and displaying streams of interesting actions and can handle following and unfollowing of different activity sources.
For example, it could be used to emulate the Github dashboard in which a user sees changes to projects they are watching and the actions of users they are following.

Action events are categorized by four main components.

 * ``Actor``. The object that performed the activity.
 * ``Verb``. The verb phrase that identifies the action of the activity.
 * ``Action Object``. *(Optional)* The object linked to the action itself.
 * ``Target``. *(Optional)* The object to which the activity was performed.

``Actor``, ``Action Object`` and ``Target`` are `GenericForeignKeys <https://docs.djangoproject.com/en/dev/ref/contrib/contenttypes/#django.contrib.contenttypes.fields.GenericForeignKey>`_ to any arbitrary Django object and so can represent any Django model in your project.
An action is a description of an action that was performed (``Verb``) at some instant in time by some ``Actor`` on some optional ``Target`` that results in an ``Action Object`` getting created/updated/deleted.

For example: `justquick <https://github.com/justquick/>`_ ``(actor)`` *closed* ``(verb)`` `issue 2 <https://github.com/justquick/django-activity-stream/issues/2>`_ ``(object)`` on `django-activity-stream <https://github.com/justquick/django-activity-stream/>`_ ``(target)`` 12 hours ago

Nomenclature of this specification is based on the Activity Streams Spec: `<http://activitystrea.ms/>`_

For complete documentation see `Django Activity Stream Documentation <http://django-activity-stream.rtfd.io/en/latest/>`_



Contributors

------------

This project exists thanks to all the people who contribute!

.. image:: https://opencollective.com/django-activity-stream/contributors.svg?width=890&button=false


