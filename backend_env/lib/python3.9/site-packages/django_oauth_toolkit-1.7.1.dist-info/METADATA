Metadata-Version: 2.1
Name: django-oauth-toolkit
Version: 1.7.1
Summary: OAuth2 Provider for Django
Home-page: https://github.com/jazzband/django-oauth-toolkit
Author: <PERSON>, <PERSON><PERSON><PERSON><PERSON>
Author-email: <EMAIL>
License: UNKNOWN
Keywords: django,oauth,oauth2,oauthlib
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Framework :: Django
Classifier: Framework :: Django :: 2.2
Classifier: Framework :: Django :: 3.2
Classifier: Framework :: Django :: 4.0
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Topic :: Internet :: WWW/HTTP
Description-Content-Type: text/x-rst
License-File: LICENSE
License-File: AUTHORS
Requires-Dist: django (!=4.0.0,>=2.2)
Requires-Dist: requests (>=2.13.0)
Requires-Dist: oauthlib (>=3.1.0)
Requires-Dist: jwcrypto (>=0.8.0)

Django OAuth Toolkit
====================

.. image:: https://jazzband.co/static/img/badge.svg
   :target: https://jazzband.co/
   :alt: Jazzband

*OAuth2 goodies for the Djangonauts!*

.. image:: https://badge.fury.io/py/django-oauth-toolkit.svg
    :target: http://badge.fury.io/py/django-oauth-toolkit

.. image:: https://github.com/jazzband/django-oauth-toolkit/workflows/Test/badge.svg
   :target: https://github.com/jazzband/django-oauth-toolkit/actions
   :alt: GitHub Actions

.. image:: https://codecov.io/gh/jazzband/django-oauth-toolkit/branch/master/graph/badge.svg
   :target: https://codecov.io/gh/jazzband/django-oauth-toolkit
   :alt: Coverage

.. image:: https://img.shields.io/pypi/pyversions/django-oauth-toolkit.svg
   :target: https://pypi.org/project/django-oauth-toolkit/
   :alt: Supported Python versions

.. image:: https://img.shields.io/pypi/djversions/django-oauth-toolkit.svg
   :target: https://pypi.org/project/django-oauth-toolkit/
   :alt: Supported Django versions

If you are facing one or more of the following:
 * Your Django app exposes a web API you want to protect with OAuth2 authentication,
 * You need to implement an OAuth2 authorization server to provide tokens management for your infrastructure,

Django OAuth Toolkit can help you providing out of the box all the endpoints, data and logic needed to add OAuth2
capabilities to your Django projects. Django OAuth Toolkit makes extensive use of the excellent
`OAuthLib <https://github.com/idan/oauthlib>`_, so that everything is
`rfc-compliant <http://tools.ietf.org/html/rfc6749>`_.

Note: If you have issues installing Django 4.0.0, it is because we only support
Django 4.0.1+ due to a regression in Django 4.0.0. Besides 4.0.0, Django 2.2+ is supported.
`Explanation <https://github.com/jazzband/django-oauth-toolkit/pull/1046#issuecomment-998015272>`_.

Contributing
------------

We love contributions, so please feel free to fix bugs, improve things, provide documentation. Just `follow the
guidelines <https://django-oauth-toolkit.readthedocs.io/en/latest/contributing.html>`_ and submit a PR.

Reporting security issues
-------------------------

Please report any security issues to the JazzBand security team at <<EMAIL>>. Do not file an issue on the tracker.

Requirements
------------

* Python 3.7+
* Django 2.2, 3.2, or >=4.0.1
* oauthlib 3.1+

Installation
------------

Install with pip::

    pip install django-oauth-toolkit

Add `oauth2_provider` to your `INSTALLED_APPS`

.. code-block:: python

    INSTALLED_APPS = (
        ...
        'oauth2_provider',
    )


If you need an OAuth2 provider you'll want to add the following to your urls.py.
Notice that `oauth2_provider` namespace is mandatory.

.. code-block:: python

    urlpatterns = [
        ...
        path('o/', include('oauth2_provider.urls', namespace='oauth2_provider')),
    ]

Changelog
---------

See `CHANGELOG.md <https://github.com/jazzband/django-oauth-toolkit/blob/master/CHANGELOG.md>`_.


Documentation
--------------

The `full documentation <https://django-oauth-toolkit.readthedocs.io/>`_ is on *Read the Docs*.

License
-------

django-oauth-toolkit is released under the terms of the **BSD license**. Full details in ``LICENSE`` file.


