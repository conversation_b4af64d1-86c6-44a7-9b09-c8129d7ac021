# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: django-filter\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-10 11:07+0000\n"
"PO-Revision-Date: 2013-08-10 12:29+0100\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 1.5.4\n"

#: conf.py:16
msgid "date"
msgstr "Datum"

#: conf.py:17
msgid "year"
msgstr "Jahr"

#: conf.py:18
msgid "month"
msgstr "Monat"

#: conf.py:19
msgid "day"
msgstr "Tag"

#: conf.py:20
msgid "week day"
msgstr "Wochentag"

#: conf.py:21
msgid "hour"
msgstr "Stunde"

#: conf.py:22
msgid "minute"
msgstr "Minute"

#: conf.py:23
msgid "second"
msgstr "Sekunde"

#: conf.py:27 conf.py:28
msgid "contains"
msgstr "enthält"

#: conf.py:29
msgid "is in"
msgstr "ist in"

#: conf.py:30
msgid "is greater than"
msgstr "ist größer als"

#: conf.py:31
msgid "is greater than or equal to"
msgstr "ist größer oder gleich"

#: conf.py:32
msgid "is less than"
msgstr "ist kleiner als"

#: conf.py:33
msgid "is less than or equal to"
msgstr "ist kleiner oder gleich"

#: conf.py:34 conf.py:35
msgid "starts with"
msgstr "beginnt mit"

#: conf.py:36 conf.py:37
msgid "ends with"
msgstr "endet mit"

#: conf.py:38
msgid "is in range"
msgstr "ist im Bereich"

#: conf.py:39
msgid "is null"
msgstr ""

#: conf.py:40 conf.py:41
msgid "matches regex"
msgstr "passt auf Regex"

#: conf.py:42 conf.py:49
msgid "search"
msgstr "Suche"

#: conf.py:44
msgid "is contained by"
msgstr "ist enthalten in"

#: conf.py:45
msgid "overlaps"
msgstr "überlappen"

#: conf.py:46
msgid "has key"
msgstr "hat Schlüssel"

#: conf.py:47
msgid "has keys"
msgstr "hat Schlüssel"

#: conf.py:48
msgid "has any keys"
msgstr "hat beliebige Schlüssel"

#: fields.py:94
msgid "Select a lookup."
msgstr ""

#: fields.py:198
msgid "Range query expects two values."
msgstr "Die Bereichsabfrage erwartet zwei Werte."

#: filters.py:437
msgid "Today"
msgstr "Heute"

#: filters.py:438
msgid "Yesterday"
msgstr "Gestern"

#: filters.py:439
msgid "Past 7 days"
msgstr "Letzte 7 Tage"

#: filters.py:440
msgid "This month"
msgstr "Diesen Monat"

#: filters.py:441
msgid "This year"
msgstr "Dieses Jahr"

#: filters.py:543
msgid "Multiple values may be separated by commas."
msgstr "Mehrere Werte können durch Kommas getrennt sein."

#: filters.py:721
#, python-format
msgid "%s (descending)"
msgstr "%s (absteigend)"

#: filters.py:737
msgid "Ordering"
msgstr "Sortierung"

#: rest_framework/filterset.py:33
#: templates/django_filters/rest_framework/form.html:5
msgid "Submit"
msgstr "Absenden"

#: templates/django_filters/rest_framework/crispy_form.html:4
#: templates/django_filters/rest_framework/form.html:2
msgid "Field filters"
msgstr "Feldfilter"

#: utils.py:308
msgid "exclude"
msgstr "ausschließen"

#: widgets.py:58
msgid "All"
msgstr "Alle"

#: widgets.py:162
msgid "Unknown"
msgstr "Unbekannte"

#: widgets.py:162
msgid "Yes"
msgstr "Ja"

#: widgets.py:162
msgid "No"
msgstr "Nein"

#~ msgid "Any date"
#~ msgstr "Alle Daten"
