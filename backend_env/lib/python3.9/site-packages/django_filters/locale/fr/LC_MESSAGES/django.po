# Django Filter translation.
# Copyright (C) 2013
# This file is distributed under the same license as the django_filter package.
# <PERSON> <<EMAIL>>, 2013.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-10 11:07+0000\n"
"PO-Revision-Date: 2013-07-05 19:24+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: French\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: conf.py:16
msgid "date"
msgstr "date"

#: conf.py:17
msgid "year"
msgstr "année"

#: conf.py:18
msgid "month"
msgstr "mois"

#: conf.py:19
msgid "day"
msgstr "jour"

#: conf.py:20
msgid "week day"
msgstr "jour de la semaine"

#: conf.py:21
msgid "hour"
msgstr "heure"

#: conf.py:22
msgid "minute"
msgstr "minute"

#: conf.py:23
msgid "second"
msgstr "seconde"

#: conf.py:27 conf.py:28
msgid "contains"
msgstr "contient"

#: conf.py:29
msgid "is in"
msgstr "est inclus dans"

#: conf.py:30
msgid "is greater than"
msgstr "supérieur à"

#: conf.py:31
msgid "is greater than or equal to"
msgstr "supérieur ou égal à"

#: conf.py:32
msgid "is less than"
msgstr "inférieur à"

#: conf.py:33
msgid "is less than or equal to"
msgstr "inférieur ou égale à"

#: conf.py:34 conf.py:35
msgid "starts with"
msgstr "commence par"

#: conf.py:36 conf.py:37
msgid "ends with"
msgstr "se termine par"

#: conf.py:38
msgid "is in range"
msgstr "entre"

#: conf.py:39
msgid "is null"
msgstr "est nul"

#: conf.py:40 conf.py:41
msgid "matches regex"
msgstr "correspond à l'expression régulière"

#: conf.py:42 conf.py:49
msgid "search"
msgstr "recherche"

#: conf.py:44
msgid "is contained by"
msgstr "est contenu dans"

#: conf.py:45
msgid "overlaps"
msgstr "chevauche"

#: conf.py:46
msgid "has key"
msgstr "contient la clé"

#: conf.py:47
msgid "has keys"
msgstr "contient les clés"

#: conf.py:48
msgid "has any keys"
msgstr "a une des clés"

#: fields.py:94
msgid "Select a lookup."
msgstr "Sélectionner un lookup"

#: fields.py:198
msgid "Range query expects two values."
msgstr "La fouchette doit avoir 2 valeurs"

#: filters.py:437
msgid "Today"
msgstr "Aujourd'hui"

#: filters.py:438
msgid "Yesterday"
msgstr "Hier"

#: filters.py:439
msgid "Past 7 days"
msgstr "7 derniers jours"

#: filters.py:440
msgid "This month"
msgstr "Ce mois-ci"

#: filters.py:441
msgid "This year"
msgstr "Cette année"

#: filters.py:543
msgid "Multiple values may be separated by commas."
msgstr "Les valeurs multiples doivent être séparées par des virgules."

#: filters.py:721
#, python-format
msgid "%s (descending)"
msgstr "%s (décroisssant)"

#: filters.py:737
msgid "Ordering"
msgstr "Tri"

#: rest_framework/filterset.py:33
#: templates/django_filters/rest_framework/form.html:5
msgid "Submit"
msgstr "Envoyer"

#: templates/django_filters/rest_framework/crispy_form.html:4
#: templates/django_filters/rest_framework/form.html:2
msgid "Field filters"
msgstr "Filtres de champ"

#: utils.py:308
msgid "exclude"
msgstr "Exclut"

#: widgets.py:58
msgid "All"
msgstr "Tous"

#: widgets.py:162
msgid "Unknown"
msgstr "Inconnu"

#: widgets.py:162
msgid "Yes"
msgstr "Oui"

#: widgets.py:162
msgid "No"
msgstr "Non"

#~ msgid "This is an exclusion filter"
#~ msgstr "Ceci est un filtre d'exclusion"
