# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# Seraf<PERSON> Papastefanos <<EMAIL>>, 2017.
#
msgid ""
msgstr ""
"Project-Id-Version: django-filter\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-10 11:07+0000\n"
"PO-Revision-Date: 2017-11-16 10:04+0200\n"
"Last-Translator: <PERSON>af<PERSON> Papastefanos <<EMAIL>>\n"
"Language-Team: \n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 1.6.5\n"

#: conf.py:16
msgid "date"
msgstr "ημερομηνία"

#: conf.py:17
msgid "year"
msgstr "έτος"

#: conf.py:18
msgid "month"
msgstr "μήνας"

#: conf.py:19
msgid "day"
msgstr "ημέρα"

#: conf.py:20
msgid "week day"
msgstr "ημέρα της εβδομάδας"

#: conf.py:21
msgid "hour"
msgstr "ώρα"

#: conf.py:22
msgid "minute"
msgstr "λεπτό"

#: conf.py:23
msgid "second"
msgstr "δευτερόλεπτο"

#: conf.py:27 conf.py:28
msgid "contains"
msgstr "περιέχει"

#: conf.py:29
msgid "is in"
msgstr "είναι εντός των"

#: conf.py:30
msgid "is greater than"
msgstr "είναι μεγαλύτερο από"

#: conf.py:31
msgid "is greater than or equal to"
msgstr "είναι μεγαλύτερο ή ίσο του"

#: conf.py:32
msgid "is less than"
msgstr "είναι μικρότερο από"

#: conf.py:33
msgid "is less than or equal to"
msgstr "είναι μικρότερο ή ίσο του"

#: conf.py:34 conf.py:35
msgid "starts with"
msgstr "ξεκινά με"

#: conf.py:36 conf.py:37
msgid "ends with"
msgstr "τελειώνει με"

#: conf.py:38
msgid "is in range"
msgstr "είναι εντος του εύρους"

#: conf.py:39
msgid "is null"
msgstr ""

#: conf.py:40 conf.py:41
msgid "matches regex"
msgstr "περιέχει regex"

#: conf.py:42 conf.py:49
msgid "search"
msgstr "αναζήτηση"

#: conf.py:44
msgid "is contained by"
msgstr "περιέχεται σε"

#: conf.py:45
msgid "overlaps"
msgstr "επικαλύπτεται"

#: conf.py:46
msgid "has key"
msgstr "έχει το κλειδί"

#: conf.py:47
msgid "has keys"
msgstr "έχει τα κλειδιά"

#: conf.py:48
msgid "has any keys"
msgstr "έχει οποιαδήποτε κλειδιά"

#: fields.py:94
msgid "Select a lookup."
msgstr ""

#: fields.py:198
msgid "Range query expects two values."
msgstr "Το ερώτημα εύρους απαιτεί δύο τιμές,"

#: filters.py:437
msgid "Today"
msgstr "Σήμερα"

#: filters.py:438
msgid "Yesterday"
msgstr "Χτες"

#: filters.py:439
msgid "Past 7 days"
msgstr "Τις προηγούμενες 7 ημέρες"

#: filters.py:440
msgid "This month"
msgstr "Αυτό το μήνα"

#: filters.py:441
msgid "This year"
msgstr "Αυτό το έτος"

#: filters.py:543
msgid "Multiple values may be separated by commas."
msgstr "Οι πολλαπλές τιμές πρέπει να διαχωρίζονται με κόμμα."

#: filters.py:721
#, python-format
msgid "%s (descending)"
msgstr "%s (φθίνουσα"

#: filters.py:737
msgid "Ordering"
msgstr "Ταξινόμηση"

#: rest_framework/filterset.py:33
#: templates/django_filters/rest_framework/form.html:5
msgid "Submit"
msgstr "Υποβολή"

#: templates/django_filters/rest_framework/crispy_form.html:4
#: templates/django_filters/rest_framework/form.html:2
msgid "Field filters"
msgstr "Φίλτρα πεδίων"

#: utils.py:308
msgid "exclude"
msgstr "απέκλεισε"

#: widgets.py:58
msgid "All"
msgstr "Όλα"

#: widgets.py:162
msgid "Unknown"
msgstr "Άγνωστο"

#: widgets.py:162
msgid "Yes"
msgstr "Ναι"

#: widgets.py:162
msgid "No"
msgstr "Όχι"

#~ msgid "Any date"
#~ msgstr "Οποιαδήποτε ημερομηνία"
