# -*- coding: utf-8 -*-
# Copyright (C) 2006-2013 <PERSON><PERSON><PERSON>, European Environment Agency
#
# This library is free software; you can redistribute it and/or
# modify it under the terms of the GNU Lesser General Public
# License as published by the Free Software Foundation; either
# version 2.1 of the License, or (at your option) any later version.
#
# This library is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
# Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public
# License along with this library; if not, write to the Free Software
# Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
#
# Contributor(s):
#
import re, sys, os.path
sys.path.append(os.path.dirname(__file__))


from odf.namespaces import TEXTNS
from odf.element import Element
from odf.style import StyleElement

# Autogenerated
def A(**args):
    args.setdefault('type', 'simple')
    return Element(qname = (TEXTNS,'a'), **args)

def AlphabeticalIndex(**args):
    return Element(qname = (TEXTNS,'alphabetical-index'), **args)

def AlphabeticalIndexAutoMarkFile(**args):
    args.setdefault('type', 'simple')
    return Element(qname = (TEXTNS,'alphabetical-index-auto-mark-file'), **args)

def AlphabeticalIndexEntryTemplate(**args):
    return Element(qname = (TEXTNS,'alphabetical-index-entry-template'), **args)

def AlphabeticalIndexMark(**args):
    return Element(qname = (TEXTNS,'alphabetical-index-mark'), **args)

def AlphabeticalIndexMarkEnd(**args):
    return Element(qname = (TEXTNS,'alphabetical-index-mark-end'), **args)

def AlphabeticalIndexMarkStart(**args):
    return Element(qname = (TEXTNS,'alphabetical-index-mark-start'), **args)

def AlphabeticalIndexSource(**args):
    return Element(qname = (TEXTNS,'alphabetical-index-source'), **args)

def AuthorInitials(**args):
    return Element(qname = (TEXTNS,'author-initials'), **args)

def AuthorName(**args):
    return Element(qname = (TEXTNS,'author-name'), **args)

def Bibliography(**args):
    return Element(qname = (TEXTNS,'bibliography'), **args)

def BibliographyConfiguration(**args):
    return Element(qname = (TEXTNS,'bibliography-configuration'), **args)

def BibliographyEntryTemplate(**args):
    return Element(qname = (TEXTNS,'bibliography-entry-template'), **args)

def BibliographyMark(**args):
    return Element(qname = (TEXTNS,'bibliography-mark'), **args)

def BibliographySource(**args):
    return Element(qname = (TEXTNS,'bibliography-source'), **args)

def Bookmark(**args):
    return Element(qname = (TEXTNS,'bookmark'), **args)

def BookmarkEnd(**args):
    return Element(qname = (TEXTNS,'bookmark-end'), **args)

def BookmarkRef(**args):
    return Element(qname = (TEXTNS,'bookmark-ref'), **args)

def BookmarkStart(**args):
    return Element(qname = (TEXTNS,'bookmark-start'), **args)

def Change(**args):
    return Element(qname = (TEXTNS,'change'), **args)

def ChangeEnd(**args):
    return Element(qname = (TEXTNS,'change-end'), **args)

def ChangeStart(**args):
    return Element(qname = (TEXTNS,'change-start'), **args)

def ChangedRegion(**args):
    return Element(qname = (TEXTNS,'changed-region'), **args)

def Chapter(**args):
    return Element(qname = (TEXTNS,'chapter'), **args)

def CharacterCount(**args):
    return Element(qname = (TEXTNS,'character-count'), **args)

def ConditionalText(**args):
    return Element(qname = (TEXTNS,'conditional-text'), **args)

def CreationDate(**args):
    return Element(qname = (TEXTNS,'creation-date'), **args)

def CreationTime(**args):
    return Element(qname = (TEXTNS,'creation-time'), **args)

def Creator(**args):
    return Element(qname = (TEXTNS,'creator'), **args)

def DatabaseDisplay(**args):
    return Element(qname = (TEXTNS,'database-display'), **args)

def DatabaseName(**args):
    return Element(qname = (TEXTNS,'database-name'), **args)

def DatabaseNext(**args):
    return Element(qname = (TEXTNS,'database-next'), **args)

def DatabaseRowNumber(**args):
    return Element(qname = (TEXTNS,'database-row-number'), **args)

def DatabaseRowSelect(**args):
    return Element(qname = (TEXTNS,'database-row-select'), **args)

def Date(**args):
    return Element(qname = (TEXTNS,'date'), **args)

def DdeConnection(**args):
    return Element(qname = (TEXTNS,'dde-connection'), **args)

def DdeConnectionDecl(**args):
    return Element(qname = (TEXTNS,'dde-connection-decl'), **args)

def DdeConnectionDecls(**args):
    return Element(qname = (TEXTNS,'dde-connection-decls'), **args)

def Deletion(**args):
    return Element(qname = (TEXTNS,'deletion'), **args)

def Description(**args):
    return Element(qname = (TEXTNS,'description'), **args)

def EditingCycles(**args):
    return Element(qname = (TEXTNS,'editing-cycles'), **args)

def EditingDuration(**args):
    return Element(qname = (TEXTNS,'editing-duration'), **args)

def ExecuteMacro(**args):
    return Element(qname = (TEXTNS,'execute-macro'), **args)

def Expression(**args):
    return Element(qname = (TEXTNS,'expression'), **args)

def FileName(**args):
    return Element(qname = (TEXTNS,'file-name'), **args)

def FormatChange(**args):
    return Element(qname = (TEXTNS,'format-change'), **args)

def H(**args):
    return Element(qname = (TEXTNS, 'h'), **args)

def HiddenParagraph(**args):
    return Element(qname = (TEXTNS,'hidden-paragraph'), **args)

def HiddenText(**args):
    return Element(qname = (TEXTNS,'hidden-text'), **args)

def IllustrationIndex(**args):
    return Element(qname = (TEXTNS,'illustration-index'), **args)

def IllustrationIndexEntryTemplate(**args):
    return Element(qname = (TEXTNS,'illustration-index-entry-template'), **args)

def IllustrationIndexSource(**args):
    return Element(qname = (TEXTNS,'illustration-index-source'), **args)

def ImageCount(**args):
    return Element(qname = (TEXTNS,'image-count'), **args)

def IndexBody(**args):
    return Element(qname = (TEXTNS,'index-body'), **args)

def IndexEntryBibliography(**args):
    return Element(qname = (TEXTNS,'index-entry-bibliography'), **args)

def IndexEntryChapter(**args):
    return Element(qname = (TEXTNS,'index-entry-chapter'), **args)

def IndexEntryLinkEnd(**args):
    return Element(qname = (TEXTNS,'index-entry-link-end'), **args)

def IndexEntryLinkStart(**args):
    return Element(qname = (TEXTNS,'index-entry-link-start'), **args)

def IndexEntryPageNumber(**args):
    return Element(qname = (TEXTNS,'index-entry-page-number'), **args)

def IndexEntrySpan(**args):
    return Element(qname = (TEXTNS,'index-entry-span'), **args)

def IndexEntryTabStop(**args):
    return Element(qname = (TEXTNS,'index-entry-tab-stop'), **args)

def IndexEntryText(**args):
    return Element(qname = (TEXTNS,'index-entry-text'), **args)

def IndexSourceStyle(**args):
    return Element(qname = (TEXTNS,'index-source-style'), **args)

def IndexSourceStyles(**args):
    return Element(qname = (TEXTNS,'index-source-styles'), **args)

def IndexTitle(**args):
    return Element(qname = (TEXTNS,'index-title'), **args)

def IndexTitleTemplate(**args):
    return Element(qname = (TEXTNS,'index-title-template'), **args)

def InitialCreator(**args):
    return Element(qname = (TEXTNS,'initial-creator'), **args)

def Insertion(**args):
    return Element(qname = (TEXTNS,'insertion'), **args)

def Keywords(**args):
    return Element(qname = (TEXTNS,'keywords'), **args)

def LineBreak(**args):
    return Element(qname = (TEXTNS,'line-break'), **args)

def LinenumberingConfiguration(**args):
    return Element(qname = (TEXTNS,'linenumbering-configuration'), **args)

def LinenumberingSeparator(**args):
    return Element(qname = (TEXTNS,'linenumbering-separator'), **args)

def List(**args):
    return Element(qname = (TEXTNS,'list'), **args)

def ListHeader(**args):
    return Element(qname = (TEXTNS,'list-header'), **args)

def ListItem(**args):
    return Element(qname = (TEXTNS,'list-item'), **args)

def ListLevelStyleBullet(**args):
    return Element(qname = (TEXTNS,'list-level-style-bullet'), **args)

def ListLevelStyleImage(**args):
    return Element(qname = (TEXTNS,'list-level-style-image'), **args)

def ListLevelStyleNumber(**args):
    return Element(qname = (TEXTNS,'list-level-style-number'), **args)

def ListStyle(**args):
    return StyleElement(qname = (TEXTNS,'list-style'), **args)

def Measure(**args):
    return Element(qname = (TEXTNS,'measure'), **args)

def Meta(**args):
    return Element(qname = (TEXTNS,'meta'), **args)

def MetaField(**args):
    return Element(qname = (TEXTNS,'meta-field'), **args)

def ModificationDate(**args):
    return Element(qname = (TEXTNS,'modification-date'), **args)

def ModificationTime(**args):
    return Element(qname = (TEXTNS,'modification-time'), **args)

def Note(**args):
    return Element(qname = (TEXTNS,'note'), **args)

def NoteBody(**args):
    return Element(qname = (TEXTNS,'note-body'), **args)

def NoteCitation(**args):
    return Element(qname = (TEXTNS,'note-citation'), **args)

def NoteContinuationNoticeBackward(**args):
    return Element(qname = (TEXTNS,'note-continuation-notice-backward'), **args)

def NoteContinuationNoticeForward(**args):
    return Element(qname = (TEXTNS,'note-continuation-notice-forward'), **args)

def NoteRef(**args):
    return Element(qname = (TEXTNS,'note-ref'), **args)

def NotesConfiguration(**args):
    return Element(qname = (TEXTNS,'notes-configuration'), **args)

def Number(**args):
    return Element(qname = (TEXTNS,'number'), **args)

def NumberedParagraph(**args):
    return Element(qname = (TEXTNS,'numbered-paragraph'), **args)

def ObjectCount(**args):
    return Element(qname = (TEXTNS,'object-count'), **args)

def ObjectIndex(**args):
    return Element(qname = (TEXTNS,'object-index'), **args)

def ObjectIndexEntryTemplate(**args):
    return Element(qname = (TEXTNS,'object-index-entry-template'), **args)

def ObjectIndexSource(**args):
    return Element(qname = (TEXTNS,'object-index-source'), **args)

def OutlineLevelStyle(**args):
    return Element(qname = (TEXTNS,'outline-level-style'), **args)

def OutlineStyle(**args):
    return Element(qname = (TEXTNS,'outline-style'), **args)

def P(**args):
    return Element(qname = (TEXTNS, 'p'), **args)

def Page(**args):
    return Element(qname = (TEXTNS,'page'), **args)

def PageContinuation(**args):
    return Element(qname = (TEXTNS,'page-continuation'), **args)

def PageCount(**args):
    return Element(qname = (TEXTNS,'page-count'), **args)

def PageNumber(**args):
    return Element(qname = (TEXTNS,'page-number'), **args)

def PageSequence(**args):
    return Element(qname = (TEXTNS,'page-sequence'), **args)

def PageVariableGet(**args):
    return Element(qname = (TEXTNS,'page-variable-get'), **args)

def PageVariableSet(**args):
    return Element(qname = (TEXTNS,'page-variable-set'), **args)

def ParagraphCount(**args):
    return Element(qname = (TEXTNS,'paragraph-count'), **args)

def Placeholder(**args):
    return Element(qname = (TEXTNS,'placeholder'), **args)

def PrintDate(**args):
    return Element(qname = (TEXTNS,'print-date'), **args)

def PrintTime(**args):
    return Element(qname = (TEXTNS,'print-time'), **args)

def PrintedBy(**args):
    return Element(qname = (TEXTNS,'printed-by'), **args)

def ReferenceMark(**args):
    return Element(qname = (TEXTNS,'reference-mark'), **args)

def ReferenceMarkEnd(**args):
    return Element(qname = (TEXTNS,'reference-mark-end'), **args)

def ReferenceMarkStart(**args):
    return Element(qname = (TEXTNS,'reference-mark-start'), **args)

def ReferenceRef(**args):
    return Element(qname = (TEXTNS,'reference-ref'), **args)

def Ruby(**args):
    return Element(qname = (TEXTNS,'ruby'), **args)

def RubyBase(**args):
    return Element(qname = (TEXTNS,'ruby-base'), **args)

def RubyText(**args):
    return Element(qname = (TEXTNS,'ruby-text'), **args)

def S(**args):
    return Element(qname = (TEXTNS,'s'), **args)

def Script(**args):
    return Element(qname = (TEXTNS,'script'), **args)

def Section(**args):
    return Element(qname = (TEXTNS,'section'), **args)

def SectionSource(**args):
    return Element(qname = (TEXTNS,'section-source'), **args)

def SenderCity(**args):
    return Element(qname = (TEXTNS,'sender-city'), **args)

def SenderCompany(**args):
    return Element(qname = (TEXTNS,'sender-company'), **args)

def SenderCountry(**args):
    return Element(qname = (TEXTNS,'sender-country'), **args)

def SenderEmail(**args):
    return Element(qname = (TEXTNS,'sender-email'), **args)

def SenderFax(**args):
    return Element(qname = (TEXTNS,'sender-fax'), **args)

def SenderFirstname(**args):
    return Element(qname = (TEXTNS,'sender-firstname'), **args)

def SenderInitials(**args):
    return Element(qname = (TEXTNS,'sender-initials'), **args)

def SenderLastname(**args):
    return Element(qname = (TEXTNS,'sender-lastname'), **args)

def SenderPhonePrivate(**args):
    return Element(qname = (TEXTNS,'sender-phone-private'), **args)

def SenderPhoneWork(**args):
    return Element(qname = (TEXTNS,'sender-phone-work'), **args)

def SenderPosition(**args):
    return Element(qname = (TEXTNS,'sender-position'), **args)

def SenderPostalCode(**args):
    return Element(qname = (TEXTNS,'sender-postal-code'), **args)

def SenderStateOrProvince(**args):
    return Element(qname = (TEXTNS,'sender-state-or-province'), **args)

def SenderStreet(**args):
    return Element(qname = (TEXTNS,'sender-street'), **args)

def SenderTitle(**args):
    return Element(qname = (TEXTNS,'sender-title'), **args)

def Sequence(**args):
    return Element(qname = (TEXTNS,'sequence'), **args)

def SequenceDecl(**args):
    return Element(qname = (TEXTNS,'sequence-decl'), **args)

def SequenceDecls(**args):
    return Element(qname = (TEXTNS,'sequence-decls'), **args)

def SequenceRef(**args):
    return Element(qname = (TEXTNS,'sequence-ref'), **args)

def SheetName(**args):
    return Element(qname = (TEXTNS,'sheet-name'), **args)

def SoftPageBreak(**args):
    return Element(qname = (TEXTNS,'soft-page-break'), **args)

def SortKey(**args):
    return Element(qname = (TEXTNS,'sort-key'), **args)

def Span(**args):
    return Element(qname = (TEXTNS,'span'), **args)

def Subject(**args):
    return Element(qname = (TEXTNS,'subject'), **args)

def Tab(**args):
    return Element(qname = (TEXTNS,'tab'), **args)

def TableCount(**args):
    return Element(qname = (TEXTNS,'table-count'), **args)

def TableFormula(**args):
    return Element(qname = (TEXTNS,'table-formula'), **args)

def TableIndex(**args):
    return Element(qname = (TEXTNS,'table-index'), **args)

def TableIndexEntryTemplate(**args):
    return Element(qname = (TEXTNS,'table-index-entry-template'), **args)

def TableIndexSource(**args):
    return Element(qname = (TEXTNS,'table-index-source'), **args)

def TableOfContent(**args):
    return Element(qname = (TEXTNS,'table-of-content'), **args)

def TableOfContentEntryTemplate(**args):
    return Element(qname = (TEXTNS,'table-of-content-entry-template'), **args)

def TableOfContentSource(**args):
    return Element(qname = (TEXTNS,'table-of-content-source'), **args)

def TemplateName(**args):
    return Element(qname = (TEXTNS,'template-name'), **args)

def TextInput(**args):
    return Element(qname = (TEXTNS,'text-input'), **args)

def Time(**args):
    return Element(qname = (TEXTNS,'time'), **args)

def Title(**args):
    return Element(qname = (TEXTNS,'title'), **args)

def TocMark(**args):
    return Element(qname = (TEXTNS,'toc-mark'), **args)

def TocMarkEnd(**args):
    return Element(qname = (TEXTNS,'toc-mark-end'), **args)

def TocMarkStart(**args):
    return Element(qname = (TEXTNS,'toc-mark-start'), **args)

def TrackedChanges(**args):
    return Element(qname = (TEXTNS,'tracked-changes'), **args)

def UserDefined(**args):
    return Element(qname = (TEXTNS,'user-defined'), **args)

def UserFieldDecl(**args):
    return Element(qname = (TEXTNS,'user-field-decl'), **args)

def UserFieldDecls(**args):
    return Element(qname = (TEXTNS,'user-field-decls'), **args)

def UserFieldGet(**args):
    return Element(qname = (TEXTNS,'user-field-get'), **args)

def UserFieldInput(**args):
    return Element(qname = (TEXTNS,'user-field-input'), **args)

def UserIndex(**args):
    return Element(qname = (TEXTNS,'user-index'), **args)

def UserIndexEntryTemplate(**args):
    return Element(qname = (TEXTNS,'user-index-entry-template'), **args)

def UserIndexMark(**args):
    return Element(qname = (TEXTNS,'user-index-mark'), **args)

def UserIndexMarkEnd(**args):
    return Element(qname = (TEXTNS,'user-index-mark-end'), **args)

def UserIndexMarkStart(**args):
    return Element(qname = (TEXTNS,'user-index-mark-start'), **args)

def UserIndexSource(**args):
    return Element(qname = (TEXTNS,'user-index-source'), **args)

def VariableDecl(**args):
    return Element(qname = (TEXTNS,'variable-decl'), **args)

def VariableDecls(**args):
    return Element(qname = (TEXTNS,'variable-decls'), **args)

def VariableGet(**args):
    return Element(qname = (TEXTNS,'variable-get'), **args)

def VariableInput(**args):
    return Element(qname = (TEXTNS,'variable-input'), **args)

def VariableSet(**args):
    return Element(qname = (TEXTNS,'variable-set'), **args)

def WordCount(**args):
    return Element(qname = (TEXTNS,'word-count'), **args)

