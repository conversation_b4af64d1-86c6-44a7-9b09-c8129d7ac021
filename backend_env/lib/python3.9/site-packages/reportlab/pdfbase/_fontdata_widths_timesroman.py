widths = {'A': 722,
 'AE': 889,
 'Aacute': 722,
 'Acircumflex': 722,
 'Adieresis': 722,
 'Agrave': 722,
 'Aring': 722,
 'Atilde': 722,
 'B': 667,
 'C': 667,
 '<PERSON><PERSON><PERSON>': 667,
 'D': 722,
 'E': 611,
 'Eacute': 611,
 'Ecircumflex': 611,
 'Edieresis': 611,
 'Egrave': 611,
 'Eth': 722,
 'Euro': 500,
 'F': 556,
 'G': 722,
 'H': 722,
 'I': 333,
 'Iacute': 333,
 'Icircumflex': 333,
 'Idieresis': 333,
 'Igrave': 333,
 'J': 389,
 'K': 722,
 'L': 611,
 'Lslash': 611,
 'M': 889,
 'N': 722,
 'Ntilde': 722,
 'O': 722,
 'OE': 889,
 'Oacute': 722,
 'Ocircumflex': 722,
 'Odieresis': 722,
 'Ograve': 722,
 'Oslash': 722,
 'Otilde': 722,
 'P': 556,
 'Q': 722,
 'R': 667,
 '<PERSON>': 556,
 '<PERSON>aron': 556,
 'T': 611,
 'Thorn': 556,
 '<PERSON>': 722,
 'Uacute': 722,
 'Ucircumflex': 722,
 'Udieresis': 722,
 'Ugrave': 722,
 'V': 722,
 'W': 944,
 'X': 722,
 'Y': 722,
 'Yacute': 722,
 'Ydieresis': 722,
 'Z': 611,
 'Zcaron': 611,
 'a': 444,
 'aacute': 444,
 'acircumflex': 444,
 'acute': 333,
 'adieresis': 444,
 'ae': 667,
 'agrave': 444,
 'ampersand': 778,
 'aring': 444,
 'asciicircum': 469,
 'asciitilde': 541,
 'asterisk': 500,
 'at': 921,
 'atilde': 444,
 'b': 500,
 'backslash': 278,
 'bar': 200,
 'braceleft': 480,
 'braceright': 480,
 'bracketleft': 333,
 'bracketright': 333,
 'breve': 333,
 'brokenbar': 200,
 'bullet': 350,
 'c': 444,
 'caron': 333,
 'ccedilla': 444,
 'cedilla': 333,
 'cent': 500,
 'circumflex': 333,
 'colon': 278,
 'comma': 250,
 'copyright': 760,
 'currency': 500,
 'd': 500,
 'dagger': 500,
 'daggerdbl': 500,
 'degree': 400,
 'dieresis': 333,
 'divide': 564,
 'dollar': 500,
 'dotaccent': 333,
 'dotlessi': 278,
 'e': 444,
 'eacute': 444,
 'ecircumflex': 444,
 'edieresis': 444,
 'egrave': 444,
 'eight': 500,
 'ellipsis': 1000,
 'emdash': 1000,
 'endash': 500,
 'equal': 564,
 'eth': 500,
 'exclam': 333,
 'exclamdown': 333,
 'f': 333,
 'fi': 556,
 'five': 500,
 'fl': 556,
 'florin': 500,
 'four': 500,
 'fraction': 167,
 'g': 500,
 'germandbls': 500,
 'grave': 333,
 'greater': 564,
 'guillemotleft': 500,
 'guillemotright': 500,
 'guilsinglleft': 333,
 'guilsinglright': 333,
 'h': 500,
 'hungarumlaut': 333,
 'hyphen': 333,
 'i': 278,
 'iacute': 278,
 'icircumflex': 278,
 'idieresis': 278,
 'igrave': 278,
 'j': 278,
 'k': 500,
 'l': 278,
 'less': 564,
 'logicalnot': 564,
 'lslash': 278,
 'm': 778,
 'macron': 333,
 'minus': 564,
 'mu': 500,
 'multiply': 564,
 'n': 500,
 'nine': 500,
 'ntilde': 500,
 'numbersign': 500,
 'o': 500,
 'oacute': 500,
 'ocircumflex': 500,
 'odieresis': 500,
 'oe': 722,
 'ogonek': 333,
 'ograve': 500,
 'one': 500,
 'onehalf': 750,
 'onequarter': 750,
 'onesuperior': 300,
 'ordfeminine': 276,
 'ordmasculine': 310,
 'oslash': 500,
 'otilde': 500,
 'p': 500,
 'paragraph': 453,
 'parenleft': 333,
 'parenright': 333,
 'percent': 833,
 'period': 250,
 'periodcentered': 250,
 'perthousand': 1000,
 'plus': 564,
 'plusminus': 564,
 'q': 500,
 'question': 444,
 'questiondown': 444,
 'quotedbl': 408,
 'quotedblbase': 444,
 'quotedblleft': 444,
 'quotedblright': 444,
 'quoteleft': 333,
 'quoteright': 333,
 'quotesinglbase': 333,
 'quotesingle': 180,
 'r': 333,
 'registered': 760,
 'ring': 333,
 's': 389,
 'scaron': 389,
 'section': 500,
 'semicolon': 278,
 'seven': 500,
 'six': 500,
 'slash': 278,
 'space': 250,
 'sterling': 500,
 't': 278,
 'thorn': 500,
 'three': 500,
 'threequarters': 750,
 'threesuperior': 300,
 'tilde': 333,
 'trademark': 980,
 'two': 500,
 'twosuperior': 300,
 'u': 500,
 'uacute': 500,
 'ucircumflex': 500,
 'udieresis': 500,
 'ugrave': 500,
 'underscore': 500,
 'v': 500,
 'w': 722,
 'x': 500,
 'y': 500,
 'yacute': 500,
 'ydieresis': 500,
 'yen': 500,
 'z': 444,
 'zcaron': 444,
 'zero': 500}
