widths = {'A': 667,
 'AE': 944,
 'Aacute': 667,
 'Acircumflex': 667,
 'Adier<PERSON>': 667,
 'Agrave': 667,
 'Aring': 667,
 'Atilde': 667,
 'B': 667,
 'C': 667,
 '<PERSON><PERSON><PERSON>': 667,
 'D': 722,
 'E': 667,
 'Eacute': 667,
 'Ecircumflex': 667,
 'Edieresis': 667,
 'Egrave': 667,
 'Eth': 722,
 'Euro': 500,
 'F': 667,
 'G': 722,
 'H': 778,
 'I': 389,
 'Iacute': 389,
 'Icircumflex': 389,
 'Idieresis': 389,
 'Igrave': 389,
 'J': 500,
 'K': 667,
 'L': 611,
 'Lslash': 611,
 'M': 889,
 'N': 722,
 'Ntilde': 722,
 'O': 722,
 'OE': 944,
 'Oacute': 722,
 'Ocircumflex': 722,
 'Odieresis': 722,
 'Ograve': 722,
 '<PERSON><PERSON><PERSON>': 722,
 'Otilde': 722,
 'P': 611,
 'Q': 722,
 'R': 667,
 '<PERSON>': 556,
 '<PERSON><PERSON><PERSON>': 556,
 'T': 611,
 'Thorn': 611,
 'U': 722,
 'Uacute': 722,
 'Ucircumflex': 722,
 'Udieresis': 722,
 'Ugrave': 722,
 'V': 667,
 'W': 889,
 'X': 667,
 'Y': 611,
 'Yacute': 611,
 'Ydieresis': 611,
 'Z': 611,
 'Zcaron': 611,
 'a': 500,
 'aacute': 500,
 'acircumflex': 500,
 'acute': 333,
 'adieresis': 500,
 'ae': 722,
 'agrave': 500,
 'ampersand': 778,
 'aring': 500,
 'asciicircum': 570,
 'asciitilde': 570,
 'asterisk': 500,
 'at': 832,
 'atilde': 500,
 'b': 500,
 'backslash': 278,
 'bar': 220,
 'braceleft': 348,
 'braceright': 348,
 'bracketleft': 333,
 'bracketright': 333,
 'breve': 333,
 'brokenbar': 220,
 'bullet': 350,
 'c': 444,
 'caron': 333,
 'ccedilla': 444,
 'cedilla': 333,
 'cent': 500,
 'circumflex': 333,
 'colon': 333,
 'comma': 250,
 'copyright': 747,
 'currency': 500,
 'd': 500,
 'dagger': 500,
 'daggerdbl': 500,
 'degree': 400,
 'dieresis': 333,
 'divide': 570,
 'dollar': 500,
 'dotaccent': 333,
 'dotlessi': 278,
 'e': 444,
 'eacute': 444,
 'ecircumflex': 444,
 'edieresis': 444,
 'egrave': 444,
 'eight': 500,
 'ellipsis': 1000,
 'emdash': 1000,
 'endash': 500,
 'equal': 570,
 'eth': 500,
 'exclam': 389,
 'exclamdown': 389,
 'f': 333,
 'fi': 556,
 'five': 500,
 'fl': 556,
 'florin': 500,
 'four': 500,
 'fraction': 167,
 'g': 500,
 'germandbls': 500,
 'grave': 333,
 'greater': 570,
 'guillemotleft': 500,
 'guillemotright': 500,
 'guilsinglleft': 333,
 'guilsinglright': 333,
 'h': 556,
 'hungarumlaut': 333,
 'hyphen': 333,
 'i': 278,
 'iacute': 278,
 'icircumflex': 278,
 'idieresis': 278,
 'igrave': 278,
 'j': 278,
 'k': 500,
 'l': 278,
 'less': 570,
 'logicalnot': 606,
 'lslash': 278,
 'm': 778,
 'macron': 333,
 'minus': 606,
 'mu': 576,
 'multiply': 570,
 'n': 556,
 'nine': 500,
 'ntilde': 556,
 'numbersign': 500,
 'o': 500,
 'oacute': 500,
 'ocircumflex': 500,
 'odieresis': 500,
 'oe': 722,
 'ogonek': 333,
 'ograve': 500,
 'one': 500,
 'onehalf': 750,
 'onequarter': 750,
 'onesuperior': 300,
 'ordfeminine': 266,
 'ordmasculine': 300,
 'oslash': 500,
 'otilde': 500,
 'p': 500,
 'paragraph': 500,
 'parenleft': 333,
 'parenright': 333,
 'percent': 833,
 'period': 250,
 'periodcentered': 250,
 'perthousand': 1000,
 'plus': 570,
 'plusminus': 570,
 'q': 500,
 'question': 500,
 'questiondown': 500,
 'quotedbl': 555,
 'quotedblbase': 500,
 'quotedblleft': 500,
 'quotedblright': 500,
 'quoteleft': 333,
 'quoteright': 333,
 'quotesinglbase': 333,
 'quotesingle': 278,
 'r': 389,
 'registered': 747,
 'ring': 333,
 's': 389,
 'scaron': 389,
 'section': 500,
 'semicolon': 333,
 'seven': 500,
 'six': 500,
 'slash': 278,
 'space': 250,
 'sterling': 500,
 't': 278,
 'thorn': 500,
 'three': 500,
 'threequarters': 750,
 'threesuperior': 300,
 'tilde': 333,
 'trademark': 1000,
 'two': 500,
 'twosuperior': 300,
 'u': 556,
 'uacute': 556,
 'ucircumflex': 556,
 'udieresis': 556,
 'ugrave': 556,
 'underscore': 500,
 'v': 444,
 'w': 667,
 'x': 500,
 'y': 444,
 'yacute': 444,
 'ydieresis': 444,
 'yen': 500,
 'z': 389,
 'zcaron': 389,
 'zero': 500}
