Metadata-Version: 2.1
Name: django-debug-toolbar
Version: 4.2.0
Summary: A configurable set of panels that display various debug information about the current request/response.
Project-URL: Download, https://pypi.org/project/django-debug-toolbar/
Project-URL: Homepage, https://github.com/jazzband/django-debug-toolbar
Author: Rob Hudson
License: BSD-3-Clause
License-File: LICENSE
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Framework :: Django
Classifier: Framework :: Django :: 3.2
Classifier: Framework :: Django :: 4.0
Classifier: Framework :: Django :: 4.1
Classifier: Framework :: Django :: 4.2
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.8
Requires-Dist: django>=3.2.4
Requires-Dist: sqlparse>=0.2
Description-Content-Type: text/x-rst

=====================================
Django Debug Toolbar |latest-version|
=====================================

|jazzband| |build-status| |coverage| |docs| |python-support| |django-support|

.. |latest-version| image:: https://img.shields.io/pypi/v/django-debug-toolbar.svg
   :target: https://pypi.org/project/django-debug-toolbar/
   :alt: Latest version on PyPI

.. |jazzband| image:: https://jazzband.co/static/img/badge.svg
   :target: https://jazzband.co/
   :alt: Jazzband

.. |build-status| image:: https://github.com/jazzband/django-debug-toolbar/workflows/Test/badge.svg
   :target: https://github.com/jazzband/django-debug-toolbar/actions
   :alt: Build Status

.. |coverage| image:: https://img.shields.io/badge/Coverage-94%25-green
   :target: https://github.com/jazzband/django-debug-toolbar/actions/workflows/test.yml?query=branch%3Amain
   :alt: Test coverage status

.. |docs| image:: https://img.shields.io/readthedocs/django-debug-toolbar/latest.svg
   :target: https://readthedocs.org/projects/django-debug-toolbar/
   :alt: Documentation status

.. |python-support| image:: https://img.shields.io/pypi/pyversions/django-debug-toolbar
   :target: https://pypi.org/project/django-debug-toolbar/
   :alt: Supported Python versions

.. |django-support| image:: https://img.shields.io/pypi/djversions/django-debug-toolbar
   :target: https://pypi.org/project/django-debug-toolbar/
   :alt: Supported Django versions

The Django Debug Toolbar is a configurable set of panels that display various
debug information about the current request/response and when clicked, display
more details about the panel's content.

Here's a screenshot of the toolbar in action:

.. image:: https://raw.github.com/jazzband/django-debug-toolbar/main/example/django-debug-toolbar.png
   :alt: Django Debug Toolbar screenshot

In addition to the built-in panels, a number of third-party panels are
contributed by the community.

The current stable version of the Debug Toolbar is 4.1.0. It works on
Django ≥ 3.2.4.

Documentation, including installation and configuration instructions, is
available at https://django-debug-toolbar.readthedocs.io/.

The Django Debug Toolbar is released under the BSD license, like Django
itself. If you like it, please consider contributing!

The Django Debug Toolbar was originally created by Rob Hudson <<EMAIL>>
in August 2008 and was further developed by many contributors_.

.. _contributors: https://github.com/jazzband/django-debug-toolbar/graphs/contributors
