<div style="display: none" id="current-docs-container" class="container">
  <div class="col-sm-12">
    <a id="current-docs-link">
      Go to the latest version of this document.
    </a>
  </div>

  <!-- Google analytics, obviously. -->
  <script src="//www.google-analytics.com/urchin.js" type="text/javascript"></script>
  <script type="text/javascript">
    _uacct = "UA-99018-6";
    urchinTracker();
  </script>

  <!-- If the documentation isn't current, insert a current link. -->
  <script type="text/javascript">
    if (window.location.pathname.indexOf('/current/') == -1) {
      <!-- Give the user a link to this page, but in the current version of the docs. -->
      var link = document.getElementById('current-docs-link');
      link.href = window.location.pathname.replace(/\/\d+\.\d+\.\d+\/api\//, '/current/api/');
      <!-- And make it visible -->
      var container = document.getElementById('current-docs-container');
      container.style.display = "";
      delete link;
      delete container;
    }
  </script>
  
</div>
