# -*- test-case-name: twisted.python.test.test_url -*-
# Copyright (c) Twisted Matrix Laboratories.
# See LICENSE for details.

"""
Tests for L{twisted.python.url}.
"""
from __future__ import annotations

from typing import Iterable

from typing_extensions import Protocol

from twisted.trial.unittest import SynchronousTestCase
from ..url import URL

theurl = "http://www.foo.com/a/nice/path/?zot=23&zut"

# Examples from RFC 3986 section 5.4, Reference Resolution Examples
relativeLinkBaseForRFC3986 = "http://a/b/c/d;p?q"
relativeLinkTestsForRFC3986 = [
    # "Normal"
    # ('g:h', 'g:h'),     # Not supported:  scheme with relative path
    ("g", "http://a/b/c/g"),
    ("./g", "http://a/b/c/g"),
    ("g/", "http://a/b/c/g/"),
    ("/g", "http://a/g"),
    ("//g", "http://g"),
    ("?y", "http://a/b/c/d;p?y"),
    ("g?y", "http://a/b/c/g?y"),
    ("#s", "http://a/b/c/d;p?q#s"),
    ("g#s", "http://a/b/c/g#s"),
    ("g?y#s", "http://a/b/c/g?y#s"),
    (";x", "http://a/b/c/;x"),
    ("g;x", "http://a/b/c/g;x"),
    ("g;x?y#s", "http://a/b/c/g;x?y#s"),
    ("", "http://a/b/c/d;p?q"),
    (".", "http://a/b/c/"),
    ("./", "http://a/b/c/"),
    ("..", "http://a/b/"),
    ("../", "http://a/b/"),
    ("../g", "http://a/b/g"),
    ("../..", "http://a/"),
    ("../../", "http://a/"),
    ("../../g", "http://a/g"),
    # Abnormal examples
    # ".." cannot be used to change the authority component of a URI.
    ("../../../g", "http://a/g"),
    ("../../../../g", "http://a/g"),
    # Only include "." and ".." when they are only part of a larger segment,
    # not by themselves.
    ("/./g", "http://a/g"),
    ("/../g", "http://a/g"),
    ("g.", "http://a/b/c/g."),
    (".g", "http://a/b/c/.g"),
    ("g..", "http://a/b/c/g.."),
    ("..g", "http://a/b/c/..g"),
    # Unnecessary or nonsensical forms of "." and "..".
    ("./../g", "http://a/b/g"),
    ("./g/.", "http://a/b/c/g/"),
    ("g/./h", "http://a/b/c/g/h"),
    ("g/../h", "http://a/b/c/h"),
    ("g;x=1/./y", "http://a/b/c/g;x=1/y"),
    ("g;x=1/../y", "http://a/b/c/y"),
    # Separating the reference's query and fragment components from the path.
    ("g?y/./x", "http://a/b/c/g?y/./x"),
    ("g?y/../x", "http://a/b/c/g?y/../x"),
    ("g#s/./x", "http://a/b/c/g#s/./x"),
    ("g#s/../x", "http://a/b/c/g#s/../x"),
    # Not supported:  scheme with relative path
    # ("http:g", "http:g"),              # strict
    # ("http:g", "http://a/b/c/g"),      # non-strict
]


_percentenc = lambda s: "".join("%%%02X" % ord(c) for c in s)


class _HasException(Protocol):
    @property
    def exception(self) -> BaseException:
        ...


class TestURL(SynchronousTestCase):
    """
    Tests for L{URL}.
    """

    def assertUnicoded(self, u: URL) -> None:
        """
        The given L{URL}'s components should be L{unicode}.

        @param u: The L{URL} to test.
        """
        self.assertIsInstance(u.scheme, str, repr(u))
        self.assertIsInstance(u.host, str, repr(u))
        for seg in u.path:
            self.assertIsInstance(seg, str, repr(u))
        for k, v in u.query:
            self.assertIsInstance(k, str, repr(u))
            self.assertTrue(v is None or isinstance(v, str), repr(u))
        self.assertIsInstance(u.fragment, str, repr(u))

    def assertURL(
        self,
        u: URL,
        scheme: str,
        host: str,
        path: Iterable[str],
        query: Iterable[tuple[str, str | None]],
        fragment: str,
        port: int | None,
        userinfo: str = "",
    ) -> None:
        """
        The given L{URL} should have the given components.

        @param u: The actual L{URL} to examine.

        @param scheme: The expected scheme.

        @param host: The expected host.

        @param path: The expected path.

        @param query: The expected query.

        @param fragment: The expected fragment.

        @param port: The expected port.

        @param userinfo: The expected userinfo.
        """
        actual = (u.scheme, u.host, u.path, u.query, u.fragment, u.port, u.userinfo)
        expected = (scheme, host, tuple(path), tuple(query), fragment, port, u.userinfo)
        self.assertEqual(actual, expected)

    def test_initDefaults(self) -> None:
        """
        L{URL} should have appropriate default values.
        """

        def check(u: URL) -> None:
            self.assertUnicoded(u)
            self.assertURL(u, "http", "", [], [], "", 80, "")

        check(URL("http", ""))
        check(URL("http", "", [], []))
        check(URL("http", "", [], [], ""))

    def test_init(self) -> None:
        """
        L{URL} should accept L{unicode} parameters.
        """
        u = URL("s", "h", ["p"], [("k", "v"), ("k", None)], "f")
        self.assertUnicoded(u)
        self.assertURL(u, "s", "h", ["p"], [("k", "v"), ("k", None)], "f", None)

        self.assertURL(
            URL("http", "\xe0", ["\xe9"], [("\u03bb", "\u03c0")], "\u22a5"),
            "http",
            "\xe0",
            ["\xe9"],
            [("\u03bb", "\u03c0")],
            "\u22a5",
            80,
        )

    def test_initPercent(self) -> None:
        """
        L{URL} should accept (and not interpret) percent characters.
        """
        u = URL("s", "%68", ["%70"], [("%6B", "%76"), ("%6B", None)], "%66")
        self.assertUnicoded(u)
        self.assertURL(
            u, "s", "%68", ["%70"], [("%6B", "%76"), ("%6B", None)], "%66", None
        )

    def test_repr(self) -> None:
        """
        L{URL.__repr__} will display the canonical form of the URL, wrapped in
        a L{URL.fromText} invocation, so that it is C{eval}-able but still easy
        to read.
        """
        self.assertEqual(
            repr(
                URL(
                    scheme="http",
                    host="foo",
                    path=["bar"],
                    query=[("baz", None), ("k", "v")],
                    fragment="frob",
                )
            ),
            "URL.from_text({})".format(repr("http://foo/bar?baz&k=v#frob")),
        )

    def test_fromText(self) -> None:
        """
        Round-tripping L{URL.fromText} with C{str} results in an equivalent
        URL.
        """
        urlpath = URL.fromText(theurl)
        self.assertEqual(theurl, urlpath.asText())

    def test_roundtrip(self) -> None:
        """
        L{URL.asText} should invert L{URL.fromText}.
        """
        tests = (
            "http://localhost",
            "http://localhost/",
            "http://localhost/foo",
            "http://localhost/foo/",
            "http://localhost/foo!!bar/",
            "http://localhost/foo%20bar/",
            "http://localhost/foo%2Fbar/",
            "http://localhost/foo?n",
            "http://localhost/foo?n=v",
            "http://localhost/foo?n=/a/b",
            "http://example.com/foo!@$bar?b!@z=123",
            "http://localhost/asd?a=asd%20sdf/345",
            "http://(%2525)/(%2525)?(%2525)&(%2525)=(%2525)#(%2525)",
            "http://(%C3%A9)/(%C3%A9)?(%C3%A9)&(%C3%A9)=(%C3%A9)#(%C3%A9)",
        )
        for test in tests:
            result = URL.fromText(test).asText()
            self.assertEqual(test, result)

    def test_equality(self) -> None:
        """
        Two URLs decoded using L{URL.fromText} will be equal (C{==}) if they
        decoded same URL string, and unequal (C{!=}) if they decoded different
        strings.
        """
        urlpath = URL.fromText(theurl)
        self.assertEqual(urlpath, URL.fromText(theurl))
        self.assertNotEqual(
            urlpath,
            URL.fromText(
                "ftp://www.anotherinvaliddomain.com/" "foo/bar/baz/?zot=21&zut"
            ),
        )

    def test_fragmentEquality(self) -> None:
        """
        An URL created with the empty string for a fragment compares equal
        to an URL created with an unspecified fragment.
        """
        self.assertEqual(URL(fragment=""), URL())
        self.assertEqual(
            URL.fromText("http://localhost/#"), URL.fromText("http://localhost/")
        )

    def test_child(self) -> None:
        """
        L{URL.child} appends a new path segment, but does not affect the query
        or fragment.
        """
        urlpath = URL.fromText(theurl)
        self.assertEqual(
            "http://www.foo.com/a/nice/path/gong?zot=23&zut",
            urlpath.child("gong").asText(),
        )
        self.assertEqual(
            "http://www.foo.com/a/nice/path/gong%2F?zot=23&zut",
            urlpath.child("gong/").asText(),
        )
        self.assertEqual(
            "http://www.foo.com/a/nice/path/gong%2Fdouble?zot=23&zut",
            urlpath.child("gong/double").asText(),
        )
        self.assertEqual(
            "http://www.foo.com/a/nice/path/gong%2Fdouble%2F?zot=23&zut",
            urlpath.child("gong/double/").asText(),
        )

    def test_multiChild(self) -> None:
        """
        L{URL.child} receives multiple segments as C{*args} and appends each in
        turn.
        """
        self.assertEqual(
            URL.fromText("http://example.com/a/b").child("c", "d", "e").asText(),
            "http://example.com/a/b/c/d/e",
        )

    def test_childInitRoot(self) -> None:
        """
        L{URL.child} of a L{URL} without a path produces a L{URL} with a single
        path segment.
        """
        childURL = URL(host="www.foo.com").child("c")
        self.assertTrue(childURL.rooted)
        self.assertEqual("http://www.foo.com/c", childURL.asText())

    def test_sibling(self) -> None:
        """
        L{URL.sibling} of a L{URL} replaces the last path segment, but does not
        affect the query or fragment.
        """
        urlpath = URL.fromText(theurl)
        self.assertEqual(
            "http://www.foo.com/a/nice/path/sister?zot=23&zut",
            urlpath.sibling("sister").asText(),
        )
        # Use an url without trailing '/' to check child removal.
        theurl2 = "http://www.foo.com/a/nice/path?zot=23&zut"
        urlpath = URL.fromText(theurl2)
        self.assertEqual(
            "http://www.foo.com/a/nice/sister?zot=23&zut",
            urlpath.sibling("sister").asText(),
        )

    def test_click(self) -> None:
        """
        L{URL.click} interprets the given string as a relative URI-reference
        and returns a new L{URL} interpreting C{self} as the base absolute URI.
        """
        urlpath = URL.fromText(theurl)
        # A null uri should be valid (return here).
        self.assertEqual(
            "http://www.foo.com/a/nice/path/?zot=23&zut", urlpath.click("").asText()
        )
        # A simple relative path remove the query.
        self.assertEqual(
            "http://www.foo.com/a/nice/path/click", urlpath.click("click").asText()
        )
        # An absolute path replace path and query.
        self.assertEqual("http://www.foo.com/click", urlpath.click("/click").asText())
        # Replace just the query.
        self.assertEqual(
            "http://www.foo.com/a/nice/path/?burp", urlpath.click("?burp").asText()
        )
        # One full url to another should not generate '//' between authority.
        # and path
        self.assertNotIn(
            "//foobar", urlpath.click("http://www.foo.com/foobar").asText()
        )

        # From a url with no query clicking a url with a query, the query
        # should be handled properly.
        u = URL.fromText("http://www.foo.com/me/noquery")
        self.assertEqual(
            "http://www.foo.com/me/17?spam=158", u.click("/me/17?spam=158").asText()
        )

        # Check that everything from the path onward is removed when the click
        # link has no path.
        u = URL.fromText("http://localhost/foo?abc=def")
        self.assertEqual(
            u.click("http://www.python.org").asText(), "http://www.python.org"
        )

    def test_clickRFC3986(self) -> None:
        """
        L{URL.click} should correctly resolve the examples in RFC 3986.
        """
        base = URL.fromText(relativeLinkBaseForRFC3986)
        for ref, expected in relativeLinkTestsForRFC3986:
            self.assertEqual(base.click(ref).asText(), expected)

    def test_clickSchemeRelPath(self) -> None:
        """
        L{URL.click} should not accept schemes with relative paths.
        """
        base = URL.fromText(relativeLinkBaseForRFC3986)
        self.assertRaises(NotImplementedError, base.click, "g:h")
        self.assertRaises(NotImplementedError, base.click, "http:h")

    def test_cloneUnchanged(self) -> None:
        """
        Verify that L{URL.replace} doesn't change any of the arguments it
        is passed.
        """
        urlpath = URL.fromText("https://x:1/y?z=1#A")
        self.assertEqual(
            urlpath.replace(
                urlpath.scheme,
                urlpath.host,
                urlpath.path,
                urlpath.query,
                urlpath.fragment,
                urlpath.port,
            ),
            urlpath,
        )
        self.assertEqual(urlpath.replace(), urlpath)

    def test_clickCollapse(self) -> None:
        """
        L{URL.click} collapses C{.} and C{..} according to RFC 3986 section
        5.2.4.
        """
        tests = [
            ["http://localhost/", ".", "http://localhost/"],
            ["http://localhost/", "..", "http://localhost/"],
            ["http://localhost/a/b/c", ".", "http://localhost/a/b/"],
            ["http://localhost/a/b/c", "..", "http://localhost/a/"],
            ["http://localhost/a/b/c", "./d/e", "http://localhost/a/b/d/e"],
            ["http://localhost/a/b/c", "../d/e", "http://localhost/a/d/e"],
            ["http://localhost/a/b/c", "/./d/e", "http://localhost/d/e"],
            ["http://localhost/a/b/c", "/../d/e", "http://localhost/d/e"],
            ["http://localhost/a/b/c/", "../../d/e/", "http://localhost/a/d/e/"],
            ["http://localhost/a/./c", "../d/e", "http://localhost/d/e"],
            ["http://localhost/a/./c/", "../d/e", "http://localhost/a/d/e"],
            ["http://localhost/a/b/c/d", "./e/../f/../g", "http://localhost/a/b/c/g"],
            ["http://localhost/a/b/c", "d//e", "http://localhost/a/b/d//e"],
        ]
        for start, click, expected in tests:
            actual = URL.fromText(start).click(click).asText()
            self.assertEqual(
                actual,
                expected,
                "{start}.click({click}) => {actual} not {expected}".format(
                    start=start,
                    click=repr(click),
                    actual=actual,
                    expected=expected,
                ),
            )

    def test_queryAdd(self) -> None:
        """
        L{URL.add} adds query parameters.
        """
        self.assertEqual(
            "http://www.foo.com/a/nice/path/?foo=bar",
            URL.fromText("http://www.foo.com/a/nice/path/").add("foo", "bar").asText(),
        )
        self.assertEqual(
            "http://www.foo.com/?foo=bar",
            URL(host="www.foo.com").add("foo", "bar").asText(),
        )
        urlpath = URL.fromText(theurl)
        self.assertEqual(
            "http://www.foo.com/a/nice/path/?zot=23&zut&burp",
            urlpath.add("burp").asText(),
        )
        self.assertEqual(
            "http://www.foo.com/a/nice/path/?zot=23&zut&burp=xxx",
            urlpath.add("burp", "xxx").asText(),
        )
        self.assertEqual(
            "http://www.foo.com/a/nice/path/?zot=23&zut&burp=xxx&zing",
            urlpath.add("burp", "xxx").add("zing").asText(),
        )
        # Note the inversion!
        self.assertEqual(
            "http://www.foo.com/a/nice/path/?zot=23&zut&zing&burp=xxx",
            urlpath.add("zing").add("burp", "xxx").asText(),
        )
        # Note the two values for the same name.
        self.assertEqual(
            "http://www.foo.com/a/nice/path/?zot=23&zut&burp=xxx&zot=32",
            urlpath.add("burp", "xxx").add("zot", "32").asText(),
        )

    def test_querySet(self) -> None:
        """
        L{URL.set} replaces query parameters by name.
        """
        urlpath = URL.fromText(theurl)
        self.assertEqual(
            "http://www.foo.com/a/nice/path/?zot=32&zut",
            urlpath.set("zot", "32").asText(),
        )
        # Replace name without value with name/value and vice-versa.
        self.assertEqual(
            "http://www.foo.com/a/nice/path/?zot&zut=itworked",
            urlpath.set("zot").set("zut", "itworked").asText(),
        )
        # Q: what happens when the query has two values and we replace?
        # A: we replace both values with a single one
        self.assertEqual(
            "http://www.foo.com/a/nice/path/?zot=32&zut",
            urlpath.add("zot", "xxx").set("zot", "32").asText(),
        )

    def test_queryRemove(self) -> None:
        """
        L{URL.remove} removes all instances of a query parameter.
        """
        url = URL.fromText("https://example.com/a/b/?foo=1&bar=2&foo=3")
        self.assertEqual(
            url.remove("foo"), URL.fromText("https://example.com/a/b/?bar=2")
        )

    def test_empty(self) -> None:
        """
        An empty L{URL} should serialize as the empty string.
        """
        self.assertEqual(URL().asText(), "")

    def test_justQueryText(self) -> None:
        """
        An L{URL} with query text should serialize as just query text.
        """
        u = URL(query=[("hello", "world")])
        self.assertEqual(u.asText(), "?hello=world")

    def test_identicalEqual(self) -> None:
        """
        L{URL} compares equal to itself.
        """
        u = URL.fromText("http://localhost/")
        self.assertEqual(u, u)

    def test_similarEqual(self) -> None:
        """
        URLs with equivalent components should compare equal.
        """
        u1 = URL.fromText("http://localhost/")
        u2 = URL.fromText("http://localhost/")
        self.assertEqual(u1, u2)

    def test_differentNotEqual(self) -> None:
        """
        L{URL}s that refer to different resources are both unequal (C{!=}) and
        also not equal (not C{==}).
        """
        u1 = URL.fromText("http://localhost/a")
        u2 = URL.fromText("http://localhost/b")
        self.assertFalse(u1 == u2, f"{u1!r} != {u2!r}")
        self.assertNotEqual(u1, u2)

    def test_otherTypesNotEqual(self) -> None:
        """
        L{URL} is not equal (C{==}) to other types.
        """
        u = URL.fromText("http://localhost/")
        self.assertFalse(u == 42, "URL must not equal a number.")
        self.assertFalse(u == object(), "URL must not equal an object.")
        self.assertNotEqual(u, 42)
        self.assertNotEqual(u, object())

    def test_identicalNotUnequal(self) -> None:
        """
        Identical L{URL}s are not unequal (C{!=}) to each other.
        """
        u = URL.fromText("http://localhost/")
        self.assertFalse(u != u, "%r == itself" % u)

    def test_similarNotUnequal(self) -> None:
        """
        Structurally similar L{URL}s are not unequal (C{!=}) to each other.
        """
        u1 = URL.fromText("http://localhost/")
        u2 = URL.fromText("http://localhost/")
        self.assertFalse(u1 != u2, f"{u1!r} == {u2!r}")

    def test_differentUnequal(self) -> None:
        """
        Structurally different L{URL}s are unequal (C{!=}) to each other.
        """
        u1 = URL.fromText("http://localhost/a")
        u2 = URL.fromText("http://localhost/b")
        self.assertTrue(u1 != u2, f"{u1!r} == {u2!r}")

    def test_otherTypesUnequal(self) -> None:
        """
        L{URL} is unequal (C{!=}) to other types.
        """
        u = URL.fromText("http://localhost/")
        self.assertTrue(u != 42, "URL must differ from a number.")
        self.assertTrue(u != object(), "URL must be differ from an object.")

    def test_asURI(self) -> None:
        """
        L{URL.asURI} produces an URI which converts any URI unicode encoding
        into pure US-ASCII and returns a new L{URL}.
        """
        unicodey = (
            "http://\N{LATIN SMALL LETTER E WITH ACUTE}.com/"
            "\N{LATIN SMALL LETTER E}\N{COMBINING ACUTE ACCENT}"
            "?\N{LATIN SMALL LETTER A}\N{COMBINING ACUTE ACCENT}="
            "\N{LATIN SMALL LETTER I}\N{COMBINING ACUTE ACCENT}"
            "#\N{LATIN SMALL LETTER U}\N{COMBINING ACUTE ACCENT}"
        )
        iri = URL.fromText(unicodey)
        uri = iri.asURI()
        self.assertEqual(iri.host, "\N{LATIN SMALL LETTER E WITH ACUTE}.com")
        self.assertEqual(
            iri.path[0], "\N{LATIN SMALL LETTER E}\N{COMBINING ACUTE ACCENT}"
        )
        self.assertEqual(iri.asText(), unicodey)
        expectedURI = "http://xn--9ca.com/%C3%A9?%C3%A1=%C3%AD#%C3%BA"
        actualURI = uri.asText()
        self.assertEqual(actualURI, expectedURI, f"{actualURI!r} != {expectedURI!r}")

    def test_asIRI(self) -> None:
        """
        L{URL.asIRI} decodes any percent-encoded text in the URI, making it
        more suitable for reading by humans, and returns a new L{URL}.
        """
        asciiish = "http://xn--9ca.com/%C3%A9?%C3%A1=%C3%AD#%C3%BA"
        uri = URL.fromText(asciiish)
        iri = uri.asIRI()
        self.assertEqual(uri.host, "xn--9ca.com")
        self.assertEqual(uri.path[0], "%C3%A9")
        self.assertEqual(uri.asText(), asciiish)
        expectedIRI = (
            "http://\N{LATIN SMALL LETTER E WITH ACUTE}.com/"
            "\N{LATIN SMALL LETTER E WITH ACUTE}"
            "?\N{LATIN SMALL LETTER A WITH ACUTE}="
            "\N{LATIN SMALL LETTER I WITH ACUTE}"
            "#\N{LATIN SMALL LETTER U WITH ACUTE}"
        )
        actualIRI = iri.asText()
        self.assertEqual(actualIRI, expectedIRI, f"{actualIRI!r} != {expectedIRI!r}")

    def test_badUTF8AsIRI(self) -> None:
        """
        Bad UTF-8 in a path segment, query parameter, or fragment results in
        that portion of the URI remaining percent-encoded in the IRI.
        """
        urlWithBinary = "http://xn--9ca.com/%00%FF/%C3%A9"
        uri = URL.fromText(urlWithBinary)
        iri = uri.asIRI()
        expectedIRI = (
            "http://\N{LATIN SMALL LETTER E WITH ACUTE}.com/"
            "%00%FF/"
            "\N{LATIN SMALL LETTER E WITH ACUTE}"
        )
        actualIRI = iri.asText()
        self.assertEqual(actualIRI, expectedIRI, f"{actualIRI!r} != {expectedIRI!r}")

    def test_alreadyIRIAsIRI(self) -> None:
        """
        A L{URL} composed of non-ASCII text will result in non-ASCII text.
        """
        unicodey = (
            "http://\N{LATIN SMALL LETTER E WITH ACUTE}.com/"
            "\N{LATIN SMALL LETTER E}\N{COMBINING ACUTE ACCENT}"
            "?\N{LATIN SMALL LETTER A}\N{COMBINING ACUTE ACCENT}="
            "\N{LATIN SMALL LETTER I}\N{COMBINING ACUTE ACCENT}"
            "#\N{LATIN SMALL LETTER U}\N{COMBINING ACUTE ACCENT}"
        )
        iri = URL.fromText(unicodey)
        alsoIRI = iri.asIRI()
        self.assertEqual(alsoIRI.asText(), unicodey)

    def test_alreadyURIAsURI(self) -> None:
        """
        A L{URL} composed of encoded text will remain encoded.
        """
        expectedURI = "http://xn--9ca.com/%C3%A9?%C3%A1=%C3%AD#%C3%BA"
        uri = URL.fromText(expectedURI)
        actualURI = uri.asURI().asText()
        self.assertEqual(actualURI, expectedURI)

    def test_userinfo(self) -> None:
        """
        L{URL.fromText} will parse the C{userinfo} portion of the URI
        separately from the host and port.
        """
        url = URL.fromText(
            "http://someuser:<EMAIL>/some-segment@ignore"
        )
        self.assertEqual(url.authority(True), "someuser:<EMAIL>")
        self.assertEqual(url.authority(False), "someuser:@example.com")
        self.assertEqual(url.userinfo, "someuser:somepassword")
        self.assertEqual(url.user, "someuser")
        self.assertEqual(
            url.asText(), "http://someuser:@example.com/some-segment@ignore"
        )
        self.assertEqual(
            url.replace(userinfo="someuser").asText(),
            "http://<EMAIL>/some-segment@ignore",
        )

    def test_portText(self) -> None:
        """
        L{URL.fromText} parses custom port numbers as integers.
        """
        portURL = URL.fromText("http://www.example.com:8080/")
        self.assertEqual(portURL.port, 8080)
        self.assertEqual(portURL.asText(), "http://www.example.com:8080/")

    def test_mailto(self) -> None:
        """
        Although L{URL} instances are mainly for dealing with HTTP, other
        schemes (such as C{mailto:}) should work as well.  For example,
        L{URL.fromText}/L{URL.asText} round-trips cleanly for a C{mailto:} URL
        representing an email address.
        """
        self.assertEqual(
            URL.fromText("mailto:<EMAIL>").asText(), "mailto:<EMAIL>"
        )

    def test_queryIterable(self) -> None:
        """
        When a L{URL} is created with a C{query} argument, the C{query}
        argument is converted into an N-tuple of 2-tuples.
        """
        # note the type here is invalid as only 2-tuples are accepted
        url = URL(query=[["alpha", "beta"]])  # type: ignore[list-item]
        self.assertEqual(url.query, (("alpha", "beta"),))

    def test_pathIterable(self) -> None:
        """
        When a L{URL} is created with a C{path} argument, the C{path} is
        converted into a tuple.
        """
        url = URL(path=["hello", "world"])
        self.assertEqual(url.path, ("hello", "world"))

    def test_invalidArguments(self) -> None:
        """
        Passing an argument of the wrong type to any of the constructor
        arguments of L{URL} will raise a descriptive L{TypeError}.

        L{URL} typechecks very aggressively to ensure that its constitutent
        parts are all properly immutable and to prevent confusing errors when
        bad data crops up in a method call long after the code that called the
        constructor is off the stack.
        """

        class Unexpected:
            def __str__(self) -> str:
                return "wrong"

            def __repr__(self) -> str:
                return "<unexpected>"

        defaultExpectation = "unicode" if bytes is str else "str"

        def assertRaised(raised: _HasException, expectation: str, name: str) -> None:
            self.assertEqual(
                str(raised.exception),
                "expected {} for {}, got {}".format(expectation, name, "<unexpected>"),
            )

        def check(param: str, expectation: str = defaultExpectation) -> None:
            with self.assertRaises(TypeError) as raised:
                URL(**{param: Unexpected()})  # type: ignore[arg-type]
            assertRaised(raised, expectation, param)

        check("scheme")
        check("host")
        check("fragment")
        check("rooted", "bool")
        check("userinfo")
        check("port", "int or NoneType")

        with self.assertRaises(TypeError) as raised:
            URL(
                path=[
                    Unexpected(),  # type: ignore[list-item]
                ]
            )
        assertRaised(raised, defaultExpectation, "path segment")
        with self.assertRaises(TypeError) as raised:
            URL(
                query=[
                    ("name", Unexpected()),  # type: ignore[list-item]
                ]
            )
        assertRaised(
            raised, defaultExpectation + " or NoneType", "query parameter value"
        )
        with self.assertRaises(TypeError) as raised:
            URL(
                query=[
                    (Unexpected(), "value"),  # type: ignore[list-item]
                ]
            )
        assertRaised(raised, defaultExpectation, "query parameter name")
        # No custom error message for this one, just want to make sure
        # non-2-tuples don't get through.
        with self.assertRaises(TypeError):
            URL(query=[Unexpected()])  # type: ignore[list-item]
        with self.assertRaises(ValueError):
            URL(query=[("k", "v", "vv")])  # type: ignore[list-item]
        with self.assertRaises(ValueError):
            URL(query=[("k",)])  # type: ignore[list-item]

        url = URL.fromText("https://valid.example.com/")
        with self.assertRaises(TypeError) as raised:
            url.child(Unexpected())  # type: ignore[arg-type]
        assertRaised(raised, defaultExpectation, "path segment")
        with self.assertRaises(TypeError) as raised:
            url.sibling(Unexpected())  # type: ignore[arg-type]
        assertRaised(raised, defaultExpectation, "path segment")
        with self.assertRaises(TypeError) as raised:
            url.click(Unexpected())  # type: ignore[arg-type]
        assertRaised(raised, defaultExpectation, "relative URL")

    def test_technicallyTextIsIterableBut(self) -> None:
        """
        Technically, L{str} (or L{unicode}, as appropriate) is iterable, but
        C{URL(path="foo")} resulting in C{URL.fromText("f/o/o")} is never what
        you want.
        """
        with self.assertRaises(TypeError) as raised:
            URL(path="foo")
        self.assertEqual(
            str(raised.exception),
            "expected iterable of text for path, not: {}".format(repr("foo")),
        )


class URLDeprecationTests(SynchronousTestCase):
    """
    L{twisted.python.url} is deprecated.
    """

    def test_urlDeprecation(self) -> None:
        """
        L{twisted.python.url} is deprecated since Twisted 17.5.0.
        """
        from twisted.python import url

        url

        warningsShown = self.flushWarnings([self.test_urlDeprecation])
        self.assertEqual(1, len(warningsShown))
        self.assertEqual(
            (
                "twisted.python.url was deprecated in Twisted 17.5.0:"
                " Please use hyperlink from PyPI instead."
            ),
            warningsShown[0]["message"],
        )
