# SPANISH (ARGENTINA) TRANSLATION
# This file is distributed under the same license as the PACKAGE django-mptt.
# <PERSON><PERSON><PERSON>, 2015.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2014-08-29 12:31+0200\n"
"PO-Revision-Date: 2015-10-11 21:39-0300\n"
"Last-Translator: <PERSON><PERSON><PERSON>\n"
"Language-Team: Spanish (Argentina)\n"
"Language: es_AR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 1.6.10\n"

#: __init__.py:34
#, python-format
msgid "The model %s has already been registered."
msgstr "El modelo %s ya ha sido registrado."

#: forms.py:41
msgid "First child"
msgstr "Primer hijo"

#: forms.py:42
msgid "Last child"
msgstr "Último hijo"

#: forms.py:43
msgid "Left sibling"
msgstr "<PERSON>o izquierdo"

#: forms.py:44
msgid "Right sibling"
msgstr "Hermano derecho"

#: managers.py:121
msgid "Cannot insert a node which has already been saved."
msgstr "No se puede insertar un nodo que ya ha sido guardado."

#: managers.py:306 managers.py:480 managers.py:516 managers.py:673
#, python-format
msgid "An invalid position was given: %s."
msgstr "Posición inválida: %s."

#: managers.py:466 managers.py:653
msgid "A node may not be made a sibling of itself."
msgstr "Un nodo no puede ser hermano de sí mismo."

#: managers.py:632 managers.py:753
msgid "A node may not be made a child of itself."
msgstr "Un nodo no puede ser hijo de sí mismo."

#: managers.py:634 managers.py:755
msgid "A node may not be made a child of any of its descendants."
msgstr "Un nodo no puede ser hijo de alguno de sus descendientes."

#: managers.py:655
msgid "A node may not be made a sibling of any of its descendants."
msgstr "Un nodo no puede ser hermano de alguno de sus descendientes."

#: templatetags/mptt_tags.py:23
#, python-format
msgid "full_tree_for_model tag was given an invalid model: %s"
msgstr "Modelo inválido para tag full_tree_for_model: %s"

#: templatetags/mptt_tags.py:44
#, python-format
msgid "drilldown_tree_for_node tag was given an invalid model: %s"
msgstr "Modelo inválido para tag drilldown_tree_for_node: %s"

#: templatetags/mptt_tags.py:48
#, python-format
msgid "drilldown_tree_for_node tag was given an invalid model field: %s"
msgstr "Campo de modelo inválido para tag drilldown_tree_for_node: %s"

#: templatetags/mptt_tags.py:72
#, python-format
msgid "%s tag requires three arguments"
msgstr "El tag %s require tres argumentos"

#: templatetags/mptt_tags.py:74 templatetags/mptt_tags.py:125
#, python-format
msgid "second argument to %s tag must be 'as'"
msgstr "El segundo argumento para el tag %s debe ser 'as'"

#: templatetags/mptt_tags.py:123
#, python-format
msgid "%s tag requires either three, seven or eight arguments"
msgstr "El tag %s requiere tres, siete u ocho argumentos"

#: templatetags/mptt_tags.py:128
#, python-format
msgid "if seven arguments are given, fourth argument to %s tag must be 'with'"
msgstr ""
"Si se proporcionan siete argumentos, el cuarto para el tag %s debe ser 'with'"

#: templatetags/mptt_tags.py:130
#, python-format
msgid "if seven arguments are given, sixth argument to %s tag must be 'in'"
msgstr ""
"Si se proporcionan siete argumentos, el sexto para el tag %s debe ser 'in'"

#: templatetags/mptt_tags.py:134
#, python-format
msgid ""
"if eight arguments are given, fourth argument to %s tag must be 'cumulative'"
msgstr ""
"Si se proporcionan ocho argumentos, el cuarto para el tag %s debe ser "
"'cumulative'"

#: templatetags/mptt_tags.py:136
#, python-format
msgid "if eight arguments are given, fifth argument to %s tag must be 'count'"
msgstr ""
"Si se proporcionan ocho argumentos, el quinto para el tag %s debe ser 'count'"

#: templatetags/mptt_tags.py:138
#, python-format
msgid "if eight arguments are given, seventh argument to %s tag must be 'in'"
msgstr ""
"Si se proporcionan ocho argumentos, el séptimo para el tag %s debe ser 'in'"
