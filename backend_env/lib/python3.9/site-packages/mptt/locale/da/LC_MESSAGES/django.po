# Danish translation for django-mptt
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the django-mptt package.
# <PERSON> <<EMAIL>>, 2009.
# scootergrisen, 2017.
#
msgid ""
msgstr ""
"Project-Id-Version: django-mptt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2009-09-11 10:38+0200\n"
"PO-Revision-Date: 2017-01-06 00:00+0000\n"
"Last-Translator: scootergrisen\n"
"Language-Team: Danish\n"
"Language: da\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: __init__.py:34
#, python-format
msgid "The model %s has already been registered."
msgstr "Din model er allerede registreret %s."

#: forms.py:40
msgid "First child"
msgstr "Første barn"

#: forms.py:41
msgid "Last child"
msgstr "Sidste barn"

#: forms.py:42
msgid "Left sibling"
msgstr "Venstre søskend"

#: forms.py:43
msgid "Right sibling"
msgstr "Højre søskend"

#: managers.py:121
msgid "Cannot insert a node which has already been saved."
msgstr "Kan ikke indsættte en knude, der allerede er blevet gemt."

#: managers.py:237 managers.py:411 managers.py:447 managers.py:604
#, python-format
msgid "An invalid position was given: %s."
msgstr "En ugyldig position blev givet: %s."

#: managers.py:397 managers.py:584
msgid "A node may not be made a sibling of itself."
msgstr "En knude må ikke være søskend til sig selv."

#: managers.py:563 managers.py:684
msgid "A node may not be made a child of itself."
msgstr "En knude må ikke være barn af sig selv."

#: managers.py:565 managers.py:686
msgid "A node may not be made a child of any of its descendants."
msgstr "En knude må ikke være barn af nogle af dets efterkommere."

#: managers.py:586
msgid "A node may not be made a sibling of any of its descendants."
msgstr "En knude må ikke være søskend med nogle af dets efterkommere."

#: templatetags/mptt_tags.py:23
#, python-format
msgid "full_tree_for_model tag was given an invalid model: %s"
msgstr "full_tree_for_model tagget blev givet en ugyldig model: %s"

#: templatetags/mptt_tags.py:44
#, python-format
msgid "drilldown_tree_for_node tag was given an invalid model: %s"
msgstr "drilldown_tree_for_node tagget blev givet en ugyldig model: %s"

#: templatetags/mptt_tags.py:48
#, python-format
msgid "drilldown_tree_for_node tag was given an invalid model field: %s"
msgstr "drilldown_tree_for_node tagget blev givet et ugyldigt modelfelt: %s"

#: templatetags/mptt_tags.py:72
#, python-format
msgid "%s tag requires three arguments"
msgstr "%s tagget påkræver tre argumenter"

#: templatetags/mptt_tags.py:74 templatetags/mptt_tags.py:125
#, python-format
msgid "second argument to %s tag must be 'as'"
msgstr "det andet argument til %s tagget skal være 'as'"

#: templatetags/mptt_tags.py:123
#, python-format
msgid "%s tag requires either three, seven or eight arguments"
msgstr "%s tagget påkræver enten tre, syv eller otte argumenter"

#: templatetags/mptt_tags.py:128
#, python-format
msgid "if seven arguments are given, fourth argument to %s tag must be 'with'"
msgstr "hvis syv argumenter gives, skal det fjerde argument til %s tagget være 'with'"

#: templatetags/mptt_tags.py:130
#, python-format
msgid "if seven arguments are given, sixth argument to %s tag must be 'in'"
msgstr "hvis syv argumenter gives, skal det sjette argument til %s tagget være 'in'"

#: templatetags/mptt_tags.py:134
#, python-format
msgid ""
"if eight arguments are given, fourth argument to %s tag must be 'cumulative'"
msgstr ""
"hvis otte argumenter gives, skal det fjedre argument til %s tagget være 'cumulative'"

#: templatetags/mptt_tags.py:136
#, python-format
msgid "if eight arguments are given, fifth argument to %s tag must be 'count'"
msgstr "hvis otte argumenter gives, skal det fjedre argument til %s tagget være 'count'"

#: templatetags/mptt_tags.py:138
#, python-format
msgid "if eight arguments are given, seventh argument to %s tag must be 'in'"
msgstr "hvis otte argumenter gives, skal det syvne argument til %s tagget være 'in'"
