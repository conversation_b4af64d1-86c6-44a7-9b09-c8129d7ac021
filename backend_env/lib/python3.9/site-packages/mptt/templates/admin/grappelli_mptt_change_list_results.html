{% if result_hidden_fields %}
    <div class="hiddenfields">{# DIV for HTML validation #}
        {% for item in result_hidden_fields %}{{ item }}{% endfor %}
    </div>
{% endif %}

{% if results %}
    <div class="grp-module grp-changelist-results">
        <table id="result_list" cellspacing="0" class="grp-table grp-sortable">
            <thead>
                <tr>
                    {% for header in result_headers %}
                        <th scope="col" {{ header.class_attrib }}>
                            <div class="grp-text">
                                <span>{{ header.text|capfirst }}</span>
                            </div>
                        </th>{% endfor %}
                </tr>
            </thead>
            <tbody>
                {% for result in results %}
                    {% if result.form.non_field_errors %}
                        <tr class="grp-errors"><td colspan="{{ result|length }}">{{ result.form.non_field_errors }}</td></tr>
                    {% endif %}
                    <tr class="grp-row {% cycle 'grp-row-even' 'grp-row-odd' %}">{% for item in result %}{{ item }}{% endfor %}</tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
{% endif %}
