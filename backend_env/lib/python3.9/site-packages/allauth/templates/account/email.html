{% extends "account/base.html" %}

{% load i18n %}

{% block head_title %}{% trans "Email Addresses" %}{% endblock head_title %}

{% block content %}
    <h1>{% trans "Email Addresses" %}</h1>
{% if emailaddresses %}
<p>{% trans 'The following email addresses are associated with your account:' %}</p>

<form action="{% url 'account_email' %}" class="email_list" method="post">
{% csrf_token %}
<fieldset class="blockLabels">

  {% for emailaddress in emailaddresses %}
<div class="ctrlHolder">
      <label for="email_radio_{{forloop.counter}}" class="{% if emailaddress.primary %}primary_email{%endif%}">

      <input id="email_radio_{{forloop.counter}}" type="radio" name="email" {% if emailaddress.primary or emailaddresses|length == 1 %}checked="checked"{%endif %} value="{{emailaddress.email}}"/>

{{ emailaddress.email }}
    {% if emailaddress.verified %}
    <span class="verified">{% trans "Verified" %}</span>
    {% else %}
    <span class="unverified">{% trans "Unverified" %}</span>
    {% endif %}
      {% if emailaddress.primary %}<span class="primary">{% trans "Primary" %}</span>{% endif %}
</label>
</div>
  {% endfor %}

<div class="buttonHolder">
      <button class="secondaryAction" type="submit" name="action_primary" >{% trans 'Make Primary' %}</button>
      <button class="secondaryAction" type="submit" name="action_send" >{% trans 'Re-send Verification' %}</button>
      <button class="primaryAction" type="submit" name="action_remove" >{% trans 'Remove' %}</button>
</div>

</fieldset>
</form>

{% else %}
{% include "account/snippets/warn_no_email.html" %}
{% endif %}

  {% if can_add_email %}
    <h2>{% trans "Add Email Address" %}</h2>

    <form method="post" action="{% url 'account_email' %}" class="add_email">
        {% csrf_token %}
        {{ form.as_p }}
        <button name="action_add" type="submit">{% trans "Add Email" %}</button>
    </form>
  {% endif %}

{% endblock content %}


{% block extra_body %}
<script type="text/javascript">
(function() {
  var message = "{% trans 'Do you really want to remove the selected email address?' %}";
  var actions = document.getElementsByName('action_remove');
  if (actions.length) {
    actions[0].addEventListener("click", function(e) {
      if (! confirm(message)) {
        e.preventDefault();
      }
    });
  }
})();
</script>
{% endblock extra_body %}
