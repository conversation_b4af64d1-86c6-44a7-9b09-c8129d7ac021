# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON>, 2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2021-11-16 13:19+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>\n"
"Language-Team: Malay (http://www.transifex.com/django/django/language/ms/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ms\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "GIS"
msgstr "GIS"

msgid "The base GIS field."
msgstr "Medan asas GIS"

msgid ""
"The base Geometry field — maps to the OpenGIS Specification Geometry type."
msgstr ""
"Medan asas geometri - disuaikan ke jenis Spesifikasi Geometri OpenGIS ."

msgid "Point"
msgstr "Titik"

msgid "Line string"
msgstr "Rentetan baris"

msgid "Polygon"
msgstr "Polygon"

msgid "Multi-point"
msgstr "Berbilang-titik"

msgid "Multi-line string"
msgstr "Rentetan berbilang-baris"

msgid "Multi polygon"
msgstr "Berbilang polygon"

msgid "Geometry collection"
msgstr "Koleksi geometri"

msgid "Extent Aggregate Field"
msgstr "Medan Agregat Takat"

msgid "Raster Field"
msgstr "Medan Raster"

msgid "No geometry value provided."
msgstr "Tiada nilai geometri diberikan."

msgid "Invalid geometry value."
msgstr "Nilai geometri tidak sah."

msgid "Invalid geometry type."
msgstr "Jenis geometri tidak sah."

msgid ""
"An error occurred when transforming the geometry to the SRID of the geometry "
"form field."
msgstr ""
"Ralat telah berlaku semasa mengubah geometri kepada SRID geometri medan "
"borang."

msgid "Delete all Features"
msgstr "Hapuskan semua Ciri-cri"

msgid "WKT debugging window:"
msgstr "Tingkap penyahpijatan WKT:"

msgid "Debugging window (serialized value)"
msgstr "Tingkap penyahpijatan (nilai bersiri)"

msgid "No feeds are registered."
msgstr "Tiada feed didaftarkan."

#, python-format
msgid "Slug %r isn’t registered."
msgstr "Slug %r tidak didaftarkan."
