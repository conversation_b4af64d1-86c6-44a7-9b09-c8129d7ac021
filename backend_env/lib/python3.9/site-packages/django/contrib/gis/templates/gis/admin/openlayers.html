{% block extrastyle %}
{% load i18n static %}{% get_current_language_bidi as LANGUAGE_BIDI %}
<style type="text/css">
  #{{ id }}_map { width: {{ map_width }}px; height: {{ map_height }}px; }
  #{{ id }}_map .aligned label { float:inherit; }
  #{{ id }}_admin_map { position: relative; vertical-align: top; z-index: 0; float: {{ LANGUAGE_BIDI|yesno:"right,left" }}; }
  {% if not display_wkt %}#{{ id }} { display: none; }{% endif %}
  .olControlEditingToolbar .olControlModifyFeatureItemActive {
     background-image: url("{% static "admin/img/gis/move_vertex_on.svg" %}");
     background-repeat: no-repeat;
  }
  .olControlEditingToolbar .olControlModifyFeatureItemInactive {
     background-image: url("{% static "admin/img/gis/move_vertex_off.svg" %}");
     background-repeat: no-repeat;
  }
</style>
{% endblock %}
<span id="{{ id }}_admin_map">
<script>
//<![CDATA[
{% block openlayers %}{% include "gis/admin/openlayers.js" %}{% endblock %}
//]]>
</script>
<div id="{{ id }}_map" dir="{{ LANGUAGE_BIDI|yesno:'rtl,ltr,auto' }}"></div>
{% if editable %}
<a href="javascript:{{ module }}.clearFeatures()">{% translate "Delete all Features" %}</a>
{% endif %}
{% if display_wkt %}<p>{% translate "WKT debugging window:" %} </p>{% endif %}
<textarea id="{{ id }}" class="vWKTField required" cols="150" rows="10" name="{{ name }}">{{ wkt }}</textarea>
<script>{% block init_function %}{{ module }}.init();{% endblock %}</script>
</span>
