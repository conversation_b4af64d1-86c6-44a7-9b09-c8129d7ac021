# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2017
# jamin <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2012
# <PERSON><PERSON> <<EMAIL>>, 2011,2015
# Liping <PERSON> <<EMAIL>>, 2016-2017
# <AUTHOR> <EMAIL>, 2016
# <PERSON> <<EMAIL>>, 2012-2013
# Wenta<PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2016
# ced773123cfad7b4e8b79ca80f736af9, 2011
# <PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-21 10:22+0200\n"
"PO-Revision-Date: 2021-11-22 03:12+0000\n"
"Last-Translator: lanbla <<EMAIL>>\n"
"Language-Team: Chinese (China) (http://www.transifex.com/django/django/"
"language/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Personal info"
msgstr "个人信息"

msgid "Permissions"
msgstr "权限"

msgid "Important dates"
msgstr "重要日期"

#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr "具有主键 %(key)r 的对象 %(name)s 不存在。"

msgid "Password changed successfully."
msgstr "密码修改成功。"

#, python-format
msgid "Change password: %s"
msgstr "修改密码：%s"

msgid "Authentication and Authorization"
msgstr "认证和授权"

msgid "password"
msgstr "密码"

msgid "last login"
msgstr "上次登录"

msgid "No password set."
msgstr "密码未设置。"

msgid "Invalid password format or unknown hashing algorithm."
msgstr "不可用的密码格式或未知的哈希算法。"

msgid "The two password fields didn’t match."
msgstr "输入的两个密码不一致。"

msgid "Password"
msgstr "密码"

msgid "Password confirmation"
msgstr "密码确认"

msgid "Enter the same password as before, for verification."
msgstr "为了校验，请输入与上面相同的密码。"

msgid ""
"Raw passwords are not stored, so there is no way to see this user’s "
"password, but you can change the password using <a href=\"{}\">this form</a>."
msgstr ""
"密码原文未存储在系统中，因此无法看到该用户的密码。然而你可以通过<a href="
"\"{}\">这个表单</a>来修改密码。"

#, python-format
msgid ""
"Please enter a correct %(username)s and password. Note that both fields may "
"be case-sensitive."
msgstr "请输入一个正确的%(username)s和密码。注意，两者都区分大小写。"

msgid "This account is inactive."
msgstr "该帐号未激活。"

msgid "Email"
msgstr "电子邮件"

msgid "New password"
msgstr "新密码"

msgid "New password confirmation"
msgstr "新密码确认"

msgid "Your old password was entered incorrectly. Please enter it again."
msgstr "你的旧密码不正确。请重新输入。"

msgid "Old password"
msgstr "旧密码"

msgid "Password (again)"
msgstr "密码（重复）"

msgid "algorithm"
msgstr "算法"

msgid "iterations"
msgstr "迭代次数"

msgid "salt"
msgstr "盐"

msgid "hash"
msgstr "哈希"

msgid "variety"
msgstr "多样性"

msgid "version"
msgstr "版本"

msgid "memory cost"
msgstr "内存花销"

msgid "time cost"
msgstr "时间花销"

msgid "parallelism"
msgstr "对比"

msgid "work factor"
msgstr "加密因子"

msgid "checksum"
msgstr "校验和"

msgid "block size"
msgstr "块大小"

msgid "name"
msgstr "名称"

msgid "content type"
msgstr "内容类型"

msgid "codename"
msgstr "代码名称"

msgid "permission"
msgstr "权限"

msgid "permissions"
msgstr "权限"

msgid "group"
msgstr "组"

msgid "groups"
msgstr "组"

msgid "superuser status"
msgstr "超级用户状态"

msgid ""
"Designates that this user has all permissions without explicitly assigning "
"them."
msgstr "指明该用户缺省拥有所有权限。"

msgid ""
"The groups this user belongs to. A user will get all permissions granted to "
"each of their groups."
msgstr "该用户归属的组。一个用户将得到其归属的组的所有权限。"

msgid "user permissions"
msgstr "用户权限"

msgid "Specific permissions for this user."
msgstr "这个用户的特定权限。"

msgid "username"
msgstr "用户名"

msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr ""
"必填；长度为150个字符或以下；只能包含字母、数字、特殊字符“@”、“.”、“-”和“_”。"

msgid "A user with that username already exists."
msgstr "已存在一位使用该名字的用户。"

msgid "first name"
msgstr "名字"

msgid "last name"
msgstr "姓氏"

msgid "email address"
msgstr "电子邮件地址"

msgid "staff status"
msgstr "工作人员状态"

msgid "Designates whether the user can log into this admin site."
msgstr "指明用户是否可以登录到这个管理站点。"

msgid "active"
msgstr "有效"

msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr "指明用户是否被认为是活跃的。以反选代替删除帐号。"

msgid "date joined"
msgstr "加入日期"

msgid "user"
msgstr "用户"

msgid "users"
msgstr "用户"

#, python-format
msgid ""
"This password is too short. It must contain at least %(min_length)d "
"character."
msgid_plural ""
"This password is too short. It must contain at least %(min_length)d "
"characters."
msgstr[0] "密码长度太短。密码必须包含至少 %(min_length)d 个字符。"

#, python-format
msgid "Your password must contain at least %(min_length)d character."
msgid_plural "Your password must contain at least %(min_length)d characters."
msgstr[0] "你的密码必须包含至少 %(min_length)d 个字符。"

#, python-format
msgid "The password is too similar to the %(verbose_name)s."
msgstr "密码跟 %(verbose_name)s 太相似了。"

msgid "Your password can’t be too similar to your other personal information."
msgstr "你的密码不能与你的其他个人信息太相似。"

msgid "This password is too common."
msgstr "这个密码太常见了。"

msgid "Your password can’t be a commonly used password."
msgstr "你的密码不能是一个常见密码。"

msgid "This password is entirely numeric."
msgstr "密码只包含数字。"

msgid "Your password can’t be entirely numeric."
msgstr "你的密码不能全都是数字。"

#, python-format
msgid "Password reset on %(site_name)s"
msgstr "重置 %(site_name)s 的密码"

msgid ""
"Enter a valid username. This value may contain only English letters, "
"numbers, and @/./+/-/_ characters."
msgstr ""
"请输入合法的用户名。只能包含英文字母、数字、特殊字符“@”、“.”、“-”和“_”。"

msgid ""
"Enter a valid username. This value may contain only letters, numbers, and "
"@/./+/-/_ characters."
msgstr ""
"请输入合法的用户名。只能包含英文字母、数字、特殊字符“@”、“.”、“-”和“_”。"

msgid "Logged out"
msgstr "登出"

msgid "Password reset"
msgstr "重置密码"

msgid "Password reset sent"
msgstr "密码重置链接已经发送。"

msgid "Enter new password"
msgstr "输入新密码"

msgid "Password reset unsuccessful"
msgstr "密码重置失败"

msgid "Password reset complete"
msgstr "密码重置完成"

msgid "Password change"
msgstr "密码更改"

msgid "Password change successful"
msgstr "密码更改成功"
