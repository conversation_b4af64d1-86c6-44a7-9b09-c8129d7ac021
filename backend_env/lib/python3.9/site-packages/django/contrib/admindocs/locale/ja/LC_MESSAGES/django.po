# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON>dekasp <<EMAIL>>, 2012
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2013-2016
# Ta<PERSON>ya <PERSON> <<EMAIL>>, 2020
# T<PERSON><PERSON><PERSON> <<EMAIL>>, 2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-05-09 04:02+0000\n"
"Last-Translator: T<PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Japanese (http://www.transifex.com/django/django/language/"
"ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Administrative Documentation"
msgstr "管理用ドキュメント"

msgid "Home"
msgstr "ホーム"

msgid "Documentation"
msgstr "ドキュメント"

msgid "Bookmarklets"
msgstr "ブックマークレット"

msgid "Documentation bookmarklets"
msgstr "ドキュメントへのブックマークレット"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"ブックマークレットをインストールするには、リンクをブックマークツールバーにド"
"ラッグするか、 リンクを右クリックしてブックマークに追加してください。これでサ"
"イトのどのページからでもブックマークレットを選択可能になります。"

msgid "Documentation for this page"
msgstr "このページのドキュメント"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr "各ページから、ページを生成したビューのドキュメントにジャンプします。"

msgid "Tags"
msgstr "タグ"

msgid "List of all the template tags and their functions."
msgstr "すべてのテンプレートタグとその機能の一覧です。"

msgid "Filters"
msgstr "フィルター"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"フィルタは、テンプレート内の変数に適用して出力を変更するためのアクションで"
"す。"

msgid "Models"
msgstr "モデル"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"モデルは、すべてのシステム内のオブジェクトとそれに関連するフィールドの説明で"
"す。各モデルは、テンプレート変数としてアクセスできるフィールドのリストを持っ"
"ています。"

msgid "Views"
msgstr "ビュー"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"公開サイトの各ページは、ビューによって生成されます。ビューは、ページの生成に"
"使用されるテンプレートとそのテンプレートで利用できるオブジェクトが定義されて"
"います。"

msgid "Tools for your browser to quickly access admin functionality."
msgstr "ブラウザから管理機能にすぐにアクセスするためのツールです。"

msgid "Please install docutils"
msgstr "docutilsをインストールして下さい"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"管理用ドキュメントシステムにはPythonの<a href=\"%(link)s\">docutils</a>ライブ"
"ラリが必要です。"

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"管理者に <a href=\"%(link)s\">docutils</a> のインストールについて問い合せて下"
"さい。"

#, python-format
msgid "Model: %(name)s"
msgstr "モデル: %(name)s"

msgid "Fields"
msgstr "フィールド"

msgid "Field"
msgstr "フィールド"

msgid "Type"
msgstr "型"

msgid "Description"
msgstr "説明"

msgid "Methods with arguments"
msgstr "メソッドと引数"

msgid "Method"
msgstr "メソッド"

msgid "Arguments"
msgstr "引数"

msgid "Back to Model documentation"
msgstr "モデルドキュメントに戻る"

msgid "Model documentation"
msgstr "モデルドキュメント"

msgid "Model groups"
msgstr "モデルグループ"

msgid "Templates"
msgstr "テンプレート"

#, python-format
msgid "Template: %(name)s"
msgstr "テンプレート: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "テンプレート: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "テンプレートの検索パス <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(存在しません)"

msgid "Back to Documentation"
msgstr "ドキュメントに戻る"

msgid "Template filters"
msgstr "テンプレートフィルタ"

msgid "Template filter documentation"
msgstr "テンプレートフィルタドキュメント"

msgid "Built-in filters"
msgstr "組み込みフィルタ"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"これらのフィルタを使用するには、テンプレート内でのフィルタの使用箇所より前に "
"<code>%(code)s</code> を記述してください。"

msgid "Template tags"
msgstr "テンプレートタグ"

msgid "Template tag documentation"
msgstr "テンプレートタグドキュメント"

msgid "Built-in tags"
msgstr "組み込みタグ"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"これらのタグを使用するためには、タグの使用箇所より前に <code>%(code)s</code> "
"をテンプレート内に記述します。"

#, python-format
msgid "View: %(name)s"
msgstr "ビュー: %(name)s"

msgid "Context:"
msgstr "コンテキスト:"

msgid "Templates:"
msgstr "テンプレート:"

msgid "Back to View documentation"
msgstr "ビュードキュメントに戻る"

msgid "View documentation"
msgstr "ビュードキュメント"

msgid "Jump to namespace"
msgstr "ネームスペースへ移動"

msgid "Empty namespace"
msgstr "空のネームスペース"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "ネームスペース %(name)s のビュー"

msgid "Views by empty namespace"
msgstr "空のネームスペースのよるビュー"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"ビュー関数: <code>%(full_name)s</code>. 名前: <code>%(url_name)s</code>.\n"

msgid "tag:"
msgstr "タグ"

msgid "filter:"
msgstr "フィルター"

msgid "view:"
msgstr "ビュー"

#, python-format
msgid "App %(app_label)r not found"
msgstr "アプリケーション %(app_label)r が見つかりません"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr ""
"モデル %(model_name)r が %(app_label)r アプリケーションに見つかりません"

msgid "model:"
msgstr "モデル :"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "`%(app_label)s.%(data_type)s` (関連オブジェクト)"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "`%(app_label)s.%(object_name)s` (関連オブジェクト)"

#, python-format
msgid "all %s"
msgstr "全ての %s"

#, python-format
msgid "number of %s"
msgstr "%s の数"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s はurlpatternオブジェクトでは無いようです"
