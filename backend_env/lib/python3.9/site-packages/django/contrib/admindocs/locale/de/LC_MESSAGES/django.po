# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON>, 2013
# <PERSON><PERSON> <<EMAIL>>, 2013-2016,2020
# <PERSON><PERSON>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-11-28 17:15+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: German (http://www.transifex.com/django/django/language/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Administrative Dokumentation"

msgid "Home"
msgstr "Start"

msgid "Documentation"
msgstr "Dokumentation"

msgid "Bookmarklets"
msgstr "Bookmarklets"

msgid "Documentation bookmarklets"
msgstr "Dokumentations-Bookmarklets"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Um die Bookmarklets zu installieren, muss dieser Link in die Browser-"
"Werkzeugleiste gezogen werden oder mittels rechter Maustaste in den "
"Bookmarks gespeichert werden. Danach können die Bookmarklets von jeder Seite "
"aufgerufen werden."

msgid "Documentation for this page"
msgstr "Dokumentation für diese Seite"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Springt von jeder Seite zu der Dokumentation für den View, der diese Seite "
"erzeugt."

msgid "Tags"
msgstr "Tags"

msgid "List of all the template tags and their functions."
msgstr "Alle Template-Tags und deren Funktionen auflisten."

msgid "Filters"
msgstr "Filter"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Filter sind Aktionen, die in Templates auf Variablen angewendet werden "
"können, um deren Ausgabe zu verändern."

msgid "Models"
msgstr "Modelle"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Modelle sind Beschreibungen aller Objekte und ihrer Felder, die sich im "
"System befinden. Jedes Model hat eine Reihe von Feldern, auf in Form von "
"Templatevariablen zugegriffen werden kann."

msgid "Views"
msgstr "Views"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Jede öffentliche Seite wird durch einen View generiert. Dieser View "
"definiert, welches Template genutzt wird, um die Seite zu generieren und "
"welche Objekte in dem jeweiligen Template zur Verfügung stehen."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Hilfsfunktionen für den Browser, um schnell auf den Administrationsbereich "
"zugreifen zu können."

msgid "Please install docutils"
msgstr "Bitte docutils installieren."

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Das Admin-Dokumentationssystem erfordert die Python-Bibliothek <a href="
"\"%(link)s\">docutils</a>."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Bitte durch den/die Administrator/in <a href=\"%(link)s\">docutils</a> "
"installieren lassen."

#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

msgid "Fields"
msgstr "Felder"

msgid "Field"
msgstr "Feld"

msgid "Type"
msgstr "Klasse"

msgid "Description"
msgstr "Beschreibung"

msgid "Methods with arguments"
msgstr "Methode mit Argumenten"

msgid "Method"
msgstr "Methode"

msgid "Arguments"
msgstr "Argumente"

msgid "Back to Model documentation"
msgstr "Zurück zur Model-Dokumentation"

msgid "Model documentation"
msgstr "Model-Dokumentation"

msgid "Model groups"
msgstr "Model-Gruppen"

msgid "Templates"
msgstr "Templates"

#, python-format
msgid "Template: %(name)s"
msgstr "Template: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Template: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Suchpfade für Template <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(existiert nicht)"

msgid "Back to Documentation"
msgstr "Zurück zur Dokumentation"

msgid "Template filters"
msgstr "Template-Filter"

msgid "Template filter documentation"
msgstr "Template-Filter-Dokumentation"

msgid "Built-in filters"
msgstr "Mitgelieferte Filter"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Um diese Filter zu nutzen, muss sich <code>%(code)s</code> vor Aufruf des "
"Filters im Template befinden."

msgid "Template tags"
msgstr "Template-Tags"

msgid "Template tag documentation"
msgstr "Template-Tag-Dokumentation"

msgid "Built-in tags"
msgstr "Mitgelieferte Tags"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Um diese Tags zu nutzen, muss sich <code>%(code)s</code> vor Aufruf des Tags "
"im Template befinden."

#, python-format
msgid "View: %(name)s"
msgstr "View: %(name)s"

msgid "Context:"
msgstr "Kontext:"

msgid "Templates:"
msgstr "Templates:"

msgid "Back to View documentation"
msgstr "Zurück zur View-Dokumentation"

msgid "View documentation"
msgstr "View Dokumentation"

msgid "Jump to namespace"
msgstr "Zu Namespace springen"

msgid "Empty namespace"
msgstr "Leerer Namespace"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Views in Namespace %(name)s"

msgid "Views by empty namespace"
msgstr "Views ohne Namespace"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    View-Funktion: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "Tag:"

msgid "filter:"
msgstr "Filter:"

msgid "view:"
msgstr "View:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Anwendung %(app_label)r nicht gefunden"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Modell %(model_name)r wurde nicht in Anwendung %(app_label)r gefunden"

msgid "model:"
msgstr "Modell:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "Das verknüpfte `%(app_label)s.%(data_type)s` Objekt"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "verknüpfte `%(app_label)s.%(object_name)s` Objekte"

#, python-format
msgid "all %s"
msgstr "Alle %s"

#, python-format
msgid "number of %s"
msgstr "Anzahl von %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s ist scheinbar kein urlpattern-Objekt"
