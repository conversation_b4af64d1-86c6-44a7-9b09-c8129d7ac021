# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2012
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2013,2015-2016,2019
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-01-15 18:23+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Danish (http://www.transifex.com/django/django/language/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Administrationsdokumentation"

msgid "Home"
msgstr "Hjem"

msgid "Documentation"
msgstr "Dokumentation"

msgid "Bookmarklets"
msgstr "Bookmarklets"

msgid "Documentation bookmarklets"
msgstr "Dokumentations-bookmarklets"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"For at installere bookmarklets, skal du trække linket til din bogmærkelinje "
"eller højreklikke linket og tilføje det til dine bogmærker. Derefter kan du "
"vælge bookmarkletten fra enhver side på websitet."

msgid "Documentation for this page"
msgstr "Dokumentation for denne side"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Bringer dig fra en hvilken som helst side til dokumentationen for det view, "
"der genererer den pågældende side."

msgid "Tags"
msgstr "Mærker"

msgid "List of all the template tags and their functions."
msgstr "Liste af alle template tags og deres funktioner."

msgid "Filters"
msgstr "Filtre"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Filtre er handlinger, der kan anvendes på variabler i en skabelon for at "
"ændre outputtet."

msgid "Models"
msgstr "Modeller"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Modeller er beskrivelser af alle objekter i systemet, og deres tilhørende "
"felter. Hver model har en liste af felter som kan tilgås som en skabelon "
"variabel."

msgid "Views"
msgstr "Views"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Hver side på den offentlige side bliver genereret af et view. Viewet "
"definerer hvilken skabelon der bruges til at generere siden og hvilke "
"objekter er tilgængelige fra skabelonen."

msgid "Tools for your browser to quickly access admin functionality."
msgstr "Værktøjer for din browser til hurtig adgang til admin funktionalitet."

msgid "Please install docutils"
msgstr "Installer venligst docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Admin dokumentationssystemet kræver Pythons <a href=\"%(link)s\">docutils</"
"a> bibliotek."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Venligst spørg dine administratorer om at installere <a href=\"%(link)s"
"\">docutils</a> ."

#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

msgid "Fields"
msgstr "Felter"

msgid "Field"
msgstr "Felt"

msgid "Type"
msgstr "Type"

msgid "Description"
msgstr "Beskrivelse"

msgid "Methods with arguments"
msgstr "Metode med argumenter"

msgid "Method"
msgstr "Metode"

msgid "Arguments"
msgstr "Argumenter"

msgid "Back to Model documentation"
msgstr "Tilbage til modeldokumentationen"

msgid "Model documentation"
msgstr "Modeldokumentation"

msgid "Model groups"
msgstr "Modelgrupper"

msgid "Templates"
msgstr "Skabeloner"

#, python-format
msgid "Template: %(name)s"
msgstr "Skabelon: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Skabelon: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Søgesti for skabelon <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(eksisterer ikke)"

msgid "Back to Documentation"
msgstr "Tilbage til dokumentationen"

msgid "Template filters"
msgstr "Skabelonfiltre"

msgid "Template filter documentation"
msgstr "Skabelonfilterdokumentation"

msgid "Built-in filters"
msgstr "Indbyggede filtre"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"For at bruge disse filtre, indsæt <code>%(code)s</code> i din skabelon, før "
"du bruger filteret."

msgid "Template tags"
msgstr "Skabelon-tags"

msgid "Template tag documentation"
msgstr "Skabelon-tag-dokumentation"

msgid "Built-in tags"
msgstr "Indbyggede tags"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"For at bruge disse tags, indsæt <code>%(code)s</code> i din skabelon, før du "
"bruger tagget."

#, python-format
msgid "View: %(name)s"
msgstr "View: %(name)s"

msgid "Context:"
msgstr "Kontekst:"

msgid "Templates:"
msgstr "Skabeloner:"

msgid "Back to View documentation"
msgstr "Tilbage til View-dokumentationen"

msgid "View documentation"
msgstr "View dokumentation"

msgid "Jump to namespace"
msgstr "Hop til namespace"

msgid "Empty namespace"
msgstr "Tomt namespace"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Views efter namespace %(name)s"

msgid "Views by empty namespace"
msgstr "Views efter tomt namespace"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    View funktion: <code>%(full_name)s</code>. Navn: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "tag:"

msgid "filter:"
msgstr "filter:"

msgid "view:"
msgstr "view:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Applikation %(app_label)r blev ikke fundet"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Modellen %(model_name)r ikke fundet i applikationen %(app_label)r"

msgid "model:"
msgstr "model:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "det relaterede `%(app_label)s.%(data_type)s`-objekt"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "relaterede `%(app_label)s.%(object_name)s`-objekter"

#, python-format
msgid "all %s"
msgstr "alle %s"

#, python-format
msgid "number of %s"
msgstr "antal %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s ser ikke ud til at være et urlpattern-objekt"
