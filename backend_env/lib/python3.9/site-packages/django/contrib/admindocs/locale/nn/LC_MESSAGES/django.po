# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011
# Sivert <PERSON>, 2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-11-16 23:22+0000\n"
"Last-Translator: Si<PERSON>\n"
"Language-Team: Norwegian Nynorsk (http://www.transifex.com/django/django/"
"language/nn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: nn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Administrasjons-dokumentasjon"

msgid "Home"
msgstr "Heim"

msgid "Documentation"
msgstr "Dokumentasjon"

msgid "Bookmarklets"
msgstr "Bokmerke"

msgid "Documentation bookmarklets"
msgstr "Dokumentasjonsbokmerke"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"For å installere bokmerke, dra lenkja til verktøylinja for bokmerke, eller "
"høgreklikk og legg til i dine bokmerke. No kan du velje bokmerket frå kva "
"for helst side på nettstaden."

msgid "Documentation for this page"
msgstr "Dokumentasjon for denne sida"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Hopp frå kva som helst side til dokumentasjonen for visingsfunksjonen som "
"genererte sida."

msgid "Tags"
msgstr "Taggar"

msgid "List of all the template tags and their functions."
msgstr "Liste over alle mal-taggar og funksjonane deira."

msgid "Filters"
msgstr "Filter"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Filter er handlingar som kan bli brukt på variablar i ein mal for å endre "
"utdata."

msgid "Models"
msgstr "Modellar"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Modellar er beskrivingar av alle objekta i eit system og tilhøyrande felt. "
"Kvar modell har ein liste over felt som kan bli brukt som mal-variablar."

msgid "Views"
msgstr "Views"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Kvar side på den offentlege nettstaden er generert av ein view. Viewen "
"definerer kva for ein mal som blir brukt til å generere sida og kva for "
"nokre objekt som er tilgjengelege for den malen."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Verktøy for nettlesaren din for å få rask tilgang til admin-funksjonalitet"

msgid "Please install docutils"
msgstr "Installer docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Admin-verktøyet sitt dokumentasjonssystem behøver Python sitt <a href="
"\"%(link)s\">docutils</a>-bibliotek."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Spør administratorane dine om å installere <a href=\"%(link)s\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Modell: %(name)s"

msgid "Fields"
msgstr "Felt"

msgid "Field"
msgstr "Felt"

msgid "Type"
msgstr "Type"

msgid "Description"
msgstr "Beskriving"

msgid "Methods with arguments"
msgstr "Metodar med argument"

msgid "Method"
msgstr "Metode"

msgid "Arguments"
msgstr "Argument"

msgid "Back to Model documentation"
msgstr "Attende til Modelldokumentasjon"

msgid "Model documentation"
msgstr "Modelldokumentasjon"

msgid "Model groups"
msgstr "Modellgrupper"

msgid "Templates"
msgstr "Malar"

#, python-format
msgid "Template: %(name)s"
msgstr "Mal: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Mal: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Søkjebane for mal <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(finnast ikkje)"

msgid "Back to Documentation"
msgstr "Attende til Dokumentasjon"

msgid "Template filters"
msgstr "Malfilter"

msgid "Template filter documentation"
msgstr "Malfilterdokumentasjon"

msgid "Built-in filters"
msgstr "Innbygde filter"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"For å bruke desse filtera, set inn <code>%(code)s</code> i malen før du "
"brukar filteret."

msgid "Template tags"
msgstr "Maltaggar"

msgid "Template tag documentation"
msgstr "Maltagdokumentasjon"

msgid "Built-in tags"
msgstr "Innebygde taggar"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"For å bruke desse taggane, set inn <code>%(code)s</code> i malen før du "
"brukar taggen."

#, python-format
msgid "View: %(name)s"
msgstr "View: %(name)s"

msgid "Context:"
msgstr "Kontekst:"

msgid "Templates:"
msgstr "Malar:"

msgid "Back to View documentation"
msgstr "Attende til Viewdokumentasjon"

msgid "View documentation"
msgstr "Viewdokumentasjon"

msgid "Jump to namespace"
msgstr "Gå til namnerom"

msgid "Empty namespace"
msgstr "Tomt namnerom"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Views etter namnerom %(name)s"

msgid "Views by empty namespace"
msgstr "Views etter tomt namnerom"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
" Viewfunksjon: <code>%(full_name)s</code>. Namn: <code>%(url_name)s</code>.\n"

msgid "tag:"
msgstr "tag:"

msgid "filter:"
msgstr "filter:"

msgid "view:"
msgstr "view:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Applikasjon %(app_label)r ikkje funne"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr ""
"Kunne ikkje finne modellen %(model_name)r i applikasjonen %(app_label)r"

msgid "model:"
msgstr "modell:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "det relaterte `%(app_label)s.%(data_type)s`-objektet"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "relaterte `%(app_label)s.%(object_name)s`-objekt"

#, python-format
msgid "all %s"
msgstr "alle %s"

#, python-format
msgid "number of %s"
msgstr "tal på %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s ser ikkje ut til å vere eit urlpattern-objekt"
