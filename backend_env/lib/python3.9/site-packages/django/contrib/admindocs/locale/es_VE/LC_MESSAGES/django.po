# This file is distributed under the same license as the Django package.
#
# Translators:
# Hotellook, 2014
# <PERSON> G. <<EMAIL>>, 2016
# Yo<PERSON>vedo, 2017
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-01-19 16:49+0100\n"
"PO-Revision-Date: 2017-09-19 19:11+0000\n"
"Last-Translator: <PERSON><PERSON>\n"
"Language-Team: Spanish (Venezuela) (http://www.transifex.com/django/django/"
"language/es_VE/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: es_VE\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Documentación Administrativa"

msgid "Home"
msgstr "Inicio"

msgid "Documentation"
msgstr "Documentación"

msgid "Bookmarklets"
msgstr "Bookmarklets"

msgid "Documentation bookmarklets"
msgstr "Documentación de Bookmarklets"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Para instalar bookmarklets, arrastre el enlace a tu barra de marcadores, o "
"haga clic derecho en el enlace y agregarlo a tus marcadores. Ahora puede "
"seleccionar el bookmarklet desde cualquier página en el sitio."

msgid "Documentation for this page"
msgstr "Documentación para esta página"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Ir desde cualquier página a la documentación de vista que la genera esa "
"página."

msgid "Tags"
msgstr "Etiquetas"

msgid "List of all the template tags and their functions."
msgstr "Lista de todas las etiquetas de plantillas y sus funciones."

msgid "Filters"
msgstr "Filtros"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Los filtros son acciones que pueden ser aplicadas a variables en la "
"plantilla para cambiar el resultado."

msgid "Models"
msgstr "Modelos"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Los modelos son descripción de los objetos del sistema y sus campos "
"asociados. Cada modelo tiene una lista de campos a la que se puede acceder "
"como variables de plantilla"

msgid "Views"
msgstr "Vistas"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Cada página del sitio es generada por una vista. En la vista se define que "
"plantilla se utilizara para generar la página y que objetos están "
"disponibles para esa plantilla."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Herramientas para su navegador para acceder rápidamente a la funcionalidad "
"de administrador."

msgid "Please install docutils"
msgstr "Por favor, instale la biblioteca docutils"

#, python-format
msgid ""
"The admin documentation system requires Python's <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"El sistema de documentación de la administración requiere la biblioteca <a "
"href=\"%(link)s\">docutils</a> de Python."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Por favor, pregunte a su administrador como instalar la biblioteca <a href="
"\"%(link)s\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Modelo: %(name)s"

msgid "Fields"
msgstr "Campos"

msgid "Field"
msgstr "Campo"

msgid "Type"
msgstr "Tipo"

msgid "Description"
msgstr "Descripción"

msgid "Methods with arguments"
msgstr "Métodos con argumentos"

msgid "Method"
msgstr "Método"

msgid "Arguments"
msgstr "Argumentos"

msgid "Back to Model documentation"
msgstr "Volver a la documentación de Modelos"

msgid "Model documentation"
msgstr "Documentación del Modelo"

msgid "Model groups"
msgstr "Grupo de modelos"

msgid "Templates"
msgstr "Plantillas"

#, python-format
msgid "Template: %(name)s"
msgstr "Plantilla: %(name)s"

#, python-format
msgid "Template: \"%(name)s\""
msgstr "Plantilla: \"%(name)s\""

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template \"%(name)s\":"
msgstr "Buscar ruta para la plantilla \"%(name)s\":"

msgid "(does not exist)"
msgstr "(no existe)"

msgid "Back to Documentation"
msgstr "Volver a la documentación"

msgid "Template filters"
msgstr "Filtros de plantilla"

msgid "Template filter documentation"
msgstr "Documentación de los filtros de plantilla"

msgid "Built-in filters"
msgstr "Filtros incorporados"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Para utilizar estos filtros, incluya <code>%(code)s</code> en su plantilla "
"antes de usar el filtro."

msgid "Template tags"
msgstr "Etiquetas de plantilla"

msgid "Template tag documentation"
msgstr "Documentación de las etiquetas de plantilla"

msgid "Built-in tags"
msgstr "Etiquetas incorporadas"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Para utilizar estas etiquetas, incluya <code>%(code)s</code> en su plantilla "
"antes de utilizar la etiqueta."

#, python-format
msgid "View: %(name)s"
msgstr "Vista: %(name)s"

msgid "Context:"
msgstr "Contexto:"

msgid "Templates:"
msgstr "Plantillas:"

msgid "Back to View documentation"
msgstr "Volver a la documentación de Vistas"

msgid "View documentation"
msgstr "Documentación de la vista"

msgid "Jump to namespace"
msgstr "Ir al namespace"

msgid "Empty namespace"
msgstr "Namespace vacío"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Vistas por namespace %(name)s"

msgid "Views by empty namespace"
msgstr "Vistas por namespace vacío"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    Ver función: <code>%(full_name)s</code>. Nombre: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "etiqueta:"

msgid "filter:"
msgstr "filtro:"

msgid "view:"
msgstr "vista:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Aplicación %(app_label)r no encontrada"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Modelo %(model_name)r no encontrado en la aplicación %(app_label)r"

msgid "model:"
msgstr "modelo:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "el objeto relacionado '%(app_label)s.%(data_type)s'"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "objetos relacionados '%(app_label)s.%(object_name)s'"

#, python-format
msgid "all %s"
msgstr "todo %s"

#, python-format
msgid "number of %s"
msgstr "numero de %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s no parece ser un objeto urlpattern"
