# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2014-2015
# <AUTHOR> <EMAIL>, 2016,2019,2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-01-15 17:29+0000\n"
"Last-Translator: znotdead <<EMAIL>>\n"
"Language-Team: Belarusian (http://www.transifex.com/django/django/language/"
"be/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: be\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n"
"%100>=11 && n%100<=14)? 2 : 3);\n"

msgid "Administrative Documentation"
msgstr "Адміністратыўная дакументацыя"

msgid "Home"
msgstr "Пачатак"

msgid "Documentation"
msgstr "Дакумэнтацыя"

msgid "Bookmarklets"
msgstr "Закладкі"

msgid "Documentation bookmarklets"
msgstr "Закладкі дакумэнтацыі"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Каб паставіць закладку, перацягніце спасылку на паліцу закладак або "
"пстрыкніце па спасылцы праваю кнопкаю і дадайце яе да закладак. Цяпер можна "
"абраць закладку з якой пажадаеце бачыны."

msgid "Documentation for this page"
msgstr "Дакумэнтацыя па бачыне"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr "Накіроўвае з хоць-якое бачыны да прагляду, які стварае гэтую бачыну."

msgid "Tags"
msgstr "Цэтлікі"

msgid "List of all the template tags and their functions."
msgstr "Спіс усіх тэгаў шаблонаў і іх функцый."

msgid "Filters"
msgstr "Сіты"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Фільтры гэта дзеянні, якія могуць быць выкананы над пераменнымі ў шаблоне "
"каб змяніць вынік."

msgid "Models"
msgstr "Мадэлі"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Мадэлі - гэта апісанне ўсіх аб'ектаў у сістэме і іх асацыіраваныя палі. "
"Кожная мадэль мае спіс палёў, якія могуць быць даступны як пераменная шаблону"

msgid "Views"
msgstr "Прагляды"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Кожная бачына сайту генерыруеца праглядам. Прагляд вызначае які шаблон "
"выкарыстоўваць для генерацыі бачыны і якія аб'екты даступны для гэтага "
"шаблону."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Інструмент для вашага браўзэра для хуткага доступу да кіраўнічай "
"функцыянальнасці. "

msgid "Please install docutils"
msgstr "Калі ласка, усталюйце Docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Дакументацыя кіраўнічай сістэмы патрабуе бібліятэку Python <a href=\"%(link)s"
"\">docutils</a>."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Калі ласка, спытайцу вашых адміністратараў усталяваць <a href=\"%(link)s"
"\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Мадэль: %(name)s"

msgid "Fields"
msgstr "Палі"

msgid "Field"
msgstr "Поле"

msgid "Type"
msgstr "Тып"

msgid "Description"
msgstr "Апісаньне"

msgid "Methods with arguments"
msgstr "Метад з аргументамі"

msgid "Method"
msgstr "Метад"

msgid "Arguments"
msgstr "Аргументы"

msgid "Back to Model documentation"
msgstr "Вярнуцца да дакументацыі мадэлі"

msgid "Model documentation"
msgstr "Дакументацыя мадэлі"

msgid "Model groups"
msgstr "Групы мадэлі"

msgid "Templates"
msgstr "Шаблёны"

#, python-format
msgid "Template: %(name)s"
msgstr "Шаблон: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Шаблон: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Шлях пошуку для шаблону <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(не існуе)"

msgid "Back to Documentation"
msgstr "Вярнуцца да дакументацыі"

msgid "Template filters"
msgstr "Фільтры шаблону"

msgid "Template filter documentation"
msgstr "Дакументацыя фільтраў шаблону"

msgid "Built-in filters"
msgstr "Убудаваныя фільтры"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Каб карыстаць гэтыя фільтры, трэба памясціць  <code>%(code)s</code> у ваш "
"шаблон перад тым як карыстаць гэты фільтр."

msgid "Template tags"
msgstr "Тэгі шаблону"

msgid "Template tag documentation"
msgstr "Дакументацыя тэгаў шаблону"

msgid "Built-in tags"
msgstr "Убудаваныя тэгі"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Каб карыстаць гэтыя тэгі, трэба памясціць  <code>%(code)s</code> у ваш "
"шаблон перад тым як карыстаць гэты тэг."

#, python-format
msgid "View: %(name)s"
msgstr "Прагляд: %(name)s"

msgid "Context:"
msgstr "Кантэкст:"

msgid "Templates:"
msgstr "Шаблоны:"

msgid "Back to View documentation"
msgstr "Вярнуцца да дакументацыі прагляду"

msgid "View documentation"
msgstr "Паглядзець дакументацыю"

msgid "Jump to namespace"
msgstr "Перайсці да прасторы імёнаў"

msgid "Empty namespace"
msgstr "Ачысціць прастору імёнаў"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Прагляды ў просторы імёнаў %(name)s"

msgid "Views by empty namespace"
msgstr "Прагляды ў пустым просторы імёнаў"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"Функцыя прагляду: <code>%(full_name)s</code>. Імя: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "цэтлік:"

msgid "filter:"
msgstr "сіта:"

msgid "view:"
msgstr "прагляд:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Праграму %(app_label)r не знайшлі"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Мадэль %(model_name)r у праґраме «%(app_label)r» не знайшлі"

msgid "model:"
msgstr "мадэль:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "залежны аб’ект «%(app_label)s.%(data_type)s»"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "залежныя аб’екты «%(app_label)s.%(object_name)s»"

#, python-format
msgid "all %s"
msgstr "усе %s"

#, python-format
msgid "number of %s"
msgstr "колькасьць %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s не падобны да аб’екта «шаблён спасылкі» — «urlpattern»"
