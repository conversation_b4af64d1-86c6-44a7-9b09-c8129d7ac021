# This file is distributed under the same license as the Django package.
#
msgid ""
msgstr ""
"Project-Id-Version: Django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-17 02:13-0600\n"
"PO-Revision-Date: 2015-01-18 20:56+0100\n"
"Last-Translator: Django team\n"
"Language-Team: English <<EMAIL>>\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: contrib/postgres/apps.py:54
msgid "PostgreSQL extensions"
msgstr ""

#: contrib/postgres/fields/array.py:21 contrib/postgres/forms/array.py:17
#: contrib/postgres/forms/array.py:185
#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr ""

#: contrib/postgres/fields/array.py:22
msgid "Nested arrays must have the same length."
msgstr ""

#: contrib/postgres/fields/hstore.py:15
msgid "Map of strings to strings/nulls"
msgstr ""

#: contrib/postgres/fields/hstore.py:17
#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr ""

#: contrib/postgres/forms/hstore.py:17
msgid "Could not load JSON data."
msgstr ""

#: contrib/postgres/forms/hstore.py:18
msgid "Input must be a JSON dictionary."
msgstr ""

#: contrib/postgres/forms/ranges.py:42
msgid "Enter two valid values."
msgstr ""

#: contrib/postgres/forms/ranges.py:44
msgid "The start of the range must not exceed the end of the range."
msgstr ""

#: contrib/postgres/forms/ranges.py:99
msgid "Enter two whole numbers."
msgstr ""

#: contrib/postgres/forms/ranges.py:105
msgid "Enter two numbers."
msgstr ""

#: contrib/postgres/forms/ranges.py:111
msgid "Enter two valid date/times."
msgstr ""

#: contrib/postgres/forms/ranges.py:117
msgid "Enter two valid dates."
msgstr ""

#: contrib/postgres/validators.py:15
#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
msgstr[1] ""

#: contrib/postgres/validators.py:25
#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
msgstr[1] ""

#: contrib/postgres/validators.py:38
#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr ""

#: contrib/postgres/validators.py:39
#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr ""

#: contrib/postgres/validators.py:81
#, python-format
msgid ""
"Ensure that the upper bound of the range is not greater than %(limit_value)s."
msgstr ""

#: contrib/postgres/validators.py:90
#, python-format
msgid ""
"Ensure that the lower bound of the range is not less than %(limit_value)s."
msgstr ""
