# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON>, 2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-11 20:56+0200\n"
"PO-Revision-Date: 2021-11-16 12:53+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>\n"
"Language-Team: Malay (http://www.transifex.com/django/django/language/ms/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ms\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "PostgreSQL extensions"
msgstr "Sambungan PoestgreSQL"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr "item %(nth)s didalam tatasusunan tidak disahkan:"

msgid "Nested arrays must have the same length."
msgstr "Tatasusunan bersarang haruslah sama panjang."

msgid "Map of strings to strings/nulls"
msgstr "Suaian rentetan ke rentetan/nulls"

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr "Nilai \"%(key)s\" bukan rentetan atau adalah null."

msgid "Could not load JSON data."
msgstr "Tidak dapat memuatkan data JSON."

msgid "Input must be a JSON dictionary."
msgstr "Input mestilah dalam bentuk kamus JSON."

msgid "Enter two valid values."
msgstr "Masukkan dua nilai yang sah."

msgid "The start of the range must not exceed the end of the range."
msgstr "Permulaan julat tidak boleh melebihi akhir julat."

msgid "Enter two whole numbers."
msgstr "Masukkan dua nombor bulat."

msgid "Enter two numbers."
msgstr "Masukkan duan nombor."

msgid "Enter two valid date/times."
msgstr "Masukkan dua tarikh.masa yang sah."

msgid "Enter two valid dates."
msgstr "Masukkan dua tarikh yang sah."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
"Senarai mempunyai %(show_value)d perkara, tetapi sepatutnya mempunyai lebih "
"daripada %(limit_value)d."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
"Senarai mempunyai %(show_value)d perkara, tetapi ia sepatutnya mempunyai "
"tidak kurang daripaada %(limit_value)d."

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr "Sesetengah kunci hilang: %(keys)s"

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr "Sesetengah kunci yang diberikan tidak diketahui: %(keys)s"

#, python-format
msgid ""
"Ensure that this range is completely less than or equal to %(limit_value)s."
msgstr ""
"Pastikan julat ini adalah kurang daripada atau sama dengan %(limit_value)s."

#, python-format
msgid ""
"Ensure that this range is completely greater than or equal to "
"%(limit_value)s."
msgstr "Pastikan julat ini lebih daripada atau sama dengan %(limit_value)s."
