# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2018
# And<PERSON><PERSON>Szentkirályi, 2016
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# János R, 2017
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-11 20:56+0200\n"
"PO-Revision-Date: 2020-05-12 20:01+0000\n"
"Last-Translator: Transifex Bot <>\n"
"Language-Team: Hungarian (http://www.transifex.com/django/django/language/"
"hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "PostgreSQL extensions"
msgstr "PostgreSQL kiterjesztések"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr "A tömb %(nth)s-ik eleme érvénytelen:"

msgid "Nested arrays must have the same length."
msgstr "A belső tömböknek egyforma hosszúaknak kell lenniük."

msgid "Map of strings to strings/nulls"
msgstr "String-string/null leképezés"

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr "A(z) \"%(key)s\" nem karakterlánc, vagy null."

msgid "Could not load JSON data."
msgstr "JSON adat betöltése sikertelen."

msgid "Input must be a JSON dictionary."
msgstr "A bemenetnek JSON szótárnak kell lennie."

msgid "Enter two valid values."
msgstr "Adjon meg két érvényes értéket."

msgid "The start of the range must not exceed the end of the range."
msgstr "A tartomány eleje nem lehet nagyobb a tartomány végénél."

msgid "Enter two whole numbers."
msgstr "Adjon meg két egész számot."

msgid "Enter two numbers."
msgstr "Adjon meg két számot."

msgid "Enter two valid date/times."
msgstr "Adjon meg két érvényes dátumot/időt."

msgid "Enter two valid dates."
msgstr "Adjon meg két érvényes dátumot."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
"A lista %(show_value)d elemet tartalmaz, legfeljebb %(limit_value)d lehetne."
msgstr[1] ""
"A lista %(show_value)d elemet tartalmaz, legfeljebb %(limit_value)d lehetne."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
"A lista %(show_value)d elemet tartalmaz, legalább %(limit_value)d kellene."
msgstr[1] ""
"A lista %(show_value)d elemet tartalmaz, legalább %(limit_value)d kellene."

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr "Néhány kulcs hiányzik: %(keys)s"

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr "Néhány ismeretlen kulcs érkezett: %(keys)s"

#, python-format
msgid ""
"Ensure that this range is completely less than or equal to %(limit_value)s."
msgstr ""
"Bizonyosodjon meg róla, hogy a tartomány egésze kevesebb mint "
"%(limit_value)s."

#, python-format
msgid ""
"Ensure that this range is completely greater than or equal to "
"%(limit_value)s."
msgstr ""
"Bizonyosodjon meg róla, hogy a tartomány egésze nagyobb mint %(limit_value)s."
