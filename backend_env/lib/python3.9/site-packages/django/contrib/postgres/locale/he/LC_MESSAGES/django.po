# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2015,2017,2019
# <AUTHOR> <EMAIL>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-11 20:56+0200\n"
"PO-Revision-Date: 2020-05-12 20:01+0000\n"
"Last-Translator: Transifex Bot <>\n"
"Language-Team: Hebrew (http://www.transifex.com/django/django/language/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % "
"1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

msgid "PostgreSQL extensions"
msgstr "הרחבות PostgreSQL"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr "פריט %(nth)s במערך לא עבר בדיקת חוקיות: "

msgid "Nested arrays must have the same length."
msgstr "מערכים מקוננים חייבים להיות באותו האורך."

msgid "Map of strings to strings/nulls"
msgstr "מיפוי מחרוזות אל מחרוזות/nulls."

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr "הערך של \"%(key)s\" אינו מחרוזת או null."

msgid "Could not load JSON data."
msgstr "לא ניתן לטעון מידע JSON."

msgid "Input must be a JSON dictionary."
msgstr "הקלט חייב להיות מילון JSON."

msgid "Enter two valid values."
msgstr "נא להזין שני ערכים חוקיים."

msgid "The start of the range must not exceed the end of the range."
msgstr "התחלת טווח אינה יכולה גדולה יותר מסופו."

msgid "Enter two whole numbers."
msgstr "נא להזין שני מספרים שלמים."

msgid "Enter two numbers."
msgstr "נא להזין שני מספרים."

msgid "Enter two valid date/times."
msgstr "נא להזין שני תאריך/שעה חוקיים."

msgid "Enter two valid dates."
msgstr "נא להזין שני תאריכים חוקיים."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
"הרשימה מכילה פריט %(show_value)d, עליה להכיל לא יותר מ-%(limit_value)d."
msgstr[1] ""
"הרשימה מכילה %(show_value)d פריטים, עליה להכיל לא יותר מ-%(limit_value)d."
msgstr[2] ""
"הרשימה מכילה %(show_value)d פריטים, עליה להכיל לא יותר מ-%(limit_value)d."
msgstr[3] ""
"הרשימה מכילה %(show_value)d פריטים, עליה להכיל לא יותר מ-%(limit_value)d."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
"הרשימה מכילה פריט %(show_value)d, עליה להכיל לא פחות מ-%(limit_value)d."
msgstr[1] ""
"הרשימה מכילה %(show_value)d פריטים, עליה להכיל לא פחות מ-%(limit_value)d."
msgstr[2] ""
"הרשימה מכילה %(show_value)d פריטים, עליה להכיל לא פחות מ-%(limit_value)d."
msgstr[3] ""
"הרשימה מכילה %(show_value)d פריטים, עליה להכיל לא פחות מ-%(limit_value)d."

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr "חלק מהמפתחות חסרים: %(keys)s"

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr "סופקו מספר מפתחות לא ידועים: %(keys)s"

#, python-format
msgid ""
"Ensure that this range is completely less than or equal to %(limit_value)s."
msgstr "יש לוודא שטווח זה קטן מ או שווה ל-%(limit_value)s בשלמותו."

#, python-format
msgid ""
"Ensure that this range is completely greater than or equal to "
"%(limit_value)s."
msgstr "יש לוודא שטווח זה גדול מ או שווה ל-%(limit_value)s בשלמותו."
