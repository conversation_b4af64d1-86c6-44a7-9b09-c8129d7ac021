# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2016
# <PERSON>, 2017
# <PERSON><PERSON> Volochii <<EMAIL>>, 2021,2023
# <PERSON><PERSON> <<EMAIL>>, 2015-2016
# <AUTHOR> <EMAIL>, 2018
# <PERSON><PERSON><PERSON>, 2017
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-17 02:13-0600\n"
"PO-Revision-Date: 2023-04-19 09:22+0000\n"
"Last-Translator: <PERSON>lia Volochii <<EMAIL>>, 2021,2023\n"
"Language-Team: Ukrainian (http://www.transifex.com/django/django/language/"
"uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != "
"11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % "
"100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || "
"(n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

msgid "PostgreSQL extensions"
msgstr "Розширення PostgreSQL"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr "Елемент %(nth)s у масиві не коректний:"

msgid "Nested arrays must have the same length."
msgstr "Вкладени масиви повинні бути одинакової довжини."

msgid "Map of strings to strings/nulls"
msgstr "Асоціативний масив із рядків у рядки/обнулення"

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr "Значення “%(key)s” не є рядком чи null."

msgid "Could not load JSON data."
msgstr "Не вдалося завантажити JSON-дані."

msgid "Input must be a JSON dictionary."
msgstr "Значення повинне бути JSON-словником."

msgid "Enter two valid values."
msgstr "Введіть два корректних значення."

msgid "The start of the range must not exceed the end of the range."
msgstr "Початок діапазону не повинен перевищувати кінець діапазону."

msgid "Enter two whole numbers."
msgstr "Введіть два ціліх числа."

msgid "Enter two numbers."
msgstr "Введіть два числа."

msgid "Enter two valid date/times."
msgstr "Введіть дві коректні дати з часом."

msgid "Enter two valid dates."
msgstr "Введіть дві коректні дати."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
"Список містить %(show_value)d елемент, кількість яких не має перевищувати "
"%(limit_value)d."
msgstr[1] ""
"Список містить %(show_value)d елементи, кількість яких не має перевищувати "
"%(limit_value)d."
msgstr[2] ""
"Список містить %(show_value)d елементів, кількість яких не має перевищувати "
"%(limit_value)d."
msgstr[3] ""
"Список містить %(show_value)d елементів, кількість яких не має перевищувати "
"%(limit_value)d."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
"Список містить %(show_value)d елемент, кількість яких не має бути не менша "
"%(limit_value)d."
msgstr[1] ""
"Список містить %(show_value)d елементів, кількість яких не має бути не менша "
"%(limit_value)d."
msgstr[2] ""
"Список містить %(show_value)d елемента, кількість яких не має бути не менша "
"%(limit_value)d."
msgstr[3] ""
"Список містить %(show_value)d елемента, кількість яких не має бути не менша "
"%(limit_value)d."

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr "Не вистачає наступних ключів: %(keys)s"

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr "Були надані наступні невідомі ключі: %(keys)s"

#, python-format
msgid ""
"Ensure that the upper bound of the range is not greater than %(limit_value)s."
msgstr "Переконайтеся, що верхня межа діапазону не перевищує %(limit_value)s."

#, python-format
msgid ""
"Ensure that the lower bound of the range is not less than %(limit_value)s."
msgstr "Переконайтеся, що нижня межа діапазону не менша ніж %(limit_value)s."
