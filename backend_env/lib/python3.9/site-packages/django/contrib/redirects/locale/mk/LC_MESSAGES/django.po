# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <j<PERSON><PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON> <vvan<PERSON><PERSON><PERSON>@gmail.com>, 2016
# <PERSON><PERSON><PERSON> <vvangel<PERSON><EMAIL>>, 2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-10-09 17:42+0200\n"
"PO-Revision-Date: 2017-09-23 18:54+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <vvangel<PERSON><EMAIL>>\n"
"Language-Team: Macedonian (http://www.transifex.com/django/django/language/"
"mk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: mk\n"
"Plural-Forms: nplurals=2; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : 1;\n"

msgid "Redirects"
msgstr "Пренасочувања"

msgid "site"
msgstr "сајт"

msgid "redirect from"
msgstr "пренасочи од"

msgid ""
"This should be an absolute path, excluding the domain name. Example: '/"
"events/search/'."
msgstr ""
"Ова треба да биде апсолутна патека без името на домејнот. На пр. „/nastani/"
"prebaraj/“."

msgid "redirect to"
msgstr "пренасочи кон"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with "
"'http://'."
msgstr ""
"Ова може да биде или апсолутна патека (како погоре) или цела адреса што "
"почнува со „http://“."

msgid "redirect"
msgstr "пренасочување"

msgid "redirects"
msgstr "пренасочувања"
