# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2014-2015,2019
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-01-15 18:30+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Danish (http://www.transifex.com/django/django/language/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Redirects"
msgstr "Omdirigeringer"

msgid "site"
msgstr "webside"

msgid "redirect from"
msgstr "omdiriger fra"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""
"Dette skal være en absolut sti uden domænenavnet. Eksempel: “/nyheder/soeg/”"

msgid "redirect to"
msgstr "omdiriger til"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"Dette kan være enten en absolut sti (som ovenfor) eller en hel URL der "
"starter med en protokol, fx “https://”."

msgid "redirect"
msgstr "omdiriger"

msgid "redirects"
msgstr "omdirigeringer"
