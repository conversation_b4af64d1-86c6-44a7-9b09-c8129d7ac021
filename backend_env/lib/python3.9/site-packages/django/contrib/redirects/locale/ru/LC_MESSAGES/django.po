# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2011
# Panasoft, 2021
# <AUTHOR> <EMAIL>, 2014-2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-03-16 22:53+0000\n"
"Last-Translator: Panasoft\n"
"Language-Team: Russian (http://www.transifex.com/django/django/language/"
"ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n"
"%100>=11 && n%100<=14)? 2 : 3);\n"

msgid "Redirects"
msgstr "Перенаправления"

msgid "site"
msgstr "сайт"

msgid "redirect from"
msgstr "перенаправить с"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr "Нужно указать корневой относительный путь. Пример:  “/events/search/”."

msgid "redirect to"
msgstr "перенаправить на"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"Это может быть либо абсолютный путь (как указано выше), либо полный URL-"
"адрес, начинающийся со схемы, такой как «https://»."

msgid "redirect"
msgstr "перенаправление"

msgid "redirects"
msgstr "перенаправления"
