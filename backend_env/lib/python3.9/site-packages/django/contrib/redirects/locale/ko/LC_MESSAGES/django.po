# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON>, Ha <<EMAIL>>, 2016
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# minsung kang, 2015
# 정훈 이, 2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-12-24 18:32+0000\n"
"Last-Translator: 정훈 이\n"
"Language-Team: Korean (http://www.transifex.com/django/django/language/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Redirects"
msgstr "리다이렉트"

msgid "site"
msgstr "사이트"

msgid "redirect from"
msgstr "에서 리다이렉트"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""
"도메인 이름을 제외한 절대 경로여야 합니다.\n"
"예제: \"/events/search/\"."

msgid "redirect to"
msgstr "(으)로 리다이렉트"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"(위와 같은) 절대경로 혹은 \"https://\" 같은 식으로 시작하는 완전한 URL 모두 "
"가능합니다."

msgid "redirect"
msgstr "리다이렉트"

msgid "redirects"
msgstr "리다이렉트"
