# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-10-09 17:42+0200\n"
"PO-Revision-Date: 2019-03-03 10:36+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Malayalam (http://www.transifex.com/django/django/language/"
"ml/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ml\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Redirects"
msgstr "തിരിച്ചുവിടലുകൾ"

msgid "site"
msgstr "സൈറ്റ്"

msgid "redirect from"
msgstr "എവിടെ നിന്ന്"

msgid ""
"This should be an absolute path, excluding the domain name. Example: '/"
"events/search/'."
msgstr ""
"ഇത് ഡൊമൈനിന്റെ പേര് ഉൾപ്പെടാതെയുള്ള ഒരു അബ്സല്യൂട്ട് പാത്ത് ആയിരിക്കണം.  ഉദാഹരണം: '/events/"
"search/'."

msgid "redirect to"
msgstr "എങ്ങോട്ട്"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with "
"'http://'."
msgstr ""
"ഇതൊരു  അബ്സല്യൂട്ട് പാത്തോ (മുകളിലത്തേതു പോലെ)  'http://' എന്നു തുടങ്ങുന്ന ഒരു മുഴുവൻ യു.ആർ.എല്ലോ "
"ആവാം"

msgid "redirect"
msgstr "തിരിച്ചുവിടൽ"

msgid "redirects"
msgstr "തിരിച്ചുവിടലുകൾ"
