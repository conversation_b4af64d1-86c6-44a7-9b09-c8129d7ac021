# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-10-09 17:42+0200\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Kannada (http://www.transifex.com/django/django/language/"
"kn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: kn\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

msgid "Advanced options"
msgstr ""

msgid "Flat Pages"
msgstr ""

msgid "URL"
msgstr "URL"

msgid ""
"Example: '/about/contact/'. Make sure to have leading and trailing slashes."
msgstr ""
"ಉದಾ:'/about/contact/'. ಮೊದಲು ಮತ್ತು ಕೊನೆಯಲ್ಲಿ ಓರೆಗೆರೆ (/) ಇರುವಂತೆ ನೋಡಿಕೊಳ್ಳಿ."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""

msgid "URL is missing a leading slash."
msgstr ""

msgid "URL is missing a trailing slash."
msgstr ""

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr ""

msgid "title"
msgstr "ಶೀರ್ಷಿಕೆ"

msgid "content"
msgstr "ಒಳವಿಷಯ"

msgid "enable comments"
msgstr "ಟಿಪ್ಪಣಿಗಳನ್ನು ಸಕ್ರಿಯಗೊಳಿಸಿ"

msgid "template name"
msgstr "ಟೆಂಪ್ಲೇಟಿನ ಹೆಸರು"

msgid ""
"Example: 'flatpages/contact_page.html'. If this isn't provided, the system "
"will use 'flatpages/default.html'."
msgstr ""
"ಉದಾ:'flatpages/contact_page.html'. ಇದನ್ನು ಕೊಡದಿದ್ದರೆ ಗಣಕವ್ಯವಸ್ಥೆಯು  'flatpages/"
"default.html' ಅನ್ನು ಬಳಸುವದು."

msgid "registration required"
msgstr "ನೋಂದಾವಣೆ ಅಗತ್ಯವಿದೆ."

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"ಇದರಲ್ಲಿ ಗುರುತು  ಮಾಡಿದರೆ,  ಒಳಬಂದ (ಲಾಗಿನ್ ಆದ) ಬಳಕೆದಾರರು ಮಾತ್ರ ಪುಟವನ್ನು ನೋಡಬಹುದು."

msgid "sites"
msgstr ""

msgid "flat page"
msgstr "ಚಪ್ಪಟೆ ಪುಟ"

msgid "flat pages"
msgstr "ಚಪ್ಪಟೆ  ಪುಟಗಳು"
