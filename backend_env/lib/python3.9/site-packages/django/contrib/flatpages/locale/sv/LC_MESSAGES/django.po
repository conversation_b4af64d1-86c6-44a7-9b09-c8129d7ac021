# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2011-2012
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON>, 2014
# Pet<PERSON> Strand<PERSON> <<EMAIL>>, 2019
# <PERSON>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2022-07-24 19:03+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Swedish (http://www.transifex.com/django/django/language/"
"sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Avancerade inställningar"

msgid "Flat Pages"
msgstr "Statiska sidor"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Exempel: \"/om/kontakt/\". Se till att ha inledande och avslutande "
"snedstreck."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Detta värde får endast innehålla bokstäver, siffror, punkter, understreck, "
"bindestreck, snedstreck och tilde."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr "Exempel: \"/om/kontakt/\". Se till att ett inledande snedstreck."

msgid "URL is missing a leading slash."
msgstr "URL:en saknar ett inledande snedstreck."

msgid "URL is missing a trailing slash."
msgstr "URL:en saknar ett avslutande snedstreck."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "Statisk sida med %(url)s finns redan för webbplatsen %(site)s"

msgid "title"
msgstr "titel"

msgid "content"
msgstr "innehåll"

msgid "enable comments"
msgstr "aktivera kommentarer"

msgid "template name"
msgstr "mallnamn"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Exempel: 'sidor/kontaktsida.html'. Om detta inte fylls i kommer systemet att "
"använda 'flatpages/default.html'."

msgid "registration required"
msgstr "registrering krävs"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "Om detta bockas för kommer endast inloggade användare kunna se sidan."

msgid "sites"
msgstr "webbplatser"

msgid "flat page"
msgstr "statisk sida"

msgid "flat pages"
msgstr "statiska sidor"
