# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2012,2014
# Gil <PERSON>dor<PERSON> Via <<EMAIL>>, 2019
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-04-28 20:26+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Catalan (http://www.transifex.com/django/django/language/"
"ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Opcions avançades"

msgid "Flat Pages"
msgstr "Pàgines Estàtiques"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Exemple: '/about/contact/'. Assegureu-vos de posar les barres al principi i "
"al final."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Aquest valor sols pot contenir lletres, nombres, punts, subratllats, guions, "
"barres o accents."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr ""
"Exemple: '/about/contact/'. Assegureu-vos de posar la barra al principi."

msgid "URL is missing a leading slash."
msgstr "La URL no comença amb \"/\"."

msgid "URL is missing a trailing slash."
msgstr "La URL no acaba amb \"/\"."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "Ja hi ha una pàgina estàtica amb la URL %(url)s per al lloc %(site)s"

msgid "title"
msgstr "títol"

msgid "content"
msgstr "contingut"

msgid "enable comments"
msgstr "habilitar comentaris"

msgid "template name"
msgstr "nom de la plantilla"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Exemple: 'flatpages/contact_page.html'. Si no es proporciona, el sistema "
"utilitzarà 'flatpages/default.html'."

msgid "registration required"
msgstr "cal estar registrat"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "Si està marcat, només els usuaris registrats podran veure la pàgina."

msgid "sites"
msgstr "llocs"

msgid "flat page"
msgstr "pàgina estàtica"

msgid "flat pages"
msgstr "pàgines estàtiques"
