# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <o.ch<PERSON><PERSON><PERSON>@gmail.com>, 2014
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# Panasoft, 2016
# <PERSON> <<EMAIL>>, 2011-2012
# <AUTHOR> <EMAIL>, 2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2021-01-20 00:29+0000\n"
"Last-Translator: <PERSON><PERSON> Volochii <<EMAIL>>\n"
"Language-Team: Ukrainian (http://www.transifex.com/django/django/language/"
"uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != "
"11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % "
"100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || "
"(n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

msgid "Advanced options"
msgstr "Додаткові опції"

msgid "Flat Pages"
msgstr "Прості сторінки"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Наприклад, “/about/contact/”. Переконайтеся у наявності скісної риски на "
"початку та вкінці."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Це значення повинне містити тільки літери, цифри, крапки, підкреслення, "
"тире, косі риси чи тільди."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr ""
"Наприклад, “/about/contact/”. Переконайтеся у наявності скісної риски на "
"початку."

msgid "URL is missing a leading slash."
msgstr "На початку URL відсутня коса риса."

msgid "URL is missing a trailing slash."
msgstr "В кінці URL відсутня коса риса."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "Проста сторінка з адресою %(url)s вже існує для сайту %(site)s"

msgid "title"
msgstr "заголовок"

msgid "content"
msgstr "зміст"

msgid "enable comments"
msgstr "увімкнути коментарі"

msgid "template name"
msgstr "ім'я шаблона"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Наприклад, “/about/contact/”. Якщо не вказано, система використовуватиме "
"“flatpages/default.html”."

msgid "registration required"
msgstr "потрібна реєстрація"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"Якщо тут є галочка, тільки користувачі, що увійшли, зможуть переглядати цю "
"сторінку."

msgid "sites"
msgstr "сайти"

msgid "flat page"
msgstr "проста сторінка"

msgid "flat pages"
msgstr "прості сторінки"
