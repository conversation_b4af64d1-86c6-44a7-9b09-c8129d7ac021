# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-10-09 17:42+0200\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Khmer (http://www.transifex.com/django/django/language/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Advanced options"
msgstr ""

msgid "Flat Pages"
msgstr ""

msgid "URL"
msgstr "អាស័យដ្ឋានគេហទំព័រ(URL)"

msgid ""
"Example: '/about/contact/'. Make sure to have leading and trailing slashes."
msgstr ""
"ឧទាហរណ៍  '/about/contact/' ។ ត្រូវប្រាកដថាមានសញ្ញា / ទាំងនៅផ្នែកខាងមុខ និង ខាងក្រោយ។"

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""

msgid "URL is missing a leading slash."
msgstr ""

msgid "URL is missing a trailing slash."
msgstr ""

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr ""

msgid "title"
msgstr "ចំណងជើង"

msgid "content"
msgstr "អត្ថន័យ"

msgid "enable comments"
msgstr "អនុញ្ញាត"

msgid "template name"
msgstr "ឈ្មោះឯកសារគំរូ"

msgid ""
"Example: 'flatpages/contact_page.html'. If this isn't provided, the system "
"will use 'flatpages/default.html'."
msgstr ""
"ឧទាហរណ៍ 'flatpages/contact_page.html'។ ប្រសិនឯកសារនេះមិនមានទេ​"
" នោះឯកសារ 'flatpages/default.html'នឹងត្រូវប្រើ។"

msgid "registration required"
msgstr "ត្រូវការសមាជិកភាព"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "ប្រសិនជាចុចជ្រើសរើសយកជំរើសនេះ នោះ មានតែសមាជិកទេដែលអាចមើលទំព័រនេះបាន​។"

msgid "sites"
msgstr ""

msgid "flat page"
msgstr "ទំព័ថ្មី"

msgid "flat pages"
msgstr "ទំព័ថ្មី"
