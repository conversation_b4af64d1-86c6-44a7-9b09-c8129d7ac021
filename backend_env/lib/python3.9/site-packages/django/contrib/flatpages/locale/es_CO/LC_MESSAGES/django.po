# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2014-2015
# <PERSON><PERSON> <<EMAIL>>, 2011
# <AUTHOR> <EMAIL>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-10-09 17:42+0200\n"
"PO-Revision-Date: 2017-09-20 03:01+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Colombia) (http://www.transifex.com/django/django/"
"language/es_CO/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: es_CO\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Opciones avanzadas"

msgid "Flat Pages"
msgstr "Páginas estáticas"

msgid "URL"
msgstr "URL"

msgid ""
"Example: '/about/contact/'. Make sure to have leading and trailing slashes."
msgstr ""
"Ejemplo: '/about/contact/'. Asegúrese de que pone barras al principio y al "
"final."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Este valor solo puede contener letras, números, puntos, guiones bajos o "
"medios, barras o tildes."

msgid "URL is missing a leading slash."
msgstr "A la URL le falta la barra inicial."

msgid "URL is missing a trailing slash."
msgstr "A la URL le falta la barra final."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr " En el sitio %(site)s ya hay un Flatpage con la url %(url)s"

msgid "title"
msgstr "título"

msgid "content"
msgstr "contenido"

msgid "enable comments"
msgstr "habilitar comentarios"

msgid "template name"
msgstr "nombre de plantilla"

msgid ""
"Example: 'flatpages/contact_page.html'. If this isn't provided, the system "
"will use 'flatpages/default.html'."
msgstr ""
"Ejemplo: 'flatpages/contact_page.html'. Si no se proporciona uno, el sistema "
"usará 'flatpages/default.html'."

msgid "registration required"
msgstr "debe estar registrado"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "Si está marcado, sólo los usuarios registrados podrán ver la página."

msgid "sites"
msgstr "sitios"

msgid "flat page"
msgstr "página estática"

msgid "flat pages"
msgstr "páginas estáticas"
