# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# V<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2011-2012,2014
# V<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2015,2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-09-19 09:23+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Czech (http://www.transifex.com/django/django/language/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n "
"<= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

msgid "Advanced options"
msgstr "Pokročilá nastavení"

msgid "Flat Pages"
msgstr "Statické stránky"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Příklad: \"/about/contact/\". Ujistěte se, že máte počáteční a konečná "
"lomítka."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Tato hodnota musí obsahovat pouze písmena, číslice, tečky, podtržítka, "
"pomlčky, lomítka nebo vlnovky."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr "Příklad: \"/about/contact\". Úvodní lomítko je důležité."

msgid "URL is missing a leading slash."
msgstr "V adrese URL chybí úvodní lomítko."

msgid "URL is missing a trailing slash."
msgstr "V adrese URL chybí koncové lomítko."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "Flat stránka s adresou %(url)s pro web %(site)s již existuje."

msgid "title"
msgstr "titulek"

msgid "content"
msgstr "obsah"

msgid "enable comments"
msgstr "povolit komentáře"

msgid "template name"
msgstr "název šablony"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Příklad: \"flatpages/kontaktni_stranka.html\". Pokud toto není zadáno, bude "
"použita šablona \"flatpages/default.html\"."

msgid "registration required"
msgstr "nutná registrace"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "Určuje, že tuto stránku uvidí pouze přihlášení uživatelé."

msgid "sites"
msgstr "weby"

msgid "flat page"
msgstr "statická stránka"

msgid "flat pages"
msgstr "statické stránky"
