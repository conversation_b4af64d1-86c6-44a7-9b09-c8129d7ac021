# This file is distributed under the same license as the Django package.
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-03-18 09:16+0100\n"
"PO-Revision-Date: 2015-03-18 08:34+0000\n"
"Last-Translator: <PERSON><PERSON> <jann<PERSON>@leidel.info>\n"
"Language-Team: Western Frisian (http://www.transifex.com/projects/p/django/"
"language/fy/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: fy\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr ""

msgid "Flat Pages"
msgstr ""

msgid "URL"
msgstr ""

msgid ""
"Example: '/about/contact/'. Make sure to have leading and trailing slashes."
msgstr ""

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""

msgid "URL is missing a leading slash."
msgstr ""

msgid "URL is missing a trailing slash."
msgstr ""

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr ""

msgid "title"
msgstr ""

msgid "content"
msgstr ""

msgid "enable comments"
msgstr ""

msgid "template name"
msgstr ""

msgid ""
"Example: 'flatpages/contact_page.html'. If this isn't provided, the system "
"will use 'flatpages/default.html'."
msgstr ""

msgid "registration required"
msgstr ""

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""

msgid "flat page"
msgstr ""

msgid "flat pages"
msgstr ""
