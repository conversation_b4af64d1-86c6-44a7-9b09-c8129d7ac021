# This file is distributed under the same license as the Django package.
#
# Translators:
# angularcircle, 2012
# angularcircle, 2012
# <PERSON><PERSON> <j<PERSON><PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-11-11 14:08+0000\n"
"Last-Translator: m_aciek <<EMAIL>>\n"
"Language-Team: Polish (http://www.transifex.com/django/django/language/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n"
"%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n"
"%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

msgid "Advanced options"
msgstr "Opcje zaawansowane"

msgid "Flat Pages"
msgstr "Strony statyczne"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Przykład: „/about/contact/”. Upewnij się, że wpisałeś początkowy i końcowy "
"ukośnik."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"To pole może zawierać jedynie litery, cyfry, kropki, podkreślenia, myślniki, "
"ukośniki i tyldy."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr ""
"Przykład: „/about/contact”. Upewnij się, że wpisałeś początkowy ukośnik."

msgid "URL is missing a leading slash."
msgstr "W URL-u brakuje początkowego ukośnika."

msgid "URL is missing a trailing slash."
msgstr "W URL-u brakuje końcowego ukośnika."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr ""
"Strona statyczna o adresie %(url)s została już utworzona dla strony %(site)s"

msgid "title"
msgstr "tytuł"

msgid "content"
msgstr "zawartość"

msgid "enable comments"
msgstr "włącz komentarze"

msgid "template name"
msgstr "nazwa szablonu"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Przykład: „flatpages/contact_page.html”. Jeżeli nie zostanie podane, system "
"użyje „flatpages/default.html”."

msgid "registration required"
msgstr "wymagana rejestracja"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"Jeżeli zaznaczone - tylko zalogowani użytkownicy będą mogli zobaczyć stronę."

msgid "sites"
msgstr "strony"

msgid "flat page"
msgstr "strona statyczna"

msgid "flat pages"
msgstr "strony statyczne"
