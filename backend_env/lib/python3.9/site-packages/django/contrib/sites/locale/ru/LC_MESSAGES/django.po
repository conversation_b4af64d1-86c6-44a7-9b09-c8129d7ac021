# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2013
# <PERSON> <<EMAIL>>, 2016
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-23 18:54+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Russian (http://www.transifex.com/django/django/language/"
"ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n"
"%100>=11 && n%100<=14)? 2 : 3);\n"

msgid "Sites"
msgstr "Сайты"

msgid "The domain name cannot contain any spaces or tabs."
msgstr "Доменное имя не может содержать пробелы или табуляцию."

msgid "domain name"
msgstr "доменное имя"

msgid "display name"
msgstr "отображаемое имя"

msgid "site"
msgstr "сайт"

msgid "sites"
msgstr "сайты"
