# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2011
# Emin <PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2018-04-27 17:01+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Azerbaijani (http://www.transifex.com/django/django/language/"
"az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Sites"
msgstr "Saytlar"

msgid "The domain name cannot contain any spaces or tabs."
msgstr "Domen adında boşluq və tab bo<PERSON><PERSON><PERSON><PERSON>ı<PERSON>."

msgid "domain name"
msgstr "domen"

msgid "display name"
msgstr "adı"

msgid "site"
msgstr "sayt"

msgid "sites"
msgstr "saytlar"
