# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2016,2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-09-21 19:25+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Upper Sorbian (http://www.transifex.com/django/django/"
"language/hsb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: hsb\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n"
"%100==4 ? 2 : 3);\n"

msgid "Content Types"
msgstr "Wobsahowe typy"

msgid "python model class name"
msgstr "klasowe mjeno pythonoweho modela"

msgid "content type"
msgstr "wobsahowy typ"

msgid "content types"
msgstr "wobsahowe typy"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Objekt wobsahoweho typa %(ct_id)s nima zwjazany model"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Objekt %(obj_id)s wobsahoweho typa %(ct_id)s njeeksistuje"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "Objekty %(ct_name)s nimaja metodu get_absolute_url()"
