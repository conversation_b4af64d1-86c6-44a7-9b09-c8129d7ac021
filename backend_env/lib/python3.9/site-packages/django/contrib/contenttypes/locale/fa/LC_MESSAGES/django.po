# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2012
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-05-27 09:31+0000\n"
"Last-Translator: rahim agh <<EMAIL>>\n"
"Language-Team: Persian (http://www.transifex.com/django/django/language/"
"fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

msgid "Content Types"
msgstr "نوع‌های محتوا"

msgid "python model class name"
msgstr "نام پایتونی کلاس مدل"

msgid "content type"
msgstr "نوع محتوا"

msgid "content types"
msgstr "نوع‌های محتوا"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "نوع محتوای %(ct_id)s به هیچ مدلی مرتبط نشده است"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "شیء %(obj_id)s از نمونه محتوای %(ct_id)s وجود ندارد"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "شیء %(ct_name)s متد get_absolute_url() را ندارد"
