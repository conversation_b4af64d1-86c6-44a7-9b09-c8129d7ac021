# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2012
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Croatian (http://www.transifex.com/django/django/language/"
"hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

msgid "Content Types"
msgstr "Tipovi sadržaja"

msgid "python model class name"
msgstr "ime klase (class) python modela"

msgid "content type"
msgstr "tip sadržaja"

msgid "content types"
msgstr "tipovi sadržaja"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Tip sadržaja %(ct_id)s objekt nema pridruženi model"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn't exist"
msgstr "Tip sadržaja %(ct_id)s objekt  %(obj_id)s ne postoji"

#, python-format
msgid "%(ct_name)s objects don't have a get_absolute_url() method"
msgstr "%(ct_name)s objekti nemaju get_absolute_url() metodu"
