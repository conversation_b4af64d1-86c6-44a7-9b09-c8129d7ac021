# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <j<PERSON><PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2012,2014,2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-08-02 13:30+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Hebrew (http://www.transifex.com/django/django/language/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % "
"1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

msgid "Content Types"
msgstr "סוגי תוכן"

msgid "python model class name"
msgstr "שם ה־class של מודל פייתון"

msgid "content type"
msgstr "סוג תוכן"

msgid "content types"
msgstr "סוגי תוכן"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "לא משוייך מודל לאובייקט מסוג התוכן  %(ct_id)s"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "סוג תוכן %(ct_id)s אובייקט %(obj_id)s אינו קיים"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "אובייקטי %(ct_name)s אינם כוללים מתודת get_absolute_url()‎"
