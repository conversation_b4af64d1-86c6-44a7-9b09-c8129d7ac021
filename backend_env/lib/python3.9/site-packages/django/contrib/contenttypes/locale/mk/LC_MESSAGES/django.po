# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <j<PERSON><PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON> <vvan<PERSON><PERSON><PERSON>@gmail.com>, 2014
# <PERSON><PERSON><PERSON> <vvan<PERSON><PERSON><EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-23 18:54+0000\n"
"Last-Translator: dekomote <<EMAIL>>\n"
"Language-Team: Macedonian (http://www.transifex.com/django/django/language/"
"mk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: mk\n"
"Plural-Forms: nplurals=2; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : 1;\n"

msgid "Content Types"
msgstr "Типови содржини"

msgid "python model class name"
msgstr "име на класата за python моделoт"

msgid "content type"
msgstr "тип на содржина"

msgid "content types"
msgstr "типови содржини"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Типот на содржина %(ct_id)s објект нема асоциран модел"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn't exist"
msgstr "Типот на содржина %(ct_id)s објект %(obj_id)s не постои"

#, python-format
msgid "%(ct_name)s objects don't have a get_absolute_url() method"
msgstr "%(ct_name)s објекти немаат get_absolute_url() метод"
