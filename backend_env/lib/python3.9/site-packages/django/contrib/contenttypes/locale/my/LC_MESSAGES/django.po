# This file is distributed under the same license as the Django package.
#
# Translators:
# Yhal <PERSON>tet Aung <<EMAIL>>, 2013,2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: Yhal Htet Aung <<EMAIL>>\n"
"Language-Team: Burmese (http://www.transifex.com/django/django/language/"
"my/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: my\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Content Types"
msgstr "အကြောင်းအရာအမျိုးအစားများ"

msgid "python model class name"
msgstr "စပါးကြီးမော်ဒယ်အမျိုးအစားနာမည်"

msgid "content type"
msgstr "အကြောင်းအရာအမျိုးအစား"

msgid "content types"
msgstr "အကြောင်းအရာအမျိုးအစားများ"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "အကြောင်းအရာအမျိုးအစား %(ct_id)s အရာဝတ္ထုမှာဆက်နွယ်သောမော်ဒယ်မရှိ"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn't exist"
msgstr "အကြောင်းအရာအမျိုးအစား %(ct_id)s အရာဝတ္ထု %(obj_id)s မတည်ရှိနေ"

#, python-format
msgid "%(ct_name)s objects don't have a get_absolute_url() method"
msgstr "%(ct_name)s အရာဝတ္ထုများ get_absolute_url() နည်းလမ်းမရှိ"
