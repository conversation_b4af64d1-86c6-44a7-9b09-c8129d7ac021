# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2014
# <AUTHOR> <EMAIL>, 2022
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2022-04-24 19:22+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Esperanto (http://www.transifex.com/django/django/language/"
"eo/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: eo\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Enhavaj tipoj"

msgid "python model class name"
msgstr "klasa nomo de pitona modelo"

msgid "content type"
msgstr "enhava tipo"

msgid "content types"
msgstr "enhavaj tipoj"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Objekto kun enhava tipo %(ct_id)s ne havas modelojn asociitajn kun ĝi"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Objekto %(obj_id)s kun enhava tipo %(ct_id)s ne ekzistas"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "Objektoj %(ct_name)s ne havas metodon get_absolute_url()"
