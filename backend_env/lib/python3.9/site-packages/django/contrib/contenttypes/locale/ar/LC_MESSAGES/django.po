# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON>, 2014
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2014
# Mu<PERSON>z <PERSON>ed, 2020
# <AUTHOR> <EMAIL>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-04-06 19:57+0000\n"
"Last-Translator: صفا الفليج <<EMAIL>>\n"
"Language-Team: Arabic (http://www.transifex.com/django/django/language/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

msgid "Content Types"
msgstr "أنواع المحتوى"

msgid "python model class name"
msgstr "اسم صنف النموذج في بايثون"

msgid "content type"
msgstr "نوع المحتوى"

msgid "content types"
msgstr "أنواع المحتوى"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "ليس لكائن نوع المحتوى %(ct_id)s أيّ نموذج مرتبط"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "كائن نوع المحتوى %(ct_id)s بالمعرّف %(obj_id)s غير موجود"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "ليس لكائنات %(ct_name)s الدالة التابِعة get_absolute_url()‎"
