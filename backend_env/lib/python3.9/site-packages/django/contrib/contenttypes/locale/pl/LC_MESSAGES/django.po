# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2014
# angularcircle, 2012
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-11-11 14:09+0000\n"
"Last-Translator: m_aciek <<EMAIL>>\n"
"Language-Team: Polish (http://www.transifex.com/django/django/language/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n"
"%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n"
"%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

msgid "Content Types"
msgstr "Typy Zawartości"

msgid "python model class name"
msgstr "Nazwa klasy modelu pythona"

msgid "content type"
msgstr "typ zawartości"

msgid "content types"
msgstr "typy zawartości"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Obiekt typu zawartości %(ct_id)s nie posiada przypisanego modelu"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Obiekt %(obj_id)s typu zawartości %(ct_id)s nie istnieje"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "Obiekty %(ct_name)s nie posiadają metody get_absolute_url()"
