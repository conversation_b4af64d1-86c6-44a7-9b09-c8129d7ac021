{% extends "admin/import_export/base.html" %}
{% load i18n %}
{% load admin_urls %}
{% load import_export_tags %}
{% load static %}

{% block extrastyle %}{{ block.super }}<link rel="stylesheet" type="text/css" href="{% static "import_export/import.css" %}" />{% endblock %}

{% block extrahead %}{{ block.super }}
  <script type="text/javascript" src="{% url 'admin:jsi18n' %}"></script>
  {% if confirm_form %}
    {{ confirm_form.media }}
  {% else %}
    {{ form.media }}
  {% endif %}
{% endblock %}

{% block breadcrumbs_last %}
{% trans "Import" %}
{% endblock %}

{% block content %}

  {% if confirm_form %}
    {% block confirm_import_form %}
    <form action="{% url opts|admin_urlname:"process_import" %}" method="POST">
      {% csrf_token %}
      {{ confirm_form.as_p }}
      <p>
        {% trans "Below is a preview of data to be imported. If you are satisfied with the results, click 'Confirm import'" %}
      </p>
      <div class="submit-row">
        <input type="submit" class="default" name="confirm" value="{% trans "Confirm import" %}">
      </div>
    </form>
    {% endblock %}
  {% else %}
    {% block import_form %}
    <form action="" method="post" enctype="multipart/form-data">
      {% csrf_token %}

      {% block fields_help %}
        <p>
          {% trans "This importer will import the following fields: " %}
          {% if fields_list|length <= 1 %}
             <code>{{ fields_list.0.1|join:", " }}</code>
          {% else %}
             <dl>
                {% for resource, fields in fields_list %}
                    <dt>{{ resource }}</dt>
                    <dd><code>{{ fields|join:", " }}</code></dd>
                {% endfor %}
             </dl>
          {% endif %}
        </p>
      {% endblock %}

      {% block form_detail %}
          <fieldset class="module aligned">
          {% for field in form %}
            <div class="form-row">
              {{ field.errors }}

              {{ field.label_tag }}

              {{ field }}

              {% if field.field.help_text %}
              <p class="help">{{ field.field.help_text|safe }}</p>
              {% endif %}
            </div>
          {% endfor %}
        </fieldset>
      {% endblock %}

      {% block form_submit_button %}
        <div class="submit-row">
          <input type="submit" class="default" value="{% trans "Submit" %}">
        </div>
      {% endblock %}
    </form>
    {% endblock %}
  {% endif %}

  {% if result %}

    {% if result.has_errors %}
    {% block errors %}
      <h2>{% trans "Errors" %}</h2>
      <ul>
        {% for error in result.base_errors  %}
        <li>
          {{ error.error }}
          <div class="traceback">{{ error.traceback|linebreaks }}</div>
        </li>
        {% endfor %}
        {% for line, errors in result.row_errors %}
          {% for error in errors %}
            <li>
              {% trans "Line number" %}: {{ line }} - {{ error.error }}
              <div><code>{{ error.row.values|join:", " }}</code></div>
              <div class="traceback">{{ error.traceback|linebreaks }}</div>
            </li>
          {% endfor %}
        {% endfor %}
      </ul>
    {% endblock %}

    {% elif result.has_validation_errors %}

    {% block validation_errors %}
      <h2>{% trans "Some rows failed to validate" %}</h2>

      <p>{% trans "Please correct these errors in your data where possible, then reupload it using the form above." %}</p>

      <table class="import-preview">
        <thead>
          <tr>
            <th>{% trans "Row" %}</th>
            <th>{% trans "Errors" %}</th>
            {% for field in result.diff_headers %}
              <th>{{ field }}</th>
            {% endfor %}
          </tr>
        </thead>
        <tbody>
        {% for row in result.invalid_rows %}
          <tr>
            <td>{{ row.number }} </td>
            <td class="errors">
              <span class="validation-error-count">{{ row.error_count }}</span>
              <div class="validation-error-container">
                <ul class="validation-error-list">
                  {% for field_name, error_list in row.field_specific_errors.items %}
                    <li>
                        <span class="validation-error-field-label">{{ field_name }}</span>
                        <ul>
                          {% for error in error_list %}
                            <li>{{ error }}</li>
                          {% endfor %}
                        </ul>
                    </li>
                  {% endfor %}
                  {% if row.non_field_specific_errors %}
                    <li>
                      <span class="validation-error-field-label">{% trans "Non field specific" %}</span>
                      <ul>
                        {% for error in row.non_field_specific_errors %}
                          <li>{{ error }}</li>
                        {% endfor %}
                      </ul>
                    </li>
                  {% endif %}
                </ul>
              </div>
            </td>
            {% for field in row.values %}
              <td>{{ field }}</td>
            {% endfor %}
          </tr>
        {% endfor %}
        </tbody>
      </table>
      {% endblock %}

    {% else %}

      {% block preview %}
      <h2>{% trans "Preview" %}</h2>

      <table class="import-preview">
        <thead>
          <tr>
            <th></th>
            {% for field in result.diff_headers %}
              <th>{{ field }}</th>
            {% endfor %}
          </tr>
        </thead>
        {% for row in result.valid_rows %}
          <tr class="{{ row.import_type }}">
            <td class="import-type">
              {% if row.import_type == 'new' %}
                {% trans "New" %}
              {% elif row.import_type == 'skip' %}
                {% trans "Skipped" %}
              {% elif row.import_type == 'delete' %}
                {% trans "Delete" %}
              {% elif row.import_type == 'update' %}
                {% trans "Update" %}
              {% endif %}
            </td>
            {% for field in row.diff %}
              <td>{{ field }}</td>
            {% endfor %}
          </tr>
        {% endfor %}
      </table>
      {% endblock %}

    {% endif %}

  {% endif %}
{% endblock %}
