{% extends "admin/import_export/base.html" %}
{% load i18n %}
{% load admin_urls %}
{% load import_export_tags %}

{% block extrahead %}{{ block.super }}
<script type="text/javascript" src="{% url 'admin:jsi18n' %}"></script>
{{ form.media }}
{% endblock %}

{% block breadcrumbs_last %}
{% trans "Export" %}
{% endblock %}

{% block content %}
<form action="" method="POST">
  {% csrf_token %}

    <fieldset class="module aligned">
      {% for field in form %}
        <div class="form-row">
          {{ field.errors }}

          {{ field.label_tag }}

          {{ field }}

          {% if field.field.help_text %}
          <p class="help">{{ field.field.help_text|safe }}</p>
          {% endif %}
        </div>
      {% endfor %}
    </fieldset>

    <div class="submit-row">
      <input type="submit" class="default" value="{% trans "Submit" %}">
    </div>
</form>
{% endblock %}
