# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-12 15:15+0100\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: hao wang <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: admin.py:241
#, python-format
msgid "%s through import_export"
msgstr "%s 通过 django-import-export导入"

#: admin.py:248
msgid "Import finished, with {} new and {} updated {}."
msgstr "导入成功，新增{}条记录，更新{}条记录。"

#: admin.py:496
#, python-format
msgid ""
"%(exc_name)s encountered while trying to read file. Ensure you have chosen "
"the correct format for the file."
msgstr ""

#: admin.py:650 templates/admin/import_export/change_list_import_item.html:5
#: templates/admin/import_export/import.html:19
msgid "Import"
msgstr "导入"

#: admin.py:836 templates/admin/import_export/change_list_export_item.html:5
#: templates/admin/import_export/export.html:12
msgid "Export"
msgstr "导出"

#: admin.py:901
msgid "You must select an export format."
msgstr "您必须选择一个导出格式。"

#: admin.py:926
#, python-format
msgid "Export selected %(verbose_name_plural)s"
msgstr "导出选中的 %(verbose_name_plural)s"

#: forms.py:12
msgid "Resource"
msgstr ""

#: forms.py:40
msgid "File to import"
msgstr "导入文件"

#: forms.py:42 forms.py:83 forms.py:116
msgid "Format"
msgstr "格式"

#: templates/admin/import_export/base.html:11
msgid "Home"
msgstr ""

#: templates/admin/import_export/export.html:36
#: templates/admin/import_export/import.html:78
msgid "Submit"
msgstr "提交"

#: templates/admin/import_export/import.html:30
msgid ""
"Below is a preview of data to be imported. If you are satisfied with the "
"results, click 'Confirm import'"
msgstr "以下是导入数据的预览。如果确认结果没有问题，可以点击 “确认导入”"

#: templates/admin/import_export/import.html:33
msgid "Confirm import"
msgstr "确认导入"

#: templates/admin/import_export/import.html:44
msgid "This importer will import the following fields: "
msgstr "此次将导入以下字段："

#: templates/admin/import_export/import.html:89
#: templates/admin/import_export/import.html:120
msgid "Errors"
msgstr "错误"

#: templates/admin/import_export/import.html:100
msgid "Line number"
msgstr "行号"

#: templates/admin/import_export/import.html:112
msgid "Some rows failed to validate"
msgstr "某些行验数据证失败"

#: templates/admin/import_export/import.html:114
msgid ""
"Please correct these errors in your data where possible, then reupload it "
"using the form above."
msgstr "请使用上面的表单，纠正这些提示有错误的数据，并重新上传"

#: templates/admin/import_export/import.html:119
msgid "Row"
msgstr "行"

#: templates/admin/import_export/import.html:146
msgid "Non field specific"
msgstr "没有指定的字段"

#: templates/admin/import_export/import.html:169
msgid "Preview"
msgstr "预览"

#: templates/admin/import_export/import.html:184
msgid "New"
msgstr "新增"

#: templates/admin/import_export/import.html:186
msgid "Skipped"
msgstr "忽略"

#: templates/admin/import_export/import.html:188
msgid "Delete"
msgstr "删除"

#: templates/admin/import_export/import.html:190
msgid "Update"
msgstr "更新"
