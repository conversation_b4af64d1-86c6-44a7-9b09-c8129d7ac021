Metadata-Version: 2.3
Name: pyphen
Version: 0.17.2
Summary: Pure Python module to hyphenate text
Keywords: hyphenation
Author-email: <PERSON> <<EMAIL>>
Maintainer-email: CourtBouillon <<EMAIL>>
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: GNU General Public License v2 or later (GPLv2+)
Classifier: License :: OSI Approved :: GNU Lesser General Public License v2 or later (LGPLv2+)
Classifier: License :: OSI Approved :: Mozilla Public License 1.1 (MPL 1.1)
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Text Processing
Classifier: Topic :: Text Processing :: Linguistic
Requires-Dist: sphinx ; extra == "doc"
Requires-Dist: sphinx_rtd_theme ; extra == "doc"
Requires-Dist: pytest ; extra == "test"
Requires-Dist: ruff ; extra == "test"
Project-URL: Changelog, https://github.com/Kozea/Pyphen/releases
Project-URL: Code, https://github.com/Kozea/Pyphen
Project-URL: Documentation, https://pyphen.org/
Project-URL: Donation, https://opencollective.com/courtbouillon
Project-URL: Homepage, https://www.courtbouillon.org/pyphen
Project-URL: Issues, https://github.com/Kozea/Pyphen/issues
Provides-Extra: doc
Provides-Extra: test

Pyphen is a pure Python module to hyphenate text using existing Hunspell
hyphenation dictionaries.

This module is a fork of python-hyphenator, written by Wilbert Berendsen.

Many dictionaries are included in Pyphen, they come from the LibreOffice git
repository and are distributed under GPL, LGPL and/or MPL. Dictionaries are not
modified in this repository. See the dictionaries and LibreOffice's repository
for more details.

https://git.libreoffice.org/dictionaries

* Free software: GPL 2.0+ or LGPL 2.1+ or MPL 1.1 for the code
* For Python 3.9+, tested on CPython and PyPy
* Documentation: https://doc.courtbouillon.org/pyphen
* Changelog: https://github.com/Kozea/pyphen/releases
* Code, issues, tests: https://github.com/Kozea/pyphen
* Code of conduct: https://www.courtbouillon.org/code-of-conduct
* Professional support: https://www.courtbouillon.org
* Donation: https://opencollective.com/courtbouillon

Pyphen has been created and developed by Kozea (https://kozea.fr).
Professional support, maintenance and community management is provided by
CourtBouillon (https://www.courtbouillon.org).

Copyrights are retained by their contributors, no copyright assignment is
required to contribute to Pyphen. Unless explicitly stated otherwise, any
contribution intentionally submitted for inclusion is licensed under
GPL 2.0+/LGPL 2.1+/MPL 1.1, without any additional terms or conditions. For
full authorship information, see the version control history.

