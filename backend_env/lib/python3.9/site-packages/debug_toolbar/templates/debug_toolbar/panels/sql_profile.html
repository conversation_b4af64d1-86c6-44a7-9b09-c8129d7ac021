{% load i18n %}
<div class="djDebugPanelTitle">
  <button type="button" class="djDebugClose">»</button>
  <h3>{% trans "SQL profiled" %}</h3>
</div>
<div class="djDebugPanelContent">
  <div class="djdt-scroll">
    {% if result %}
      <dl>
        <dt>{% trans "Executed SQL" %}</dt>
        <dd>{{ sql|safe }}</dd>
        <dt>{% trans "Time" %}</dt>
        <dd>{{ duration }} ms</dd>
        <dt>{% trans "Database" %}</dt>
        <dd>{{ alias }}</dd>
      </dl>
      <table>
        <thead>
          <tr>
            {% for h in headers %}
              <th>{{ h|upper }}</th>
            {% endfor %}
          </tr>
        </thead>
        <tbody>
          {% for row in result %}
            <tr>
              {% for column in row %}
                <td>{{ column|escape }}</td>
              {% endfor %}
            </tr>
          {% endfor %}
        </tbody>
      </table>
    {% else %}
      <dl>
        <dt>{% trans "Error" %}</dt>
        <dd>{{ result_error }}</dd>
      </dl>
    {% endif %}
  </div>
</div>
