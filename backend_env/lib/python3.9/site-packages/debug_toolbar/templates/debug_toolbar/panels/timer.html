{% load i18n %}
<h4>{% trans "Resource usage" %}</h4>
<table>
  <colgroup>
    <col class="djdt-width-20">
    <col>
  </colgroup>
  <thead>
    <tr>
      <th>{% trans "Resource" %}</th>
      <th>{% trans "Value" %}</th>
    </tr>
  </thead>
  <tbody>
    {% for key, value in rows %}
      <tr>
        <td>{{ key|escape }}</td>
        <td>{{ value|escape }}</td>
      </tr>
    {% endfor %}
  </tbody>
</table>

<!-- This hidden div is populated and displayed by code in toolbar.timer.js -->
<div id="djDebugBrowserTiming" class="djdt-hidden">
  <h4>{% trans "Browser timing" %}</h4>
  <table>
    <colgroup>
      <col class="djdt-width-20">
      <col class="djdt-width-60">
      <col class="djdt-width-20">
    </colgroup>
    <thead>
      <tr>
        <th>{% trans "Timing attribute" %}</th>
        <th>{% trans "Timeline" %}</th>
        <th>{% trans "Milliseconds since navigation start (+length)" %}</th>
      </tr>
    </thead>
    <tbody id="djDebugBrowserTimingTableBody">
    </tbody>
  </table>
</div>
