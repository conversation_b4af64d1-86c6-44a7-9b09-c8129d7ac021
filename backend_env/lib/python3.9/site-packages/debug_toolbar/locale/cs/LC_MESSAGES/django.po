# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2013
msgid ""
msgstr ""
"Project-Id-Version: Django Debug Toolbar\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-20 17:23+0100\n"
"PO-Revision-Date: 2014-04-25 19:53+0000\n"
"Last-Translator: Aymeric Augustin <<EMAIL>>\n"
"Language-Team: Czech (http://www.transifex.com/projects/p/django-debug-"
"toolbar/language/cs/)\n"
"Language: cs\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1) ? 0 : (n>=2 && n<=4) ? 1 : 2;\n"

#: apps.py:15
msgid "Debug Toolbar"
msgstr ""

#: panels/cache.py:180
msgid "Cache"
msgstr "Mezipaměť"

#: panels/cache.py:186
#, python-format
msgid "%(cache_calls)d call in %(time).2fms"
msgid_plural "%(cache_calls)d calls in %(time).2fms"
msgstr[0] "%(cache_calls)d volání během %(time).2fms"
msgstr[1] "%(cache_calls)d volání během %(time).2fms"
msgstr[2] "%(cache_calls)d volání během %(time).2fms"

#: panels/cache.py:195
#, python-format
msgid "Cache calls from %(count)d backend"
msgid_plural "Cache calls from %(count)d backends"
msgstr[0] "Volání mezipaměti z %(count)d backendu"
msgstr[1] "Volání mezipaměti z %(count)d backendů"
msgstr[2] "Volání mezipaměti z %(count)d backendů"

#: panels/headers.py:31
msgid "Headers"
msgstr "Záhlaví"

#: panels/history/panel.py:18 panels/history/panel.py:19
msgid "History"
msgstr ""

#: panels/profiling.py:140
msgid "Profiling"
msgstr "Profilování"

#: panels/redirects.py:14
msgid "Intercept redirects"
msgstr "Zachycení přesměrování"

#: panels/request.py:16
msgid "Request"
msgstr "Požadavek"

#: panels/request.py:36
msgid "<no view>"
msgstr "<žádný pohled>"

#: panels/request.py:53
msgid "<unavailable>"
msgstr "<není k dispozici>"

#: panels/settings.py:17
msgid "Settings"
msgstr "Settings"

#: panels/settings.py:20
#, fuzzy, python-format
#| msgid "Settings from <code>%s</code>"
msgid "Settings from %s"
msgstr "Nastavení z modulu <code>%s</code>"

#: panels/signals.py:57
#, python-format
msgid "%(num_receivers)d receiver of 1 signal"
msgid_plural "%(num_receivers)d receivers of 1 signal"
msgstr[0] "%(num_receivers)d příjemce 1 signálu"
msgstr[1] "%(num_receivers)d příjemci 1 signálu"
msgstr[2] "%(num_receivers)d příjemců 1 signálu"

#: panels/signals.py:62
#, python-format
msgid "%(num_receivers)d receiver of %(num_signals)d signals"
msgid_plural "%(num_receivers)d receivers of %(num_signals)d signals"
msgstr[0] "%(num_receivers)d příjemce %(num_signals)d signálů"
msgstr[1] "%(num_receivers)d příjemci %(num_signals)d signálů"
msgstr[2] "%(num_receivers)d příjemců %(num_signals)d signálů"

#: panels/signals.py:67
msgid "Signals"
msgstr "Signály"

#: panels/sql/panel.py:23
msgid "Autocommit"
msgstr "Autocommit"

#: panels/sql/panel.py:24
msgid "Read uncommitted"
msgstr "Read uncommitted"

#: panels/sql/panel.py:25
msgid "Read committed"
msgstr "Read committed"

#: panels/sql/panel.py:26
msgid "Repeatable read"
msgstr "Repeatable read"

#: panels/sql/panel.py:27
msgid "Serializable"
msgstr "Serializable"

#: panels/sql/panel.py:39
msgid "Idle"
msgstr "V klidu (idle)"

#: panels/sql/panel.py:40
msgid "Active"
msgstr "Aktivní"

#: panels/sql/panel.py:41
msgid "In transaction"
msgstr "Uvnitř transakce"

#: panels/sql/panel.py:42
msgid "In error"
msgstr "V chybovém stavu"

#: panels/sql/panel.py:43
msgid "Unknown"
msgstr "Neznámé"

#: panels/sql/panel.py:130
msgid "SQL"
msgstr "SQL"

#: panels/sql/panel.py:135
#, fuzzy, python-format
#| msgid "%(cache_calls)d call in %(time).2fms"
#| msgid_plural "%(cache_calls)d calls in %(time).2fms"
msgid "%(query_count)d query in %(sql_time).2fms"
msgid_plural "%(query_count)d queries in %(sql_time).2fms"
msgstr[0] "%(cache_calls)d volání během %(time).2fms"
msgstr[1] "%(cache_calls)d volání během %(time).2fms"
msgstr[2] "%(cache_calls)d volání během %(time).2fms"

#: panels/sql/panel.py:147
#, python-format
msgid "SQL queries from %(count)d connection"
msgid_plural "SQL queries from %(count)d connections"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: panels/staticfiles.py:84
#, python-format
msgid "Static files (%(num_found)s found, %(num_used)s used)"
msgstr "Statické soubory (nalezeno: %(num_found)s, použito: %(num_used)s)"

#: panels/staticfiles.py:105
msgid "Static files"
msgstr "Statické soubory"

#: panels/staticfiles.py:111
#, python-format
msgid "%(num_used)s file used"
msgid_plural "%(num_used)s files used"
msgstr[0] "%(num_used)s soubor použit"
msgstr[1] "%(num_used)s soubory použity"
msgstr[2] "%(num_used)s souborů použito"

#: panels/templates/panel.py:143
msgid "Templates"
msgstr "Šablony"

#: panels/templates/panel.py:148
#, python-format
msgid "Templates (%(num_templates)s rendered)"
msgstr "Šablony (renderovaných: %(num_templates)s)"

#: panels/templates/panel.py:180
msgid "No origin"
msgstr ""

#: panels/timer.py:25
#, python-format
msgid "CPU: %(cum)0.2fms (%(total)0.2fms)"
msgstr "CPU: %(cum)0.2fms (%(total)0.2fms)"

#: panels/timer.py:30
#, python-format
msgid "Total: %0.2fms"
msgstr "Celkem: %0.2fms"

#: panels/timer.py:36 templates/debug_toolbar/panels/history.html:9
#: templates/debug_toolbar/panels/sql_explain.html:11
#: templates/debug_toolbar/panels/sql_profile.html:12
#: templates/debug_toolbar/panels/sql_select.html:11
msgid "Time"
msgstr "Čas"

#: panels/timer.py:44
msgid "User CPU time"
msgstr "Uživatelský čas CPU"

#: panels/timer.py:44
#, python-format
msgid "%(utime)0.3f msec"
msgstr "%(utime)0.3f msec"

#: panels/timer.py:45
msgid "System CPU time"
msgstr "Systémový čas CPU"

#: panels/timer.py:45
#, python-format
msgid "%(stime)0.3f msec"
msgstr "%(stime)0.3f msec"

#: panels/timer.py:46
msgid "Total CPU time"
msgstr "Celkový čas CPU"

#: panels/timer.py:46
#, python-format
msgid "%(total)0.3f msec"
msgstr "%(total)0.3f msec"

#: panels/timer.py:47
msgid "Elapsed time"
msgstr "Uplynulý čas"

#: panels/timer.py:47
#, python-format
msgid "%(total_time)0.3f msec"
msgstr "%(total_time)0.3f msec"

#: panels/timer.py:49
msgid "Context switches"
msgstr "Přepnutí kontextu"

#: panels/timer.py:50
#, python-format
msgid "%(vcsw)d voluntary, %(ivcsw)d involuntary"
msgstr "%(vcsw)d dobrovolně, %(ivcsw)d nedobrovolně"

#: panels/versions.py:19
msgid "Versions"
msgstr "Verze"

#: templates/debug_toolbar/base.html:22
msgid "Hide toolbar"
msgstr "Skrýt lištu"

#: templates/debug_toolbar/base.html:22
msgid "Hide"
msgstr "Skrýt"

#: templates/debug_toolbar/base.html:29
msgid "Show toolbar"
msgstr "Zobrazit lištu"

#: templates/debug_toolbar/includes/panel_button.html:4
msgid "Disable for next and successive requests"
msgstr "Vypnout pro následné požadavky"

#: templates/debug_toolbar/includes/panel_button.html:4
msgid "Enable for next and successive requests"
msgstr "Zapnout pro následné požadavky"

#: templates/debug_toolbar/panels/cache.html:2
msgid "Summary"
msgstr "Souhrn"

#: templates/debug_toolbar/panels/cache.html:6
msgid "Total calls"
msgstr "Celkem volání"

#: templates/debug_toolbar/panels/cache.html:7
msgid "Total time"
msgstr "Celkový čas"

#: templates/debug_toolbar/panels/cache.html:8
msgid "Cache hits"
msgstr "Nalezení v mezipaměti"

#: templates/debug_toolbar/panels/cache.html:9
msgid "Cache misses"
msgstr "Nebylo v mezipaměti"

#: templates/debug_toolbar/panels/cache.html:21
msgid "Commands"
msgstr "Příkazy"

#: templates/debug_toolbar/panels/cache.html:39
msgid "Calls"
msgstr "Volání"

#: templates/debug_toolbar/panels/cache.html:43
#: templates/debug_toolbar/panels/sql.html:36
msgid "Time (ms)"
msgstr "Čas (ms)"

#: templates/debug_toolbar/panels/cache.html:44
msgid "Type"
msgstr "Typ"

#: templates/debug_toolbar/panels/cache.html:45
#: templates/debug_toolbar/panels/request.html:8
msgid "Arguments"
msgstr "Argumenty"

#: templates/debug_toolbar/panels/cache.html:46
#: templates/debug_toolbar/panels/request.html:9
msgid "Keyword arguments"
msgstr "Klíčované argumenty"

#: templates/debug_toolbar/panels/cache.html:47
msgid "Backend"
msgstr "Backend"

#: templates/debug_toolbar/panels/headers.html:3
msgid "Request headers"
msgstr "Záhlaví požadavku"

#: templates/debug_toolbar/panels/headers.html:8
#: templates/debug_toolbar/panels/headers.html:27
#: templates/debug_toolbar/panels/headers.html:48
msgid "Key"
msgstr "Klíč"

#: templates/debug_toolbar/panels/headers.html:9
#: templates/debug_toolbar/panels/headers.html:28
#: templates/debug_toolbar/panels/headers.html:49
#: templates/debug_toolbar/panels/history_tr.html:23
#: templates/debug_toolbar/panels/request_variables.html:12
#: templates/debug_toolbar/panels/settings.html:6
#: templates/debug_toolbar/panels/timer.html:11
msgid "Value"
msgstr "Hodnota"

#: templates/debug_toolbar/panels/headers.html:22
msgid "Response headers"
msgstr "Záhlaví odezvy"

#: templates/debug_toolbar/panels/headers.html:41
msgid "WSGI environ"
msgstr "Prostředí WSGI"

#: templates/debug_toolbar/panels/headers.html:43
msgid ""
"Since the WSGI environ inherits the environment of the server, only a "
"significant subset is shown below."
msgstr ""
"Níže je zobrazena pouze podstatná část proměnných prostředí, protože WSGI je "
"dědí od serveru."

#: templates/debug_toolbar/panels/history.html:10
msgid "Method"
msgstr ""

#: templates/debug_toolbar/panels/history.html:11
#: templates/debug_toolbar/panels/staticfiles.html:43
msgid "Path"
msgstr "Cesta"

#: templates/debug_toolbar/panels/history.html:12
#, fuzzy
#| msgid "Request headers"
msgid "Request Variables"
msgstr "Záhlaví požadavku"

#: templates/debug_toolbar/panels/history.html:13
msgid "Status"
msgstr ""

#: templates/debug_toolbar/panels/history.html:14
#: templates/debug_toolbar/panels/sql.html:37
msgid "Action"
msgstr "Akce"

#: templates/debug_toolbar/panels/history_tr.html:22
#: templates/debug_toolbar/panels/request_variables.html:11
msgid "Variable"
msgstr "Proměnná"

#: templates/debug_toolbar/panels/profiling.html:5
msgid "Call"
msgstr "Volání"

#: templates/debug_toolbar/panels/profiling.html:6
msgid "CumTime"
msgstr "KumulČas"

#: templates/debug_toolbar/panels/profiling.html:7
#: templates/debug_toolbar/panels/profiling.html:9
msgid "Per"
msgstr "Celk. za volání"

#: templates/debug_toolbar/panels/profiling.html:8
msgid "TotTime"
msgstr "CelkČas"

#: templates/debug_toolbar/panels/profiling.html:10
msgid "Count"
msgstr "Počet"

#: templates/debug_toolbar/panels/request.html:3
msgid "View information"
msgstr "Informace o pohledových funkcích"

#: templates/debug_toolbar/panels/request.html:7
msgid "View function"
msgstr "Pohledová funkce"

#: templates/debug_toolbar/panels/request.html:10
msgid "URL name"
msgstr "Název URL"

#: templates/debug_toolbar/panels/request.html:24
msgid "Cookies"
msgstr "Soubory cookie"

#: templates/debug_toolbar/panels/request.html:27
msgid "No cookies"
msgstr "Žádné soubory cookie"

#: templates/debug_toolbar/panels/request.html:31
msgid "Session data"
msgstr "Data sezení"

#: templates/debug_toolbar/panels/request.html:34
msgid "No session data"
msgstr "Žádná data sezení"

#: templates/debug_toolbar/panels/request.html:38
msgid "GET data"
msgstr "Data typu GET"

#: templates/debug_toolbar/panels/request.html:41
msgid "No GET data"
msgstr "Žádná data typu GET"

#: templates/debug_toolbar/panels/request.html:45
msgid "POST data"
msgstr "Data typu POST"

#: templates/debug_toolbar/panels/request.html:48
msgid "No POST data"
msgstr "Žádná data typu POST"

#: templates/debug_toolbar/panels/settings.html:5
msgid "Setting"
msgstr "Nastavení"

#: templates/debug_toolbar/panels/signals.html:5
msgid "Signal"
msgstr "Signál"

#: templates/debug_toolbar/panels/signals.html:6
msgid "Receivers"
msgstr "Příjemci"

#: templates/debug_toolbar/panels/sql.html:6
#, python-format
msgid "%(num)s query"
msgid_plural "%(num)s queries"
msgstr[0] "%(num)s dotaz"
msgstr[1] "%(num)s dotazy"
msgstr[2] "%(num)s dotazů"

#: templates/debug_toolbar/panels/sql.html:8
#, python-format
msgid ""
"including <abbr title=\"Similar queries are queries with the same SQL, but "
"potentially different parameters.\">%(count)s similar</abbr>"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:12
#, python-format
msgid ""
"and <abbr title=\"Duplicate queries are identical to each other: they "
"execute exactly the same SQL and parameters.\">%(dupes)s duplicates</abbr>"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:34
msgid "Query"
msgstr "Dotaz"

#: templates/debug_toolbar/panels/sql.html:35
#: templates/debug_toolbar/panels/timer.html:36
msgid "Timeline"
msgstr "Časová osa"

#: templates/debug_toolbar/panels/sql.html:52
#, fuzzy, python-format
#| msgid "%(count)s message"
#| msgid_plural "%(count)s messages"
msgid "%(count)s similar queries."
msgstr "%(count)s zpráva"

#: templates/debug_toolbar/panels/sql.html:58
#, python-format
msgid "Duplicated %(dupes)s times."
msgstr ""

#: templates/debug_toolbar/panels/sql.html:95
msgid "Connection:"
msgstr "Spojení:"

#: templates/debug_toolbar/panels/sql.html:97
msgid "Isolation level:"
msgstr "Úroveň izolace:"

#: templates/debug_toolbar/panels/sql.html:100
msgid "Transaction status:"
msgstr "Stav transakce:"

#: templates/debug_toolbar/panels/sql.html:114
msgid "(unknown)"
msgstr "(neznámé)"

#: templates/debug_toolbar/panels/sql.html:123
msgid "No SQL queries were recorded during this request."
msgstr "Pro tento požadavek nebyl zaznamenán žádný dotaz SQL."

#: templates/debug_toolbar/panels/sql_explain.html:4
msgid "SQL explained"
msgstr "Vysvětlené SQL"

#: templates/debug_toolbar/panels/sql_explain.html:9
#: templates/debug_toolbar/panels/sql_profile.html:10
#: templates/debug_toolbar/panels/sql_select.html:9
msgid "Executed SQL"
msgstr "Spuštěné SQL"

#: templates/debug_toolbar/panels/sql_explain.html:13
#: templates/debug_toolbar/panels/sql_profile.html:14
#: templates/debug_toolbar/panels/sql_select.html:13
msgid "Database"
msgstr "Databáze"

#: templates/debug_toolbar/panels/sql_profile.html:4
msgid "SQL profiled"
msgstr "Profilované SQL"

#: templates/debug_toolbar/panels/sql_profile.html:37
msgid "Error"
msgstr "Chyba"

#: templates/debug_toolbar/panels/sql_select.html:4
msgid "SQL selected"
msgstr "Vybrané SQL"

#: templates/debug_toolbar/panels/sql_select.html:36
msgid "Empty set"
msgstr "Prázdná sada"

#: templates/debug_toolbar/panels/staticfiles.html:3
msgid "Static file path"
msgid_plural "Static file paths"
msgstr[0] "Cesta ke statickým souborům"
msgstr[1] "Cesty ke statickým souborům"
msgstr[2] "Cesty ke statickým souborům"

#: templates/debug_toolbar/panels/staticfiles.html:7
#, python-format
msgid "(prefix %(prefix)s)"
msgstr "(prefix %(prefix)s)"

#: templates/debug_toolbar/panels/staticfiles.html:11
#: templates/debug_toolbar/panels/staticfiles.html:22
#: templates/debug_toolbar/panels/staticfiles.html:34
#: templates/debug_toolbar/panels/templates.html:10
#: templates/debug_toolbar/panels/templates.html:30
#: templates/debug_toolbar/panels/templates.html:47
msgid "None"
msgstr "Žádné"

#: templates/debug_toolbar/panels/staticfiles.html:14
msgid "Static file app"
msgid_plural "Static file apps"
msgstr[0] "Aplikace se statickými soubory"
msgstr[1] "Aplikace se statickými soubory"
msgstr[2] "Aplikace se statickými soubory"

#: templates/debug_toolbar/panels/staticfiles.html:25
msgid "Static file"
msgid_plural "Static files"
msgstr[0] "Statický soubor"
msgstr[1] "Statické soubory"
msgstr[2] "Statické soubory"

#: templates/debug_toolbar/panels/staticfiles.html:39
#, python-format
msgid "%(payload_count)s file"
msgid_plural "%(payload_count)s files"
msgstr[0] "%(payload_count)s soubor"
msgstr[1] "%(payload_count)s soubory"
msgstr[2] "%(payload_count)s souborů"

#: templates/debug_toolbar/panels/staticfiles.html:44
msgid "Location"
msgstr "Adresa"

#: templates/debug_toolbar/panels/template_source.html:4
msgid "Template source:"
msgstr "Zdroj šablony:"

#: templates/debug_toolbar/panels/templates.html:2
msgid "Template path"
msgid_plural "Template paths"
msgstr[0] "Cesta k šabloně"
msgstr[1] "Cesty k šablonám"
msgstr[2] "Cesty k šablonám"

#: templates/debug_toolbar/panels/templates.html:13
msgid "Template"
msgid_plural "Templates"
msgstr[0] "Šablona"
msgstr[1] "Šablony"
msgstr[2] "Šablony"

#: templates/debug_toolbar/panels/templates.html:22
#: templates/debug_toolbar/panels/templates.html:40
msgid "Toggle context"
msgstr "Zap./vyp. kontext"

#: templates/debug_toolbar/panels/templates.html:33
msgid "Context processor"
msgid_plural "Context processors"
msgstr[0] "Procesor kontextu"
msgstr[1] "Procesory kontextu"
msgstr[2] "Procesory kontextu"

#: templates/debug_toolbar/panels/timer.html:2
msgid "Resource usage"
msgstr "Využití zdrojů"

#: templates/debug_toolbar/panels/timer.html:10
msgid "Resource"
msgstr "Prostředek"

#: templates/debug_toolbar/panels/timer.html:26
msgid "Browser timing"
msgstr "Časování prohlížeče"

#: templates/debug_toolbar/panels/timer.html:35
msgid "Timing attribute"
msgstr "Atribut"

#: templates/debug_toolbar/panels/timer.html:37
msgid "Milliseconds since navigation start (+length)"
msgstr "Milisekund od začátku navigace (+délka)"

#: templates/debug_toolbar/panels/versions.html:10
msgid "Package"
msgstr ""

#: templates/debug_toolbar/panels/versions.html:11
msgid "Name"
msgstr "Název"

#: templates/debug_toolbar/panels/versions.html:12
msgid "Version"
msgstr "Verze"

#: templates/debug_toolbar/redirect.html:10
msgid "Location:"
msgstr "Adresa:"

#: templates/debug_toolbar/redirect.html:12
msgid ""
"The Django Debug Toolbar has intercepted a redirect to the above URL for "
"debug viewing purposes. You can click the above link to continue with the "
"redirect as normal."
msgstr ""
"Aplikace Django Debug Toolbar zachytila přesměrování na výše uvedenou adresu "
"URL za účelem ladicího zobrazení. Chcete-li přesměrování dokončit, klepněte "
"na odkaz výše."

#: views.py:16
msgid ""
"Data for this panel isn't available anymore. Please reload the page and "
"retry."
msgstr ""
"Data pro tento panel již nejsou k dispozici. Obnovte stránku a zkuste to "
"znova."
