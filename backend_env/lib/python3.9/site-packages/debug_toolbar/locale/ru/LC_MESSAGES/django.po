# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2014
# <PERSON><PERSON> <<EMAIL>>, 2013
# <PERSON>, 2009
# <AUTHOR> <EMAIL>, 2013,2015
msgid ""
msgstr ""
"Project-Id-Version: Django Debug Toolbar\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-20 17:23+0100\n"
"PO-Revision-Date: 2021-08-14 15:25+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Russian (http://www.transifex.com/django-debug-toolbar/django-"
"debug-toolbar/language/ru/)\n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || "
"(n%100>=11 && n%100<=14)? 2 : 3);\n"

#: apps.py:15
msgid "Debug Toolbar"
msgstr "Панель отладки"

#: panels/cache.py:180
msgid "Cache"
msgstr "Кэш"

#: panels/cache.py:186
#, python-format
msgid "%(cache_calls)d call in %(time).2fms"
msgid_plural "%(cache_calls)d calls in %(time).2fms"
msgstr[0] "%(cache_calls)d обращение за %(time).2fms"
msgstr[1] "%(cache_calls)d обращения за %(time).2fms"
msgstr[2] "%(cache_calls)d обращений за %(time).2fms"
msgstr[3] "%(cache_calls)d обращений за %(time).2fms"

#: panels/cache.py:195
#, python-format
msgid "Cache calls from %(count)d backend"
msgid_plural "Cache calls from %(count)d backends"
msgstr[0] "Обращения к кэшу от %(count)d бэкенда"
msgstr[1] "Обращения к кэшу от %(count)d бэкендов"
msgstr[2] "Обращения к кэшу от %(count)d бэкендов"
msgstr[3] "Обращения к кэшу от %(count)d бэкендов"

#: panels/headers.py:31
msgid "Headers"
msgstr "Заголовки"

#: panels/history/panel.py:18 panels/history/panel.py:19
msgid "History"
msgstr ""

#: panels/profiling.py:140
msgid "Profiling"
msgstr "Профилирование"

#: panels/redirects.py:14
msgid "Intercept redirects"
msgstr "Перехват редиректов"

#: panels/request.py:16
msgid "Request"
msgstr "Запрос"

#: panels/request.py:36
msgid "<no view>"
msgstr "<нет view>"

#: panels/request.py:53
msgid "<unavailable>"
msgstr "<недоступно>"

#: panels/settings.py:17
msgid "Settings"
msgstr "Настройки"

#: panels/settings.py:20
#, python-format
msgid "Settings from %s"
msgstr ""

#: panels/signals.py:57
#, python-format
msgid "%(num_receivers)d receiver of 1 signal"
msgid_plural "%(num_receivers)d receivers of 1 signal"
msgstr[0] "%(num_receivers)d получатель 1 сигнала"
msgstr[1] "%(num_receivers)d получателя 1 сигнала"
msgstr[2] "%(num_receivers)d получателей 1 сигнала"
msgstr[3] "%(num_receivers)d получателей 1 сигнала"

#: panels/signals.py:62
#, python-format
msgid "%(num_receivers)d receiver of %(num_signals)d signals"
msgid_plural "%(num_receivers)d receivers of %(num_signals)d signals"
msgstr[0] "%(num_receivers)d получатель %(num_signals)d сигнала(ов)"
msgstr[1] "%(num_receivers)d получателя %(num_signals)d сигнала(ов)"
msgstr[2] "%(num_receivers)d получателей %(num_signals)d сигнала(ов)"
msgstr[3] "%(num_receivers)d получателей %(num_signals)d сигнала(ов)"

#: panels/signals.py:67
msgid "Signals"
msgstr "Сигналы"

#: panels/sql/panel.py:23
msgid "Autocommit"
msgstr "Autocommit"

#: panels/sql/panel.py:24
msgid "Read uncommitted"
msgstr "Read uncommitted"

#: panels/sql/panel.py:25
msgid "Read committed"
msgstr "Read committed"

#: panels/sql/panel.py:26
msgid "Repeatable read"
msgstr "Repeatable read"

#: panels/sql/panel.py:27
msgid "Serializable"
msgstr "Serializable"

#: panels/sql/panel.py:39
msgid "Idle"
msgstr "Ожидание"

#: panels/sql/panel.py:40
msgid "Active"
msgstr "Действие"

#: panels/sql/panel.py:41
msgid "In transaction"
msgstr "В транзакции"

#: panels/sql/panel.py:42
msgid "In error"
msgstr "Ошибка"

#: panels/sql/panel.py:43
msgid "Unknown"
msgstr "Неизвестно"

#: panels/sql/panel.py:130
msgid "SQL"
msgstr "SQL"

#: panels/sql/panel.py:135
#, python-format
msgid "%(query_count)d query in %(sql_time).2fms"
msgid_plural "%(query_count)d queries in %(sql_time).2fms"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""

#: panels/sql/panel.py:147
#, python-format
msgid "SQL queries from %(count)d connection"
msgid_plural "SQL queries from %(count)d connections"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""

#: panels/staticfiles.py:84
#, python-format
msgid "Static files (%(num_found)s found, %(num_used)s used)"
msgstr "Статические файлы (найдено %(num_found)s, используется %(num_used)s)"

#: panels/staticfiles.py:105
msgid "Static files"
msgstr "Статические файлы"

#: panels/staticfiles.py:111
#, python-format
msgid "%(num_used)s file used"
msgid_plural "%(num_used)s files used"
msgstr[0] "%(num_used)s файл используется"
msgstr[1] " %(num_used)s файла используется"
msgstr[2] "%(num_used)s файлов используется"
msgstr[3] "%(num_used)s файлов используется"

#: panels/templates/panel.py:143
msgid "Templates"
msgstr "Шаблоны"

#: panels/templates/panel.py:148
#, python-format
msgid "Templates (%(num_templates)s rendered)"
msgstr "Шаблоны (обработано %(num_templates)s)"

#: panels/templates/panel.py:180
msgid "No origin"
msgstr ""

#: panels/timer.py:25
#, python-format
msgid "CPU: %(cum)0.2fms (%(total)0.2fms)"
msgstr "CPU: %(cum)0.2fms (%(total)0.2fms)"

#: panels/timer.py:30
#, python-format
msgid "Total: %0.2fms"
msgstr "Итого: %0.2fms"

#: panels/timer.py:36 templates/debug_toolbar/panels/history.html:9
#: templates/debug_toolbar/panels/sql_explain.html:11
#: templates/debug_toolbar/panels/sql_profile.html:12
#: templates/debug_toolbar/panels/sql_select.html:11
msgid "Time"
msgstr "Время"

#: panels/timer.py:44
msgid "User CPU time"
msgstr "User CPU time"

#: panels/timer.py:44
#, python-format
msgid "%(utime)0.3f msec"
msgstr "%(utime)0.3f мс"

#: panels/timer.py:45
msgid "System CPU time"
msgstr "System CPU time"

#: panels/timer.py:45
#, python-format
msgid "%(stime)0.3f msec"
msgstr "%(stime)0.3f мс"

#: panels/timer.py:46
msgid "Total CPU time"
msgstr "Total CPU time"

#: panels/timer.py:46
#, python-format
msgid "%(total)0.3f msec"
msgstr "%(total)0.3f мс"

#: panels/timer.py:47
msgid "Elapsed time"
msgstr "Затраченное время"

#: panels/timer.py:47
#, python-format
msgid "%(total_time)0.3f msec"
msgstr "%(total_time)0.3f мс"

#: panels/timer.py:49
msgid "Context switches"
msgstr "Переключений контекста"

#: panels/timer.py:50
#, python-format
msgid "%(vcsw)d voluntary, %(ivcsw)d involuntary"
msgstr "%(vcsw)d намеренных, %(ivcsw)d вынужденных"

#: panels/versions.py:19
msgid "Versions"
msgstr "Версии"

#: templates/debug_toolbar/base.html:22
msgid "Hide toolbar"
msgstr "Скрыть панель"

#: templates/debug_toolbar/base.html:22
msgid "Hide"
msgstr "Скрыть"

#: templates/debug_toolbar/base.html:29
msgid "Show toolbar"
msgstr "Показать панель"

#: templates/debug_toolbar/includes/panel_button.html:4
msgid "Disable for next and successive requests"
msgstr "Отключить для последующих запросов"

#: templates/debug_toolbar/includes/panel_button.html:4
msgid "Enable for next and successive requests"
msgstr "Включить для последующих запросов"

#: templates/debug_toolbar/panels/cache.html:2
msgid "Summary"
msgstr "Сводка"

#: templates/debug_toolbar/panels/cache.html:6
msgid "Total calls"
msgstr "Всего вызовов"

#: templates/debug_toolbar/panels/cache.html:7
msgid "Total time"
msgstr "Общее время"

#: templates/debug_toolbar/panels/cache.html:8
msgid "Cache hits"
msgstr "Cache хитов"

#: templates/debug_toolbar/panels/cache.html:9
msgid "Cache misses"
msgstr "Промахи кэша"

#: templates/debug_toolbar/panels/cache.html:21
msgid "Commands"
msgstr "Команды"

#: templates/debug_toolbar/panels/cache.html:39
msgid "Calls"
msgstr "Вызовы"

#: templates/debug_toolbar/panels/cache.html:43
#: templates/debug_toolbar/panels/sql.html:36
msgid "Time (ms)"
msgstr "Время (мс)"

#: templates/debug_toolbar/panels/cache.html:44
msgid "Type"
msgstr "Тип"

#: templates/debug_toolbar/panels/cache.html:45
#: templates/debug_toolbar/panels/request.html:8
msgid "Arguments"
msgstr "Аргументы"

#: templates/debug_toolbar/panels/cache.html:46
#: templates/debug_toolbar/panels/request.html:9
msgid "Keyword arguments"
msgstr "Именованные аргументы"

#: templates/debug_toolbar/panels/cache.html:47
msgid "Backend"
msgstr "Бэкенд"

#: templates/debug_toolbar/panels/headers.html:3
msgid "Request headers"
msgstr "Заголовки запроса"

#: templates/debug_toolbar/panels/headers.html:8
#: templates/debug_toolbar/panels/headers.html:27
#: templates/debug_toolbar/panels/headers.html:48
msgid "Key"
msgstr "Заголовок"

#: templates/debug_toolbar/panels/headers.html:9
#: templates/debug_toolbar/panels/headers.html:28
#: templates/debug_toolbar/panels/headers.html:49
#: templates/debug_toolbar/panels/history_tr.html:23
#: templates/debug_toolbar/panels/request_variables.html:12
#: templates/debug_toolbar/panels/settings.html:6
#: templates/debug_toolbar/panels/timer.html:11
msgid "Value"
msgstr "Значение"

#: templates/debug_toolbar/panels/headers.html:22
msgid "Response headers"
msgstr "Заголовки ответа"

#: templates/debug_toolbar/panels/headers.html:41
msgid "WSGI environ"
msgstr "WSGI-окружение"

#: templates/debug_toolbar/panels/headers.html:43
msgid ""
"Since the WSGI environ inherits the environment of the server, only a "
"significant subset is shown below."
msgstr ""
"Так как WSGI-окружение наследует окружение сервера, ниже отображены лишь те "
"из переменных, которые важны для нужд отладки."

#: templates/debug_toolbar/panels/history.html:10
msgid "Method"
msgstr "Метод"

#: templates/debug_toolbar/panels/history.html:11
#: templates/debug_toolbar/panels/staticfiles.html:43
msgid "Path"
msgstr "Путь"

#: templates/debug_toolbar/panels/history.html:12
msgid "Request Variables"
msgstr ""

#: templates/debug_toolbar/panels/history.html:13
msgid "Status"
msgstr ""

#: templates/debug_toolbar/panels/history.html:14
#: templates/debug_toolbar/panels/sql.html:37
msgid "Action"
msgstr "Действие"

#: templates/debug_toolbar/panels/history_tr.html:22
#: templates/debug_toolbar/panels/request_variables.html:11
msgid "Variable"
msgstr "Переменная"

#: templates/debug_toolbar/panels/profiling.html:5
msgid "Call"
msgstr "Вызов"

#: templates/debug_toolbar/panels/profiling.html:6
msgid "CumTime"
msgstr "КумулВрем"

#: templates/debug_toolbar/panels/profiling.html:7
#: templates/debug_toolbar/panels/profiling.html:9
msgid "Per"
msgstr "ЗаВызов"

#: templates/debug_toolbar/panels/profiling.html:8
msgid "TotTime"
msgstr "ИтогВремя"

#: templates/debug_toolbar/panels/profiling.html:10
msgid "Count"
msgstr "Кол-во"

#: templates/debug_toolbar/panels/request.html:3
msgid "View information"
msgstr "View"

#: templates/debug_toolbar/panels/request.html:7
msgid "View function"
msgstr "View функция"

#: templates/debug_toolbar/panels/request.html:10
msgid "URL name"
msgstr "URL Name"

#: templates/debug_toolbar/panels/request.html:24
msgid "Cookies"
msgstr "Cookies"

#: templates/debug_toolbar/panels/request.html:27
msgid "No cookies"
msgstr "Нет cookies"

#: templates/debug_toolbar/panels/request.html:31
msgid "Session data"
msgstr "Сессия"

#: templates/debug_toolbar/panels/request.html:34
msgid "No session data"
msgstr "Нет данных в сессии"

#: templates/debug_toolbar/panels/request.html:38
msgid "GET data"
msgstr "GET"

#: templates/debug_toolbar/panels/request.html:41
msgid "No GET data"
msgstr "Нет GET данных"

#: templates/debug_toolbar/panels/request.html:45
msgid "POST data"
msgstr "POST"

#: templates/debug_toolbar/panels/request.html:48
msgid "No POST data"
msgstr "Нет POST данных"

#: templates/debug_toolbar/panels/settings.html:5
msgid "Setting"
msgstr "Параметр"

#: templates/debug_toolbar/panels/signals.html:5
msgid "Signal"
msgstr "Сигнал"

#: templates/debug_toolbar/panels/signals.html:6
msgid "Receivers"
msgstr "Получатели сигнала"

#: templates/debug_toolbar/panels/sql.html:6
#, python-format
msgid "%(num)s query"
msgid_plural "%(num)s queries"
msgstr[0] "%(num)s запрос"
msgstr[1] "%(num)s запроса"
msgstr[2] "%(num)s запросов"
msgstr[3] "%(num)s запросов"

#: templates/debug_toolbar/panels/sql.html:8
#, python-format
msgid ""
"including <abbr title=\"Similar queries are queries with the same SQL, but "
"potentially different parameters.\">%(count)s similar</abbr>"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:12
#, python-format
msgid ""
"and <abbr title=\"Duplicate queries are identical to each other: they "
"execute exactly the same SQL and parameters.\">%(dupes)s duplicates</abbr>"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:34
msgid "Query"
msgstr "Запрос"

#: templates/debug_toolbar/panels/sql.html:35
#: templates/debug_toolbar/panels/timer.html:36
msgid "Timeline"
msgstr "Временная диаграмма"

#: templates/debug_toolbar/panels/sql.html:52
#, python-format
msgid "%(count)s similar queries."
msgstr ""

#: templates/debug_toolbar/panels/sql.html:58
#, python-format
msgid "Duplicated %(dupes)s times."
msgstr ""

#: templates/debug_toolbar/panels/sql.html:95
msgid "Connection:"
msgstr "Соединение:"

#: templates/debug_toolbar/panels/sql.html:97
msgid "Isolation level:"
msgstr "Уровень изоляции:"

#: templates/debug_toolbar/panels/sql.html:100
msgid "Transaction status:"
msgstr "Статус транзакции:"

#: templates/debug_toolbar/panels/sql.html:114
msgid "(unknown)"
msgstr "(неизвестно)"

#: templates/debug_toolbar/panels/sql.html:123
msgid "No SQL queries were recorded during this request."
msgstr ""
"Во время обработки этого HTTP-запроса не было записано ни одного SQL-запроса."

#: templates/debug_toolbar/panels/sql_explain.html:4
msgid "SQL explained"
msgstr "SQL Explain"

#: templates/debug_toolbar/panels/sql_explain.html:9
#: templates/debug_toolbar/panels/sql_profile.html:10
#: templates/debug_toolbar/panels/sql_select.html:9
msgid "Executed SQL"
msgstr "Запрос"

#: templates/debug_toolbar/panels/sql_explain.html:13
#: templates/debug_toolbar/panels/sql_profile.html:14
#: templates/debug_toolbar/panels/sql_select.html:13
msgid "Database"
msgstr "База данных"

#: templates/debug_toolbar/panels/sql_profile.html:4
msgid "SQL profiled"
msgstr "Профилирование SQL"

#: templates/debug_toolbar/panels/sql_profile.html:37
msgid "Error"
msgstr "Ошибка"

#: templates/debug_toolbar/panels/sql_select.html:4
msgid "SQL selected"
msgstr "Выбранные SQL-запросы"

#: templates/debug_toolbar/panels/sql_select.html:36
msgid "Empty set"
msgstr "Ничего, ноль строк"

#: templates/debug_toolbar/panels/staticfiles.html:3
msgid "Static file path"
msgid_plural "Static file paths"
msgstr[0] "Путь к статическим файлам"
msgstr[1] "Пути к статическим файлам"
msgstr[2] "Пути к статическим файлам"
msgstr[3] "Пути к статическим файлам"

#: templates/debug_toolbar/panels/staticfiles.html:7
#, python-format
msgid "(prefix %(prefix)s)"
msgstr "(префикс %(prefix)s)"

#: templates/debug_toolbar/panels/staticfiles.html:11
#: templates/debug_toolbar/panels/staticfiles.html:22
#: templates/debug_toolbar/panels/staticfiles.html:34
#: templates/debug_toolbar/panels/templates.html:10
#: templates/debug_toolbar/panels/templates.html:30
#: templates/debug_toolbar/panels/templates.html:47
msgid "None"
msgstr "Нет"

#: templates/debug_toolbar/panels/staticfiles.html:14
msgid "Static file app"
msgid_plural "Static file apps"
msgstr[0] "Приложение, использующее статические файлы"
msgstr[1] "Приложения, использующие статические файлы"
msgstr[2] "Приложения, использующие статические файлы"
msgstr[3] "Приложения, использующие статические файлы"

#: templates/debug_toolbar/panels/staticfiles.html:25
msgid "Static file"
msgid_plural "Static files"
msgstr[0] "Статический файл"
msgstr[1] "Статические файлы"
msgstr[2] "Статические файлы"
msgstr[3] "Статические файлы"

#: templates/debug_toolbar/panels/staticfiles.html:39
#, python-format
msgid "%(payload_count)s file"
msgid_plural "%(payload_count)s files"
msgstr[0] "%(payload_count)s файл"
msgstr[1] "%(payload_count)s файла"
msgstr[2] "%(payload_count)s файлов"
msgstr[3] "%(payload_count)s файлов"

#: templates/debug_toolbar/panels/staticfiles.html:44
msgid "Location"
msgstr "Место"

#: templates/debug_toolbar/panels/template_source.html:4
msgid "Template source:"
msgstr "Источник шаблона:"

#: templates/debug_toolbar/panels/templates.html:2
msgid "Template path"
msgid_plural "Template paths"
msgstr[0] "Путь к шаблонам"
msgstr[1] "Пути к шаблонам"
msgstr[2] "Пути к шаблонам"
msgstr[3] "Пути к шаблонам"

#: templates/debug_toolbar/panels/templates.html:13
msgid "Template"
msgid_plural "Templates"
msgstr[0] "Шаблон"
msgstr[1] "Шаблоны"
msgstr[2] "Шаблоны"
msgstr[3] "Шаблоны"

#: templates/debug_toolbar/panels/templates.html:22
#: templates/debug_toolbar/panels/templates.html:40
msgid "Toggle context"
msgstr "Контекст"

#: templates/debug_toolbar/panels/templates.html:33
msgid "Context processor"
msgid_plural "Context processors"
msgstr[0] "Контекст процессор"
msgstr[1] "Контекст процессоры"
msgstr[2] "Контекст процессоры"
msgstr[3] "Контекст процессоры"

#: templates/debug_toolbar/panels/timer.html:2
msgid "Resource usage"
msgstr "Потребление ресурсов"

#: templates/debug_toolbar/panels/timer.html:10
msgid "Resource"
msgstr "Ресурс"

#: templates/debug_toolbar/panels/timer.html:26
msgid "Browser timing"
msgstr "Браузерное время"

#: templates/debug_toolbar/panels/timer.html:35
msgid "Timing attribute"
msgstr "Событие"

#: templates/debug_toolbar/panels/timer.html:37
msgid "Milliseconds since navigation start (+length)"
msgstr "С начала навигации в мс (+продолжительность)"

#: templates/debug_toolbar/panels/versions.html:10
msgid "Package"
msgstr ""

#: templates/debug_toolbar/panels/versions.html:11
msgid "Name"
msgstr "Название"

#: templates/debug_toolbar/panels/versions.html:12
msgid "Version"
msgstr "Версия"

#: templates/debug_toolbar/redirect.html:10
msgid "Location:"
msgstr "Адрес:"

#: templates/debug_toolbar/redirect.html:12
msgid ""
"The Django Debug Toolbar has intercepted a redirect to the above URL for "
"debug viewing purposes. You can click the above link to continue with the "
"redirect as normal."
msgstr ""
"Django Debug Toolbar в перехватил редирект на адрес, указанный выше. Вы "
"можете нажать на ссылку, чтобы выполнить переход самостоятельно."

#: views.py:16
msgid ""
"Data for this panel isn't available anymore. Please reload the page and "
"retry."
msgstr ""
"Данные этой панели больше недоступны. Пожалуйста, перезагрузите страницу и "
"попробуйте ещё раз."
