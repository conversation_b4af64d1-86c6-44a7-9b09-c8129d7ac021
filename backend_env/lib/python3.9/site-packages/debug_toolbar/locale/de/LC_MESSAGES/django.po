# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2012-2014,2021
# <PERSON> <<EMAIL>>, 2021
msgid ""
msgstr ""
"Project-Id-Version: Django Debug Toolbar\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-20 17:23+0100\n"
"PO-Revision-Date: 2021-12-04 17:38+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: German (http://www.transifex.com/django-debug-toolbar/django-"
"debug-toolbar/language/de/)\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: apps.py:15
msgid "Debug Toolbar"
msgstr "Debug Toolbar"

#: panels/cache.py:180
msgid "Cache"
msgstr "Cache"

#: panels/cache.py:186
#, python-format
msgid "%(cache_calls)d call in %(time).2fms"
msgid_plural "%(cache_calls)d calls in %(time).2fms"
msgstr[0] "%(cache_calls)d Abfrage in %(time).2fms"
msgstr[1] "%(cache_calls)d Abfragen in %(time).2fms"

#: panels/cache.py:195
#, python-format
msgid "Cache calls from %(count)d backend"
msgid_plural "Cache calls from %(count)d backends"
msgstr[0] "Cache-Aufrufe von %(count)d Backend"
msgstr[1] "Cache-Aufrufe von %(count)d Backends"

#: panels/headers.py:31
msgid "Headers"
msgstr "Header"

#: panels/history/panel.py:18 panels/history/panel.py:19
msgid "History"
msgstr "Geschichte"

#: panels/profiling.py:140
msgid "Profiling"
msgstr "Profiling"

#: panels/redirects.py:14
msgid "Intercept redirects"
msgstr "Umleitungen abfangen"

#: panels/request.py:16
msgid "Request"
msgstr "Anfrage"

#: panels/request.py:36
msgid "<no view>"
msgstr "<kein View>"

#: panels/request.py:53
msgid "<unavailable>"
msgstr "<nicht verfügbar>"

#: panels/settings.py:17
msgid "Settings"
msgstr "Einstellungen"

#: panels/settings.py:20
#, python-format
msgid "Settings from %s"
msgstr "Einstellungen von %s"

#: panels/signals.py:57
#, python-format
msgid "%(num_receivers)d receiver of 1 signal"
msgid_plural "%(num_receivers)d receivers of 1 signal"
msgstr[0] "%(num_receivers)d Empfänger von einem Signal"
msgstr[1] "%(num_receivers)d Empfänger von einem Signal"

#: panels/signals.py:62
#, python-format
msgid "%(num_receivers)d receiver of %(num_signals)d signals"
msgid_plural "%(num_receivers)d receivers of %(num_signals)d signals"
msgstr[0] "%(num_receivers)d Empfänger von %(num_signals)d Signalen"
msgstr[1] "%(num_receivers)d Empfänger von %(num_signals)d Signalen"

#: panels/signals.py:67
msgid "Signals"
msgstr "Signale"

#: panels/sql/panel.py:23
msgid "Autocommit"
msgstr "Autocommit"

#: panels/sql/panel.py:24
msgid "Read uncommitted"
msgstr "Read uncommitted"

#: panels/sql/panel.py:25
msgid "Read committed"
msgstr "Read committed"

#: panels/sql/panel.py:26
msgid "Repeatable read"
msgstr "Repeatable read"

#: panels/sql/panel.py:27
msgid "Serializable"
msgstr "Serializable"

#: panels/sql/panel.py:39
msgid "Idle"
msgstr "Wartet"

#: panels/sql/panel.py:40
msgid "Active"
msgstr "Aktiv"

#: panels/sql/panel.py:41
msgid "In transaction"
msgstr "In einer Transaktion"

#: panels/sql/panel.py:42
msgid "In error"
msgstr "Fehler"

#: panels/sql/panel.py:43
msgid "Unknown"
msgstr "Unbekannt"

#: panels/sql/panel.py:130
msgid "SQL"
msgstr "SQL"

#: panels/sql/panel.py:135
#, python-format
msgid "%(query_count)d query in %(sql_time).2fms"
msgid_plural "%(query_count)d queries in %(sql_time).2fms"
msgstr[0] "%(query_count)d Abfrage in %(sql_time).2f ms"
msgstr[1] "%(query_count)d Abfragen in %(sql_time).2f ms"

#: panels/sql/panel.py:147
#, python-format
msgid "SQL queries from %(count)d connection"
msgid_plural "SQL queries from %(count)d connections"
msgstr[0] "SQL-Abfragen von %(count)d Verbindung"
msgstr[1] "SQL-Abfragen von %(count)d Verbindungen"

#: panels/staticfiles.py:84
#, python-format
msgid "Static files (%(num_found)s found, %(num_used)s used)"
msgstr "Statische Dateien (%(num_found)s gefunden, %(num_used)s benutzt)"

#: panels/staticfiles.py:105
msgid "Static files"
msgstr "Statische Dateien"

#: panels/staticfiles.py:111
#, python-format
msgid "%(num_used)s file used"
msgid_plural "%(num_used)s files used"
msgstr[0] "%(num_used)s Datei benutzt"
msgstr[1] "%(num_used)s Dateien benutzt"

#: panels/templates/panel.py:143
msgid "Templates"
msgstr "Templates"

#: panels/templates/panel.py:148
#, python-format
msgid "Templates (%(num_templates)s rendered)"
msgstr "Templates (%(num_templates)s gerendert)"

#: panels/templates/panel.py:180
msgid "No origin"
msgstr "Kein Ursprung"

#: panels/timer.py:25
#, python-format
msgid "CPU: %(cum)0.2fms (%(total)0.2fms)"
msgstr "CPU: %(cum)0.2fms (%(total)0.2fms)"

#: panels/timer.py:30
#, python-format
msgid "Total: %0.2fms"
msgstr "Gesamt: %0.2fms"

#: panels/timer.py:36 templates/debug_toolbar/panels/history.html:9
#: templates/debug_toolbar/panels/sql_explain.html:11
#: templates/debug_toolbar/panels/sql_profile.html:12
#: templates/debug_toolbar/panels/sql_select.html:11
msgid "Time"
msgstr "Zeit"

#: panels/timer.py:44
msgid "User CPU time"
msgstr "CPU-Zeit Benutzer"

#: panels/timer.py:44
#, python-format
msgid "%(utime)0.3f msec"
msgstr "%(utime)0.3f ms"

#: panels/timer.py:45
msgid "System CPU time"
msgstr "CPU-Zeit System"

#: panels/timer.py:45
#, python-format
msgid "%(stime)0.3f msec"
msgstr "%(stime)0.3f ms"

#: panels/timer.py:46
msgid "Total CPU time"
msgstr "CPU-Zeit gesamt"

#: panels/timer.py:46
#, python-format
msgid "%(total)0.3f msec"
msgstr "%(total)0.3f ms"

#: panels/timer.py:47
msgid "Elapsed time"
msgstr "Verstrichene Zeit"

#: panels/timer.py:47
#, python-format
msgid "%(total_time)0.3f msec"
msgstr "%(total_time)0.3f ms"

#: panels/timer.py:49
msgid "Context switches"
msgstr "Kontextwechsel"

#: panels/timer.py:50
#, python-format
msgid "%(vcsw)d voluntary, %(ivcsw)d involuntary"
msgstr "%(vcsw)d freiwillig, %(ivcsw)d unfreiwillig"

#: panels/versions.py:19
msgid "Versions"
msgstr "Versionen"

#: templates/debug_toolbar/base.html:22
msgid "Hide toolbar"
msgstr "Toolbar ausblenden"

#: templates/debug_toolbar/base.html:22
msgid "Hide"
msgstr "Ausblenden"

#: templates/debug_toolbar/base.html:29
msgid "Show toolbar"
msgstr "Toolbar einblenden"

#: templates/debug_toolbar/includes/panel_button.html:4
msgid "Disable for next and successive requests"
msgstr "Für nächste und die darauffolgenden Anfragen deaktivieren"

#: templates/debug_toolbar/includes/panel_button.html:4
msgid "Enable for next and successive requests"
msgstr "Für nächste und die darauffolgenden Anfragen aktivieren"

#: templates/debug_toolbar/panels/cache.html:2
msgid "Summary"
msgstr "Zusammenfassung"

#: templates/debug_toolbar/panels/cache.html:6
msgid "Total calls"
msgstr "Aufrufe gesamt"

#: templates/debug_toolbar/panels/cache.html:7
msgid "Total time"
msgstr "Zeit gesamt"

#: templates/debug_toolbar/panels/cache.html:8
msgid "Cache hits"
msgstr "Cache erfolgreich"

#: templates/debug_toolbar/panels/cache.html:9
msgid "Cache misses"
msgstr "Cache verfehlt"

#: templates/debug_toolbar/panels/cache.html:21
msgid "Commands"
msgstr "Befehle"

#: templates/debug_toolbar/panels/cache.html:39
msgid "Calls"
msgstr "Aufrufe"

#: templates/debug_toolbar/panels/cache.html:43
#: templates/debug_toolbar/panels/sql.html:36
msgid "Time (ms)"
msgstr "Zeit (ms)"

#: templates/debug_toolbar/panels/cache.html:44
msgid "Type"
msgstr "Typ"

#: templates/debug_toolbar/panels/cache.html:45
#: templates/debug_toolbar/panels/request.html:8
msgid "Arguments"
msgstr "Argumente"

#: templates/debug_toolbar/panels/cache.html:46
#: templates/debug_toolbar/panels/request.html:9
msgid "Keyword arguments"
msgstr "Schlüsselwort-Argumente"

#: templates/debug_toolbar/panels/cache.html:47
msgid "Backend"
msgstr "Backend"

#: templates/debug_toolbar/panels/headers.html:3
msgid "Request headers"
msgstr "Anfrage-Header"

#: templates/debug_toolbar/panels/headers.html:8
#: templates/debug_toolbar/panels/headers.html:27
#: templates/debug_toolbar/panels/headers.html:48
msgid "Key"
msgstr "Schlüssel"

#: templates/debug_toolbar/panels/headers.html:9
#: templates/debug_toolbar/panels/headers.html:28
#: templates/debug_toolbar/panels/headers.html:49
#: templates/debug_toolbar/panels/history_tr.html:23
#: templates/debug_toolbar/panels/request_variables.html:12
#: templates/debug_toolbar/panels/settings.html:6
#: templates/debug_toolbar/panels/timer.html:11
msgid "Value"
msgstr "Wert"

#: templates/debug_toolbar/panels/headers.html:22
msgid "Response headers"
msgstr "Antwort-Header"

#: templates/debug_toolbar/panels/headers.html:41
msgid "WSGI environ"
msgstr "WSGI-Umgebung"

#: templates/debug_toolbar/panels/headers.html:43
msgid ""
"Since the WSGI environ inherits the environment of the server, only a "
"significant subset is shown below."
msgstr ""
"Da sich die WSGI-Umgebung von der Umgebung des Servers ableitet, wird nur "
"eine notwendige Teilmenge dargestellt."

#: templates/debug_toolbar/panels/history.html:10
msgid "Method"
msgstr "Methode"

#: templates/debug_toolbar/panels/history.html:11
#: templates/debug_toolbar/panels/staticfiles.html:43
msgid "Path"
msgstr "Pfad"

#: templates/debug_toolbar/panels/history.html:12
msgid "Request Variables"
msgstr "Anfrage-Variablen"

#: templates/debug_toolbar/panels/history.html:13
msgid "Status"
msgstr "Status"

#: templates/debug_toolbar/panels/history.html:14
#: templates/debug_toolbar/panels/sql.html:37
msgid "Action"
msgstr "Aktion"

#: templates/debug_toolbar/panels/history_tr.html:22
#: templates/debug_toolbar/panels/request_variables.html:11
msgid "Variable"
msgstr "Variable"

#: templates/debug_toolbar/panels/profiling.html:5
msgid "Call"
msgstr "Aufruf"

#: templates/debug_toolbar/panels/profiling.html:6
msgid "CumTime"
msgstr "Gesamt"

#: templates/debug_toolbar/panels/profiling.html:7
#: templates/debug_toolbar/panels/profiling.html:9
msgid "Per"
msgstr "Per"

#: templates/debug_toolbar/panels/profiling.html:8
msgid "TotTime"
msgstr "Total"

#: templates/debug_toolbar/panels/profiling.html:10
msgid "Count"
msgstr "Anzahl"

#: templates/debug_toolbar/panels/request.html:3
msgid "View information"
msgstr "View-Informationen"

#: templates/debug_toolbar/panels/request.html:7
msgid "View function"
msgstr "View-Funktion"

#: templates/debug_toolbar/panels/request.html:10
msgid "URL name"
msgstr "URL-Name"

#: templates/debug_toolbar/panels/request.html:24
msgid "Cookies"
msgstr "Cookies"

#: templates/debug_toolbar/panels/request.html:27
msgid "No cookies"
msgstr "Keine Cookies"

#: templates/debug_toolbar/panels/request.html:31
msgid "Session data"
msgstr "Sitzungsdaten"

#: templates/debug_toolbar/panels/request.html:34
msgid "No session data"
msgstr "Keine Sitzungsdaten"

#: templates/debug_toolbar/panels/request.html:38
msgid "GET data"
msgstr "GET-Daten"

#: templates/debug_toolbar/panels/request.html:41
msgid "No GET data"
msgstr "Keine GET-Daten"

#: templates/debug_toolbar/panels/request.html:45
msgid "POST data"
msgstr "POST-Daten"

#: templates/debug_toolbar/panels/request.html:48
msgid "No POST data"
msgstr "Keine POST-Daten"

#: templates/debug_toolbar/panels/settings.html:5
msgid "Setting"
msgstr "Einstellung"

#: templates/debug_toolbar/panels/signals.html:5
msgid "Signal"
msgstr "Signal"

#: templates/debug_toolbar/panels/signals.html:6
msgid "Receivers"
msgstr "Empfänger"

#: templates/debug_toolbar/panels/sql.html:6
#, python-format
msgid "%(num)s query"
msgid_plural "%(num)s queries"
msgstr[0] "%(num)s Abfrage"
msgstr[1] "%(num)s Abfragen"

#: templates/debug_toolbar/panels/sql.html:8
#, python-format
msgid ""
"including <abbr title=\"Similar queries are queries with the same SQL, but "
"potentially different parameters.\">%(count)s similar</abbr>"
msgstr ""
"inklusive <abbr title=\"Similar queries are queries with the same SQL, but "
"potentially different parameters.\">%(count)s ähnlich</abbr>"

#: templates/debug_toolbar/panels/sql.html:12
#, python-format
msgid ""
"and <abbr title=\"Duplicate queries are identical to each other: they "
"execute exactly the same SQL and parameters.\">%(dupes)s duplicates</abbr>"
msgstr ""
"und <abbr title=\"Duplicate queries are identical to each other: they "
"execute exactly the same SQL and parameters.\">%(dupes)s dupliziert</abbr>"

#: templates/debug_toolbar/panels/sql.html:34
msgid "Query"
msgstr "Abfrage"

#: templates/debug_toolbar/panels/sql.html:35
#: templates/debug_toolbar/panels/timer.html:36
msgid "Timeline"
msgstr "Verlauf"

#: templates/debug_toolbar/panels/sql.html:52
#, python-format
msgid "%(count)s similar queries."
msgstr "%(count)s ähnliche Abfragen."

#: templates/debug_toolbar/panels/sql.html:58
#, python-format
msgid "Duplicated %(dupes)s times."
msgstr "%(dupes)s-mal dupliziert."

#: templates/debug_toolbar/panels/sql.html:95
msgid "Connection:"
msgstr "Verbindung:"

#: templates/debug_toolbar/panels/sql.html:97
msgid "Isolation level:"
msgstr "Isolationsebene:"

#: templates/debug_toolbar/panels/sql.html:100
msgid "Transaction status:"
msgstr "Transaktionsstatus:"

#: templates/debug_toolbar/panels/sql.html:114
msgid "(unknown)"
msgstr "(unbekannt)"

#: templates/debug_toolbar/panels/sql.html:123
msgid "No SQL queries were recorded during this request."
msgstr "Es wurde keine SQL-Abfrage während dieses Vorgangs aufgezeichnet."

#: templates/debug_toolbar/panels/sql_explain.html:4
msgid "SQL explained"
msgstr "SQL erklärt"

#: templates/debug_toolbar/panels/sql_explain.html:9
#: templates/debug_toolbar/panels/sql_profile.html:10
#: templates/debug_toolbar/panels/sql_select.html:9
msgid "Executed SQL"
msgstr "Ausgeführtes SQL"

#: templates/debug_toolbar/panels/sql_explain.html:13
#: templates/debug_toolbar/panels/sql_profile.html:14
#: templates/debug_toolbar/panels/sql_select.html:13
msgid "Database"
msgstr "Datenbank"

#: templates/debug_toolbar/panels/sql_profile.html:4
msgid "SQL profiled"
msgstr "SQL durchleuchtet"

#: templates/debug_toolbar/panels/sql_profile.html:37
msgid "Error"
msgstr "Fehler"

#: templates/debug_toolbar/panels/sql_select.html:4
msgid "SQL selected"
msgstr "SQL ausgewählt"

#: templates/debug_toolbar/panels/sql_select.html:36
msgid "Empty set"
msgstr "Leeres Set"

#: templates/debug_toolbar/panels/staticfiles.html:3
msgid "Static file path"
msgid_plural "Static file paths"
msgstr[0] "Pfad mit statischen Dateien"
msgstr[1] "Pfade mit statischen Dateien"

#: templates/debug_toolbar/panels/staticfiles.html:7
#, python-format
msgid "(prefix %(prefix)s)"
msgstr "(Präfix %(prefix)s)"

#: templates/debug_toolbar/panels/staticfiles.html:11
#: templates/debug_toolbar/panels/staticfiles.html:22
#: templates/debug_toolbar/panels/staticfiles.html:34
#: templates/debug_toolbar/panels/templates.html:10
#: templates/debug_toolbar/panels/templates.html:30
#: templates/debug_toolbar/panels/templates.html:47
msgid "None"
msgstr "-"

#: templates/debug_toolbar/panels/staticfiles.html:14
msgid "Static file app"
msgid_plural "Static file apps"
msgstr[0] "App mit statischen Dateien"
msgstr[1] "Apps mit statischen Dateien"

#: templates/debug_toolbar/panels/staticfiles.html:25
msgid "Static file"
msgid_plural "Static files"
msgstr[0] "Statische Datei"
msgstr[1] "Statische Dateien"

#: templates/debug_toolbar/panels/staticfiles.html:39
#, python-format
msgid "%(payload_count)s file"
msgid_plural "%(payload_count)s files"
msgstr[0] "%(payload_count)s Datei"
msgstr[1] "%(payload_count)s Dateien"

#: templates/debug_toolbar/panels/staticfiles.html:44
msgid "Location"
msgstr "Ort"

#: templates/debug_toolbar/panels/template_source.html:4
msgid "Template source:"
msgstr "Template-Quelle:"

#: templates/debug_toolbar/panels/templates.html:2
msgid "Template path"
msgid_plural "Template paths"
msgstr[0] "Template-Pfad"
msgstr[1] "Template-Pfade"

#: templates/debug_toolbar/panels/templates.html:13
msgid "Template"
msgid_plural "Templates"
msgstr[0] "Template"
msgstr[1] "Templates"

#: templates/debug_toolbar/panels/templates.html:22
#: templates/debug_toolbar/panels/templates.html:40
msgid "Toggle context"
msgstr "Context zeigen"

#: templates/debug_toolbar/panels/templates.html:33
msgid "Context processor"
msgid_plural "Context processors"
msgstr[0] "Context-Prozessor"
msgstr[1] "Context-Prozessoren"

#: templates/debug_toolbar/panels/timer.html:2
msgid "Resource usage"
msgstr "Ressourcenverwendung"

#: templates/debug_toolbar/panels/timer.html:10
msgid "Resource"
msgstr "Ressource"

#: templates/debug_toolbar/panels/timer.html:26
msgid "Browser timing"
msgstr "Browserzeit"

#: templates/debug_toolbar/panels/timer.html:35
msgid "Timing attribute"
msgstr "Timing-Attribut"

#: templates/debug_toolbar/panels/timer.html:37
msgid "Milliseconds since navigation start (+length)"
msgstr "Millisekunden seit Seitenaufruf (plus Dauer)"

#: templates/debug_toolbar/panels/versions.html:10
msgid "Package"
msgstr "Paket"

#: templates/debug_toolbar/panels/versions.html:11
msgid "Name"
msgstr "Name"

#: templates/debug_toolbar/panels/versions.html:12
msgid "Version"
msgstr "Version"

#: templates/debug_toolbar/redirect.html:10
msgid "Location:"
msgstr "Ziel:"

#: templates/debug_toolbar/redirect.html:12
msgid ""
"The Django Debug Toolbar has intercepted a redirect to the above URL for "
"debug viewing purposes. You can click the above link to continue with the "
"redirect as normal."
msgstr ""
"Die Django Debug Toolbar hat eine Weiterleitung an die obenstehende URL zur "
"weiteren Überprüfung abgefangen. Klicken Sie den Link, um wie gewohnt "
"weitergeleitet zu werden."

#: views.py:16
msgid ""
"Data for this panel isn't available anymore. Please reload the page and "
"retry."
msgstr ""
"Die Daten für dieses Panel sind nicht mehr verfügbar. Bitte laden Sie die "
"Seite neu."
