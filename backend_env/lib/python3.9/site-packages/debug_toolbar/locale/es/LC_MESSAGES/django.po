# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
#
# Translators:
# <AUTHOR> <EMAIL>, 2014
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2013-2014,2020
# <AUTHOR> <EMAIL>, 2013
# <PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: Django Debug Toolbar\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-20 17:23+0100\n"
"PO-Revision-Date: 2021-10-01 11:10+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (http://www.transifex.com/django-debug-toolbar/django-"
"debug-toolbar/language/es/)\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: apps.py:15
msgid "Debug Toolbar"
msgstr "Barra de herramientas de Depuración"

#: panels/cache.py:180
msgid "Cache"
msgstr "Cache"

#: panels/cache.py:186
#, python-format
msgid "%(cache_calls)d call in %(time).2fms"
msgid_plural "%(cache_calls)d calls in %(time).2fms"
msgstr[0] "%(cache_calls)d llamada en %(time).2fms"
msgstr[1] "%(cache_calls)d llamadas en %(time).2fms"

#: panels/cache.py:195
#, python-format
msgid "Cache calls from %(count)d backend"
msgid_plural "Cache calls from %(count)d backends"
msgstr[0] "%(count)d llamadas al Cache desde el backend"
msgstr[1] "%(count)d llamadas al Caché desde backends"

#: panels/headers.py:31
msgid "Headers"
msgstr "Encabezados"

#: panels/history/panel.py:18 panels/history/panel.py:19
msgid "History"
msgstr "Historial"

#: panels/profiling.py:140
msgid "Profiling"
msgstr "Análisis de rendimiento"

#: panels/redirects.py:14
msgid "Intercept redirects"
msgstr "Interceptar re-direcionamiento"

#: panels/request.py:16
msgid "Request"
msgstr "Petición"

#: panels/request.py:36
msgid "<no view>"
msgstr "<sin vista>"

#: panels/request.py:53
msgid "<unavailable>"
msgstr "<no disponible>"

#: panels/settings.py:17
msgid "Settings"
msgstr "Configuraciones"

#: panels/settings.py:20
#, python-format
msgid "Settings from %s"
msgstr "Valores procedentes de %s"

#: panels/signals.py:57
#, python-format
msgid "%(num_receivers)d receiver of 1 signal"
msgid_plural "%(num_receivers)d receivers of 1 signal"
msgstr[0] "%(num_receivers)d receptor de 1 señal"
msgstr[1] "%(num_receivers)d receptores de 1 señal"

#: panels/signals.py:62
#, python-format
msgid "%(num_receivers)d receiver of %(num_signals)d signals"
msgid_plural "%(num_receivers)d receivers of %(num_signals)d signals"
msgstr[0] "%(num_receivers)d receptor de %(num_signals)d señales"
msgstr[1] "%(num_receivers)d receptores de %(num_signals)d señales"

#: panels/signals.py:67
msgid "Signals"
msgstr "Señales"

#: panels/sql/panel.py:23
msgid "Autocommit"
msgstr "Autocommit"

#: panels/sql/panel.py:24
msgid "Read uncommitted"
msgstr "Leer cambios tentativos"

#: panels/sql/panel.py:25
msgid "Read committed"
msgstr "Leer cambios permanentes"

#: panels/sql/panel.py:26
msgid "Repeatable read"
msgstr "Lectura repetible"

#: panels/sql/panel.py:27
msgid "Serializable"
msgstr "Serializable"

#: panels/sql/panel.py:39
msgid "Idle"
msgstr "Inactivo"

#: panels/sql/panel.py:40
msgid "Active"
msgstr "Activo"

#: panels/sql/panel.py:41
msgid "In transaction"
msgstr "En transacción"

#: panels/sql/panel.py:42
msgid "In error"
msgstr "En error"

#: panels/sql/panel.py:43
msgid "Unknown"
msgstr "Desconocido"

#: panels/sql/panel.py:130
msgid "SQL"
msgstr "SQL"

#: panels/sql/panel.py:135
#, python-format
msgid "%(query_count)d query in %(sql_time).2fms"
msgid_plural "%(query_count)d queries in %(sql_time).2fms"
msgstr[0] ""
msgstr[1] ""

#: panels/sql/panel.py:147
#, python-format
msgid "SQL queries from %(count)d connection"
msgid_plural "SQL queries from %(count)d connections"
msgstr[0] ""
msgstr[1] ""

#: panels/staticfiles.py:84
#, python-format
msgid "Static files (%(num_found)s found, %(num_used)s used)"
msgstr "Archivos estáticos (%(num_found)s encontrados, %(num_used)s en uso)"

#: panels/staticfiles.py:105
msgid "Static files"
msgstr "Archivos estáticos"

#: panels/staticfiles.py:111
#, python-format
msgid "%(num_used)s file used"
msgid_plural "%(num_used)s files used"
msgstr[0] "%(num_used)s archivo usado"
msgstr[1] "%(num_used)s archivos usados"

#: panels/templates/panel.py:143
msgid "Templates"
msgstr "Plantillas"

#: panels/templates/panel.py:148
#, python-format
msgid "Templates (%(num_templates)s rendered)"
msgstr "Plantillas (%(num_templates)s renderizadas)"

#: panels/templates/panel.py:180
msgid "No origin"
msgstr "Sin origen"

#: panels/timer.py:25
#, python-format
msgid "CPU: %(cum)0.2fms (%(total)0.2fms)"
msgstr "CPU: %(cum)0.2fms (%(total)0.2fms)"

#: panels/timer.py:30
#, python-format
msgid "Total: %0.2fms"
msgstr "Total: %0.2fms"

#: panels/timer.py:36 templates/debug_toolbar/panels/history.html:9
#: templates/debug_toolbar/panels/sql_explain.html:11
#: templates/debug_toolbar/panels/sql_profile.html:12
#: templates/debug_toolbar/panels/sql_select.html:11
msgid "Time"
msgstr "Tiempo"

#: panels/timer.py:44
msgid "User CPU time"
msgstr "Tiempo en CPU de usuario"

#: panels/timer.py:44
#, python-format
msgid "%(utime)0.3f msec"
msgstr "%(utime)0.3f mseg"

#: panels/timer.py:45
msgid "System CPU time"
msgstr "Tiempo en CPU del sistema"

#: panels/timer.py:45
#, python-format
msgid "%(stime)0.3f msec"
msgstr "%(stime)0.3f mseg"

#: panels/timer.py:46
msgid "Total CPU time"
msgstr "Tiempo total de CPU"

#: panels/timer.py:46
#, python-format
msgid "%(total)0.3f msec"
msgstr "%(total)0.3f mseg"

#: panels/timer.py:47
msgid "Elapsed time"
msgstr "Tiempo transcurrido"

#: panels/timer.py:47
#, python-format
msgid "%(total_time)0.3f msec"
msgstr "%(total_time)0.3f mseg"

#: panels/timer.py:49
msgid "Context switches"
msgstr "Cambios de contexto"

#: panels/timer.py:50
#, python-format
msgid "%(vcsw)d voluntary, %(ivcsw)d involuntary"
msgstr "%(vcsw)d voluntario, %(ivcsw)d involuntario"

#: panels/versions.py:19
msgid "Versions"
msgstr "Versiones"

#: templates/debug_toolbar/base.html:22
msgid "Hide toolbar"
msgstr "Ocutar barra de herramientas"

#: templates/debug_toolbar/base.html:22
msgid "Hide"
msgstr "Ocultar"

#: templates/debug_toolbar/base.html:29
msgid "Show toolbar"
msgstr "Mostrar barra de herramientas"

#: templates/debug_toolbar/includes/panel_button.html:4
msgid "Disable for next and successive requests"
msgstr "Deshabilitar para el próximo y sucesivos peticiones"

#: templates/debug_toolbar/includes/panel_button.html:4
msgid "Enable for next and successive requests"
msgstr "Habilitar para el próximo y sucesivos peticiones"

#: templates/debug_toolbar/panels/cache.html:2
msgid "Summary"
msgstr "Resúmen"

#: templates/debug_toolbar/panels/cache.html:6
msgid "Total calls"
msgstr "Llamadas totales"

#: templates/debug_toolbar/panels/cache.html:7
msgid "Total time"
msgstr "Tiempo total"

#: templates/debug_toolbar/panels/cache.html:8
msgid "Cache hits"
msgstr "Aciertos de caché"

#: templates/debug_toolbar/panels/cache.html:9
msgid "Cache misses"
msgstr "Errores de caché"

#: templates/debug_toolbar/panels/cache.html:21
msgid "Commands"
msgstr "Comandos"

#: templates/debug_toolbar/panels/cache.html:39
msgid "Calls"
msgstr "Llamadas"

#: templates/debug_toolbar/panels/cache.html:43
#: templates/debug_toolbar/panels/sql.html:36
msgid "Time (ms)"
msgstr "Tiempo (ms)"

#: templates/debug_toolbar/panels/cache.html:44
msgid "Type"
msgstr "Tipo"

#: templates/debug_toolbar/panels/cache.html:45
#: templates/debug_toolbar/panels/request.html:8
msgid "Arguments"
msgstr "Argumentos"

#: templates/debug_toolbar/panels/cache.html:46
#: templates/debug_toolbar/panels/request.html:9
msgid "Keyword arguments"
msgstr "Argumentos por palabra clave"

#: templates/debug_toolbar/panels/cache.html:47
msgid "Backend"
msgstr "Backend"

#: templates/debug_toolbar/panels/headers.html:3
msgid "Request headers"
msgstr "Encabezados de peticiones"

#: templates/debug_toolbar/panels/headers.html:8
#: templates/debug_toolbar/panels/headers.html:27
#: templates/debug_toolbar/panels/headers.html:48
msgid "Key"
msgstr "Clave"

#: templates/debug_toolbar/panels/headers.html:9
#: templates/debug_toolbar/panels/headers.html:28
#: templates/debug_toolbar/panels/headers.html:49
#: templates/debug_toolbar/panels/history_tr.html:23
#: templates/debug_toolbar/panels/request_variables.html:12
#: templates/debug_toolbar/panels/settings.html:6
#: templates/debug_toolbar/panels/timer.html:11
msgid "Value"
msgstr "Valor"

#: templates/debug_toolbar/panels/headers.html:22
msgid "Response headers"
msgstr "Encabezados de respuesta"

#: templates/debug_toolbar/panels/headers.html:41
msgid "WSGI environ"
msgstr "Entorno WSGI"

#: templates/debug_toolbar/panels/headers.html:43
msgid ""
"Since the WSGI environ inherits the environment of the server, only a "
"significant subset is shown below."
msgstr ""
"Ya que el entorno WSGI hereda el entorno del servidor, solo un subconjunto "
"significativo es mostrado más abajo."

#: templates/debug_toolbar/panels/history.html:10
msgid "Method"
msgstr "Método"

#: templates/debug_toolbar/panels/history.html:11
#: templates/debug_toolbar/panels/staticfiles.html:43
msgid "Path"
msgstr "Ruta"

#: templates/debug_toolbar/panels/history.html:12
msgid "Request Variables"
msgstr "Variables de la petición"

#: templates/debug_toolbar/panels/history.html:13
msgid "Status"
msgstr "Estado"

#: templates/debug_toolbar/panels/history.html:14
#: templates/debug_toolbar/panels/sql.html:37
msgid "Action"
msgstr "Acción"

#: templates/debug_toolbar/panels/history_tr.html:22
#: templates/debug_toolbar/panels/request_variables.html:11
msgid "Variable"
msgstr "Variable"

#: templates/debug_toolbar/panels/profiling.html:5
msgid "Call"
msgstr "Llamar"

#: templates/debug_toolbar/panels/profiling.html:6
msgid "CumTime"
msgstr "TiempoAcum"

#: templates/debug_toolbar/panels/profiling.html:7
#: templates/debug_toolbar/panels/profiling.html:9
msgid "Per"
msgstr "Por"

#: templates/debug_toolbar/panels/profiling.html:8
msgid "TotTime"
msgstr "TiempoTot"

#: templates/debug_toolbar/panels/profiling.html:10
msgid "Count"
msgstr "Contar"

#: templates/debug_toolbar/panels/request.html:3
msgid "View information"
msgstr "Información de Vista"

#: templates/debug_toolbar/panels/request.html:7
msgid "View function"
msgstr "Función vista"

#: templates/debug_toolbar/panels/request.html:10
msgid "URL name"
msgstr "Nombre de dirección URL"

#: templates/debug_toolbar/panels/request.html:24
msgid "Cookies"
msgstr "Cookies"

#: templates/debug_toolbar/panels/request.html:27
msgid "No cookies"
msgstr "Sin cookies"

#: templates/debug_toolbar/panels/request.html:31
msgid "Session data"
msgstr "Datos de sesión"

#: templates/debug_toolbar/panels/request.html:34
msgid "No session data"
msgstr "Sin datos de sesión"

#: templates/debug_toolbar/panels/request.html:38
msgid "GET data"
msgstr "Datos del GET"

#: templates/debug_toolbar/panels/request.html:41
msgid "No GET data"
msgstr "Sin datos GET"

#: templates/debug_toolbar/panels/request.html:45
msgid "POST data"
msgstr "Datos del POST"

#: templates/debug_toolbar/panels/request.html:48
msgid "No POST data"
msgstr "Sin datos POST"

#: templates/debug_toolbar/panels/settings.html:5
msgid "Setting"
msgstr "Configuración"

#: templates/debug_toolbar/panels/signals.html:5
msgid "Signal"
msgstr "Señal"

#: templates/debug_toolbar/panels/signals.html:6
msgid "Receivers"
msgstr "Receptores"

#: templates/debug_toolbar/panels/sql.html:6
#, python-format
msgid "%(num)s query"
msgid_plural "%(num)s queries"
msgstr[0] "%(num)s consulta"
msgstr[1] "%(num)s consultas"

#: templates/debug_toolbar/panels/sql.html:8
#, python-format
msgid ""
"including <abbr title=\"Similar queries are queries with the same SQL, but "
"potentially different parameters.\">%(count)s similar</abbr>"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:12
#, python-format
msgid ""
"and <abbr title=\"Duplicate queries are identical to each other: they "
"execute exactly the same SQL and parameters.\">%(dupes)s duplicates</abbr>"
msgstr ""
"y <abbr title=\"Las consultas repetidas son idénticas: ejecutan el mismo SQL "
"con los mismos parámetros. \">%(dupes)s repetidos</abbr>"

#: templates/debug_toolbar/panels/sql.html:34
msgid "Query"
msgstr "Query"

#: templates/debug_toolbar/panels/sql.html:35
#: templates/debug_toolbar/panels/timer.html:36
msgid "Timeline"
msgstr "Línea de tiempo"

#: templates/debug_toolbar/panels/sql.html:52
#, python-format
msgid "%(count)s similar queries."
msgstr "%(count)s consultas similares. "

#: templates/debug_toolbar/panels/sql.html:58
#, python-format
msgid "Duplicated %(dupes)s times."
msgstr "Repetidas %(dupes)s veces."

#: templates/debug_toolbar/panels/sql.html:95
msgid "Connection:"
msgstr "Conexión:"

#: templates/debug_toolbar/panels/sql.html:97
msgid "Isolation level:"
msgstr "Nivel de aislamiento:"

#: templates/debug_toolbar/panels/sql.html:100
msgid "Transaction status:"
msgstr "Estado de la transacción:"

#: templates/debug_toolbar/panels/sql.html:114
msgid "(unknown)"
msgstr "(desconocido)"

#: templates/debug_toolbar/panels/sql.html:123
msgid "No SQL queries were recorded during this request."
msgstr "No se registraron consultas SQL durante ésta petición."

#: templates/debug_toolbar/panels/sql_explain.html:4
msgid "SQL explained"
msgstr "SQL explicado"

#: templates/debug_toolbar/panels/sql_explain.html:9
#: templates/debug_toolbar/panels/sql_profile.html:10
#: templates/debug_toolbar/panels/sql_select.html:9
msgid "Executed SQL"
msgstr "SQL Ejecutado"

#: templates/debug_toolbar/panels/sql_explain.html:13
#: templates/debug_toolbar/panels/sql_profile.html:14
#: templates/debug_toolbar/panels/sql_select.html:13
msgid "Database"
msgstr "Base de datos"

#: templates/debug_toolbar/panels/sql_profile.html:4
msgid "SQL profiled"
msgstr "SQL analizado"

#: templates/debug_toolbar/panels/sql_profile.html:37
msgid "Error"
msgstr "Error"

#: templates/debug_toolbar/panels/sql_select.html:4
msgid "SQL selected"
msgstr "SQL seleccionado"

#: templates/debug_toolbar/panels/sql_select.html:36
msgid "Empty set"
msgstr "Establecer Vacío"

#: templates/debug_toolbar/panels/staticfiles.html:3
msgid "Static file path"
msgid_plural "Static file paths"
msgstr[0] "Ruta a archivos estático"
msgstr[1] "Rutas a archivos estáticos"

#: templates/debug_toolbar/panels/staticfiles.html:7
#, python-format
msgid "(prefix %(prefix)s)"
msgstr "(prefijo %(prefix)s)"

#: templates/debug_toolbar/panels/staticfiles.html:11
#: templates/debug_toolbar/panels/staticfiles.html:22
#: templates/debug_toolbar/panels/staticfiles.html:34
#: templates/debug_toolbar/panels/templates.html:10
#: templates/debug_toolbar/panels/templates.html:30
#: templates/debug_toolbar/panels/templates.html:47
msgid "None"
msgstr "Ninguno"

#: templates/debug_toolbar/panels/staticfiles.html:14
msgid "Static file app"
msgid_plural "Static file apps"
msgstr[0] "Aplicación a archivos estáticos"
msgstr[1] "Aplicaciones de archivos estáticos"

#: templates/debug_toolbar/panels/staticfiles.html:25
msgid "Static file"
msgid_plural "Static files"
msgstr[0] "Archivo estático"
msgstr[1] "Archivos estáticos"

#: templates/debug_toolbar/panels/staticfiles.html:39
#, python-format
msgid "%(payload_count)s file"
msgid_plural "%(payload_count)s files"
msgstr[0] "%(payload_count)s archivo"
msgstr[1] "%(payload_count)s archivos"

#: templates/debug_toolbar/panels/staticfiles.html:44
msgid "Location"
msgstr "Ubicación"

#: templates/debug_toolbar/panels/template_source.html:4
msgid "Template source:"
msgstr "Fuente de plantilla:"

#: templates/debug_toolbar/panels/templates.html:2
msgid "Template path"
msgid_plural "Template paths"
msgstr[0] "Ruta de plantilla"
msgstr[1] "Rutas de plantillas"

#: templates/debug_toolbar/panels/templates.html:13
msgid "Template"
msgid_plural "Templates"
msgstr[0] "Plantilla"
msgstr[1] "Plantillas"

#: templates/debug_toolbar/panels/templates.html:22
#: templates/debug_toolbar/panels/templates.html:40
msgid "Toggle context"
msgstr "Mostrar/Ocultar contexto"

#: templates/debug_toolbar/panels/templates.html:33
msgid "Context processor"
msgid_plural "Context processors"
msgstr[0] "Procesador de contexto"
msgstr[1] "Procesadores de contexto"

#: templates/debug_toolbar/panels/timer.html:2
msgid "Resource usage"
msgstr "Uso de recursos"

#: templates/debug_toolbar/panels/timer.html:10
msgid "Resource"
msgstr "Recurso"

#: templates/debug_toolbar/panels/timer.html:26
msgid "Browser timing"
msgstr "Distribución de tiempos de navegador"

#: templates/debug_toolbar/panels/timer.html:35
msgid "Timing attribute"
msgstr "Atributo de tiempo"

#: templates/debug_toolbar/panels/timer.html:37
msgid "Milliseconds since navigation start (+length)"
msgstr "Milisegundos desde inicio de la navegación (+longitud)"

#: templates/debug_toolbar/panels/versions.html:10
msgid "Package"
msgstr "Paquete"

#: templates/debug_toolbar/panels/versions.html:11
msgid "Name"
msgstr "Nombre"

#: templates/debug_toolbar/panels/versions.html:12
msgid "Version"
msgstr "Versión"

#: templates/debug_toolbar/redirect.html:10
msgid "Location:"
msgstr "Ubicación:"

#: templates/debug_toolbar/redirect.html:12
msgid ""
"The Django Debug Toolbar has intercepted a redirect to the above URL for "
"debug viewing purposes. You can click the above link to continue with the "
"redirect as normal."
msgstr ""
"El Django Debug Toolbar ha interceptado un re-direccionamiento a la "
"dirección de Internet mostrada arriba, con el propósito de inspeccionarla. "
"Usted puede hacer clic en el vínculo de arriba para continuar con el re-"
"direccionamiento normalmente."

#: views.py:16
msgid ""
"Data for this panel isn't available anymore. Please reload the page and "
"retry."
msgstr ""
"La información de este panel ya no se encuentra disponible. Por favor "
"recargue la página y pruebe nuevamente."
