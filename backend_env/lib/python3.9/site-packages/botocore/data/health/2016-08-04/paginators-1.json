{"pagination": {"DescribeAffectedEntities": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "entities"}, "DescribeEventAggregates": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "eventAggregates"}, "DescribeEvents": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "events"}, "DescribeEventTypes": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "eventTypes"}, "DescribeAffectedAccountsForOrganization": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "affectedAccounts", "non_aggregate_keys": ["eventScopeCode"]}, "DescribeAffectedEntitiesForOrganization": {"input_token": "nextToken", "limit_key": "maxResults", "non_aggregate_keys": ["failedSet"], "output_token": "nextToken", "result_key": "entities"}, "DescribeEventsForOrganization": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "events"}}}