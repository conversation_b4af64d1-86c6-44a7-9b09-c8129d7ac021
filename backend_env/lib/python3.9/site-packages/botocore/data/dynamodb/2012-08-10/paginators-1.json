{"pagination": {"ListBackups": {"input_token": "ExclusiveStartBackupArn", "output_token": "LastEvaluatedBackupArn", "limit_key": "Limit", "result_key": "BackupSummaries"}, "ListTables": {"input_token": "ExclusiveStartTableName", "output_token": "LastEvaluatedTableName", "limit_key": "Limit", "result_key": "TableNames"}, "Query": {"input_token": "ExclusiveStartKey", "output_token": "LastEvaluatedKey", "limit_key": "Limit", "result_key": ["Items", "Count", "ScannedCount"], "non_aggregate_keys": ["ConsumedCapacity"]}, "Scan": {"input_token": "ExclusiveStartKey", "output_token": "LastEvaluatedKey", "limit_key": "Limit", "result_key": ["Items", "Count", "ScannedCount"], "non_aggregate_keys": ["ConsumedCapacity"]}, "ListTagsOfResource": {"input_token": "NextToken", "output_token": "NextToken", "result_key": "Tags"}}}