{"pagination": {"GetOutpostInstanceTypes": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "InstanceTypes"}, "ListAssets": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Assets"}, "ListCatalogItems": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "CatalogItems"}, "ListOrders": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Orders"}, "ListOutposts": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Outposts"}, "ListSites": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Sites"}}}