{"pagination": {"ListMultipartUploads": {"limit_key": "MaxUploads", "more_results": "IsTruncated", "output_token": ["NextKeyMarker", "NextUploadIdMarker"], "input_token": ["<PERSON><PERSON><PERSON><PERSON>", "UploadIdMarker"], "result_key": ["Uploads", "CommonPrefixes"]}, "ListObjectVersions": {"more_results": "IsTruncated", "limit_key": "<PERSON><PERSON><PERSON><PERSON>", "output_token": ["NextKeyMarker", "NextVersionIdMarker"], "input_token": ["<PERSON><PERSON><PERSON><PERSON>", "VersionIdMarker"], "result_key": ["Versions", "DeleteMarkers", "CommonPrefixes"]}, "ListObjects": {"more_results": "IsTruncated", "limit_key": "<PERSON><PERSON><PERSON><PERSON>", "output_token": "NextMarker || Contents[-1].Key", "input_token": "<PERSON><PERSON>", "result_key": ["Contents", "CommonPrefixes"]}, "ListObjectsV2": {"more_results": "IsTruncated", "limit_key": "<PERSON><PERSON><PERSON><PERSON>", "output_token": "NextContinuationToken", "input_token": "ContinuationToken", "result_key": ["Contents", "CommonPrefixes"]}, "ListParts": {"more_results": "IsTruncated", "limit_key": "MaxParts", "output_token": "NextPartNumberMarker", "input_token": "PartNumberMarker", "result_key": "Parts"}}}