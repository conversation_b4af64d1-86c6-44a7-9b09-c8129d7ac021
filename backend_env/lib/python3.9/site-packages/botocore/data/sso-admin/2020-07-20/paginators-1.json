{"pagination": {"ListAccountAssignmentCreationStatus": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "AccountAssignmentsCreationStatus"}, "ListAccountAssignmentDeletionStatus": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "AccountAssignmentsDeletionStatus"}, "ListAccountAssignments": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "AccountAssignments"}, "ListAccountsForProvisionedPermissionSet": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "AccountIds"}, "ListInstances": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Instances"}, "ListManagedPoliciesInPermissionSet": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "AttachedManagedPolicies"}, "ListPermissionSetProvisioningStatus": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "PermissionSetsProvisioningStatus"}, "ListPermissionSets": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "PermissionSets"}, "ListPermissionSetsProvisionedToAccount": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "PermissionSets"}, "ListTagsForResource": {"input_token": "NextToken", "output_token": "NextToken", "result_key": "Tags"}, "ListCustomerManagedPolicyReferencesInPermissionSet": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "CustomerManagedPolicyReferences"}, "ListAccountAssignmentsForPrincipal": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "AccountAssignments"}, "ListApplicationAccessScopes": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "<PERSON><PERSON><PERSON>"}, "ListApplicationAssignments": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ApplicationAssignments"}, "ListApplicationAssignmentsForPrincipal": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ApplicationAssignments"}, "ListApplicationAuthenticationMethods": {"input_token": "NextToken", "output_token": "NextToken", "result_key": "AuthenticationMethods"}, "ListApplicationGrants": {"input_token": "NextToken", "output_token": "NextToken", "result_key": "<PERSON>s"}, "ListApplicationProviders": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ApplicationProviders"}, "ListApplications": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Applications"}, "ListTrustedTokenIssuers": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "TrustedTokenIssuers"}}}