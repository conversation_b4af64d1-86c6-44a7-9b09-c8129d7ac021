{"pagination": {"GetDataLakeSources": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "dataLakeSources"}, "ListDataLakeExceptions": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "exceptions"}, "ListLogSources": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "sources"}, "ListSubscribers": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "subscribers"}}}