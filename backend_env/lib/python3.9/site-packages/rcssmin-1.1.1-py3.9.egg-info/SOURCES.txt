LICENSE
MANIFEST.in
README.md
cext.h
rcssmin.c
rcssmin.py
setup.cfg
setup.py
bench/LICENSE.cssmin
bench/__init__.py
bench/bootstrap.css
bench/bootstrap.min.css
bench/cssmin.py
bench/main.py
bench/prepare.sh
bench/report.pickle
bench/report.sh
bench/run.sh
bench/tox.ini
bench/wikipedia.css
bench/wikipedia.min.css
bench/write.py
docs/BENCHMARKS
docs/CHANGES
docs/CLASSIFIERS
docs/DESCRIPTION
docs/KEYWORDS
docs/PROVIDES
docs/SUMMARY
docs/_userdoc/benchmark.txt
docs/_userdoc/conf.py
docs/_userdoc/index.txt
docs/_userdoc/_static/ci.css
rcssmin.egg-info/PKG-INFO
rcssmin.egg-info/SOURCES.txt
rcssmin.egg-info/dependency_links.txt
rcssmin.egg-info/not-zip-safe
rcssmin.egg-info/top_level.txt
tests/__init__.py
tests/_util.py
tests/coverage.txt
tests/requirements.txt
tests/test_ctype.py
tests/test_main.py
tests/test_yui.py
tests/main/atgroup_00.css
tests/main/atgroup_01.css
tests/main/atgroup_02.css
tests/main/atgroup_03.css
tests/main/atgroup_04.css
tests/main/atgroup_05.css
tests/main/atgroup_06.css
tests/main/atgroup_07.css
tests/main/atgroup_08.css
tests/main/atgroup_09.css
tests/main/atgroup_10.css
tests/main/atgroup_11.css
tests/main/calc_00.css
tests/main/calc_01.css
tests/main/calc_02.css
tests/main/calc_03.css
tests/main/calc_04.css
tests/main/comment_00.css
tests/main/comment_01.css
tests/main/comment_02.css
tests/main/comment_03.css
tests/main/comment_04.css
tests/main/comment_05.css
tests/main/comment_06.css
tests/main/escape_00.css
tests/main/escape_01.css
tests/main/escape_02.css
tests/main/escape_03.css
tests/main/escape_04.css
tests/main/escape_05.css
tests/main/escape_06.css
tests/main/first_00.css
tests/main/first_01.css
tests/main/first_02.css
tests/main/semicolon_01.css
tests/main/url_00.css
tests/main/url_01.css
tests/main/url_02.css
tests/main/url_03.css
tests/main/url_04.css
tests/main/url_05.css
tests/main/url_06.css
tests/main/url_07.css
tests/main/url_08.css
tests/main/url_09.css
tests/main/url_10.css
tests/main/out/atgroup_00.out
tests/main/out/atgroup_00.out.b
tests/main/out/atgroup_01.out
tests/main/out/atgroup_01.out.b
tests/main/out/atgroup_02.out
tests/main/out/atgroup_02.out.b
tests/main/out/atgroup_03.out
tests/main/out/atgroup_03.out.b
tests/main/out/atgroup_04.out
tests/main/out/atgroup_04.out.b
tests/main/out/atgroup_05.out
tests/main/out/atgroup_05.out.b
tests/main/out/atgroup_06.out
tests/main/out/atgroup_06.out.b
tests/main/out/atgroup_07.out
tests/main/out/atgroup_07.out.b
tests/main/out/atgroup_08.out
tests/main/out/atgroup_08.out.b
tests/main/out/atgroup_09.out
tests/main/out/atgroup_09.out.b
tests/main/out/atgroup_10.out
tests/main/out/atgroup_10.out.b
tests/main/out/atgroup_11.out
tests/main/out/atgroup_11.out.b
tests/main/out/calc_00.out
tests/main/out/calc_00.out.b
tests/main/out/calc_01.out
tests/main/out/calc_01.out.b
tests/main/out/calc_02.out
tests/main/out/calc_02.out.b
tests/main/out/calc_03.out
tests/main/out/calc_03.out.b
tests/main/out/calc_04.out
tests/main/out/calc_04.out.b
tests/main/out/comment_00.out
tests/main/out/comment_00.out.b
tests/main/out/comment_01.out
tests/main/out/comment_01.out.b
tests/main/out/comment_02.out
tests/main/out/comment_02.out.b
tests/main/out/comment_03.out
tests/main/out/comment_03.out.b
tests/main/out/comment_04.out
tests/main/out/comment_04.out.b
tests/main/out/comment_05.out
tests/main/out/comment_05.out.b
tests/main/out/comment_06.out
tests/main/out/comment_06.out.b
tests/main/out/escape_00.out
tests/main/out/escape_00.out.b
tests/main/out/escape_01.out
tests/main/out/escape_01.out.b
tests/main/out/escape_02.out
tests/main/out/escape_02.out.b
tests/main/out/escape_03.out
tests/main/out/escape_03.out.b
tests/main/out/escape_04.out
tests/main/out/escape_04.out.b
tests/main/out/escape_05.out
tests/main/out/escape_05.out.b
tests/main/out/escape_06.out
tests/main/out/escape_06.out.b
tests/main/out/first_00.out
tests/main/out/first_00.out.b
tests/main/out/first_01.out
tests/main/out/first_01.out.b
tests/main/out/first_02.out
tests/main/out/first_02.out.b
tests/main/out/semicolon_01.out
tests/main/out/semicolon_01.out.b
tests/main/out/url_00.out
tests/main/out/url_00.out.b
tests/main/out/url_01.out
tests/main/out/url_01.out.b
tests/main/out/url_02.out
tests/main/out/url_02.out.b
tests/main/out/url_03.out
tests/main/out/url_03.out.b
tests/main/out/url_04.out
tests/main/out/url_04.out.b
tests/main/out/url_05.out
tests/main/out/url_05.out.b
tests/main/out/url_06.out
tests/main/out/url_06.out.b
tests/main/out/url_07.out
tests/main/out/url_07.out.b
tests/main/out/url_08.out
tests/main/out/url_08.out.b
tests/main/out/url_09.out
tests/main/out/url_09.out.b
tests/main/out/url_10.out
tests/main/out/url_10.out.b
tests/yui/README
tests/yui/background-position.css
tests/yui/background-position.css.min
tests/yui/border-none.css
tests/yui/border-none.css.min
tests/yui/box-model-hack.css
tests/yui/box-model-hack.css.min
tests/yui/bug2527974.css
tests/yui/bug2527974.css.min
tests/yui/bug2527991.css
tests/yui/bug2527991.css.min
tests/yui/bug2527998.css
tests/yui/bug2527998.css.min
tests/yui/bug2528034.css
tests/yui/bug2528034.css.min
tests/yui/charset-media.css
tests/yui/charset-media.css.min
tests/yui/color-simple.css
tests/yui/color-simple.css.min
tests/yui/color.css
tests/yui/color.css.min
tests/yui/comment.css
tests/yui/comment.css.min
tests/yui/concat-charset.css
tests/yui/concat-charset.css.min
tests/yui/dataurl-base64-doublequotes.css
tests/yui/dataurl-base64-doublequotes.css.min
tests/yui/dataurl-base64-eof.css
tests/yui/dataurl-base64-eof.css.min
tests/yui/dataurl-base64-linebreakindata.css
tests/yui/dataurl-base64-linebreakindata.css.min
tests/yui/dataurl-base64-noquotes.css
tests/yui/dataurl-base64-noquotes.css.min
tests/yui/dataurl-base64-singlequotes.css
tests/yui/dataurl-base64-singlequotes.css.min
tests/yui/dataurl-base64-twourls.css
tests/yui/dataurl-base64-twourls.css.min
tests/yui/dataurl-dbquote-font.css
tests/yui/dataurl-dbquote-font.css.min
tests/yui/dataurl-nonbase64-doublequotes.css
tests/yui/dataurl-nonbase64-doublequotes.css.min
tests/yui/dataurl-nonbase64-noquotes.css
tests/yui/dataurl-nonbase64-noquotes.css.min
tests/yui/dataurl-nonbase64-singlequotes.css
tests/yui/dataurl-nonbase64-singlequotes.css.min
tests/yui/dataurl-noquote-multiline-font.css
tests/yui/dataurl-noquote-multiline-font.css.min
tests/yui/dataurl-realdata-doublequotes.css
tests/yui/dataurl-realdata-doublequotes.css.min
tests/yui/dataurl-realdata-noquotes.css
tests/yui/dataurl-realdata-noquotes.css.min
tests/yui/dataurl-realdata-singlequotes.css
tests/yui/dataurl-realdata-singlequotes.css.min
tests/yui/dataurl-realdata-yuiapp.css
tests/yui/dataurl-realdata-yuiapp.css.min
tests/yui/dataurl-singlequote-font.css
tests/yui/dataurl-singlequote-font.css.min
tests/yui/decimals.css
tests/yui/decimals.css.min
tests/yui/dollar-header.css
tests/yui/dollar-header.css.min
tests/yui/font-face.css
tests/yui/font-face.css.min
tests/yui/ie5mac.css
tests/yui/ie5mac.css.min
tests/yui/media-empty-class.css
tests/yui/media-empty-class.css.min
tests/yui/media-multi.css
tests/yui/media-multi.css.min
tests/yui/media-test.css
tests/yui/media-test.css.min
tests/yui/opacity-filter.css
tests/yui/opacity-filter.css.min
tests/yui/preserve-case.css
tests/yui/preserve-case.css.min
tests/yui/preserve-new-line.css
tests/yui/preserve-new-line.css.min
tests/yui/preserve-strings.css
tests/yui/preserve-strings.css.min
tests/yui/pseudo-first.css
tests/yui/pseudo-first.css.min
tests/yui/pseudo.css
tests/yui/pseudo.css.min
tests/yui/special-comments.css
tests/yui/special-comments.css.min
tests/yui/star-underscore-hacks.css
tests/yui/star-underscore-hacks.css.min
tests/yui/string-in-comment.css
tests/yui/string-in-comment.css.min
tests/yui/webkit-transform.css
tests/yui/webkit-transform.css.min
tests/yui/zeros.css
tests/yui/zeros.css.min
tests/yui/out/background-position.out
tests/yui/out/background-position.out.b
tests/yui/out/border-none.out
tests/yui/out/border-none.out.b
tests/yui/out/box-model-hack.out
tests/yui/out/box-model-hack.out.b
tests/yui/out/bug2527974.out
tests/yui/out/bug2527974.out.b
tests/yui/out/bug2527991.out
tests/yui/out/bug2527991.out.b
tests/yui/out/bug2527998.out
tests/yui/out/bug2527998.out.b
tests/yui/out/bug2528034.out
tests/yui/out/bug2528034.out.b
tests/yui/out/charset-media.out
tests/yui/out/charset-media.out.b
tests/yui/out/color-simple.out
tests/yui/out/color-simple.out.b
tests/yui/out/color.out
tests/yui/out/color.out.b
tests/yui/out/comment.out
tests/yui/out/comment.out.b
tests/yui/out/concat-charset.out
tests/yui/out/concat-charset.out.b
tests/yui/out/dataurl-base64-doublequotes.out
tests/yui/out/dataurl-base64-doublequotes.out.b
tests/yui/out/dataurl-base64-eof.out
tests/yui/out/dataurl-base64-eof.out.b
tests/yui/out/dataurl-base64-linebreakindata.out
tests/yui/out/dataurl-base64-linebreakindata.out.b
tests/yui/out/dataurl-base64-noquotes.out
tests/yui/out/dataurl-base64-noquotes.out.b
tests/yui/out/dataurl-base64-singlequotes.out
tests/yui/out/dataurl-base64-singlequotes.out.b
tests/yui/out/dataurl-base64-twourls.out
tests/yui/out/dataurl-base64-twourls.out.b
tests/yui/out/dataurl-dbquote-font.out
tests/yui/out/dataurl-dbquote-font.out.b
tests/yui/out/dataurl-nonbase64-doublequotes.out
tests/yui/out/dataurl-nonbase64-doublequotes.out.b
tests/yui/out/dataurl-nonbase64-noquotes.out
tests/yui/out/dataurl-nonbase64-noquotes.out.b
tests/yui/out/dataurl-nonbase64-singlequotes.out
tests/yui/out/dataurl-nonbase64-singlequotes.out.b
tests/yui/out/dataurl-noquote-multiline-font.out
tests/yui/out/dataurl-noquote-multiline-font.out.b
tests/yui/out/dataurl-realdata-doublequotes.out
tests/yui/out/dataurl-realdata-doublequotes.out.b
tests/yui/out/dataurl-realdata-noquotes.out
tests/yui/out/dataurl-realdata-noquotes.out.b
tests/yui/out/dataurl-realdata-singlequotes.out
tests/yui/out/dataurl-realdata-singlequotes.out.b
tests/yui/out/dataurl-realdata-yuiapp.out
tests/yui/out/dataurl-realdata-yuiapp.out.b
tests/yui/out/dataurl-singlequote-font.out
tests/yui/out/dataurl-singlequote-font.out.b
tests/yui/out/decimals.out
tests/yui/out/decimals.out.b
tests/yui/out/dollar-header.out
tests/yui/out/dollar-header.out.b
tests/yui/out/font-face.out
tests/yui/out/font-face.out.b
tests/yui/out/ie5mac.out
tests/yui/out/ie5mac.out.b
tests/yui/out/media-empty-class.out
tests/yui/out/media-empty-class.out.b
tests/yui/out/media-multi.out
tests/yui/out/media-multi.out.b
tests/yui/out/media-test.out
tests/yui/out/media-test.out.b
tests/yui/out/opacity-filter.out
tests/yui/out/opacity-filter.out.b
tests/yui/out/preserve-case.out
tests/yui/out/preserve-case.out.b
tests/yui/out/preserve-new-line.out
tests/yui/out/preserve-new-line.out.b
tests/yui/out/preserve-strings.out
tests/yui/out/preserve-strings.out.b
tests/yui/out/pseudo-first.out
tests/yui/out/pseudo-first.out.b
tests/yui/out/pseudo.out
tests/yui/out/pseudo.out.b
tests/yui/out/special-comments.out
tests/yui/out/special-comments.out.b
tests/yui/out/star-underscore-hacks.out
tests/yui/out/star-underscore-hacks.out.b
tests/yui/out/string-in-comment.out
tests/yui/out/string-in-comment.out.b
tests/yui/out/webkit-transform.out
tests/yui/out/webkit-transform.out.b
tests/yui/out/zeros.out
tests/yui/out/zeros.out.b