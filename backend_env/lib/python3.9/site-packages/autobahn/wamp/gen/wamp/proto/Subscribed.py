# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers
from flatbuffers.compat import import_numpy
np = import_numpy()

class Subscribed(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = Subscribed()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsSubscribed(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    # Subscribed
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # Subscribed
    def Session(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint64Flags, o + self._tab.Pos)
        return 0

    # Subscribed
    def Request(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint64Flags, o + self._tab.Pos)
        return 0

    # Subscribed
    def Subscription(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint64Flags, o + self._tab.Pos)
        return 0

def SubscribedStart(builder): builder.StartObject(3)
def Start(builder):
    return SubscribedStart(builder)
def SubscribedAddSession(builder, session): builder.PrependUint64Slot(0, session, 0)
def AddSession(builder, session):
    return SubscribedAddSession(builder, session)
def SubscribedAddRequest(builder, request): builder.PrependUint64Slot(1, request, 0)
def AddRequest(builder, request):
    return SubscribedAddRequest(builder, request)
def SubscribedAddSubscription(builder, subscription): builder.PrependUint64Slot(2, subscription, 0)
def AddSubscription(builder, subscription):
    return SubscribedAddSubscription(builder, subscription)
def SubscribedEnd(builder): return builder.EndObject()
def End(builder):
    return SubscribedEnd(builder)