{% extends "oauth2_provider/base.html" %}

{% load i18n %}
{% block content %}
    <div class="block-center">
        <h3 class="block-center-heading">{% trans "Are you sure to delete the application" %} {{ application.name }}?</h3>
        <form method="post" action="{% url 'oauth2_provider:delete' application.pk %}">
            {% csrf_token %}

            <div class="control-group">
                <div class="controls">
                    <a class="btn btn-large" href="{% url "oauth2_provider:list" %}">{% trans "Cancel" %}</a>
                    <input type="submit" class="btn btn-large btn-danger" name="allow" value="{% trans 'Delete' %}"/>
                </div>
            </div>
        </form>
    </div>
{% endblock content %}
