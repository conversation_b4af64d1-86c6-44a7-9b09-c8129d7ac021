{% load crispy_forms_field %}

{% if field.is_hidden %}
    {{ field }}
{% else %}
    <div id="div_{{ field.auto_id }}" class="mb-3{% if wrapper_class %} {{ wrapper_class }}{% endif %}{% if 'form-horizontal' in form_class %} row{% endif %}{% if form_group_wrapper_class %} {{ form_group_wrapper_class }}{% endif %}{% if form_show_errors and field.errors %} has-danger{% endif %}{% if field.css_classes %} {{ field.css_classes }}{% endif %}">

        {% if field.label and form_show_labels %}
            <label for="{{ field.id_for_label }}" class="{% if 'form-horizontal' in form_class %}col-form-label {% else %}form-label{% endif %}{{ label_class }}{% if field.field.required %} requiredField{% endif %}">
                {{ field.label }}{% if field.field.required %}<span class="asteriskField">*</span>{% endif %}
            </label>
        {% endif %}

        <div {% if field_class %}class="{{ field_class }}"{% endif %}>
            <div class="input-group{% if input_size %} {{ input_size }}{% endif %}">
                {# prepend #}
                {% if crispy_prepended_text %}
                    <span class="input-group-text">{{ crispy_prepended_text }}</span>
                {% endif %}

                {# input #}
                {% if field|is_select %}
                    {% if field.errors %}
                        {% crispy_field field 'class' 'form-select is-invalid' %}
                    {% else %}
                        {% crispy_field field 'class' 'form-select' %}
                    {% endif %}
                {% elif field.errors %}
                    {% crispy_field field 'class' 'form-control is-invalid' %}
                {% else %}
                    {% crispy_field field 'class' 'form-control' %}
                {% endif %}
                
                {# append #}
                {% if crispy_appended_text %}
                    <span class="input-group-text">{{ crispy_appended_text }}</span>
                {% endif %}
                {% if error_text_inline %}
                    {% include 'bootstrap5/layout/field_errors.html' %}
                {% else %}
                    {% include 'bootstrap5/layout/field_errors_block.html' %}
                {% endif %}
            </div>
        {% if not help_text_inline %}
            {% include 'bootstrap5/layout/help_text.html' %}
        {% endif %}
        </div>

    </div>
{% endif %}
