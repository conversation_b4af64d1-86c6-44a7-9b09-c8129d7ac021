{% for fieldset in form.fieldsets %}
    <fieldset class="fieldset-{{ forloop.counter }} {{ fieldset.classes }}">
        {% if fieldset.legend %}
            <legend>{{ fieldset.legend }}</legend>
        {% endif %}

        {% if fieldset.description %}
            <p class="description">{{ fieldset.description }}</p>
        {% endif %}

        {% for field in fieldset %}
            {% if field.is_hidden %}
                {{ field }}
            {% else %}
                {% include "bootstrap5/field.html" %}
            {% endif %}
        {% endfor %}
    {% if not forloop.last or not fieldset_open %}
        </fieldset>
    {% endif %}
{% endfor %}

