Metadata-Version: 2.1
Name: django-mptt
Version: 0.15.0
Summary: Utilities for implementing Modified Preorder Tree Traversal with your Django Models and working with trees of Model instances.
Project-URL: Homepage, https://github.com/django-mptt/django-mptt/
Author-email: <PERSON> <<EMAIL>>
License: MIT-License
License-File: LICENSE
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Framework :: Django
Classifier: Framework :: Django :: 3.2
Classifier: Framework :: Django :: 4.1
Classifier: Framework :: Django :: 4.2
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Requires-Python: >=3.9
Requires-Dist: django-js-asset
Provides-Extra: tests
Requires-Dist: coverage[toml]; extra == 'tests'
Requires-Dist: mock-django; extra == 'tests'
Description-Content-Type: text/x-rst

==========================================
**This project is currently unmaintained**
==========================================

Alternatives to django-mptt include:

* `django-treebeard <https://pypi.org/project/django-treebeard/>`_ includes a MPTT
  implementation (called nested set), but the state of maintenance is unclear.
* Maybe you do not need MPTT, especially when using newer databases. See
  `django-tree-queries <https://github.com/matthiask/django-tree-queries>`_ for an
  implementation using recursive Common Table Expressions (CTE). See the
  `announcement blog post <https://406.ch/writing/django-tree-queries/>`__.


===========
django-mptt
===========

Utilities for implementing Modified Preorder Tree Traversal with your
Django Models and working with trees of Model instances.

.. image:: https://secure.travis-ci.org/django-mptt/django-mptt.svg?branch=master
    :alt: Build Status
    :target: https://travis-ci.org/django-mptt/django-mptt

Project home: https://github.com/django-mptt/django-mptt/

Documentation: https://django-mptt.readthedocs.io/

Discussion group: https://groups.google.com/forum/#!forum/django-mptt-dev

What is Modified Preorder Tree Traversal?
=========================================

MPTT is a technique for storing hierarchical data in a database. The aim is to
make retrieval operations very efficient.

The trade-off for this efficiency is that performing inserts and moving
items around the tree is more involved, as there's some extra work
required to keep the tree structure in a good state at all times.

Here are a few articles about MPTT to whet your appetite and provide
details about how the technique itself works:

* `Trees in SQL`_
* `Storing Hierarchical Data in a Database`_
* `Managing Hierarchical Data in MySQL`_

.. _`Trees in SQL`: https://www.ibase.ru/files/articles/programming/dbmstrees/sqltrees.html
.. _`Storing Hierarchical Data in a Database`: https://www.sitepoint.com/hierarchical-data-database/
.. _`Managing Hierarchical Data in MySQL`: http://mikehillyer.com/articles/managing-hierarchical-data-in-mysql/

What is ``django-mptt``?
========================

``django-mptt`` is a reusable Django app which aims to make it easy for you
to use MPTT with your own Django models.

It takes care of the details of managing a database table as a tree
structure and provides tools for working with trees of model instances.

Requirements
------------

* Python 3.6+
* A supported version of Django (currently 2.2+)

Feature overview
----------------

* Simple registration of models - fields required for tree structure will be
  added automatically.

* The tree structure is automatically updated when you create or delete
  model instances, or change an instance's parent.

* Each level of the tree is automatically sorted by a field (or fields) of your
  choice.

* New model methods are added to each registered model for:

  * changing position in the tree
  * retrieving ancestors, siblings, descendants
  * counting descendants
  * other tree-related operations

* A ``TreeManager`` manager is added to all registered models. This provides
  methods to:

  * move nodes around a tree, or into a different tree
  * insert a node anywhere in a tree
  * rebuild the MPTT fields for the tree (useful when you do bulk updates
    outside of django)

* `Form fields`_ for tree models.

* `Utility functions`_ for tree models.

* `Template tags and filters`_ for rendering trees.

* `Admin classes`_ for visualizing and modifying trees in Django's administration
  interface.

.. _`Form fields`: https://django-mptt.readthedocs.io/en/latest/forms.html
.. _`Utility functions`: https://django-mptt.readthedocs.io/en/latest/utilities.html
.. _`Template tags and filters`: https://django-mptt.readthedocs.io/en/latest/templates.html
.. _`Admin classes`: https://django-mptt.readthedocs.io/en/latest/admin.html
