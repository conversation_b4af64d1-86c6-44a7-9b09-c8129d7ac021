{% spaceless %}

{% if condition %}
    {% compress js%}
        <script type="text/javascript">alert("{{ condition|default("yellow") }}");</script>
        {% with names=[] -%}
          {%- do names.append("js/one.js") -%}
          {%- do names.append("js/nonasc.js") -%}
          {% for name in names -%}
          <script type="text/javascript" src="{{url_for('static', filename=name)}}"></script>
          {%- endfor %}
        {%- endwith %}
    {% endcompress %}
{% endif %}
{% if not condition -%}
    {% compress js %}
    <script type="text/javascript">var not_ok;</script>
    {% endcompress %}
{%- else -%}
    {% compress js %}
    <script type="text/javascript">var ok = "{% if (25*4) is divisibleby 50 %}ok{% endif %}";</script>
    {% endcompress %}
{%- endif %}
{% endspaceless %}
