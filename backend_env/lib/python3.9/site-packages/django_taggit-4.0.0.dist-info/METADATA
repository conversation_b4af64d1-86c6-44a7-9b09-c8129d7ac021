Metadata-Version: 2.1
Name: django-taggit
Version: 4.0.0
Summary: django-taggit is a reusable Django application for simple tagging.
Home-page: https://github.com/jazzband/django-taggit
Author: <PERSON>
Author-email: <EMAIL>
License: BSD
Project-URL: Documentation, https://django-taggit.readthedocs.io
Project-URL: Source, https://github.com/jazzband/django-taggit
Project-URL: Tracker, https://github.com/jazzband/django-taggit/issues
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Framework :: Django
Classifier: Framework :: Django :: 3.2
Classifier: Framework :: Django :: 4.0
Classifier: Framework :: Django :: 4.1
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Requires-Python: >=3.6
License-File: LICENSE
License-File: AUTHORS
Requires-Dist: Django (>=3.2)

django-taggit
=============

.. image:: https://jazzband.co/static/img/badge.svg
   :target: https://jazzband.co/
   :alt: Jazzband

.. image:: https://img.shields.io/pypi/pyversions/django-taggit.svg
   :target: https://pypi.org/project/django-taggit/
   :alt: Supported Python versions

.. image:: https://img.shields.io/pypi/djversions/django-taggit.svg
   :target: https://pypi.org/project/django-taggit/
   :alt: Supported Django versions

.. image:: https://github.com/jazzband/django-taggit/workflows/Test/badge.svg
   :target: https://github.com/jazzband/django-taggit/actions
   :alt: GitHub Actions

.. image:: https://codecov.io/gh/jazzband/django-taggit/coverage.svg?branch=master
    :target: https://codecov.io/gh/jazzband/django-taggit?branch=master

This is a `Jazzband <https://jazzband.co>`_ project. By contributing you agree
to abide by the `Contributor Code of Conduct
<https://jazzband.co/about/conduct>`_ and follow the `guidelines
<https://jazzband.co/about/guidelines>`_.

``django-taggit`` a simpler approach to tagging with Django.  Add ``"taggit"`` to your
``INSTALLED_APPS`` then just add a TaggableManager to your model and go:

.. code:: python

    from django.db import models

    from taggit.managers import TaggableManager


    class Food(models.Model):
        # ... fields here

        tags = TaggableManager()


Then you can use the API like so:

.. code:: pycon

    >>> apple = Food.objects.create(name="apple")
    >>> apple.tags.add("red", "green", "delicious")
    >>> apple.tags.all()
    [<Tag: red>, <Tag: green>, <Tag: delicious>]
    >>> apple.tags.remove("green")
    >>> apple.tags.all()
    [<Tag: red>, <Tag: delicious>]
    >>> Food.objects.filter(tags__name__in=["red"])
    [<Food: apple>, <Food: cherry>]

Tags will show up for you automatically in forms and the admin.

``django-taggit`` requires Django 3.2 or greater.

For more info check out the `documentation
<https://django-taggit.readthedocs.io/>`_. And for questions about usage or
development you can create an issue on Github (if your question is about
usage please add the `question` tag).
