# This file is distributed under the same license as the django-model-utils package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2020.
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-04-01 15:01+0200\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: models.py:24
msgid "created"
msgstr "ایجاد شد"

#: models.py:25
msgid "modified"
msgstr "اصلاح شد"

#: models.py:49
msgid "start"
msgstr "شروع"

#: models.py:50
msgid "end"
msgstr "پایان"

#: models.py:65
msgid "status"
msgstr "وضعیت"

#: models.py:66
msgid "status changed"
msgstr "وضعیت تغییر کرد"
