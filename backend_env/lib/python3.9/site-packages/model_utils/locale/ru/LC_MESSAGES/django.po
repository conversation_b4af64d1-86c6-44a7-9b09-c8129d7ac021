# This file is distributed under the same license as the django-model-utils package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2017.
msgid ""
msgstr ""
"Project-Id-Version: django-model-utils\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-04-01 15:01+0200\n"
"PO-Revision-Date: 2017-05-22 19:46+0300\n"
"Last-Translator: Arsen<PERSON>ysolyatin <<EMAIL>>\n"
"Language-Team: \n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || "
"(n%100>=11 && n%100<=14)? 2 : 3);\n"

#: models.py:24
msgid "created"
msgstr "создано"

#: models.py:25
msgid "modified"
msgstr "изменено"

#: models.py:49
msgid "start"
msgstr "начало"

#: models.py:50
msgid "end"
msgstr "конец"

#: models.py:65
msgid "status"
msgstr "статус"

#: models.py:66
msgid "status changed"
msgstr "статус изменен"
