# This file is distributed under the same license as the django-model-utils package.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <24530683+gmc<PERSON><PERSON><PERSON>@users.noreply.github.com>, 2023.
msgid ""
msgstr ""
"Project-Id-Version: django-model-utils\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-04-01 15:00+0200\n"
"PO-Revision-Date: 2023-07-20 22:05-0300\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>."
"github.com>\n"
"Language-Team: \n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: models.py:24
msgid "created"
msgstr "criado"

#: models.py:25
msgid "modified"
msgstr "modificado"

#: models.py:49
msgid "start"
msgstr "início"

#: models.py:50
msgid "end"
msgstr "fim"

#: models.py:65
msgid "status"
msgstr "estado"

#: models.py:66
msgid "status changed"
msgstr "estado modificado"
