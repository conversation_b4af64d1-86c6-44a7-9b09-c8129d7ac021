# This file is distributed under the same license as the django-model-utils package.
#
# Translators:
# <PERSON> <s<PERSON><PERSON>@myvision.de>, 2015.
msgid ""
msgstr ""
"Project-Id-Version: django-model-utils\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-04-01 15:01+0200\n"
"PO-Revision-Date: 2015-07-01 10:12+0200\n"
"Last-Translator: <PERSON> <stein<PERSON>@myvision.de>\n"
"Language-Team: \n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: models.py:24
msgid "created"
msgstr "erstellt"

#: models.py:25
msgid "modified"
msgstr "bearbeitet"

#: models.py:49
msgid "start"
msgstr "Beginn"

#: models.py:50
msgid "end"
msgstr "Ende"

#: models.py:65
msgid "status"
msgstr "Status"

#: models.py:66
msgid "status changed"
msgstr "Status geändert"

#~ msgid "active"
#~ msgstr "aktiv"

#~ msgid "deleted"
#~ msgstr "gelöscht"

#~ msgid "on hold"
#~ msgstr "wartend"
