Metadata-Version: 2.4
Name: prompt_toolkit
Version: 3.0.51
Summary: Library for building powerful interactive command lines in Python
Author: <PERSON>ders
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python
Classifier: Topic :: Software Development
Requires-Python: >=3.8
Description-Content-Type: text/x-rst
License-File: LICENSE
License-File: AUTHORS.rst
Requires-Dist: wcwidth
Dynamic: license-file

Python Prompt Toolkit
=====================

|AppVeyor|  |PyPI|  |RTD|  |License|  |Codecov|

.. image :: https://github.com/prompt-toolkit/python-prompt-toolkit/raw/master/docs/images/logo_400px.png

``prompt_toolkit`` *is a library for building powerful interactive command line applications in Python.*

Read the `documentation on readthedocs
<http://python-prompt-toolkit.readthedocs.io/en/stable/>`_.


Gallery
*******

`ptpython <http://github.com/prompt-toolkit/ptpython/>`_ is an interactive
Python Shell, build on top of ``prompt_toolkit``.

.. image :: https://github.com/prompt-toolkit/python-prompt-toolkit/raw/master/docs/images/ptpython.png

`More examples <https://python-prompt-toolkit.readthedocs.io/en/stable/pages/gallery.html>`_


prompt_toolkit features
***********************

``prompt_toolkit`` could be a replacement for `GNU readline
<https://tiswww.case.edu/php/chet/readline/rltop.html>`_, but it can be much
more than that.

Some features:

- **Pure Python**.
- Syntax highlighting of the input while typing. (For instance, with a Pygments lexer.)
- Multi-line input editing.
- Advanced code completion.
- Both Emacs and Vi key bindings. (Similar to readline.)
- Even some advanced Vi functionality, like named registers and digraphs.
- Reverse and forward incremental search.
- Works well with Unicode double width characters. (Chinese input.)
- Selecting text for copy/paste. (Both Emacs and Vi style.)
- Support for `bracketed paste <https://cirw.in/blog/bracketed-paste>`_.
- Mouse support for cursor positioning and scrolling.
- Auto suggestions. (Like `fish shell <http://fishshell.com/>`_.)
- Multiple input buffers.
- No global state.
- Lightweight, the only dependencies are Pygments and wcwidth.
- Runs on Linux, OS X, FreeBSD, OpenBSD and Windows systems.
- And much more...

Feel free to create tickets for bugs and feature requests, and create pull
requests if you have nice patches that you would like to share with others.


Installation
************

::

    pip install prompt_toolkit

For Conda, do:

::

    conda install -c https://conda.anaconda.org/conda-forge prompt_toolkit


About Windows support
*********************

``prompt_toolkit`` is cross platform, and everything that you build on top
should run fine on both Unix and Windows systems. Windows support is best on
recent Windows 10 builds, for which the command line window supports vt100
escape sequences. (If not supported, we fall back to using Win32 APIs for color
and cursor movements).

It's worth noting that the implementation is a "best effort of what is
possible". Both Unix and Windows terminals have their limitations. But in
general, the Unix experience will still be a little better.


Getting started
***************

The most simple example of the library would look like this:

.. code:: python

    from prompt_toolkit import prompt

    if __name__ == '__main__':
        answer = prompt('Give me some input: ')
        print('You said: %s' % answer)

For more complex examples, have a look in the ``examples`` directory. All
examples are chosen to demonstrate only one thing. Also, don't be afraid to
look at the source code. The implementation of the ``prompt`` function could be
a good start.

Philosophy
**********

The source code of ``prompt_toolkit`` should be **readable**, **concise** and
**efficient**. We prefer short functions focusing each on one task and for which
the input and output types are clearly specified. We mostly prefer composition
over inheritance, because inheritance can result in too much functionality in
the same object. We prefer immutable objects where possible (objects don't
change after initialization). Reusability is important. We absolutely refrain
from having a changing global state, it should be possible to have multiple
independent instances of the same code in the same process. The architecture
should be layered: the lower levels operate on primitive operations and data
structures giving -- when correctly combined -- all the possible flexibility;
while at the higher level, there should be a simpler API, ready-to-use and
sufficient for most use cases. Thinking about algorithms and efficiency is
important, but avoid premature optimization.


`Projects using prompt_toolkit <PROJECTS.rst>`_
***********************************************

Special thanks to
*****************

- `Pygments <http://pygments.org/>`_: Syntax highlighter.
- `wcwidth <https://github.com/jquast/wcwidth>`_: Determine columns needed for a wide characters.

.. |PyPI| image:: https://img.shields.io/pypi/v/prompt_toolkit.svg
    :target: https://pypi.python.org/pypi/prompt-toolkit/
    :alt: Latest Version

.. |AppVeyor| image:: https://ci.appveyor.com/api/projects/status/32r7s2skrgm9ubva?svg=true
    :target: https://ci.appveyor.com/project/prompt-toolkit/python-prompt-toolkit/

.. |RTD| image:: https://readthedocs.org/projects/python-prompt-toolkit/badge/
    :target: https://python-prompt-toolkit.readthedocs.io/en/master/

.. |License| image:: https://img.shields.io/github/license/prompt-toolkit/python-prompt-toolkit.svg
    :target: https://github.com/prompt-toolkit/python-prompt-toolkit/blob/master/LICENSE

.. |Codecov| image:: https://codecov.io/gh/prompt-toolkit/python-prompt-toolkit/branch/master/graphs/badge.svg?style=flat
    :target: https://codecov.io/gh/prompt-toolkit/python-prompt-toolkit/

