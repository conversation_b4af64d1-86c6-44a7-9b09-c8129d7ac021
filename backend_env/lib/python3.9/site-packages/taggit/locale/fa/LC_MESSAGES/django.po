# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <PERSON> <<EMAIL>>, 2021.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-14 11:32+0900\n"
"PO-Revision-Date: 2021-07-27 23:15+0430\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: fa\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: taggit/apps.py:7
msgid "Taggit"
msgstr ""

#: taggit/forms.py:31
msgid "Please provide a comma-separated list of tags."
msgstr "لطفا لیستی از برچسب های جدا شده توسط کاما بسازید"

#: taggit/managers.py:432
msgid "Tags"
msgstr "برچسب ها"

#: taggit/managers.py:433
msgid "A comma-separated list of tags."
msgstr "یک لیست از برچسب های جدا شده توسط کاما "

#: taggit/models.py:19
msgctxt "A tag name"
msgid "name"
msgstr "نام"

#: taggit/models.py:22
msgctxt "A tag slug"
msgid "slug"
msgstr "نامک"

#: taggit/models.py:82
msgid "tag"
msgstr "برچسب"

#: taggit/models.py:83
msgid "tags"
msgstr "برچسب ها"

#: taggit/models.py:89
#, python-format
msgid "%(object)s tagged with %(tag)s"
msgstr "%(object)s برچسب گزاری شده با %(tag)s"

#: taggit/models.py:134
msgid "content type"
msgstr "نوع محتوا"

#: taggit/models.py:165 taggit/models.py:172
msgid "object ID"
msgstr "شناسه شی"

#: taggit/models.py:180
msgid "tagged item"
msgstr "آیتم برچسب گزاری شده"

#: taggit/models.py:181
msgid "tagged items"
msgstr "آیتم های برچسب گزاری شده"

#: taggit/serializers.py:40
#, python-brace-format
msgid "Expected a list of items but got type \"{input_type}\"."
msgstr ""

#: taggit/serializers.py:43
msgid ""
"Invalid json list. A tag list submitted in string form must be valid json."
msgstr ""

#: taggit/serializers.py:46
msgid "All list items must be of string type."
msgstr ""
