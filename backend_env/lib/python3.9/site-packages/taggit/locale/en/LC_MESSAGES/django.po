# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-14 11:31+0900\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: taggit/apps.py:7
msgid "Taggit"
msgstr ""

#: taggit/forms.py:31
msgid "Please provide a comma-separated list of tags."
msgstr ""

#: taggit/managers.py:432
msgid "Tags"
msgstr ""

#: taggit/managers.py:433
msgid "A comma-separated list of tags."
msgstr ""

#: taggit/models.py:19
msgctxt "A tag name"
msgid "name"
msgstr ""

#: taggit/models.py:22
msgctxt "A tag slug"
msgid "slug"
msgstr ""

#: taggit/models.py:82
msgid "tag"
msgstr ""

#: taggit/models.py:83
msgid "tags"
msgstr ""

#: taggit/models.py:89
#, python-format
msgid "%(object)s tagged with %(tag)s"
msgstr ""

#: taggit/models.py:134
msgid "content type"
msgstr ""

#: taggit/models.py:165 taggit/models.py:172
msgid "object ID"
msgstr ""

#: taggit/models.py:180
msgid "tagged item"
msgstr ""

#: taggit/models.py:181
msgid "tagged items"
msgstr ""

#: taggit/serializers.py:40
#, python-brace-format
msgid "Expected a list of items but got type \"{input_type}\"."
msgstr ""

#: taggit/serializers.py:43
msgid ""
"Invalid json list. A tag list submitted in string form must be valid json."
msgstr ""

#: taggit/serializers.py:46
msgid "All list items must be of string type."
msgstr ""
