# French translation for django-taggit
# Copyright (C) Jazz Band
# This file is distributed under the same license as the taggit package.
# <PERSON> <<EMAIL>>, 2019
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-14 11:33+0900\n"
"PO-Revision-Date: 2019-04-09 10:57+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Poedit 2.2.1\n"

#: taggit/apps.py:7
msgid "Taggit"
msgstr ""

#: taggit/forms.py:31
msgid "Please provide a comma-separated list of tags."
msgstr "Renseignez une liste d’étiquettes séparées par des virgules."

#: taggit/managers.py:432
msgid "Tags"
msgstr "Étiquettes"

#: taggit/managers.py:433
msgid "A comma-separated list of tags."
msgstr "Une liste d’étiquettes séparées par des virgules."

#: taggit/models.py:19
msgctxt "A tag name"
msgid "name"
msgstr "nom"

#: taggit/models.py:22
msgctxt "A tag slug"
msgid "slug"
msgstr "slug"

#: taggit/models.py:82
msgid "tag"
msgstr "Étiquette"

#: taggit/models.py:83
msgid "tags"
msgstr ""

#: taggit/models.py:89
#, python-format
msgid "%(object)s tagged with %(tag)s"
msgstr "%(object)s étiquetés avec %(tag)s"

#: taggit/models.py:134
msgid "content type"
msgstr "Type de contenu"

#: taggit/models.py:165 taggit/models.py:172
msgid "object ID"
msgstr "Identifiant de l’objet"

#: taggit/models.py:180
msgid "tagged item"
msgstr "Élément étiqueté"

#: taggit/models.py:181
msgid "tagged items"
msgstr "Éléments étiquetés"

#: taggit/serializers.py:40
#, python-brace-format
msgid "Expected a list of items but got type \"{input_type}\"."
msgstr ""

#: taggit/serializers.py:43
msgid ""
"Invalid json list. A tag list submitted in string form must be valid json."
msgstr ""

#: taggit/serializers.py:46
msgid "All list items must be of string type."
msgstr ""
