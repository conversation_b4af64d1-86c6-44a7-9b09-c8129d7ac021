# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: 0.9.3\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-14 11:41+0900\n"
"PO-Revision-Date: 2012-12-08 14:42+0100\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Norwegian <<EMAIL>>\n"
"Language: Norwegian\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.5.4\n"

#: taggit/apps.py:7
msgid "Taggit"
msgstr ""

#: taggit/forms.py:31
msgid "Please provide a comma-separated list of tags."
msgstr "Vennligst oppgi en kommaseparert tagg-liste."

#: taggit/managers.py:432
msgid "Tags"
msgstr "Tagger"

#: taggit/managers.py:433
msgid "A comma-separated list of tags."
msgstr "En kommaseparert tagg-liste."

#: taggit/models.py:19
msgctxt "A tag name"
msgid "name"
msgstr "Navn"

#: taggit/models.py:22
msgctxt "A tag slug"
msgid "slug"
msgstr "Slug"

#: taggit/models.py:82
msgid "tag"
msgstr "Tagg"

#: taggit/models.py:83
msgid "tags"
msgstr ""

#: taggit/models.py:89
#, python-format
msgid "%(object)s tagged with %(tag)s"
msgstr "%(object)s tagget med %(tag)s"

#: taggit/models.py:134
msgid "content type"
msgstr "Innholdstype"

#: taggit/models.py:165 taggit/models.py:172
msgid "object ID"
msgstr "Objekt-id"

#: taggit/models.py:180
msgid "tagged item"
msgstr "Tagget Element"

#: taggit/models.py:181
msgid "tagged items"
msgstr "Taggede Elementer"

#: taggit/serializers.py:40
#, python-brace-format
msgid "Expected a list of items but got type \"{input_type}\"."
msgstr ""

#: taggit/serializers.py:43
msgid ""
"Invalid json list. A tag list submitted in string form must be valid json."
msgstr ""

#: taggit/serializers.py:46
msgid "All list items must be of string type."
msgstr ""
