msgid ""
msgstr ""
"Project-Id-Version: django-taggit\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-14 11:31+0900\n"
"PO-Revision-Date: 2014-03-29 18:57+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Esperanto <<EMAIL>>\n"
"Language: eo\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 1.5.4\n"

#: taggit/apps.py:7
msgid "Taggit"
msgstr "Etikedoj"

#: taggit/forms.py:31
msgid "Please provide a comma-separated list of tags."
msgstr "Bonvolu enmeti liston da etikedoj apartitaj per komoj."

#: taggit/managers.py:432
msgid "Tags"
msgstr "Etikedoj"

#: taggit/managers.py:433
msgid "A comma-separated list of tags."
msgstr "Listo da etikedoj apartitaj per komoj."

#: taggit/models.py:19
msgctxt "A tag name"
msgid "name"
msgstr "Nomo"

#: taggit/models.py:22
msgctxt "A tag slug"
msgid "slug"
msgstr "Ĵetonvorto"

#: taggit/models.py:82
msgid "tag"
msgstr "Etikedo"

#: taggit/models.py:83
msgid "tags"
msgstr ""

#: taggit/models.py:89
#, python-format
msgid "%(object)s tagged with %(tag)s"
msgstr "%(object)s etikedita %(tag)s"

#: taggit/models.py:134
msgid "content type"
msgstr "Enhavtipo"

#: taggit/models.py:165 taggit/models.py:172
msgid "object ID"
msgstr "Objekto ID"

#: taggit/models.py:180
msgid "tagged item"
msgstr "Etikedita elemento"

#: taggit/models.py:181
msgid "tagged items"
msgstr "Etikeditaj elementoj"

#: taggit/serializers.py:40
#, python-brace-format
msgid "Expected a list of items but got type \"{input_type}\"."
msgstr ""

#: taggit/serializers.py:43
msgid ""
"Invalid json list. A tag list submitted in string form must be valid json."
msgstr ""

#: taggit/serializers.py:46
msgid "All list items must be of string type."
msgstr ""
