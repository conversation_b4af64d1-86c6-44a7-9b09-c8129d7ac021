# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-14 11:32+0900\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: taggit/apps.py:7
msgid "Taggit"
msgstr ""

#: taggit/forms.py:31
msgid "Please provide a comma-separated list of tags."
msgstr "Por favor introduzca una lista de etiquetas separadas por coma."

#: taggit/managers.py:432
msgid "Tags"
msgstr "Etiquetas"

#: taggit/managers.py:433
msgid "A comma-separated list of tags."
msgstr "Una lista de etiquetas separadas por coma."

#: taggit/models.py:19
msgctxt "A tag name"
msgid "name"
msgstr "Nombre"

#: taggit/models.py:22
msgctxt "A tag slug"
msgid "slug"
msgstr "Slug"

#: taggit/models.py:82
msgid "tag"
msgstr "Etiqueta"

#: taggit/models.py:83
msgid "tags"
msgstr ""

#: taggit/models.py:89
#, python-format
msgid "%(object)s tagged with %(tag)s"
msgstr "%(object)s etiquetados con %(tag)s"

#: taggit/models.py:134
msgid "content type"
msgstr "Tipo de contenido"

#: taggit/models.py:165 taggit/models.py:172
msgid "object ID"
msgstr "Id del objeto"

#: taggit/models.py:180
msgid "tagged item"
msgstr "Elemento etiquetado"

#: taggit/models.py:181
msgid "tagged items"
msgstr "Elementos etiquetados"

#: taggit/serializers.py:40
#, python-brace-format
msgid "Expected a list of items but got type \"{input_type}\"."
msgstr ""

#: taggit/serializers.py:43
msgid ""
"Invalid json list. A tag list submitted in string form must be valid json."
msgstr ""

#: taggit/serializers.py:46
msgid "All list items must be of string type."
msgstr ""
