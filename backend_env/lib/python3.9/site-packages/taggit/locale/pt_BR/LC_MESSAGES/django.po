# This file is distributed under WTFPL license.
#
# Translators:
# <AUTHOR> <EMAIL>, 2013.
msgid ""
msgstr ""
"Project-Id-Version: django-taggit\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-14 11:42+0900\n"
"PO-Revision-Date: 2013-01-12 18:11-0200\n"
"Last-Translator: RPB <<EMAIL>>\n"
"Language-Team: Portuguese (Brazil) <<EMAIL>>\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1)\n"

#: taggit/apps.py:7
msgid "Taggit"
msgstr ""

#: taggit/forms.py:31
msgid "Please provide a comma-separated list of tags."
msgstr "Favor fornecer uma lista de marcadores separados por vírgula."

#: taggit/managers.py:432
msgid "Tags"
msgstr "Marcadores"

#: taggit/managers.py:433
msgid "A comma-separated list of tags."
msgstr "Uma lista de marcadores separados por vírgula."

#: taggit/models.py:19
msgctxt "A tag name"
msgid "name"
msgstr "Nome"

#: taggit/models.py:22
msgctxt "A tag slug"
msgid "slug"
msgstr "Slug"

#: taggit/models.py:82
msgid "tag"
msgstr "Marcador"

#: taggit/models.py:83
msgid "tags"
msgstr ""

#: taggit/models.py:89
#, python-format
msgid "%(object)s tagged with %(tag)s"
msgstr "%(object)s marcados com %(tag)s"

#: taggit/models.py:134
msgid "content type"
msgstr "Tipo de conteúdo"

#: taggit/models.py:165 taggit/models.py:172
msgid "object ID"
msgstr "Id do objeto"

#: taggit/models.py:180
msgid "tagged item"
msgstr "Item marcado"

#: taggit/models.py:181
msgid "tagged items"
msgstr "Itens marcados"

#: taggit/serializers.py:40
#, python-brace-format
msgid "Expected a list of items but got type \"{input_type}\"."
msgstr ""

#: taggit/serializers.py:43
msgid ""
"Invalid json list. A tag list submitted in string form must be valid json."
msgstr ""

#: taggit/serializers.py:46
msgid "All list items must be of string type."
msgstr ""
