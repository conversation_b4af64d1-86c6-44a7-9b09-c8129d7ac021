msgid ""
msgstr ""
"Project-Id-Version: django-taggit\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-14 11:41+0900\n"
"PO-Revision-Date: 2010-09-07 23:04+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Dutch\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: taggit/apps.py:7
msgid "Taggit"
msgstr ""

#: taggit/forms.py:31
msgid "Please provide a comma-separated list of tags."
msgstr "Geef een door komma gescheiden lijst van tags."

#: taggit/managers.py:432
msgid "Tags"
msgstr "Tags"

#: taggit/managers.py:433
msgid "A comma-separated list of tags."
msgstr "Een door komma gescheiden lijst van tags."

#: taggit/models.py:19
msgctxt "A tag name"
msgid "name"
msgstr "Naam"

#: taggit/models.py:22
msgctxt "A tag slug"
msgid "slug"
msgstr "Slug"

#: taggit/models.py:82
#, fuzzy
#| msgid "Tag"
msgid "tag"
msgstr "Tag"

#: taggit/models.py:83
msgid "tags"
msgstr ""

#: taggit/models.py:89
#, python-format
msgid "%(object)s tagged with %(tag)s"
msgstr "%(object)s getagged met %(tag)s"

#: taggit/models.py:134
msgid "content type"
msgstr "Inhoudstype"

#: taggit/models.py:165 taggit/models.py:172
msgid "object ID"
msgstr "Object-id"

#: taggit/models.py:180
msgid "tagged item"
msgstr "Object getagged"

#: taggit/models.py:181
msgid "tagged items"
msgstr "Objecten getagged"

#: taggit/serializers.py:40
#, python-brace-format
msgid "Expected a list of items but got type \"{input_type}\"."
msgstr ""

#: taggit/serializers.py:43
msgid ""
"Invalid json list. A tag list submitted in string form must be valid json."
msgstr ""

#: taggit/serializers.py:46
msgid "All list items must be of string type."
msgstr ""
