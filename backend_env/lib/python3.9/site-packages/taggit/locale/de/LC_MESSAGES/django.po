#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: django-taggit\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-14 11:29+0900\n"
"PO-Revision-Date: 2010-09-07 09:26-0700\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: German <<EMAIL>>\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"

#: taggit/apps.py:7
msgid "Taggit"
msgstr ""

#: taggit/forms.py:31
msgid "Please provide a comma-separated list of tags."
msgstr "Bitte eine durch Komma getrennte Schlagwortliste eingeben."

#: taggit/managers.py:432
msgid "Tags"
msgstr "Schlagwörter"

#: taggit/managers.py:433
msgid "A comma-separated list of tags."
msgstr "Eine durch Komma getrennte Schlagwortliste."

#: taggit/models.py:19
msgctxt "A tag name"
msgid "name"
msgstr "Name"

#: taggit/models.py:22
msgctxt "A tag slug"
msgid "slug"
msgstr "Kürzel"

#: taggit/models.py:82
msgid "tag"
msgstr "Schlagwort"

#: taggit/models.py:83
msgid "tags"
msgstr ""

#: taggit/models.py:89
#, python-format
msgid "%(object)s tagged with %(tag)s"
msgstr "%(object)s verschlagwortet mit %(tag)s"

#: taggit/models.py:134
msgid "content type"
msgstr "Inhaltstyp"

#: taggit/models.py:165 taggit/models.py:172
msgid "object ID"
msgstr "Objekt-ID"

#: taggit/models.py:180
msgid "tagged item"
msgstr "Verschlagwortetes Objekt"

#: taggit/models.py:181
msgid "tagged items"
msgstr "Verschlagwortete Objekte"

#: taggit/serializers.py:40
#, python-brace-format
msgid "Expected a list of items but got type \"{input_type}\"."
msgstr ""

#: taggit/serializers.py:43
msgid ""
"Invalid json list. A tag list submitted in string form must be valid json."
msgstr ""

#: taggit/serializers.py:46
msgid "All list items must be of string type."
msgstr ""
