# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-14 11:27+0900\n"
"PO-Revision-Date: 2021-04-19 23:30+0100\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: ar_DZ\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.3\n"
"Plural-Forms: nplurals=6; plural=(n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5);\n"

#: taggit/apps.py:7
msgid "Taggit"
msgstr "أوْسِمه"

#: taggit/forms.py:31
msgid "Please provide a comma-separated list of tags."
msgstr "يرجى توفير قائمة وسوم مفصولة بفاصلة."

#: taggit/managers.py:432
msgid "Tags"
msgstr "الوسوم"

#: taggit/managers.py:433
msgid "A comma-separated list of tags."
msgstr "قائمة وسوم مفصولة بفاصلة."

#: taggit/models.py:19
msgctxt "A tag name"
msgid "name"
msgstr "الإسم"

#: taggit/models.py:22
msgctxt "A tag slug"
msgid "slug"
msgstr "سبيكة الوسم"

#: taggit/models.py:82
msgid "tag"
msgstr "الوسم"

#: taggit/models.py:83
msgid "tags"
msgstr "الوسوم"

#: taggit/models.py:89
#, python-format
msgid "%(object)s tagged with %(tag)s"
msgstr "%(object)s الموسوم بـ %(tag)s"

#: taggit/models.py:134
msgid "content type"
msgstr "نوع المحتوى"

#: taggit/models.py:165 taggit/models.py:172
msgid "object ID"
msgstr "معرِّف الكائن"

#: taggit/models.py:180
msgid "tagged item"
msgstr "العنصر الموسوم"

#: taggit/models.py:181
msgid "tagged items"
msgstr "العناصر الموسومة"

#: taggit/serializers.py:40
#, python-brace-format
msgid "Expected a list of items but got type \"{input_type}\"."
msgstr ""

#: taggit/serializers.py:43
msgid ""
"Invalid json list. A tag list submitted in string form must be valid json."
msgstr ""

#: taggit/serializers.py:46
msgid "All list items must be of string type."
msgstr ""
