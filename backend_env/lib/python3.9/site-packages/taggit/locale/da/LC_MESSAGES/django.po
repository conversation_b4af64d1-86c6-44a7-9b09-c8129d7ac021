# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2020.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-14 11:29+0900\n"
"PO-Revision-Date: 2020-10-25 14:29+0100\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: da\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 2.4.1\n"

#: taggit/apps.py:7
msgid "Taggit"
msgstr "Taggit"

#: taggit/forms.py:31
msgid "Please provide a comma-separated list of tags."
msgstr "Venligts angiv mærker adskilt af et komma."

#: taggit/managers.py:432
msgid "Tags"
msgstr "Mærker"

#: taggit/managers.py:433
msgid "A comma-separated list of tags."
msgstr "Adskil mærker med et komma."

#: taggit/models.py:19
msgctxt "A tag name"
msgid "name"
msgstr "navn"

#: taggit/models.py:22
msgctxt "A tag slug"
msgid "slug"
msgstr "slug"

#: taggit/models.py:82
msgid "tag"
msgstr "mærke"

#: taggit/models.py:83
msgid "tags"
msgstr "mærker"

#: taggit/models.py:89
#, python-format
msgid "%(object)s tagged with %(tag)s"
msgstr "%(object)s mærket med %(tag)s"

#: taggit/models.py:134
msgid "content type"
msgstr "indholdstype"

#: taggit/models.py:165 taggit/models.py:172
msgid "object ID"
msgstr "objekt ID"

#: taggit/models.py:180
msgid "tagged item"
msgstr "mærket element"

#: taggit/models.py:181
msgid "tagged items"
msgstr "mærkede elementer"

#: taggit/serializers.py:40
#, python-brace-format
msgid "Expected a list of items but got type \"{input_type}\"."
msgstr ""

#: taggit/serializers.py:43
msgid ""
"Invalid json list. A tag list submitted in string form must be valid json."
msgstr ""

#: taggit/serializers.py:46
msgid "All list items must be of string type."
msgstr ""
