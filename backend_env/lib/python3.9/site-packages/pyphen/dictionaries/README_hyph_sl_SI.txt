﻿The Slovenian hyphenation patterns for TeX were created by <PERSON><PERSON><PERSON>,
MG-SOFT Corp. <<EMAIL>>. They are published
under the LPPL license (LaTeX Project Public License). For use in
OpenOffice.org adapted by <PERSON>, <<EMAIL>>.
The OpenOffice.org Slovenian hyphenation patterns are covered by
the GNU/LGPL and GNU/GPL License and support the Slovenian language (sl_SI).

TeX patterns were re-converted for better performance in July 2010 thanks
to errors pointed out by <PERSON><PERSON><PERSON>, <<EMAIL>>.

The OpenOffice.org extension made by <PERSON>, <<EMAIL>>.

TeX hyphenation patterns conversion for OpenOffice.org is fully described at
http://wiki.services.openoffice.org/wiki/Documentation/SL/Using_TeX_hyphenation_patterns_in_OpenOffice.org
****
Slovenske vzorce za deljenje besed za TeX je ustvaril <PERSON>,
MG-SOFT Corp. <<EMAIL>>; izdani so pod licenco
LPPL (LaTeX Project Public License). Za rabo v OpenOffice.org
jih je priredil <PERSON>, <<EMAIL>>.
Slovenski delilni vzorci za OpenOffice.org so izdani pod licencama 
GNU/LGPL in GNU/GPL ter so namenjeni podpori za slovenski jezik (sl_SI).

Vzorci za TeX ponovno pretvorjeni julija 2010
zavoljo nepravilnosti, na katere je prijazno opozorila
Mojca Miklavec, <<EMAIL>>.

Razširitev za OpenOffice.org je pripravil Martin Srebotnjak, <<EMAIL>>.

Pretvorba vzorcev za deljenje besed Tex je podrobno opisana na naslovu
http://wiki.services.openoffice.org/wiki/Documentation/SL/Using_TeX_hyphenation_patterns_in_OpenOffice.org

HYPH sl SI hyph_sl_SI

=======================================================================
http://external.openoffice.org/ form data:

Product Name: Slovenian patterns for hyphenation
Product Version: 1.2.1
Vendor or Owner Name: Mojca Miklavec
Vendor or Owner Contact: <EMAIL>
OpenOffice.org Contact: <EMAIL>
Date of First Use / date of License: 1990/October 2006
URL for Product Information:
http://sl.openoffice.org/delilni.html
URL for License: http://www.gnu.org/copyleft/lgpl.html
Purpose: Patterns for Slovenian hyphenation
Type of Encryption: none
Binary or Source Code: Source
=======================================================================

For the avoidance of doubt, except that if any license choice other
than GPL or LGPL is available it will apply instead, Sun elects to use
only the Lesser General Public License version 2.1 (LGPLv2) at this
time for any software where a choice of LGPL license versions is made
available with the language indicating that LGPLv2.1 or any later
version may be used, or where a choice of which version of the LGPL is
applied is otherwise unspecified.
