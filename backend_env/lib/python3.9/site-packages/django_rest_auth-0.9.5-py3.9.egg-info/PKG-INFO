Metadata-Version: 2.4
Name: django-rest-auth
Version: 0.9.5
Summary: Create a set of REST API endpoints for Authentication and Registration
Home-page: http://github.com/Tivix/django-rest-auth
Author: Sumit <PERSON><PERSON>ra
Author-email: <EMAIL>
Keywords: django rest auth registration rest-framework django-registration api
Classifier: Framework :: Django
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: System Administrators
Classifier: Operating System :: OS Independent
Classifier: Topic :: Software Development
License-File: LICENSE
Requires-Dist: Django>=1.8.0
Requires-Dist: djangorestframework>=3.1.3
Requires-Dist: six>=1.9.0
Provides-Extra: with-social
Requires-Dist: django-allauth>=0.25.0; extra == "with-social"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: keywords
Dynamic: license-file
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: summary

Welcome to django-rest-auth
===========================

.. image:: https://travis-ci.org/Tivix/django-rest-auth.svg
    :target: https://travis-ci.org/Tivix/django-rest-auth


.. image:: https://coveralls.io/repos/Tivix/django-rest-auth/badge.svg
    :target: https://coveralls.io/r/Tivix/django-rest-auth?branch=master


.. image:: https://readthedocs.org/projects/django-rest-auth/badge/?version=latest
    :target: https://readthedocs.org/projects/django-rest-auth/?badge=latest


Django-rest-auth provides a set of REST API endpoints for Authentication and Registration


Documentation
-------------
http://django-rest-auth.readthedocs.org/en/latest/


Source code
-----------
https://github.com/Tivix/django-rest-auth


Stack Overflow
-----------
http://stackoverflow.com/questions/tagged/django-rest-auth
