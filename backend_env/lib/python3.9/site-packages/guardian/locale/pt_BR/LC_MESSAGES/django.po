# django-guardian
# Copyright (c) 2010-2013 <PERSON><PERSON><PERSON> <l<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>
# This file is distributed under the same license as the PACKAGE package.
# Fabio C. Barrionuevo da Luz <bnafta at gmail dot com>, 2013.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: 1.2.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2013-12-14 02:39-0300\n"
"PO-Revision-Date: 2013-12-14 02:39-0300\n"
"Last-Translator: Fabio C. Barrionuevo da Luz <bnafta at gmail dot com>\n"
"Language-Team: pt_BR \n"
"Language: Brazilian Portuguese\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: admin.py:32 admin.py:42 forms.py:55
msgid "Permissions"
msgstr "Permissões"

#: admin.py:189
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:12
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_group.html:14
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_group.html:25
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_user.html:14
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_user.html:25
#: templates/admin/guardian/model/change_form.html:5
#: templates/admin/guardian/model/obj_perms_manage.html:17
#: templates/admin/guardian/model/obj_perms_manage_group.html:14
#: templates/admin/guardian/model/obj_perms_manage_group.html:25
#: templates/admin/guardian/model/obj_perms_manage_user.html:14
#: templates/admin/guardian/model/obj_perms_manage_user.html:25
msgid "Object permissions"
msgstr "Permissões para Objetos"

#: admin.py:280 admin.py:333
msgid "Permissions saved."
msgstr "Permissões salvas"

#: admin.py:377
msgid "User identification"
msgstr "Identificação de Usuário"

#: admin.py:379
msgid "This user does not exist"
msgstr "Este usuário não existe"

#: admin.py:380
msgid "Enter a value compatible with User.USERNAME_FIELD"
msgstr "Insira um valor compatível com User.USERNAME_FIELD"

#: admin.py:403
msgid "This group does not exist"
msgstr "Este grupo não existe"

#: models.py:46
msgid "object ID"
msgstr "id do objeto"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:8
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_group.html:10
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_user.html:10
#: templates/admin/guardian/model/obj_perms_manage.html:13
#: templates/admin/guardian/model/obj_perms_manage_group.html:10
#: templates/admin/guardian/model/obj_perms_manage_user.html:10
msgid "Home"
msgstr "Home"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:21
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:82
msgid "Please correct the error below."
msgid_plural "Please correct the errors below."
msgstr[0] "Por favor, corrija o erro abaixo."
msgstr[1] "Por favor, corrija os erros abaixo."

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:25
#: templates/admin/guardian/model/obj_perms_manage.html:32
msgid "Users"
msgstr "Usuários"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:28
#: templates/admin/guardian/model/obj_perms_manage.html:38
msgid "User permissions"
msgstr "Permissões de usuários"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:33
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_user.html:34
#: templates/admin/guardian/model/obj_perms_manage.html:41
#: templates/admin/guardian/model/obj_perms_manage_user.html:30
msgid "User"
msgstr "Usuário"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:37
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:98
#: templates/admin/guardian/model/obj_perms_manage.html:45
#: templates/admin/guardian/model/obj_perms_manage.html:99
msgid "Action"
msgstr "Ação"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:54
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:115
#: templates/admin/guardian/model/obj_perms_manage.html:62
#: templates/admin/guardian/model/obj_perms_manage.html:116
msgid "Edit"
msgstr "Editar"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:70
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_user.html:15
#: templates/admin/guardian/model/obj_perms_manage.html:73
#: templates/admin/guardian/model/obj_perms_manage_user.html:15
msgid "Manage user"
msgstr "Gerênciar usuário"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:86
#: templates/admin/guardian/model/obj_perms_manage.html:86
msgid "Groups"
msgstr "Grupos"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:89
#: templates/admin/guardian/model/obj_perms_manage.html:92
msgid "Group permissions"
msgstr "Grupos de permissões"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:94
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_group.html:34
#: templates/admin/guardian/model/obj_perms_manage.html:95
#: templates/admin/guardian/model/obj_perms_manage_group.html:30
msgid "Group"
msgstr "Grupo"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:132
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_group.html:15
#: templates/admin/guardian/model/obj_perms_manage.html:127
#: templates/admin/guardian/model/obj_perms_manage_group.html:15
msgid "Manage group"
msgstr "Gerenciar grupo"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_group.html:28
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_user.html:28
#: templates/admin/guardian/model/obj_perms_manage_group.html:27
#: templates/admin/guardian/model/obj_perms_manage_user.html:27
msgid "Object"
msgstr "Objeto"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_group.html:45
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_user.html:45
#: templates/admin/guardian/model/obj_perms_manage_group.html:37
#: templates/admin/guardian/model/obj_perms_manage_user.html:37
msgid "Save"
msgstr "Salvar"

#: templates/admin/guardian/model/obj_perms_manage.html:28
#: templates/admin/guardian/model/obj_perms_manage.html:82
msgid "Please correct the errors below."
msgstr "Por favor, corrija os erros abaixo."
