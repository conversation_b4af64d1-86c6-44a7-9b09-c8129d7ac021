# Polish translation of django-guardian.
# This file is distributed under the same license as django-guardian's package.
# Translator: <PERSON><PERSON> <<EMAIL>>, 2013.
# 
msgid ""
msgstr ""
"Project-Id-Version: django-guardian 1.2\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2013-12-29 00:14+0100\n"
"PO-Revision-Date: 2013-12-29 17:29-0600\n"
"Last-Translator:   <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pl\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Translated-Using: django-rosetta 0.7.3\n"

#: admin.py:32 admin.py:42 forms.py:55
msgid "Permissions"
msgstr "Права"

#: admin.py:189
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:12
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_group.html:14
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_group.html:25
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_user.html:14
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_user.html:25
#: templates/admin/guardian/model/change_form.html:5
#: templates/admin/guardian/model/obj_perms_manage.html:17
#: templates/admin/guardian/model/obj_perms_manage_group.html:14
#: templates/admin/guardian/model/obj_perms_manage_group.html:25
#: templates/admin/guardian/model/obj_perms_manage_user.html:14
#: templates/admin/guardian/model/obj_perms_manage_user.html:25
msgid "Object permissions"
msgstr "Права на объект"

#: admin.py:278 admin.py:331
msgid "Permissions saved."
msgstr "Права сохранены."

#: admin.py:375
msgid "Username"
msgstr "Имя пользователя"

#: admin.py:378
msgid "This value may contain only letters, numbers and @/./+/-/_ characters."
msgstr "Это значение должно содержать буквы, цифры и символы @/./+/-/_"

#: admin.py:380
msgid "This user does not exist"
msgstr "Этот пользователь не существует"

#: admin.py:397
msgid "This group does not exist"
msgstr "Эта группа не существует"

#: models.py:46
msgid "object ID"
msgstr "ID объекта"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:8
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_group.html:10
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_user.html:10
#: templates/admin/guardian/model/obj_perms_manage.html:13
#: templates/admin/guardian/model/obj_perms_manage_group.html:10
#: templates/admin/guardian/model/obj_perms_manage_user.html:10
msgid "Home"
msgstr "Домой"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:21
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:82
msgid "Please correct the error below."
msgid_plural "Please correct the errors below."
msgstr[0] "Исправьте ошибку ниже."
msgstr[1] "Исправьте ошибки ниже."

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:25
#: templates/admin/guardian/model/obj_perms_manage.html:32
msgid "Users"
msgstr "Пользователи"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:28
#: templates/admin/guardian/model/obj_perms_manage.html:38
msgid "User permissions"
msgstr "Права пользователя"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:33
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_user.html:34
#: templates/admin/guardian/model/obj_perms_manage.html:41
#: templates/admin/guardian/model/obj_perms_manage_user.html:30
msgid "User"
msgstr "Пользователь"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:37
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:98
#: templates/admin/guardian/model/obj_perms_manage.html:45
#: templates/admin/guardian/model/obj_perms_manage.html:99
msgid "Action"
msgstr "Действие"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:54
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:115
#: templates/admin/guardian/model/obj_perms_manage.html:62
#: templates/admin/guardian/model/obj_perms_manage.html:116
msgid "Edit"
msgstr "Редактировать"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:70
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_user.html:15
#: templates/admin/guardian/model/obj_perms_manage.html:73
#: templates/admin/guardian/model/obj_perms_manage_user.html:15
msgid "Manage user"
msgstr "Управление пользователем"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:86
#: templates/admin/guardian/model/obj_perms_manage.html:86
msgid "Groups"
msgstr "Группы"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:89
#: templates/admin/guardian/model/obj_perms_manage.html:92
msgid "Group permissions"
msgstr "Групповые права"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:94
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_group.html:34
#: templates/admin/guardian/model/obj_perms_manage.html:95
#: templates/admin/guardian/model/obj_perms_manage_group.html:30
msgid "Group"
msgstr "Группа"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage.html:132
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_group.html:15
#: templates/admin/guardian/model/obj_perms_manage.html:127
#: templates/admin/guardian/model/obj_perms_manage_group.html:15
msgid "Manage group"
msgstr "Управление группой"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_group.html:28
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_user.html:28
#: templates/admin/guardian/model/obj_perms_manage_group.html:27
#: templates/admin/guardian/model/obj_perms_manage_user.html:27
msgid "Object"
msgstr "Объект"

#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_group.html:45
#: templates/admin/guardian/contrib/grappelli/obj_perms_manage_user.html:45
#: templates/admin/guardian/model/obj_perms_manage_group.html:37
#: templates/admin/guardian/model/obj_perms_manage_user.html:37
msgid "Save"
msgstr "Сохранить"

#: templates/admin/guardian/model/obj_perms_manage.html:28
#: templates/admin/guardian/model/obj_perms_manage.html:82
msgid "Please correct the errors below."
msgstr "Исправьте ошибки ниже."
