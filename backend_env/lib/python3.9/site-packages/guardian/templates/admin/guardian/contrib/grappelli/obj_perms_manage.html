{% extends "admin/change_form.html" %}
{% load i18n %}
{% load guardian_tags %}
{% load static %}
{% load admin_urls %}

{% block breadcrumbs %}
<ul>
    <li><a href="{% url 'admin:index' %}">{% trans 'Home' %}</a></li>
    <li><a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a></li>
    {% if has_change_permission %}
        <li>
            {% url opts|admin_urlname:'changelist' as changelist_url %}
            <a href="{% add_preserved_filters changelist_url %}">{{ opts.verbose_name_plural|capfirst }}</a>
        </li>
        <li>
            {% url opts|admin_urlname:'change' object.pk|admin_urlquote as object_url %}
            <a href="{% add_preserved_filters object_url %}">{{ object|truncatewords:"18" }}</a>
        </li>
    {% else %}
        <li>{{ opts.verbose_name_plural|capfirst }}</li>
        <li>{{ original|truncatewords:"18" }}</li>
    {% endif %}
    <li>{% trans 'Object permissions' %}</li>
</ul>
{% endblock %}

{% block content %}
<form action="." method="post">
    {% csrf_token %}
    <div>
    {% if user_form.errors %}
        <p class="errornote">{% blocktrans count user_form.errors|length as counter %}Please correct the error below.{% plural %}Please correct the errors below.{% endblocktrans %}</p>
        <ul class="errorlist">{% for error in user_form.errors %}<li>{{ error }}</li>{% endfor %}</ul>
    {% endif %}
    <fieldset class="grp-module">
        <h2 class="grp-collapse-handler">{% trans "Users" %}</h2>
        <div class="grp-row grp-cells-1 user_permissions">
            <div class="l-2c-fluid l-d-4">
                <div class="c-1"><label for="user-permissions" class="required">{% trans "User permissions" %}</label></div>
                <div class="c-2">
                    <table id="user-permissions" class="grp-table object-perms">
                        <thead>
                            <tr>
                                <th>{% trans "User" %}</th>
                                {% for perm in model_perms %}
                                <th>{{ perm.name }}</th>
                                {% endfor %}
                                <th>{% trans "Action" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user, user_perms in users_perms.items %}
                            <tr class="grp-row {% cycle 'grp-row-even' 'grp-row-odd' %}">
                                <td>{{ user }}</td>
                                {% for perm in model_perms %}
                                <td>
                                    {% if perm.codename in user_perms %}
                                    <img src="{% static "guardian/img/icon-yes.svg" %}"/>
                                    {% else %}
                                    <img src="{% static "guardian/img/icon-no.svg" %}"/>
                                    {% endif %}
                                </td>
                                {% endfor %}
                                <td>
                                    <a href="user-manage/{{ user.id|safe }}/">{% trans "Edit" %}</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% for field in user_form %}
            {% include "admin/guardian/contrib/grappelli/field.html" %}
        {% endfor %}
        <div class="grp-row grp-cells-1">
            <div class="l-2c-fluid l-d-4">
                <div class="c-1">&nbsp;</div>
                <div class="c-2">
                    <input name="submit_manage_user" type="submit" value="{% trans "Manage user" %}"/>
                </div>
            </div>
        </div>
    </fieldset>
    </div>
</form>

<form action="." method="post">
    {% csrf_token %}
    <div>
    {% if group_form.errors %}
        <p class="errornote">{% blocktrans count group_form.errors|length as counter %}Please correct the error below.{% plural %}Please correct the errors below.{% endblocktrans %}</p>
        <ul class="errorlist">{% for error in group_form.errors %}<li>{{ error }}</li>{% endfor %}</ul>
    {% endif %}
    <fieldset class="grp-module">
        <h2 class="grp-collapse-handler">{% trans "Groups" %}</h2>
        <div class="grp-row grp-cells-1 group_permissions">
            <div class="l-2c-fluid l-d-4">
                <div class="c-1"><label for="group-permissions" class="required">{% trans "Group permissions" %}</label></div>
                <div class="c-2">
                    <table id="group-permissions" class="grp-table object-perms">
                        <thead>
                            <tr>
                                <th>{% trans "Group" %}</th>
                                {% for perm in model_perms %}
                                <th>{{ perm.name }}</th>
                                {% endfor %}
                                <th>{% trans "Action" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for group, group_perms in groups_perms.items %}
                            <tr class="grp-row {% cycle 'grp-row-even' 'grp-row-odd' %}">
                                <td>{{ group }}</td>
                                {% for perm in model_perms %}
                                <td>
                                    {% if perm.codename in group_perms %}
                                    <img src="{% static "guardian/img/icon-yes.svg" %}"/>
                                    {% else %}
                                    <img src="{% static "guardian/img/icon-no.svg" %}"/>
                                    {% endif %}
                                </td>
                                {% endfor %}
                                <td>
                                    <a href="group-manage/{{ group.id|safe }}/">{% trans "Edit" %}</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        {% for field in group_form %}
            {% include "admin/guardian/contrib/grappelli/field.html" %}
        {% endfor %}
        <div class="grp-row grp-cells-1">
            <div class="l-2c-fluid l-d-4">
                <div class="c-1">&nbsp;</div>
                <div class="c-2">
                    <input name="submit_manage_group" type="submit" value="{% trans "Manage group" %}"/>
                </div>
            </div>
        </div>
    </fieldset>
    </div>
</form>
{% endblock %}
