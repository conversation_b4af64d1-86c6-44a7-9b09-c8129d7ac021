# Generated by Django 2.2.2 on 2019-06-11 03:54

import django.contrib.auth.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('testapp', '0002_logentrywithgroup'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='customuser',
            name='last_name',
            field=models.CharField(blank=True, max_length=150,
                                   verbose_name='last name'),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='username',
            field=models.CharField(error_messages={
                'unique': 'A user with that username already exists.'},
                help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.',
                max_length=150, unique=True, validators=[
                    django.contrib.auth.validators.UnicodeUsernameValidator()],
                verbose_name='username'),
        ),
    ]
