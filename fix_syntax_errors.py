#!/usr/bin/env python3
"""
Quick fix for syntax errors introduced by the performance fix script
"""

import re
from pathlib import Path

def fix_malformed_console_logs(file_path):
    """Fix malformed console.log statements"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix pattern 1: console.log with broken template literals
        content = re.sub(
            r'console\.log\(`([^`]*)\n\n\s*}\'\)',
            r'console.log(`\1`)',
            content,
            flags=re.MULTILINE | re.DOTALL
        )
        
        # Fix pattern 2: console.log with broken quotes
        content = re.sub(
            r'console\.log\(\'([^\']*)\n\n\s*}\'\)',
            r'console.log(`\1`)',
            content,
            flags=re.MULTILINE | re.DOTALL
        )
        
        # Fix pattern 3: console.warn with broken quotes
        content = re.sub(
            r'console\.warn\(\'([^\']*)\n\n\s*}\'\)',
            r'console.warn(`\1`)',
            content,
            flags=re.MULTILINE | re.DOTALL
        )
        
        # Fix pattern 4: Unterminated string literals
        content = re.sub(
            r'console\.(log|warn|error)\(\'([^\']*)\n\n\s*}\'\:',
            r'console.\1(`\2`:',
            content,
            flags=re.MULTILINE | re.DOTALL
        )
        
        # Fix pattern 5: Missing closing quotes
        content = re.sub(
            r'console\.(log|warn|error)\(\'([^\']*)\n\n\s*}\'\)',
            r'console.\1(`\2`)',
            content,
            flags=re.MULTILINE | re.DOTALL
        )
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Fixed console statements in {file_path}")
            return True
        else:
            print(f"ℹ️ No console fixes needed in {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error fixing {file_path}: {e}")
        return False

def fix_specific_files():
    """Fix specific files with known issues"""
    frontend_dir = Path("frontend/src")
    
    files_to_fix = [
        "utils/memoryOptimization.ts",
        "utils/performanceValidator.ts", 
        "services/websocket.ts",
        "utils/tokenRefreshManager.ts"
    ]
    
    fixed_count = 0
    
    for file_path in files_to_fix:
        full_path = frontend_dir / file_path
        if full_path.exists():
            if fix_malformed_console_logs(full_path):
                fixed_count += 1
        else:
            print(f"⚠️ File not found: {full_path}")
    
    return fixed_count

def main():
    print("🔧 Fixing Syntax Errors Introduced by Performance Script")
    print("=" * 60)
    
    fixed_count = fix_specific_files()
    
    print(f"\n📊 Fixed {fixed_count} files")
    print("✅ Syntax errors should now be resolved")
    print("   The frontend server should automatically reload")
    
    return 0

if __name__ == "__main__":
    exit(main())
