/**
 * Redux-integrated CSRF token management
 * Prevents conflicts between CSRF fetching and Redux state
 */

import { store } from '../store'
import { fetchCSRFToken, clearCSRFToken } from '../store/slices/authSlice'

/**
 * Get CSRF token from Redux state
 */
export function getCSRFTokenFromRedux(): string | null {
  const state = store.getState()
  const { csrfToken, csrfTokenExpiry } = state.auth
  
  // Check if token is still valid
  if (csrfToken && Date.now() < csrfTokenExpiry) {
    return csrfToken
  }
  
  return null
}

/**
 * Ensure CSRF token is available via Redux
 */
export async function ensureCSRFTokenRedux(): Promise<string | null> {
  const state = store.getState()
  
  // Don't fetch if already fetching
  if (state.auth.isFetchingCsrf) {
    return getCSRFTokenFromRedux()
  }
  
  // Don't fetch during logout
  if (!state.auth.isAuthenticated) {
    return null
  }
  
  // Check if we have a valid token
  const existingToken = getCSRFTokenFromRedux()
  if (existingToken) {
    return existingToken
  }
  
  // Fetch new token via Redux
  try {
    const result = await store.dispatch(fetchCSRFToken())
    if (fetchCSRFToken.fulfilled.match(result)) {
      return result.payload
    }
  } catch (error) {
    console.error('Failed to fetch CSRF token via Redux:', error)
  }
  
  return null
}

/**
 * Get CSRF headers for API requests (Redux-aware)
 */
export function getCSRFHeadersRedux(): HeadersInit {
  const state = store.getState()
  
  // Don't include CSRF headers if not authenticated
  if (!state.auth.isAuthenticated) {
    return {}
  }
  
  const token = getCSRFTokenFromRedux()
  const headers: HeadersInit = {}
  
  if (token) {
    headers['X-CSRFToken'] = token
  }
  
  return headers
}

/**
 * Clear CSRF token via Redux
 */
export function clearCSRFTokenRedux(): void {
  store.dispatch(clearCSRFToken())
}

/**
 * Initialize CSRF protection via Redux
 */
export async function initializeCSRFProtectionRedux(): Promise<void> {
  const state = store.getState()
  
  // Only initialize if authenticated
  if (state.auth.isAuthenticated) {
    try {
      await ensureCSRFTokenRedux()
      console.log('✅ CSRF protection initialized via Redux')
    } catch (error) {
      console.error('❌ Failed to initialize CSRF protection via Redux:', error)
    }
  }
}
