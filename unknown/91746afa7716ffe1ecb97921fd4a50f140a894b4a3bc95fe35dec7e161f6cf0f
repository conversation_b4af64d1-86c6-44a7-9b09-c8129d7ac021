/**
 * Advanced Analytics & User Behavior Tracking
 * Privacy-focused analytics with comprehensive user experience insights
 */

import { log } from './logger'
import { progressiveEnhancement } from './progressiveEnhancement'

interface AnalyticsEvent {
  type: 'page_view' | 'user_action' | 'performance' | 'error' | 'engagement'
  category: string
  action: string
  label?: string
  value?: number
  metadata?: Record<string, any>
  timestamp: number
  sessionId: string
  userId?: string
}

interface UserSession {
  id: string
  startTime: number
  lastActivity: number
  pageViews: number
  actions: number
  errors: number
  deviceInfo: {
    userAgent: string
    viewport: { width: number; height: number }
    language: string
    timezone: string
    platform: string
  }
  performanceMetrics: {
    averageLoadTime: number
    totalLoadTime: number
    slowestPage: string
  }
}

interface AnalyticsConfig {
  enabled: boolean
  respectDNT: boolean // Do Not Track
  anonymizeIP: boolean
  sessionTimeout: number
  batchSize: number
  flushInterval: number
  enableHeatmaps: boolean
  enableScrollTracking: boolean
  enableClickTracking: boolean
  enableFormTracking: boolean
}

class AnalyticsManager {
  private config: AnalyticsConfig
  private session: UserSession
  private eventQueue: AnalyticsEvent[] = []
  private flushTimer?: NodeJS.Timeout
  private isInitialized = false

  constructor(config: Partial<AnalyticsConfig> = {}) {
    this.config = {
      enabled: true,
      respectDNT: true,
      anonymizeIP: true,
      sessionTimeout: 30 * 60 * 1000, // 30 minutes
      batchSize: 10,
      flushInterval: 5000, // 5 seconds
      enableHeatmaps: false, // Privacy-focused default
      enableScrollTracking: true,
      enableClickTracking: true,
      enableFormTracking: true,
      ...config
    }

    this.session = this.createSession()
    this.initialize()
  }

  private initialize(): void {
    if (!this.shouldTrack()) {
      log.info('analytics', 'Analytics disabled due to DNT or configuration')
      return
    }

    this.setupEventListeners()
    this.startFlushTimer()
    this.trackPageView()
    this.isInitialized = true

    log.info('analytics', 'Analytics initialized', {
      sessionId: this.session.id,
      config: this.config
    })
  }

  private shouldTrack(): boolean {
    // Respect Do Not Track header
    if (this.config.respectDNT && navigator.doNotTrack === '1') {
      return false
    }

    // Check if analytics is enabled
    if (!this.config.enabled) {
      return false
    }

    // Check if in development mode
    if (import.meta.env.DEV && !import.meta.env.VITE_ENABLE_ANALYTICS) {
      return false
    }

    return true
  }

  private createSession(): UserSession {
    const sessionId = this.generateSessionId()
    const now = Date.now()

    return {
      id: sessionId,
      startTime: now,
      lastActivity: now,
      pageViews: 0,
      actions: 0,
      errors: 0,
      deviceInfo: {
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        language: navigator.language,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        platform: navigator.platform
      },
      performanceMetrics: {
        averageLoadTime: 0,
        totalLoadTime: 0,
        slowestPage: ''
      }
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private setupEventListeners(): void {
    // Page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.trackEvent('engagement', 'page', 'hidden')
        this.flushEvents()
      } else {
        this.trackEvent('engagement', 'page', 'visible')
      }
    })

    // Click tracking
    if (this.config.enableClickTracking) {
      document.addEventListener('click', (e) => {
        this.trackClick(e)
      })
    }

    // Scroll tracking
    if (this.config.enableScrollTracking) {
      let scrollDepth = 0
      const trackScroll = () => {
        const currentDepth = Math.round(
          (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100
        )
        
        if (currentDepth > scrollDepth && currentDepth % 25 === 0) {
          scrollDepth = currentDepth
          this.trackEvent('engagement', 'scroll', 'depth', currentDepth)
        }
      }

      window.addEventListener('scroll', trackScroll, { passive: true })
    }

    // Form tracking
    if (this.config.enableFormTracking) {
      document.addEventListener('submit', (e) => {
        this.trackFormSubmission(e)
      })

      document.addEventListener('input', (e) => {
        this.trackFormInteraction(e)
      })
    }

    // Error tracking
    window.addEventListener('error', (e) => {
      this.trackError(e.error, 'javascript_error')
    })

    window.addEventListener('unhandledrejection', (e) => {
      this.trackError(e.reason, 'unhandled_promise_rejection')
    })

    // Page unload
    window.addEventListener('beforeunload', () => {
      this.trackEvent('engagement', 'page', 'unload')
      this.flushEvents()
    })
  }

  private trackClick(event: MouseEvent): void {
    const target = event.target as HTMLElement
    const tagName = target.tagName.toLowerCase()
    const className = target.className
    const id = target.id
    const text = target.textContent?.substring(0, 50) || ''

    this.trackEvent('user_action', 'click', tagName, undefined, {
      className,
      id,
      text,
      x: event.clientX,
      y: event.clientY
    })
  }

  private trackFormSubmission(event: Event): void {
    const form = event.target as HTMLFormElement
    const formId = form.id || 'unknown'
    const formAction = form.action || 'unknown'

    this.trackEvent('user_action', 'form_submit', formId, undefined, {
      action: formAction,
      method: form.method
    })
  }

  private trackFormInteraction(event: Event): void {
    const input = event.target as HTMLInputElement
    const inputType = input.type || 'unknown'
    const inputName = input.name || 'unknown'

    this.trackEvent('user_action', 'form_interaction', inputType, undefined, {
      name: inputName,
      formId: input.form?.id || 'unknown'
    })
  }

  public trackPageView(path?: string): void {
    const currentPath = path || window.location.pathname
    const referrer = document.referrer || 'direct'

    this.session.pageViews++
    this.session.lastActivity = Date.now()

    this.trackEvent('page_view', 'navigation', 'page_view', undefined, {
      path: currentPath,
      referrer,
      title: document.title,
      loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart
    })
  }

  public trackEvent(
    type: AnalyticsEvent['type'],
    category: string,
    action: string,
    label?: string,
    metadata?: Record<string, any>,
    value?: number
  ): void {
    if (!this.isInitialized || !this.shouldTrack()) return

    const event: AnalyticsEvent = {
      type,
      category,
      action,
      label,
      value,
      metadata,
      timestamp: Date.now(),
      sessionId: this.session.id
    }

    this.eventQueue.push(event)
    this.session.actions++
    this.session.lastActivity = Date.now()

    log.debug('analytics', 'Event tracked', event)

    // Flush if queue is full
    if (this.eventQueue.length >= this.config.batchSize) {
      this.flushEvents()
    }
  }

  public trackError(error: Error | any, category = 'error'): void {
    this.session.errors++

    this.trackEvent('error', category, 'error_occurred', error.message, {
      stack: error.stack,
      name: error.name,
      url: window.location.href,
      userAgent: navigator.userAgent
    })
  }

  public trackPerformance(metrics: Record<string, number>): void {
    this.trackEvent('performance', 'metrics', 'performance_data', undefined, metrics)
  }

  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      if (this.eventQueue.length > 0) {
        this.flushEvents()
      }
    }, this.config.flushInterval)
  }

  private async flushEvents(): Promise<void> {
    if (this.eventQueue.length === 0) return

    const events = [...this.eventQueue]
    this.eventQueue = []

    try {
      // In production, send to analytics service
      if (import.meta.env.PROD) {
        await this.sendToAnalyticsService(events)
      } else {
        log.debug('analytics', 'Events flushed (dev mode)', events)
      }
    } catch (error) {
      log.error('analytics', 'Failed to flush events', error)
      // Re-queue events on failure
      this.eventQueue.unshift(...events)
    }
  }

  private async sendToAnalyticsService(events: AnalyticsEvent[]): Promise<void> {
    const payload = {
      session: this.session,
      events,
      timestamp: Date.now()
    }

    // Use fetch with retry logic
    const response = await fetch('/api/analytics/events', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    })

    if (!response.ok) {
      throw new Error(`Analytics API error: ${response.status}`)
    }
  }

  public getSession(): UserSession {
    return { ...this.session }
  }

  public destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer)
    }
    this.flushEvents()
    this.isInitialized = false
  }
}

// Singleton instance
export const analytics = new AnalyticsManager()

// Convenience functions
export const trackPageView = (path?: string) => analytics.trackPageView(path)
export const trackEvent = (category: string, action: string, label?: string, value?: number) => 
  analytics.trackEvent('user_action', category, action, label, undefined, value)
export const trackError = (error: Error | any) => analytics.trackError(error)
export const trackPerformance = (metrics: Record<string, number>) => analytics.trackPerformance(metrics)

export default analytics
