/**
 * Performance Monitoring & Core Web Vitals Tracking
 * Comprehensive performance metrics collection and analysis
 */

// Performance metrics interface
export interface PerformanceMetrics {
  // Core Web Vitals
  lcp?: number // Largest Contentful Paint
  fid?: number // First Input Delay
  cls?: number // Cumulative Layout Shift
  
  // Other important metrics
  fcp?: number // First Contentful Paint
  ttfb?: number // Time to First Byte
  
  // Custom metrics
  navigationStart?: number
  domContentLoaded?: number
  loadComplete?: number
  
  // Resource timing
  resourceCount?: number
  totalResourceSize?: number
  
  // Memory usage (if available)
  usedJSHeapSize?: number
  totalJSHeapSize?: number
  jsHeapSizeLimit?: number
  
  // Network information
  connectionType?: string
  effectiveType?: string
  downlink?: number
  
  // User agent info
  userAgent?: string
  viewport?: {
    width: number
    height: number
  }

  // ENHANCEMENT: Advanced analytics
  sessionId?: string
  userId?: string
  pageLoadId?: string

  // User engagement metrics
  userEngagement?: {
    timeOnPage: number
    scrollDepth: number
    clickCount: number
    formInteractions: number
    visibilityChanges: number
  }

  // Performance insights
  performanceInsights?: {
    slowestResource?: string
    largestResource?: string
    renderBlockingResources?: number
    unusedCSSBytes?: number
    unusedJSBytes?: number
  }

  timestamp: number
}

// Performance budget thresholds
export const PERFORMANCE_BUDGETS = {
  // Core Web Vitals thresholds (Google recommendations)
  LCP_GOOD: 2500, // ms
  LCP_NEEDS_IMPROVEMENT: 4000, // ms
  
  FID_GOOD: 100, // ms
  FID_NEEDS_IMPROVEMENT: 300, // ms
  
  CLS_GOOD: 0.1,
  CLS_NEEDS_IMPROVEMENT: 0.25,
  
  // Other metrics
  FCP_GOOD: 1800, // ms
  TTFB_GOOD: 800, // ms
  
  // Bundle size budgets
  INITIAL_BUNDLE_SIZE: 250 * 1024, // 250KB
  TOTAL_BUNDLE_SIZE: 1024 * 1024, // 1MB
  
  // Resource budgets
  MAX_RESOURCES: 100,
  MAX_RESOURCE_SIZE: 10 * 1024 * 1024, // 10MB
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics = {
    timestamp: Date.now()
  }
  
  private observers: PerformanceObserver[] = []
  private isMonitoring = false
  
  constructor() {
    this.initializeMonitoring()
  }
  
  private initializeMonitoring() {
    // PERFORMANCE FIX: Disable monitoring in production unless explicitly enabled
    const isEnabled = process.env.NODE_ENV === 'development' ||
                     import.meta.env.VITE_ENABLE_PERFORMANCE_MONITORING === 'true'

    if (typeof window === 'undefined' || this.isMonitoring || !isEnabled) return

    this.isMonitoring = true
    
    // Wait for page load to start monitoring
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.startMonitoring()
      })
    } else {
      this.startMonitoring()
    }
  }
  
  private startMonitoring() {
    this.collectNavigationTiming()
    this.collectResourceTiming()
    this.collectMemoryInfo()
    this.collectNetworkInfo()
    this.collectViewportInfo()
    this.setupCoreWebVitals()
  }
  
  private collectNavigationTiming() {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    
    if (navigation) {
      this.metrics.navigationStart = navigation.startTime || performance.timeOrigin
      this.metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.startTime
      this.metrics.loadComplete = navigation.loadEventEnd - navigation.startTime
      this.metrics.ttfb = navigation.responseStart - navigation.requestStart
    }
  }
  
  private collectResourceTiming() {
    const resources = performance.getEntriesByType('resource')
    this.metrics.resourceCount = resources.length
    this.metrics.totalResourceSize = resources.reduce((total, resource) => {
      return total + ((resource as any).transferSize || 0)
    }, 0)
  }
  
  private collectMemoryInfo() {
    const memory = (performance as Performance & { 
      memory?: { 
        usedJSHeapSize: number
        totalJSHeapSize: number
        jsHeapSizeLimit: number
      } 
    }).memory
    
    if (memory) {
      this.metrics.usedJSHeapSize = memory.usedJSHeapSize
      this.metrics.totalJSHeapSize = memory.totalJSHeapSize
      this.metrics.jsHeapSizeLimit = memory.jsHeapSizeLimit
    }
  }
  
  private collectNetworkInfo() {
    const connection = (navigator as Navigator & { 
      connection?: { 
        effectiveType?: string
        downlink?: number
        type?: string
      }
    }).connection
    
    if (connection) {
      this.metrics.connectionType = connection.type
      this.metrics.effectiveType = connection.effectiveType
      this.metrics.downlink = connection.downlink
    }
  }
  
  private collectViewportInfo() {
    this.metrics.viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    }
    this.metrics.userAgent = navigator.userAgent
  }
  
  private setupCoreWebVitals() {
    // Largest Contentful Paint (LCP)
    this.observePerformanceEntry('largest-contentful-paint', (entries) => {
      const lastEntry = entries[entries.length - 1]
      this.metrics.lcp = lastEntry.startTime
    })
    
    // First Input Delay (FID)
    this.observePerformanceEntry('first-input', (entries) => {
      const firstEntry = entries[0] as PerformanceEntry & { processingStart: number }
      this.metrics.fid = firstEntry.processingStart - firstEntry.startTime
    })
    
    // Cumulative Layout Shift (CLS)
    this.observePerformanceEntry('layout-shift', (entries) => {
      let clsValue = 0
      for (const entry of entries) {
        const layoutShiftEntry = entry as PerformanceEntry & { 
          hadRecentInput?: boolean
          value: number
        }
        if (!layoutShiftEntry.hadRecentInput) {
          clsValue += layoutShiftEntry.value
        }
      }
      this.metrics.cls = clsValue
    })
    
    // First Contentful Paint (FCP)
    this.observePerformanceEntry('paint', (entries) => {
      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint')
      if (fcpEntry) {
        this.metrics.fcp = fcpEntry.startTime
      }
    })
  }
  
  private observePerformanceEntry(
    entryType: string, 
    callback: (entries: PerformanceEntry[]) => void
  ) {
    try {
      const observer = new PerformanceObserver((list) => {
        callback(list.getEntries())
      })
      
      observer.observe({ entryTypes: [entryType] })
      this.observers.push(observer)
    } catch (error) {
      console.warn(`Performance observer for ${entryType} not supported:`, error)
    }
  }
  
  // Public methods
  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics }
  }
  
  public getPerformanceScore(): {
    score: number
    details: Record<string, { value: number; threshold: number; status: 'good' | 'needs-improvement' | 'poor' }>
  } {
    const details: Record<string, { value: number; threshold: number; status: 'good' | 'needs-improvement' | 'poor' }> = {}
    let totalScore = 0
    let metricCount = 0
    
    // LCP scoring
    if (this.metrics.lcp !== undefined) {
      const status = this.metrics.lcp <= PERFORMANCE_BUDGETS.LCP_GOOD ? 'good' :
                    this.metrics.lcp <= PERFORMANCE_BUDGETS.LCP_NEEDS_IMPROVEMENT ? 'needs-improvement' : 'poor'
      details.lcp = { value: this.metrics.lcp, threshold: PERFORMANCE_BUDGETS.LCP_GOOD, status }
      totalScore += status === 'good' ? 100 : status === 'needs-improvement' ? 50 : 0
      metricCount++
    }
    
    // FID scoring
    if (this.metrics.fid !== undefined) {
      const status = this.metrics.fid <= PERFORMANCE_BUDGETS.FID_GOOD ? 'good' :
                    this.metrics.fid <= PERFORMANCE_BUDGETS.FID_NEEDS_IMPROVEMENT ? 'needs-improvement' : 'poor'
      details.fid = { value: this.metrics.fid, threshold: PERFORMANCE_BUDGETS.FID_GOOD, status }
      totalScore += status === 'good' ? 100 : status === 'needs-improvement' ? 50 : 0
      metricCount++
    }
    
    // CLS scoring
    if (this.metrics.cls !== undefined) {
      const status = this.metrics.cls <= PERFORMANCE_BUDGETS.CLS_GOOD ? 'good' :
                    this.metrics.cls <= PERFORMANCE_BUDGETS.CLS_NEEDS_IMPROVEMENT ? 'needs-improvement' : 'poor'
      details.cls = { value: this.metrics.cls, threshold: PERFORMANCE_BUDGETS.CLS_GOOD, status }
      totalScore += status === 'good' ? 100 : status === 'needs-improvement' ? 50 : 0
      metricCount++
    }
    
    // FCP scoring
    if (this.metrics.fcp !== undefined) {
      const status = this.metrics.fcp <= PERFORMANCE_BUDGETS.FCP_GOOD ? 'good' : 'needs-improvement'
      details.fcp = { value: this.metrics.fcp, threshold: PERFORMANCE_BUDGETS.FCP_GOOD, status }
      totalScore += status === 'good' ? 100 : 50
      metricCount++
    }
    
    const score = metricCount > 0 ? Math.round(totalScore / metricCount) : 0
    
    return { score, details }
  }
  
  public logPerformanceReport() {
    // PERFORMANCE FIX: Only log in development or when explicitly enabled
    if (process.env.NODE_ENV !== 'development' &&
        import.meta.env.VITE_ENABLE_PERFORMANCE_MONITORING !== 'true') {
      return
    }

    const metrics = this.getMetrics()
    const score = this.getPerformanceScore()

    console.group('🚀 Performance Report')
    console.log('Overall Score:', score.score)
    console.log('Core Web Vitals:', score.details)
    console.log('Full Metrics:', metrics)
    console.groupEnd()
    
    // Log warnings for poor performance
    Object.entries(score.details).forEach(([metric, data]) => {
      if (data.status === 'poor') {
        console.warn(`⚠️ Poor ${metric.toUpperCase()}: ${data.value}ms (threshold: ${data.threshold}ms)`)
      }
    })
  }
  
  public sendMetricsToAnalytics(endpoint?: string) {
    const metrics = this.getMetrics()
    const score = this.getPerformanceScore()
    
    const payload = {
      ...metrics,
      performanceScore: score.score,
      url: window.location.href,
      timestamp: Date.now()
    }
    
    if (endpoint) {
      // Send to custom analytics endpoint
      fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      }).catch(error => console.warn('Failed to send performance metrics:', error))
    }
    
    // Also log to console in development
    if (process.env.NODE_ENV === 'development') {
      this.logPerformanceReport()
    }
  }

  /**
   * ENHANCEMENT: Generate comprehensive performance report
   */
  public generateAdvancedReport(): {
    score: number
    metrics: PerformanceMetrics
    recommendations: string[]
    insights: any
  } {
    const score = this.getPerformanceScore()
    const recommendations: string[] = []

    // LCP recommendations
    if (this.currentMetrics.lcp && this.currentMetrics.lcp > PERFORMANCE_BUDGETS.LCP_GOOD) {
      recommendations.push('تحسين أكبر عنصر محتوى (LCP) - استخدم تحسين الصور وتحميل الموارد المهمة أولاً')
    }

    // FID recommendations
    if (this.currentMetrics.fid && this.currentMetrics.fid > PERFORMANCE_BUDGETS.FID_GOOD) {
      recommendations.push('تحسين تأخير الإدخال الأول (FID) - قلل من JavaScript الثقيل وحسن استجابة الصفحة')
    }

    // CLS recommendations
    if (this.currentMetrics.cls && this.currentMetrics.cls > PERFORMANCE_BUDGETS.CLS_GOOD) {
      recommendations.push('تحسين تحول التخطيط التراكمي (CLS) - حدد أبعاد الصور والعناصر مسبقاً')
    }

    return {
      score: score.score,
      metrics: this.currentMetrics,
      recommendations,
      insights: {
        coreWebVitals: score.details,
        userEngagement: this.currentMetrics.userEngagement,
        performanceInsights: this.currentMetrics.performanceInsights
      }
    }
  }

  public cleanup() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    this.isMonitoring = false
  }
}

// Singleton instance
let performanceMonitor: PerformanceMonitor | null = null

export const getPerformanceMonitor = (): PerformanceMonitor => {
  if (!performanceMonitor) {
    performanceMonitor = new PerformanceMonitor()
  }
  return performanceMonitor
}

// React hook for performance monitoring
export const usePerformanceMonitoring = () => {
  const monitor = getPerformanceMonitor()
  
  return {
    getMetrics: () => monitor.getMetrics(),
    getScore: () => monitor.getPerformanceScore(),
    logReport: () => monitor.logPerformanceReport(),
    sendToAnalytics: (endpoint?: string) => monitor.sendMetricsToAnalytics(endpoint)
  }
}

// PERFORMANCE OPTIMIZATION: Only initialize when explicitly requested
// Automatic initialization causes unnecessary overhead
if (typeof window !== 'undefined' &&
    (window.location.search.includes('debug=performance') ||
     localStorage.getItem('enablePerformanceMonitoring') === 'true')) {
  getPerformanceMonitor()
  console.log('📊 Performance monitoring initialized (debug mode)')
}
