#!/usr/bin/env python3
"""
Modern Authentication System Update Script
Fixes deprecated authentication methods and updates to modern JWT patterns
"""

import os
import sys
import subprocess
import django
from pathlib import Path

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

def run_command(command, cwd=None):
    """Run a shell command and return success status and output"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            timeout=60
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def apply_migrations():
    """Apply Django migrations for token blacklist"""
    print("🔄 Applying Django migrations for modern JWT...")
    
    # Apply migrations
    success, stdout, stderr = run_command("python manage.py migrate")
    
    if success:
        print("✅ Migrations applied successfully!")
        if stdout.strip():
            print(stdout)
        return True
    else:
        print("❌ Migration failed:")
        print(stderr)
        return False

def check_deprecated_patterns():
    """Check for deprecated authentication patterns"""
    print("🔍 Checking for deprecated authentication patterns...")
    
    deprecated_patterns = []
    
    # Check for deprecated imports
    auth_views_file = Path("ems/auth_views.py")
    if auth_views_file.exists():
        content = auth_views_file.read_text()
        if "from rest_framework_simplejwt.views import TokenObtainPairView" in content:
            deprecated_patterns.append("❌ Unused TokenObtainPairView import found")
        else:
            print("✅ No deprecated JWT view imports found")
    
    # Check authentication.py for deprecated patterns
    auth_file = Path("ems/authentication.py")
    if auth_file.exists():
        content = auth_file.read_text()
        if "raw_token.encode('utf-8')" in content:
            deprecated_patterns.append("❌ Deprecated token encoding pattern found")
        else:
            print("✅ No deprecated token encoding patterns found")
    
    # Check settings for deprecated JWT settings
    settings_file = Path("backend/settings.py")
    if settings_file.exists():
        content = settings_file.read_text()
        if "'BLACKLIST_AFTER_ROTATION': False" in content:
            deprecated_patterns.append("❌ Token blacklisting is disabled (deprecated)")
        else:
            print("✅ Modern JWT settings configured")
    
    if deprecated_patterns:
        print("\n🚨 Deprecated patterns found:")
        for pattern in deprecated_patterns:
            print(f"  {pattern}")
        return False
    else:
        print("✅ No deprecated authentication patterns found!")
        return True

def test_modern_auth():
    """Test the modern authentication system"""
    print("\n🧪 Testing modern authentication system...")
    
    try:
        # Initialize Django
        django.setup()
        
        from django.contrib.auth.models import User
        from rest_framework_simplejwt.tokens import RefreshToken
        
        # Test token creation
        test_user = User.objects.filter(username='layla.hassan').first()
        if not test_user:
            print("❌ Test user 'layla.hassan' not found")
            return False
        
        # Create a refresh token
        refresh = RefreshToken.for_user(test_user)
        access = refresh.access_token
        
        print(f"✅ Token creation successful")
        print(f"  Access token: {str(access)[:50]}...")
        print(f"  Refresh token: {str(refresh)[:50]}...")
        
        # Test blacklisting
        try:
            refresh.blacklist()
            print("✅ Token blacklisting works correctly")
        except Exception as e:
            print(f"❌ Token blacklisting failed: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Authentication test failed: {e}")
        return False

def main():
    """Main update process"""
    print("🔧 Modern Authentication System Update")
    print("=" * 50)
    
    # Step 1: Apply migrations
    if not apply_migrations():
        print("\n❌ Failed to apply migrations")
        return 1
    
    # Step 2: Check for deprecated patterns
    if not check_deprecated_patterns():
        print("\n⚠️  Some deprecated patterns still exist")
        print("   Please review the authentication code manually")
    
    # Step 3: Test modern authentication
    if not test_modern_auth():
        print("\n❌ Modern authentication test failed")
        return 1
    
    print("\n🎉 Authentication system successfully updated!")
    print("\n📋 Modern Features Enabled:")
    print("  ✅ Token blacklisting for security")
    print("  ✅ Token rotation on refresh")
    print("  ✅ HttpOnly cookie authentication")
    print("  ✅ Modern JWT settings")
    print("  ✅ Removed deprecated imports")
    print("  ✅ Updated token encoding patterns")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
