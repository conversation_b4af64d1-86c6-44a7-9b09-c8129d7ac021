"""
Advanced Customer Service Services
Automated routing, AI integration, and workflow automation
"""

import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from django.utils import timezone
from django.db.models import Q, Count, Avg
from django.contrib.auth.models import User
from .models import SupportTicket, SupportAgent, TicketComment
from .models_advanced import (
    TicketRoutingRule, EscalationRule, AIAssistant, AIInteraction,
    WorkflowAutomation, WorkflowExecution, CustomerTier, CustomerProfile
)

class TicketRoutingService:
    """Automated ticket routing and assignment"""
    
    @staticmethod
    def route_ticket(ticket: SupportTicket) -> Optional[SupportAgent]:
        """Apply routing rules to assign ticket to appropriate agent"""
        
        # Get active routing rules ordered by priority
        rules = TicketRoutingRule.objects.filter(is_active=True).order_by('-priority')
        
        for rule in rules:
            if TicketRoutingService._evaluate_rule_condition(ticket, rule):
                agent = TicketRoutingService._execute_rule_action(ticket, rule)
                if agent:
                    return agent
        
        # Fallback to load balancing if no rules match
        return TicketRoutingService._load_balance_assignment(ticket)
    
    @staticmethod
    def _evaluate_rule_condition(ticket: SupportTicket, rule: TicketRoutingRule) -> bool:
        """Evaluate if a routing rule condition matches the ticket"""
        condition_params = rule.get_condition_params()
        
        if rule.condition_type == 'keyword':
            keywords = condition_params.get('keywords', [])
            text_to_search = f"{ticket.title} {ticket.description}".lower()
            return any(keyword.lower() in text_to_search for keyword in keywords)
        
        elif rule.condition_type == 'category':
            return ticket.category and ticket.category.id in condition_params.get('category_ids', [])
        
        elif rule.condition_type == 'priority':
            return ticket.priority in condition_params.get('priorities', [])
        
        elif rule.condition_type == 'customer_tier':
            customer_profile = getattr(ticket.customer, 'customer_profile', None)
            if customer_profile and customer_profile.customer_tier:
                return customer_profile.customer_tier.tier_level in condition_params.get('tiers', [])
        
        elif rule.condition_type == 'time_of_day':
            current_hour = timezone.now().hour
            start_hour = condition_params.get('start_hour', 0)
            end_hour = condition_params.get('end_hour', 23)
            return start_hour <= current_hour <= end_hour
        
        elif rule.condition_type == 'agent_workload':
            max_workload = condition_params.get('max_workload', 10)
            available_agents = SupportAgent.objects.filter(
                is_available=True,
                current_ticket_count__lt=max_workload
            )
            return available_agents.exists()
        
        elif rule.condition_type == 'language':
            customer_profile = getattr(ticket.customer, 'customer_profile', None)
            if customer_profile:
                return customer_profile.preferred_language in condition_params.get('languages', ['en'])
        
        elif rule.condition_type == 'channel':
            return ticket.source in condition_params.get('channels', [])
        
        return False
    
    @staticmethod
    def _execute_rule_action(ticket: SupportTicket, rule: TicketRoutingRule) -> Optional[SupportAgent]:
        """Execute the action defined in a routing rule"""
        action_params = rule.get_action_params()
        
        if rule.action_type == 'assign_agent':
            agent_id = action_params.get('agent_id')
            if agent_id:
                try:
                    agent = SupportAgent.objects.get(id=agent_id, is_available=True)
                    if agent.current_ticket_count < agent.max_concurrent_tickets:
                        return agent
                except SupportAgent.DoesNotExist:
                    pass
        
        elif rule.action_type == 'assign_team':
            specializations = action_params.get('specializations', [])
            agents = SupportAgent.objects.filter(
                is_available=True,
                specializations__overlap=specializations
            ).annotate(
                workload=Count('assigned_tickets', filter=Q(assigned_tickets__status__in=['open', 'in_progress']))
            ).order_by('workload')
            
            if agents.exists():
                return agents.first()
        
        elif rule.action_type == 'set_priority':
            new_priority = action_params.get('priority')
            if new_priority:
                ticket.priority = new_priority
                ticket.save()
        
        elif rule.action_type == 'add_tag':
            tags = action_params.get('tags', [])
            current_tags = ticket.tags.split(',') if ticket.tags else []
            ticket.tags = ','.join(set(current_tags + tags))
            ticket.save()
        
        return None
    
    @staticmethod
    def _load_balance_assignment(ticket: SupportTicket) -> Optional[SupportAgent]:
        """Assign ticket using load balancing when no rules match"""
        
        # Find agents with matching specializations
        category_name = ticket.category.name.lower() if ticket.category else ''
        matching_agents = SupportAgent.objects.filter(
            is_available=True,
            specializations__icontains=category_name
        )
        
        if not matching_agents.exists():
            # Fallback to any available agent
            matching_agents = SupportAgent.objects.filter(is_available=True)
        
        if matching_agents.exists():
            # Select agent with lowest current workload
            agent = matching_agents.annotate(
                workload=Count('assigned_tickets', filter=Q(assigned_tickets__status__in=['open', 'in_progress']))
            ).filter(
                workload__lt=models.F('max_concurrent_tickets')
            ).order_by('workload').first()
            
            return agent
        
        return None

class EscalationService:
    """Automated ticket escalation"""
    
    @staticmethod
    def check_escalations():
        """Check all tickets for escalation conditions"""
        rules = EscalationRule.objects.filter(is_active=True)
        
        for rule in rules:
            tickets = EscalationService._get_tickets_for_escalation(rule)
            for ticket in tickets:
                EscalationService._escalate_ticket(ticket, rule)
    
    @staticmethod
    def _get_tickets_for_escalation(rule: EscalationRule) -> List[SupportTicket]:
        """Get tickets that match escalation rule conditions"""
        tickets = SupportTicket.objects.filter(status__in=['open', 'in_progress'])
        
        if rule.trigger_type == 'time_based' and rule.trigger_after_hours:
            cutoff_time = timezone.now() - timedelta(hours=rule.trigger_after_hours)
            tickets = tickets.filter(created_at__lte=cutoff_time)
        
        elif rule.trigger_type == 'sla_breach':
            tickets = tickets.filter(is_sla_breached=True)
        
        elif rule.trigger_type == 'agent_unavailable':
            tickets = tickets.filter(
                assigned_agent__isnull=False,
                assigned_agent__is_available=False
            )
        
        return list(tickets)
    
    @staticmethod
    def _escalate_ticket(ticket: SupportTicket, rule: EscalationRule):
        """Escalate a ticket according to the rule"""
        
        # Assign to escalation agent
        if rule.escalate_to_agent:
            ticket.assigned_agent = rule.escalate_to_agent
        
        # Increase priority
        if rule.increase_priority:
            priority_levels = ['low', 'medium', 'high', 'critical']
            current_index = priority_levels.index(ticket.priority) if ticket.priority in priority_levels else 0
            if current_index < len(priority_levels) - 1:
                ticket.priority = priority_levels[current_index + 1]
        
        ticket.save()
        
        # Add escalation comment
        TicketComment.objects.create(
            ticket=ticket,
            author=None,  # System comment
            comment_type='system',
            content=f"Ticket escalated automatically due to: {rule.name}",
            is_public=False
        )
        
        # Send notifications (implementation would depend on notification system)
        if rule.send_email_notification:
            EscalationService._send_escalation_notification(ticket, rule)
    
    @staticmethod
    def _send_escalation_notification(ticket: SupportTicket, rule: EscalationRule):
        """Send escalation notification (placeholder for email service)"""
        # This would integrate with your email service
        pass

class AIAssistantService:
    """AI-powered assistance and automation"""
    
    @staticmethod
    def get_ai_response(assistant: AIAssistant, user_input: str, context: Dict = None) -> Dict:
        """Get AI response for user input"""
        
        # This is a placeholder for actual AI integration
        # In production, this would call OpenAI, Azure AI, or other AI services
        
        response_data = {
            'response': AIAssistantService._generate_mock_response(user_input, assistant),
            'confidence': 0.85,
            'suggestions': AIAssistantService._get_knowledge_suggestions(user_input),
            'escalate_to_human': False
        }
        
        # Log the interaction
        AIInteraction.objects.create(
            assistant=assistant,
            user_input=user_input,
            ai_response=response_data['response'],
            confidence_score=response_data['confidence'],
            context_data=json.dumps(context or {})
        )
        
        # Update assistant statistics
        assistant.total_interactions += 1
        assistant.save()
        
        return response_data
    
    @staticmethod
    def _generate_mock_response(user_input: str, assistant: AIAssistant) -> str:
        """Generate basic response (TODO: replace with actual AI service)"""

        # Return a generic response until real AI service is implemented
        return "Thank you for your message. A customer service representative will assist you shortly. Please provide more details about your inquiry so we can help you better."
    
    @staticmethod
    def _get_knowledge_suggestions(user_input: str) -> List[Dict]:
        """Get relevant knowledge base suggestions"""
        from .models import KnowledgeBaseArticle
        
        # Simple keyword search (in production, use full-text search or vector similarity)
        keywords = user_input.lower().split()
        articles = KnowledgeBaseArticle.objects.filter(
            is_published=True
        )
        
        suggestions = []
        for article in articles[:3]:  # Limit to top 3
            title_lower = article.title.lower()
            content_lower = article.content.lower()
            
            # Simple relevance scoring
            score = sum(1 for keyword in keywords if keyword in title_lower or keyword in content_lower)
            
            if score > 0:
                suggestions.append({
                    'id': article.id,
                    'title': article.title,
                    'summary': article.summary,
                    'relevance_score': score
                })
        
        return sorted(suggestions, key=lambda x: x['relevance_score'], reverse=True)
    
    @staticmethod
    def analyze_sentiment(text: str) -> Dict:
        """Analyze sentiment of customer message"""
        
        # Simple sentiment analysis (replace with actual service like Azure Cognitive Services)
        negative_words = ['angry', 'frustrated', 'terrible', 'awful', 'hate', 'worst', 'horrible']
        positive_words = ['great', 'excellent', 'amazing', 'love', 'perfect', 'wonderful', 'fantastic']
        
        text_lower = text.lower()
        negative_count = sum(1 for word in negative_words if word in text_lower)
        positive_count = sum(1 for word in positive_words if word in text_lower)
        
        if negative_count > positive_count:
            sentiment = 'negative'
            score = -0.7
        elif positive_count > negative_count:
            sentiment = 'positive'
            score = 0.7
        else:
            sentiment = 'neutral'
            score = 0.0
        
        return {
            'sentiment': sentiment,
            'score': score,
            'confidence': 0.8
        }

class WorkflowService:
    """Automated workflow execution"""
    
    @staticmethod
    def trigger_workflow(event: str, ticket: SupportTicket, context: Dict = None):
        """Trigger workflows based on events"""
        
        workflows = WorkflowAutomation.objects.filter(
            is_active=True,
            trigger_event=event
        )
        
        for workflow in workflows:
            if WorkflowService._evaluate_workflow_conditions(workflow, ticket, context):
                WorkflowService._execute_workflow(workflow, ticket, context)
    
    @staticmethod
    def _evaluate_workflow_conditions(workflow: WorkflowAutomation, ticket: SupportTicket, context: Dict) -> bool:
        """Evaluate if workflow conditions are met"""
        
        if not workflow.trigger_conditions:
            return True
        
        try:
            conditions = json.loads(workflow.trigger_conditions)
            
            # Simple condition evaluation (extend as needed)
            if 'priority' in conditions:
                if ticket.priority not in conditions['priority']:
                    return False
            
            if 'category' in conditions:
                if not ticket.category or ticket.category.name not in conditions['category']:
                    return False
            
            return True
        except:
            return True
    
    @staticmethod
    def _execute_workflow(workflow: WorkflowAutomation, ticket: SupportTicket, context: Dict):
        """Execute workflow steps"""
        
        execution = WorkflowExecution.objects.create(
            workflow=workflow,
            related_ticket=ticket,
            execution_data=json.dumps(context or {})
        )
        
        try:
            steps = json.loads(workflow.workflow_steps)
            execution.total_steps = len(steps)
            execution.save()
            
            for i, step in enumerate(steps):
                WorkflowService._execute_workflow_step(step, ticket, execution)
                execution.steps_completed = i + 1
                execution.save()
            
            execution.status = 'completed'
            execution.completed_at = timezone.now()
            execution.save()
            
            workflow.successful_executions += 1
            workflow.save()
            
        except Exception as e:
            execution.status = 'failed'
            execution.error_message = str(e)
            execution.save()
            
            workflow.failed_executions += 1
            workflow.save()
    
    @staticmethod
    def _execute_workflow_step(step: Dict, ticket: SupportTicket, execution: WorkflowExecution):
        """Execute individual workflow step"""
        
        step_type = step.get('type')
        
        if step_type == 'send_email':
            # Send email notification
            pass
        
        elif step_type == 'add_comment':
            TicketComment.objects.create(
                ticket=ticket,
                comment_type='system',
                content=step.get('content', 'Automated workflow action'),
                is_public=step.get('is_public', False)
            )
        
        elif step_type == 'update_priority':
            ticket.priority = step.get('priority', ticket.priority)
            ticket.save()
        
        elif step_type == 'assign_agent':
            agent_id = step.get('agent_id')
            if agent_id:
                try:
                    agent = SupportAgent.objects.get(id=agent_id)
                    ticket.assigned_agent = agent
                    ticket.save()
                except SupportAgent.DoesNotExist:
                    pass
        
        elif step_type == 'wait':
            # In a real implementation, this would schedule the next step
            pass
