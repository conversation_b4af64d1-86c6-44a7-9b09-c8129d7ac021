🚨 KPI ALERT - {{ alert_info.severity }}

KPI: {{ kpi.name }}
{{ alert_info.severity }} THRESHOLD BREACHED

ALERT DETAILS:
==============
Current Value: {{ alert_info.current_value }}{% if kpi.unit %} {{ kpi.unit }}{% endif %}
Threshold Value: {{ alert_info.threshold_value }}{% if kpi.unit %} {{ kpi.unit }}{% endif %}
Threshold Type: {{ alert_info.threshold_type|title }}
Severity: {{ alert_info.severity }}
Alert Time: {{ timestamp|date:"Y-m-d H:i:s" }}

KPI INFORMATION:
================
Description: {{ kpi.description|default:"No description available" }}
Category: {{ kpi.category.name|default:"Uncategorized" }}
Target Value: {{ kpi.target_value|default:"Not set" }}{% if kpi.unit %} {{ kpi.unit }}{% endif %}
Frequency: {{ kpi.get_frequency_display }}

MESSAGE:
========
{{ alert_info.message }}

{% if dashboard_url %}
View Dashboard: {{ dashboard_url }}
{% endif %}

---
This is an automated alert from the EMS KPI Monitoring System
Generated at {{ timestamp|date:"Y-m-d H:i:s" }}
