<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KPI Alert - {{ kpi.name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: {% if alert_info.severity == 'CRITICAL' %}#dc3545{% else %}#ffc107{% endif %};
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background: #f8f9fa;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-top: none;
        }
        .alert-details {
            background: white;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid {% if alert_info.severity == 'CRITICAL' %}#dc3545{% else %}#ffc107{% endif %};
        }
        .metric {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .metric:last-child {
            border-bottom: none;
        }
        .metric-label {
            font-weight: bold;
            color: #666;
        }
        .metric-value {
            color: #333;
        }
        .critical {
            color: #dc3545;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
        .footer {
            background: #343a40;
            color: white;
            padding: 15px;
            text-align: center;
            border-radius: 0 0 8px 8px;
            font-size: 14px;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚨 KPI Alert</h1>
        <h2>{{ kpi.name }}</h2>
        <p class="{% if alert_info.severity == 'CRITICAL' %}critical{% else %}warning{% endif %}">
            {{ alert_info.severity }} THRESHOLD BREACHED
        </p>
    </div>
    
    <div class="content">
        <div class="alert-details">
            <h3>Alert Details</h3>
            <div class="metric">
                <span class="metric-label">KPI Name:</span>
                <span class="metric-value">{{ kpi.name }}</span>
            </div>
            <div class="metric">
                <span class="metric-label">Current Value:</span>
                <span class="metric-value {% if alert_info.severity == 'CRITICAL' %}critical{% else %}warning{% endif %}">
                    {{ alert_info.current_value }}{% if kpi.unit %} {{ kpi.unit }}{% endif %}
                </span>
            </div>
            <div class="metric">
                <span class="metric-label">Threshold Value:</span>
                <span class="metric-value">{{ alert_info.threshold_value }}{% if kpi.unit %} {{ kpi.unit }}{% endif %}</span>
            </div>
            <div class="metric">
                <span class="metric-label">Threshold Type:</span>
                <span class="metric-value">{{ alert_info.threshold_type|title }}</span>
            </div>
            <div class="metric">
                <span class="metric-label">Severity:</span>
                <span class="metric-value {% if alert_info.severity == 'CRITICAL' %}critical{% else %}warning{% endif %}">
                    {{ alert_info.severity }}
                </span>
            </div>
            <div class="metric">
                <span class="metric-label">Alert Time:</span>
                <span class="metric-value">{{ timestamp|date:"Y-m-d H:i:s" }}</span>
            </div>
        </div>
        
        <div class="alert-details">
            <h3>KPI Information</h3>
            <div class="metric">
                <span class="metric-label">Description:</span>
                <span class="metric-value">{{ kpi.description|default:"No description available" }}</span>
            </div>
            <div class="metric">
                <span class="metric-label">Category:</span>
                <span class="metric-value">{{ kpi.category.name|default:"Uncategorized" }}</span>
            </div>
            <div class="metric">
                <span class="metric-label">Target Value:</span>
                <span class="metric-value">{{ kpi.target_value|default:"Not set" }}{% if kpi.unit %} {{ kpi.unit }}{% endif %}</span>
            </div>
            <div class="metric">
                <span class="metric-label">Frequency:</span>
                <span class="metric-value">{{ kpi.get_frequency_display }}</span>
            </div>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <p><strong>Message:</strong></p>
            <p style="font-size: 16px; color: {% if alert_info.severity == 'CRITICAL' %}#dc3545{% else %}#ffc107{% endif %};">
                {{ alert_info.message }}
            </p>
        </div>
        
        {% if dashboard_url %}
        <div style="text-align: center;">
            <a href="{{ dashboard_url }}" class="btn">View Dashboard</a>
        </div>
        {% endif %}
    </div>
    
    <div class="footer">
        <p>This is an automated alert from the EMS KPI Monitoring System</p>
        <p>Generated at {{ timestamp|date:"Y-m-d H:i:s" }}</p>
    </div>
</body>
</html>
