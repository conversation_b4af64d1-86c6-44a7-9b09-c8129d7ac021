#!/usr/bin/env python3
"""
Create basic leave types for testing
"""

import os
import sys
import django

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from ems.models import LeaveType

def create_leave_types():
    """Create basic leave types"""
    print("🏖️  Creating Leave Types")
    print("=" * 30)
    
    leave_types = [
        {
            'name': 'Annual Leave',
            'name_ar': 'إجازة سنوية',
            'days_allowed': 30,
            'is_paid': True,
            'requires_approval': True,
            'description': 'Annual vacation leave',
            'description_ar': 'إجازة سنوية للعطلة'
        },
        {
            'name': 'Sick Leave',
            'name_ar': 'إجازة مرضية',
            'days_allowed': 15,
            'is_paid': True,
            'requires_approval': True,
            'description': 'Medical leave for illness',
            'description_ar': 'إجازة طبية للمرض'
        },
        {
            'name': 'Emergency Leave',
            'name_ar': 'إجازة طارئة',
            'days_allowed': 5,
            'is_paid': True,
            'requires_approval': True,
            'description': 'Emergency leave for urgent matters',
            'description_ar': 'إجازة طارئة للأمور العاجلة'
        },
        {
            'name': 'Maternity Leave',
            'name_ar': 'إجازة أمومة',
            'days_allowed': 90,
            'is_paid': True,
            'requires_approval': True,
            'description': 'Maternity leave for new mothers',
            'description_ar': 'إجازة أمومة للأمهات الجدد'
        },
        {
            'name': 'Paternity Leave',
            'name_ar': 'إجازة أبوة',
            'days_allowed': 7,
            'is_paid': True,
            'requires_approval': True,
            'description': 'Paternity leave for new fathers',
            'description_ar': 'إجازة أبوة للآباء الجدد'
        }
    ]
    
    created_count = 0
    for lt_data in leave_types:
        lt, created = LeaveType.objects.get_or_create(
            name=lt_data['name'],
            defaults=lt_data
        )
        if created:
            created_count += 1
            print(f"✅ Created: {lt.name}")
        else:
            print(f"⚪ Exists: {lt.name}")
    
    print(f"\n📊 Summary: {created_count} new leave types created")
    print(f"📊 Total leave types: {LeaveType.objects.count()}")
    
    return True

if __name__ == "__main__":
    success = create_leave_types()
    if success:
        print("\n🎉 Leave types setup completed!")
    else:
        print("\n⚠️  Leave types setup failed!")
