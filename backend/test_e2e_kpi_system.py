#!/usr/bin/env python
"""
End-to-End Enterprise KPI System Test Suite

This comprehensive test validates the complete KPI system flow:
1. Backend API security and automation enforcement
2. Real-time WebSocket functionality
3. Automatic KPI calculation from operational data
4. Data integrity and enterprise standards compliance
5. Performance under realistic load

Run this to validate the entire enterprise KPI system works correctly.
"""

import os
import sys
import django
import asyncio
import json
import time
import requests
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor
import threading
from django.utils import timezone

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from django.test import TestCase, Client
from rest_framework.test import APIClient
from rest_framework import status
from channels.testing import WebsocketCommunicator
from channels.routing import URLRouter
from django.urls import path

from ems.models import (
    Employee, KPI, KPIValue, KPICategory, 
    Attendance, CustomerInvoice, Project, Department
)
from ems.consumers import KPIConsumer
from ems.enhanced_kpi_engine import enhanced_kpi_engine


class EnterpriseKPIEndToEndTest:
    """Comprehensive end-to-end test suite for enterprise KPI system"""
    
    def __init__(self):
        self.client = APIClient()
        self.results = {
            'tests_run': 0,
            'tests_passed': 0,
            'tests_failed': 0,
            'failures': [],
            'performance_metrics': {}
        }
        self.setup_test_environment()
        
    def setup_test_environment(self):
        """Setup comprehensive test environment"""
        print("🔧 Setting up enterprise test environment...")
        
        # Create admin user
        self.admin_user = User.objects.get_or_create(
            username='e2e_admin',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True,
                'first_name': 'E2E',
                'last_name': 'Admin'
            }
        )[0]
        self.admin_user.set_password('testpass123')
        self.admin_user.save()
        
        # Create test employee
        self.admin_employee = Employee.objects.get_or_create(
            user=self.admin_user,
            defaults={
                'employee_id': 'E2E001',
                'position': 'System Administrator',
                'position_ar': 'مدير النظام',
                'gender': 'M',
                'hire_date': timezone.now().date(),
                'employment_status': 'FULL_TIME',
                'is_active': True
            }
        )[0]
        
        # Create test department
        self.test_department = Department.objects.get_or_create(
            name='E2E Test Department',
            defaults={
                'name_ar': 'قسم اختبار شامل',
                'description': 'End-to-end testing department',
                'manager': self.admin_employee
            }
        )[0]
        
        print("✅ Test environment setup complete")
    
    def run_test(self, test_name, test_func):
        """Run individual test with error handling"""
        self.results['tests_run'] += 1
        try:
            print(f"\n🧪 Running: {test_name}")
            start_time = time.time()
            test_func()
            end_time = time.time()
            
            self.results['tests_passed'] += 1
            self.results['performance_metrics'][test_name] = end_time - start_time
            print(f"✅ PASSED: {test_name} ({end_time - start_time:.2f}s)")
            
        except Exception as e:
            self.results['tests_failed'] += 1
            self.results['failures'].append({
                'test': test_name,
                'error': str(e),
                'type': type(e).__name__
            })
            print(f"❌ FAILED: {test_name} - {str(e)}")
    
    def test_api_security_enforcement(self):
        """Test that manual KPI entry is completely blocked"""
        self.client.force_authenticate(user=self.admin_user)
        
        # Test 1: Manual KPI creation should be blocked
        response = self.client.post('/api/kpi/kpis/', {
            'name': 'Test Manual KPI',
            'description': 'This should be blocked',
            'category': 'test',
            'unit': '%',
            'target_value': 100
        })
        
        if response.status_code not in [403, 405, 400]:
            raise AssertionError(f"Manual KPI creation not blocked (status: {response.status_code})")
        
        # Test 2: Manual KPI value creation should be blocked
        if KPI.objects.exists():
            kpi = KPI.objects.first()
            response = self.client.post('/api/kpi/values/', {
                'kpi': kpi.id,
                'value': 85.5,
                'period_start': '2024-01-01T00:00:00Z',
                'period_end': '2024-01-31T23:59:59Z'
            })
            
            if response.status_code not in [403, 405, 400]:
                raise AssertionError(f"Manual KPI value creation not blocked (status: {response.status_code})")
        
        # Test 3: Enhanced API should work (read-only)
        response = self.client.get('/api/kpi/enhanced/kpis/')
        if response.status_code != 200:
            raise AssertionError(f"Enhanced KPI API not working (status: {response.status_code})")
    
    def test_automatic_kpi_calculation(self):
        """Test automatic KPI calculation from operational data"""
        # Create test employees for attendance calculation
        test_employees = []
        for i in range(10):
            user = User.objects.get_or_create(
                username=f'e2e_emp_{i}',
                defaults={
                    'first_name': f'Employee',
                    'last_name': f'{i}',
                    'email': f'e2e_emp_{i}@test.com'
                }
            )[0]
            
            employee = Employee.objects.get_or_create(
                user=user,
                defaults={
                    'employee_id': f'E2E{i+100:03d}',
                    'position': f'Test Employee {i}',
                    'position_ar': f'موظف اختبار {i}',
                    'department': self.test_department,
                    'gender': 'M',
                    'hire_date': timezone.now().date() - timedelta(days=30),
                    'employment_status': 'FULL_TIME',
                    'is_active': True
                }
            )[0]
            test_employees.append(employee)
        
        # Create attendance records (8 out of 10 present = 80%)
        today = timezone.now().date()
        for employee in test_employees[:8]:
            Attendance.objects.get_or_create(
                employee=employee,
                date=today,
                defaults={
                    'is_present': True,
                    'check_in': datetime.min.time().replace(hour=9),
                    'check_out': datetime.min.time().replace(hour=17)
                }
            )
        
        # Find or create attendance KPI
        attendance_kpi = None
        for kpi in KPI.objects.filter(is_automated=True):
            if 'attendance' in kpi.calculation_method.lower():
                attendance_kpi = kpi
                break
        
        if attendance_kpi:
            # Test automatic calculation
            calculated_value = enhanced_kpi_engine.calculate_kpi(attendance_kpi)
            if calculated_value is None:
                raise AssertionError("KPI calculation returned None")
            
            # Verify calculation accuracy (should be around 80%)
            expected_rate = 80.0
            calculated_float = float(calculated_value)  # Convert Decimal to float for comparison
            if abs(calculated_float - expected_rate) > 15.0:  # Allow 15% tolerance for test data variations
                raise AssertionError(f"KPI calculation inaccurate. Expected ~{expected_rate}%, got {calculated_float}%")
        else:
            print("⚠️ No automated attendance KPI found, skipping calculation test")
    
    def test_enhanced_api_endpoints(self):
        """Test all enhanced API endpoints work correctly"""
        self.client.force_authenticate(user=self.admin_user)
        
        # Test enhanced KPI list
        response = self.client.get('/api/kpi/enhanced/kpis/')
        if response.status_code != 200:
            raise AssertionError(f"Enhanced KPI list failed (status: {response.status_code})")
        
        data = response.json()
        if 'results' not in data:
            raise AssertionError("Enhanced KPI list missing results")
        
        # Test enhanced KPI values
        response = self.client.get('/api/kpi/enhanced/values/')
        if response.status_code != 200:
            raise AssertionError(f"Enhanced KPI values failed (status: {response.status_code})")
        
        # Test enhanced dashboard
        response = self.client.get('/api/kpi/enhanced-dashboard/')
        if response.status_code != 200:
            raise AssertionError(f"Enhanced dashboard failed (status: {response.status_code})")
    
    def test_websocket_functionality(self):
        """Test WebSocket consumer and real-time capabilities"""
        try:
            from ems.consumers import KPIConsumer
            consumer = KPIConsumer()
            
            # Test message handling
            test_message = {
                'type': 'kpi_update',
                'kpi_id': 'test-kpi-id',
                'value': 85.5,
                'timestamp': datetime.now().isoformat()
            }
            
            # Verify consumer can be instantiated
            if not hasattr(consumer, 'connect'):
                raise AssertionError("KPI WebSocket consumer missing connect method")
            
            if not hasattr(consumer, 'kpi_update'):
                raise AssertionError("KPI WebSocket consumer missing kpi_update handler")
                
        except ImportError as e:
            raise AssertionError(f"WebSocket consumer import failed: {str(e)}")
    
    def test_data_integrity(self):
        """Test data integrity and consistency"""
        # Test KPI model constraints
        active_kpis = KPI.objects.filter(status='ACTIVE')
        
        for kpi in active_kpis:
            # Verify automated KPIs have calculation methods
            if kpi.is_automated and not kpi.calculation_method:
                raise AssertionError(f"Automated KPI {kpi.name} missing calculation method")
            
            # Verify KPI values are within reasonable ranges
            recent_values = KPIValue.objects.filter(
                kpi=kpi,
                recorded_at__gte=datetime.now() - timedelta(days=30)
            )
            
            for value in recent_values:
                if value.value < 0:
                    raise AssertionError(f"KPI {kpi.name} has negative value: {value.value}")
                
                if kpi.unit == '%' and value.value > 100:
                    print(f"⚠️ Warning: KPI {kpi.name} has percentage value > 100%: {value.value}")
    
    def test_performance_load(self):
        """Test system performance under load"""
        self.client.force_authenticate(user=self.admin_user)
        
        # Test concurrent API requests
        def make_request():
            return self.client.get('/api/kpi/enhanced/kpis/')
        
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(50)]
            responses = [future.result() for future in futures]
        end_time = time.time()
        
        # Verify all requests succeeded
        failed_requests = [r for r in responses if r.status_code != 200]
        if failed_requests:
            raise AssertionError(f"{len(failed_requests)} requests failed under load")
        
        # Verify reasonable response time
        avg_response_time = (end_time - start_time) / 50
        if avg_response_time > 2.0:  # 2 seconds per request is too slow
            raise AssertionError(f"Average response time too slow: {avg_response_time:.2f}s")
        
        self.results['performance_metrics']['concurrent_requests'] = {
            'total_requests': 50,
            'total_time': end_time - start_time,
            'avg_response_time': avg_response_time,
            'requests_per_second': 50 / (end_time - start_time)
        }
    
    def run_all_tests(self):
        """Run complete end-to-end test suite"""
        print("🚀 Starting Enterprise KPI System End-to-End Tests")
        print("=" * 60)
        
        # Core functionality tests
        self.run_test("API Security Enforcement", self.test_api_security_enforcement)
        self.run_test("Automatic KPI Calculation", self.test_automatic_kpi_calculation)
        self.run_test("Enhanced API Endpoints", self.test_enhanced_api_endpoints)
        self.run_test("WebSocket Functionality", self.test_websocket_functionality)
        self.run_test("Data Integrity", self.test_data_integrity)
        self.run_test("Performance Load Test", self.test_performance_load)
        
        # Print comprehensive results
        self.print_test_results()
    
    def print_test_results(self):
        """Print comprehensive test results"""
        print("\n" + "=" * 60)
        print("🎉 Enterprise KPI System Test Results")
        print("=" * 60)
        
        print(f"📊 Tests Run: {self.results['tests_run']}")
        print(f"✅ Tests Passed: {self.results['tests_passed']}")
        print(f"❌ Tests Failed: {self.results['tests_failed']}")
        print(f"📈 Success Rate: {(self.results['tests_passed']/self.results['tests_run']*100):.1f}%")
        
        if self.results['failures']:
            print(f"\n❌ Failures:")
            for failure in self.results['failures']:
                print(f"  • {failure['test']}: {failure['error']}")
        
        if self.results['performance_metrics']:
            print(f"\n⚡ Performance Metrics:")
            for test, duration in self.results['performance_metrics'].items():
                if isinstance(duration, dict):
                    print(f"  • {test}:")
                    for key, value in duration.items():
                        print(f"    - {key}: {value}")
                else:
                    print(f"  • {test}: {duration:.2f}s")
        
        print(f"\n🏆 System Status: {'READY FOR PRODUCTION' if self.results['tests_failed'] == 0 else 'NEEDS ATTENTION'}")
        
        if self.results['tests_failed'] == 0:
            print("\n✨ All tests passed! The enterprise KPI system is ready for production deployment.")
            print("\n🚀 Next Steps:")
            print("  1. Deploy to staging environment")
            print("  2. Run user acceptance testing")
            print("  3. Configure production WebSocket with Redis")
            print("  4. Set up monitoring and alerting")
            print("  5. Train users on the new read-only interface")


if __name__ == '__main__':
    test_suite = EnterpriseKPIEndToEndTest()
    test_suite.run_all_tests()
