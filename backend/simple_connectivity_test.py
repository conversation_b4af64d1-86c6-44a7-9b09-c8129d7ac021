#!/usr/bin/env python
"""
Simple Backend Connectivity Test
"""

import requests
import json

def test_backend():
    base_url = 'http://127.0.0.1:8002'
    
    print("🔍 Testing Backend Connectivity...")
    
    # Test basic endpoint
    try:
        response = requests.get(f"{base_url}/api/dashboard-stats/", timeout=5)
        print(f"✅ Dashboard Stats API: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ Backend connectivity failed: {e}")
        return False

if __name__ == '__main__':
    test_backend()
