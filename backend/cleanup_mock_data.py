#!/usr/bin/env python3
"""
Cleanup script to remove all mock/test data from the EMS system
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from ems.models import Employee, LeaveRequest, LeaveType, Department, Task, Attendance

def cleanup_mock_data():
    print("🧹 Starting cleanup of mock/test data...")
    
    # 1. Delete all test leave requests
    print("\n1. Cleaning up Leave Requests...")
    leave_count = LeaveRequest.objects.count()
    LeaveRequest.objects.all().delete()
    print(f"   ✅ Deleted {leave_count} leave requests")
    
    # 2. Delete all test tasks
    print("\n2. Cleaning up Tasks...")
    task_count = Task.objects.count()
    Task.objects.all().delete()
    print(f"   ✅ Deleted {task_count} tasks")
    
    # 3. Delete all test attendance records
    print("\n3. Cleaning up Attendance Records...")
    attendance_count = Attendance.objects.count()
    Attendance.objects.all().delete()
    print(f"   ✅ Deleted {attendance_count} attendance records")
    
    # 4. Keep only essential users and delete test users
    print("\n4. Cleaning up Test Users...")
    test_users = [
        'testuser', 'testemployee', 'testuser123', 'testauth',
        'superadmin_test', 'admin_test', 'hr_test', 'finance_test', 'employee_test',
        'john_engineer', 'employee1'  # Remove the test employee1 user
    ]
    
    # Also remove users with test patterns
    all_users = User.objects.all()
    deleted_users = []
    
    for user in all_users:
        if (user.username in test_users or 
            'test' in user.username.lower() or 
            user.email.endswith('@example.com') or
            user.username.startswith('emp') or  # Remove EMP prefixed users
            user.is_active == False):  # Remove inactive users
            
            # Keep essential admin users
            if user.username not in ['admin', 'superadmin', 'hrmanager', 'financemanager']:
                deleted_users.append(user.username)
                user.delete()
    
    print(f"   ✅ Deleted {len(deleted_users)} test users: {', '.join(deleted_users)}")
    
    # 5. Clean up test employees (this will cascade delete related data)
    print("\n5. Cleaning up Test Employee Records...")
    remaining_employees = Employee.objects.all()
    deleted_employees = []
    
    for emp in remaining_employees:
        if (emp.employee_id.startswith('TEST') or 
            emp.employee_id.startswith('EMP') or
            'test' in emp.employee_id.lower()):
            deleted_employees.append(emp.employee_id)
            emp.delete()
    
    print(f"   ✅ Deleted {len(deleted_employees)} test employee records")
    
    # 6. Keep only essential departments
    print("\n6. Cleaning up Test Departments...")
    essential_departments = [
        'Administration', 'Human Resources', 'Finance', 
        'Information Technology', 'Operations'
    ]
    
    test_departments = Department.objects.exclude(name__in=essential_departments)
    dept_count = test_departments.count()
    dept_names = [dept.name for dept in test_departments]
    test_departments.delete()
    print(f"   ✅ Deleted {dept_count} test departments: {', '.join(dept_names)}")
    
    # 7. Keep leave types (they're essential for the system)
    print("\n7. Leave Types - Keeping all (essential for system)")
    leave_types = LeaveType.objects.all()
    print(f"   ✅ Keeping {leave_types.count()} leave types")
    
    print("\n🎉 Cleanup completed successfully!")
    print("\n📊 Final Database State:")
    print(f"   - Users: {User.objects.count()}")
    print(f"   - Employees: {Employee.objects.count()}")
    print(f"   - Departments: {Department.objects.count()}")
    print(f"   - Leave Types: {LeaveType.objects.count()}")
    print(f"   - Leave Requests: {LeaveRequest.objects.count()}")
    print(f"   - Tasks: {Task.objects.count()}")
    print(f"   - Attendance Records: {Attendance.objects.count()}")

if __name__ == "__main__":
    cleanup_mock_data()