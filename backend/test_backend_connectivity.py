#!/usr/bin/env python
"""
Backend Connectivity and Integration Test Suite

This script tests:
1. Backend API endpoints
2. Database connectivity
3. Authentication system
4. CRUD operations
5. Real-time features
6. Role-based access control
"""

import os
import sys
import django
import requests
import json
import time
from datetime import datetime

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from django.test import Client
from rest_framework.test import APIClient
from rest_framework import status
from ems.models import Employee, Department, Role, UserProfile

class BackendConnectivityTest:
    def __init__(self):
        self.base_url = 'http://127.0.0.1:8002'
        self.client = APIClient()
        self.test_results = {}
        
    def test_api_endpoints(self):
        """Test all major API endpoints"""
        print("🔍 Testing API Endpoints...")
        
        endpoints = [
            '/api/dashboard-stats/',
            '/api/employees/',
            '/api/departments/',
            '/api/projects/',
            '/api/notifications/notifications/',
            '/api/auth/login/',
        ]
        
        for endpoint in endpoints:
            try:
                url = f"{self.base_url}{endpoint}"
                response = requests.get(url, timeout=5)
                
                self.test_results[f"API {endpoint}"] = {
                    'status': response.status_code,
                    'success': response.status_code in [200, 401, 403],  # 401/403 are expected for protected endpoints
                    'response_time': response.elapsed.total_seconds(),
                    'error': None
                }
                
                print(f"  ✅ {endpoint}: {response.status_code} ({response.elapsed.total_seconds():.2f}s)")
                
            except Exception as e:
                self.test_results[f"API {endpoint}"] = {
                    'status': 0,
                    'success': False,
                    'response_time': None,
                    'error': str(e)
                }
                print(f"  ❌ {endpoint}: {str(e)}")
    
    def test_authentication_system(self):
        """Test authentication endpoints and user creation"""
        print("\n🔐 Testing Authentication System...")
        
        try:
            # Test user creation
            test_user, created = User.objects.get_or_create(
                username='test_integration_user',
                defaults={
                    'email': '<EMAIL>',
                    'first_name': 'Test',
                    'last_name': 'User'
                }
            )
            
            if created:
                test_user.set_password('testpass123')
                test_user.save()
                print("  ✅ Test user created successfully")
            else:
                print("  ✅ Test user already exists")
            
            # Test login endpoint
            login_data = {
                'username': 'test_integration_user',
                'password': 'testpass123'
            }
            
            response = requests.post(
                f"{self.base_url}/api/auth/login/",
                json=login_data,
                timeout=10
            )
            
            self.test_results['Authentication'] = {
                'user_creation': True,
                'login_status': response.status_code,
                'login_success': response.status_code == 200,
                'error': None
            }
            
            print(f"  ✅ Login test: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                print(f"  ✅ Login successful, user data received")
                return response_data
            
        except Exception as e:
            self.test_results['Authentication'] = {
                'user_creation': False,
                'login_status': 0,
                'login_success': False,
                'error': str(e)
            }
            print(f"  ❌ Authentication test failed: {str(e)}")
            
        return None
    
    def test_database_operations(self):
        """Test database CRUD operations"""
        print("\n💾 Testing Database Operations...")
        
        try:
            # Test Department creation
            dept, created = Department.objects.get_or_create(
                name='Test Integration Department',
                defaults={
                    'name_ar': 'قسم اختبار التكامل',
                    'description': 'Test department for integration testing',
                    'is_active': True
                }
            )
            
            print(f"  ✅ Department {'created' if created else 'exists'}: {dept.name}")
            
            # Test Employee creation
            test_user = User.objects.get(username='test_integration_user')
            employee, created = Employee.objects.get_or_create(
                user=test_user,
                defaults={
                    'employee_id': 'TEST001',
                    'department': dept,
                    'position': 'Test Position',
                    'hire_date': datetime.now().date(),
                    'employment_status': 'FULL_TIME',
                    'is_active': True
                }
            )
            
            print(f"  ✅ Employee {'created' if created else 'exists'}: {employee.employee_id}")
            
            # Test data retrieval
            employees_count = Employee.objects.count()
            departments_count = Department.objects.count()
            
            print(f"  ✅ Database contains {employees_count} employees, {departments_count} departments")
            
            self.test_results['Database Operations'] = {
                'department_creation': True,
                'employee_creation': True,
                'data_retrieval': True,
                'employees_count': employees_count,
                'departments_count': departments_count,
                'error': None
            }
            
        except Exception as e:
            self.test_results['Database Operations'] = {
                'department_creation': False,
                'employee_creation': False,
                'data_retrieval': False,
                'error': str(e)
            }
            print(f"  ❌ Database operations failed: {str(e)}")
    
    def test_role_based_access(self):
        """Test role-based access control"""
        print("\n👥 Testing Role-Based Access Control...")
        
        try:
            # Create test roles
            admin_role, created = Role.objects.get_or_create(
                name='ADMIN',
                defaults={
                    'name_ar': 'مدير',
                    'description': 'Administrator role',
                    'permissions': {
                        'all_access': True,
                        'user_management': True,
                        'system_admin': True
                    }
                }
            )
            
            employee_role, created = Role.objects.get_or_create(
                name='EMPLOYEE',
                defaults={
                    'name_ar': 'موظف',
                    'description': 'Employee role',
                    'permissions': {
                        'view_own_data': True,
                        'submit_requests': True
                    }
                }
            )
            
            print(f"  ✅ Roles created: ADMIN, EMPLOYEE")
            
            # Test user profile with role
            test_user = User.objects.get(username='test_integration_user')
            profile, created = UserProfile.objects.get_or_create(
                user=test_user,
                defaults={
                    'role': employee_role,
                    'preferred_language': 'en',
                    'timezone': 'UTC'
                }
            )
            
            print(f"  ✅ User profile {'created' if created else 'exists'} with role: {profile.role.name}")
            
            self.test_results['Role-Based Access'] = {
                'roles_created': True,
                'user_profile_created': True,
                'permissions_set': True,
                'error': None
            }
            
        except Exception as e:
            self.test_results['Role-Based Access'] = {
                'roles_created': False,
                'user_profile_created': False,
                'permissions_set': False,
                'error': str(e)
            }
            print(f"  ❌ Role-based access test failed: {str(e)}")
    
    def test_performance_metrics(self):
        """Test system performance"""
        print("\n⚡ Testing Performance Metrics...")
        
        try:
            start_time = time.time()
            
            # Test multiple API calls
            for i in range(5):
                response = requests.get(f"{self.base_url}/api/dashboard-stats/", timeout=5)
                if response.status_code != 200:
                    break
            
            end_time = time.time()
            avg_response_time = (end_time - start_time) / 5
            
            print(f"  ✅ Average API response time: {avg_response_time:.2f}s")
            
            # Test database query performance
            start_time = time.time()
            Employee.objects.all().count()
            Department.objects.all().count()
            db_query_time = time.time() - start_time
            
            print(f"  ✅ Database query time: {db_query_time:.3f}s")
            
            self.test_results['Performance'] = {
                'avg_api_response_time': avg_response_time,
                'db_query_time': db_query_time,
                'performance_acceptable': avg_response_time < 2.0 and db_query_time < 0.1,
                'error': None
            }
            
        except Exception as e:
            self.test_results['Performance'] = {
                'avg_api_response_time': None,
                'db_query_time': None,
                'performance_acceptable': False,
                'error': str(e)
            }
            print(f"  ❌ Performance test failed: {str(e)}")
    
    def run_all_tests(self):
        """Run complete backend test suite"""
        print("🚀 Starting Backend Connectivity & Integration Tests")
        print("=" * 60)
        
        self.test_api_endpoints()
        self.test_authentication_system()
        self.test_database_operations()
        self.test_role_based_access()
        self.test_performance_metrics()
        
        self.print_summary()
    
    def print_summary(self):
        """Print comprehensive test results"""
        print("\n" + "=" * 60)
        print("📊 BACKEND TEST RESULTS SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() 
                          if isinstance(result, dict) and result.get('success', False) or 
                             result.get('user_creation', False) or 
                             result.get('department_creation', False) or
                             result.get('roles_created', False) or
                             result.get('performance_acceptable', False))
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {(passed_tests / total_tests * 100):.1f}%")
        
        print("\nDetailed Results:")
        for test_name, result in self.test_results.items():
            if isinstance(result, dict):
                if result.get('error'):
                    print(f"  ❌ {test_name}: {result['error']}")
                else:
                    print(f"  ✅ {test_name}: Success")
        
        print("\n🎯 Next Steps:")
        if passed_tests == total_tests:
            print("  ✅ All tests passed! Backend is ready for full integration.")
        else:
            print("  ⚠️ Some tests failed. Check the errors above and fix issues.")
            print("  🔧 Common fixes:")
            print("    - Ensure Django server is running on port 8002")
            print("    - Check database migrations are applied")
            print("    - Verify CORS settings for frontend connectivity")

if __name__ == '__main__':
    test_suite = BackendConnectivityTest()
    test_suite.run_all_tests()
