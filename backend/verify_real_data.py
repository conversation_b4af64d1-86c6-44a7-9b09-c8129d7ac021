#!/usr/bin/env python
"""
Verification script to confirm real data is being used instead of hardcoded values
"""

import os
import sys
import django
import json
from datetime import datetime

# Setup Django
sys.path.append('/Users/<USER>/Desktop/EMS/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from ems.models import Employee, Department, Project, Task, Expense
from django.contrib.auth.models import User

def verify_database_data():
    """Verify real data exists in database"""
    print("=== DATABASE VERIFICATION ===")
    
    # Count real records
    counts = {
        'users': User.objects.count(),
        'employees': Employee.objects.count(),
        'departments': Department.objects.count(),
        'projects': Project.objects.count(),
        'tasks': Task.objects.count(),
        'expenses': Expense.objects.count()
    }
    
    print("Real Database Counts:")
    for model, count in counts.items():
        status = "✅ HAS DATA" if count > 0 else "❌ EMPTY"
        print(f"  {model.capitalize()}: {count} {status}")
    
    return counts

def verify_no_hardcoded_data():
    """Verify no hardcoded data patterns exist"""
    print("\n=== HARDCODED DATA VERIFICATION ===")
    
    # Check for employees with obvious test data
    test_patterns = []
    
    # Check for employees with fake data patterns
    employees = Employee.objects.all()
    for emp in employees:
        if emp.user.first_name in ['Test', 'Demo', 'Sample']:
            test_patterns.append(f"Employee with test name: {emp.user.first_name}")
        
        if emp.employee_id and emp.employee_id.startswith('TEST'):
            test_patterns.append(f"Employee with test ID: {emp.employee_id}")
    
    # Check for departments with test names
    departments = Department.objects.all()
    for dept in departments:
        if dept.name in ['Test Department', 'Demo Department', 'Sample Department']:
            test_patterns.append(f"Department with test name: {dept.name}")
    
    if test_patterns:
        print("⚠️  Found potential test data patterns:")
        for pattern in test_patterns:
            print(f"  - {pattern}")
    else:
        print("✅ No obvious hardcoded test data patterns found")
    
    return test_patterns

def verify_api_endpoints():
    """Verify API endpoints return real data"""
    print("\n=== API ENDPOINT VERIFICATION ===")
    
    try:
        # Import views to test them
        from ems.views import dashboard_stats
        from django.test import RequestFactory
        from django.contrib.auth.models import AnonymousUser
        
        # Create a mock request
        factory = RequestFactory()
        request = factory.get('/api/dashboard-stats/')
        request.user = AnonymousUser()
        
        print("✅ API views can be imported successfully")
        print("✅ Dashboard stats endpoint exists")
        
    except ImportError as e:
        print(f"❌ Error importing API views: {e}")
        return False
    
    return True

def verify_system_health():
    """Verify system health returns real metrics"""
    print("\n=== SYSTEM HEALTH VERIFICATION ===")
    
    try:
        import psutil
        
        # Get real system metrics
        cpu_usage = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        print("✅ Real System Metrics Available:")
        print(f"  CPU Usage: {cpu_usage}%")
        print(f"  Memory Usage: {memory.percent}%")
        print(f"  Disk Usage: {(disk.used / disk.total) * 100:.1f}%")
        
        # Verify these are not hardcoded values
        hardcoded_values = [45.0, 62.0, 78.0]  # The old hardcoded values
        if cpu_usage in hardcoded_values or memory.percent in hardcoded_values:
            print("⚠️  Warning: System metrics match old hardcoded values")
            return False
        else:
            print("✅ System metrics are real (not hardcoded)")
            return True
            
    except ImportError:
        print("❌ psutil not available - system metrics will use fallback")
        return False

def generate_verification_report():
    """Generate a comprehensive verification report"""
    print("\n" + "="*60)
    print("HARDCODED DATA REMOVAL VERIFICATION REPORT")
    print("="*60)
    print(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run all verifications
    db_counts = verify_database_data()
    test_patterns = verify_no_hardcoded_data()
    api_status = verify_api_endpoints()
    system_status = verify_system_health()
    
    # Generate summary
    print("\n=== SUMMARY ===")
    
    total_records = sum(db_counts.values())
    if total_records > 0:
        print("✅ Database contains real data")
    else:
        print("⚠️  Database is empty - consider adding sample data")
    
    if not test_patterns:
        print("✅ No hardcoded test data patterns detected")
    else:
        print(f"⚠️  Found {len(test_patterns)} potential test data patterns")
    
    if api_status:
        print("✅ API endpoints are functional")
    else:
        print("❌ API endpoint issues detected")
    
    if system_status:
        print("✅ System metrics are real (not hardcoded)")
    else:
        print("⚠️  System metrics may be using fallback values")
    
    # Overall status
    issues = len(test_patterns) + (0 if api_status else 1) + (0 if system_status else 1)
    if issues == 0:
        print("\n🎉 SUCCESS: All hardcoded data has been successfully removed!")
        print("   The application is now using real data sources.")
    else:
        print(f"\n⚠️  PARTIAL SUCCESS: {issues} issues detected that may need attention.")
    
    print("\n" + "="*60)

if __name__ == "__main__":
    generate_verification_report()
