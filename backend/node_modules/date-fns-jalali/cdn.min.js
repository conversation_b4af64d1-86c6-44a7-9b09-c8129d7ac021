(()=>{function rX(K,G){var X=typeof Symbol!=="undefined"&&K[Symbol.iterator]||K["@@iterator"];if(!X){if(Array.isArray(K)||(X=lG(K))||G&&K&&typeof K.length==="number"){if(X)K=X;var B=0,U=function Q(){};return{s:U,n:function Q(){if(B>=K.length)return{done:!0};return{done:!1,value:K[B++]}},e:function Q(H){throw H},f:U}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var Z=!0,J=!1,q;return{s:function Q(){X=X.call(K)},n:function Q(){var H=X.next();return Z=H.done,H},e:function Q(H){J=!0,q=H},f:function Q(){try{if(!Z&&X.return!=null)X.return()}finally{if(J)throw q}}}}function $(K,G,X){return G=cG(G),IK(K,AX()?Reflect.construct(G,X||[],cG(K).constructor):G.apply(K,X))}function IK(K,G){if(G&&(KG(G)==="object"||typeof G==="function"))return G;else if(G!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return R(K)}function R(K){if(K===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return K}function cG(K){return cG=Object.setPrototypeOf?Object.getPrototypeOf.bind():function G(X){return X.__proto__||Object.getPrototypeOf(X)},cG(K)}function P(K,G){if(typeof G!=="function"&&G!==null)throw new TypeError("Super expression must either be null or a function");if(K.prototype=Object.create(G&&G.prototype,{constructor:{value:K,writable:!0,configurable:!0}}),Object.defineProperty(K,"prototype",{writable:!1}),G)_G(K,G)}function T(K,G){if(!(K instanceof G))throw new TypeError("Cannot call a class as a function")}function iX(K,G){for(var X=0;X<G.length;X++){var B=G[X];if(B.enumerable=B.enumerable||!1,B.configurable=!0,"value"in B)B.writable=!0;Object.defineProperty(K,nX(B.key),B)}}function M(K,G,X){if(G)iX(K.prototype,G);if(X)iX(K,X);return Object.defineProperty(K,"prototype",{writable:!1}),K}function FK(K){return tX(K)||aX(K)||lG(K)||eX()}function sX(K,G){var X=Object.keys(K);if(Object.getOwnPropertySymbols){var B=Object.getOwnPropertySymbols(K);G&&(B=B.filter(function(U){return Object.getOwnPropertyDescriptor(K,U).enumerable})),X.push.apply(X,B)}return X}function XG(K){for(var G=1;G<arguments.length;G++){var X=arguments[G]!=null?arguments[G]:{};G%2?sX(Object(X),!0).forEach(function(B){E(K,B,X[B])}):Object.getOwnPropertyDescriptors?Object.defineProperties(K,Object.getOwnPropertyDescriptors(X)):sX(Object(X)).forEach(function(B){Object.defineProperty(K,B,Object.getOwnPropertyDescriptor(X,B))})}return K}function E(K,G,X){if(G=nX(G),G in K)Object.defineProperty(K,G,{value:X,enumerable:!0,configurable:!0,writable:!0});else K[G]=X;return K}function nX(K){var G=WK(K,"string");return KG(G)=="symbol"?G:String(G)}function WK(K,G){if(KG(K)!="object"||!K)return K;var X=K[Symbol.toPrimitive];if(X!==void 0){var B=X.call(K,G||"default");if(KG(B)!="object")return B;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(K)}function KG(K){return KG=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},KG(K)}function uG(K){return MK(K)||aX(K)||lG(K)||TK()}function TK(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function aX(K){if(typeof Symbol!=="undefined"&&K[Symbol.iterator]!=null||K["@@iterator"]!=null)return Array.from(K)}function MK(K){if(Array.isArray(K))return LX(K)}function oX(K,G,X){if(AX())return Reflect.construct.apply(null,arguments);var B=[null];B.push.apply(B,G);var U=new(K.bind.apply(K,B));return X&&_G(U,X.prototype),U}function AX(){try{var K=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(G){}return(AX=function G(){return!!K})()}function _G(K,G){return _G=Object.setPrototypeOf?Object.setPrototypeOf.bind():function X(B,U){return B.__proto__=U,B},_G(K,G)}function z(K,G){return tX(K)||YK(K,G)||lG(K,G)||eX()}function eX(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function lG(K,G){if(!K)return;if(typeof K==="string")return LX(K,G);var X=Object.prototype.toString.call(K).slice(8,-1);if(X==="Object"&&K.constructor)X=K.constructor.name;if(X==="Map"||X==="Set")return Array.from(K);if(X==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(X))return LX(K,G)}function LX(K,G){if(G==null||G>K.length)G=K.length;for(var X=0,B=new Array(G);X<G;X++)B[X]=K[X];return B}function YK(K,G){var X=K==null?null:typeof Symbol!="undefined"&&K[Symbol.iterator]||K["@@iterator"];if(X!=null){var B,U,Z,J,q=[],Q=!0,H=!1;try{if(Z=(X=X.call(K)).next,G===0){if(Object(X)!==X)return;Q=!1}else for(;!(Q=(B=Z.call(X)).done)&&(q.push(B.value),q.length!==G);Q=!0);}catch(V){H=!0,U=V}finally{try{if(!Q&&X.return!=null&&(J=X.return(),Object(J)!==J))return}finally{if(H)throw U}}return q}}function tX(K){if(Array.isArray(K))return K}var zK=Object.defineProperty,$K=function K(G,X){for(var B in X)zK(G,B,{get:X[B],enumerable:!0,configurable:!0,set:function U(Z){return X[B]=function(){return Z}}})},G0={};$K(G0,{yearsToQuarters:function K(){return Dq},yearsToMonths:function K(){return Sq},yearsToDays:function K(){return Oq},weeksToDays:function K(){return vq},transpose:function K(){return s0},toDate:function K(){return j},subYears:function K(){return Pq},subWeeks:function K(){return $q},subSeconds:function K(){return zq},subQuarters:function K(){return Yq},subMonths:function K(){return xK},subMinutes:function K(){return Mq},subMilliseconds:function K(){return Tq},subISOWeekYears:function K(){return $0},subHours:function K(){return Wq},subDays:function K(){return VX},subBusinessDays:function K(){return Fq},sub:function K(){return Iq},startOfYesterday:function K(){return bq},startOfYear:function K(){return PX},startOfWeekYear:function K(){return ZX},startOfWeek:function K(){return s},startOfTomorrow:function K(){return Cq},startOfToday:function K(){return wq},startOfSecond:function K(){return uX},startOfQuarter:function K(){return IG},startOfMonth:function K(){return BX},startOfMinute:function K(){return cX},startOfISOWeekYear:function K(){return AG},startOfISOWeek:function K(){return BG},startOfHour:function K(){return fX},startOfDecade:function K(){return Lq},startOfDay:function K(){return zG},setYear:function K(){return Aq},setWeekYear:function K(){return Rq},setWeek:function K(){return t0},setSeconds:function K(){return Eq},setQuarter:function K(){return xq},setMonth:function K(){return lX},setMinutes:function K(){return Nq},setMilliseconds:function K(){return jq},setISOWeekYear:function K(){return w0},setISOWeek:function K(){return GK},setISODay:function K(){return XK},setHours:function K(){return Vq},setDefaultOptions:function K(){return Hq},setDayOfYear:function K(){return Qq},setDay:function K(){return HX},setDate:function K(){return qq},set:function K(){return Jq},secondsToMinutes:function K(){return Zq},secondsToMilliseconds:function K(){return Uq},secondsToHours:function K(){return Bq},roundToNearestMinutes:function K(){return Kq},roundToNearestHours:function K(){return Xq},quartersToYears:function K(){return Gq},quartersToMonths:function K(){return tJ},previousWednesday:function K(){return eJ},previousTuesday:function K(){return oJ},previousThursday:function K(){return aJ},previousSunday:function K(){return nJ},previousSaturday:function K(){return sJ},previousMonday:function K(){return iJ},previousFriday:function K(){return rJ},previousDay:function K(){return CG},parsers:function K(){return KK},parseJSON:function K(){return dJ},parseISO:function K(){return PJ},parse:function K(){return BK},nextWednesday:function K(){return $J},nextTuesday:function K(){return zJ},nextThursday:function K(){return YJ},nextSunday:function K(){return MJ},nextSaturday:function K(){return TJ},nextMonday:function K(){return WJ},nextFriday:function K(){return FJ},nextDay:function K(){return wG},newDate:function K(){return IJ},monthsToYears:function K(){return bJ},monthsToQuarters:function K(){return CJ},minutesToSeconds:function K(){return wJ},minutesToMilliseconds:function K(){return LJ},minutesToHours:function K(){return AJ},min:function K(){return W0},millisecondsToSeconds:function K(){return RJ},millisecondsToMinutes:function K(){return EJ},millisecondsToHours:function K(){return xJ},milliseconds:function K(){return NJ},max:function K(){return F0},longFormatters:function K(){return JX},lightFormatters:function K(){return qG},lightFormat:function K(){return JJ},lastDayOfYear:function K(){return ZJ},lastDayOfWeek:function K(){return jK},lastDayOfQuarter:function K(){return UJ},lastDayOfMonth:function K(){return i0},lastDayOfISOWeekYear:function K(){return BJ},lastDayOfISOWeek:function K(){return KJ},lastDayOfDecade:function K(){return XJ},isYesterday:function K(){return GJ},isWithinInterval:function K(){return tZ},isWeekend:function K(){return YG},isWednesday:function K(){return eZ},isValid:function K(){return jG},isTuesday:function K(){return oZ},isTomorrow:function K(){return aZ},isToday:function K(){return nZ},isThursday:function K(){return sZ},isThisYear:function K(){return iZ},isThisWeek:function K(){return rZ},isThisSecond:function K(){return dZ},isThisQuarter:function K(){return pZ},isThisMonth:function K(){return lZ},isThisMinute:function K(){return _Z},isThisISOWeek:function K(){return uZ},isThisHour:function K(){return cZ},isSunday:function K(){return mZ},isSaturday:function K(){return fZ},isSameYear:function K(){return VK},isSameWeek:function K(){return mX},isSameSecond:function K(){return HK},isSameQuarter:function K(){return QK},isSameMonth:function K(){return qK},isSameMinute:function K(){return JK},isSameISOWeekYear:function K(){return gZ},isSameISOWeek:function K(){return ZK},isSameHour:function K(){return UK},isSameDay:function K(){return kG},isPast:function K(){return yZ},isMonday:function K(){return kZ},isMatch:function K(){return hZ},isLeapYear:function K(){return yX},isLastDayOfMonth:function K(){return P0},isFuture:function K(){return lU},isFriday:function K(){return E0},isFirstDayOfMonth:function K(){return _U},isExists:function K(){return uU},isEqual:function K(){return cU},isDate:function K(){return M0},isBefore:function K(){return mU},isAfter:function K(){return fU},intlFormatDistance:function K(){return gU},intlFormat:function K(){return kU},intervalToDuration:function K(){return hU},interval:function K(){return DU},hoursToSeconds:function K(){return SU},hoursToMinutes:function K(){return OU},hoursToMilliseconds:function K(){return vU},getYear:function K(){return PU},getWeeksInMonth:function K(){return $U},getWeekYear:function K(){return UX},getWeekOfMonth:function K(){return zU},getWeek:function K(){return SX},getUnixTime:function K(){return YU},getTime:function K(){return MU},getSeconds:function K(){return TU},getQuarter:function K(){return WX},getOverlappingDaysInIntervals:function K(){return WU},getMonth:function K(){return FU},getMinutes:function K(){return IU},getMilliseconds:function K(){return bU},getISOWeeksInYear:function K(){return CU},getISOWeekYear:function K(){return RG},getISOWeek:function K(){return OX},getISODay:function K(){return r0},getHours:function K(){return wU},getDefaultOptions:function K(){return d0},getDecade:function K(){return LU},getDaysInYear:function K(){return AU},getDaysInMonth:function K(){return p0},getDayOfYear:function K(){return h0},getDay:function K(){return qX},getDate:function K(){return l0},fromUnixTime:function K(){return EU},formatters:function K(){return DX},formatRelative:function K(){return xU},formatRFC7231:function K(){return VU},formatRFC3339:function K(){return HU},formatISODuration:function K(){return QU},formatISO9075:function K(){return qU},formatISO:function K(){return JU},formatDuration:function K(){return UU},formatDistanceToNowStrict:function K(){return BU},formatDistanceToNow:function K(){return KU},formatDistanceStrict:function K(){return _0},formatDistance:function K(){return u0},formatDate:function K(){return kX},format:function K(){return kX},endOfYesterday:function K(){return NB},endOfYear:function K(){return S0},endOfWeek:function K(){return D0},endOfTomorrow:function K(){return jB},endOfToday:function K(){return VB},endOfSecond:function K(){return HB},endOfQuarter:function K(){return QB},endOfMonth:function K(){return zX},endOfMinute:function K(){return qB},endOfISOWeekYear:function K(){return JB},endOfISOWeek:function K(){return ZB},endOfHour:function K(){return UB},endOfDecade:function K(){return BB},endOfDay:function K(){return YX},eachYearOfInterval:function K(){return KB},eachWeekendOfYear:function K(){return XB},eachWeekendOfMonth:function K(){return GB},eachWeekendOfInterval:function K(){return $X},eachWeekOfInterval:function K(){return tK},eachQuarterOfInterval:function K(){return eK},eachMonthOfInterval:function K(){return oK},eachMinuteOfInterval:function K(){return aK},eachHourOfInterval:function K(){return nK},eachDayOfInterval:function K(){return O0},differenceInYears:function K(){return v0},differenceInWeeks:function K(){return sK},differenceInSeconds:function K(){return $G},differenceInQuarters:function K(){return iK},differenceInMonths:function K(){return KX},differenceInMinutes:function K(){return XX},differenceInMilliseconds:function K(){return MX},differenceInISOWeekYears:function K(){return rK},differenceInHours:function K(){return GX},differenceInDays:function K(){return TX},differenceInCalendarYears:function K(){return yG},differenceInCalendarWeeks:function K(){return tG},differenceInCalendarQuarters:function K(){return eG},differenceInCalendarMonths:function K(){return oG},differenceInCalendarISOWeeks:function K(){return dK},differenceInCalendarISOWeekYears:function K(){return Y0},differenceInCalendarDays:function K(){return ZG},differenceInBusinessDays:function K(){return pK},daysToWeeks:function K(){return lK},constructNow:function K(){return l},constructFrom:function K(){return L},compareDesc:function K(){return _K},compareAsc:function K(){return JG},closestTo:function K(){return uK},closestIndexTo:function K(){return T0},clamp:function K(){return cK},areIntervalsOverlapping:function K(){return mK},addYears:function K(){return I0},addWeeks:function K(){return aG},addSeconds:function K(){return b0},addQuarters:function K(){return FX},addMonths:function K(){return hG},addMinutes:function K(){return IX},addMilliseconds:function K(){return nG},addISOWeekYears:function K(){return C0},addHours:function K(){return A0},addDays:function K(){return UG},addBusinessDays:function K(){return R0},add:function K(){return MG}});var X0=7,pG=365.2425,PK=Math.pow(10,8)*24*60*60*1000,yq=-PK,SG=604800000,K0=86400000,VG=60000,bG=3600000,wX=1000,B0=525600,WG=43200,dG=1440,U0=60,Z0=3,J0=12,q0=4,rG=3600,CX=60,bX=rG*24,vK=bX*7,Q0=bX*pG,H0=Q0/12,OK=H0*3,V0=Symbol.for("constructDateFrom");function TG(K,G,X){return hK(kK(K,G,X))}function iG(K,G,X){return yK(DK(K,G,X))}function SK(K){if(K===-3)return!1;var G=sG(25*K+11,33);return G<8&&G>=-1||G<=-27}function DK(K,G,X){var B=j0(K,G),U=z(B,2),Z=U[0],J=U[1];K=Z,G=J;var q=G-1,Q=K,H=X,V=N0-1+365*(Q-1)+m(8*Q+21,33);if(q!=0)V+=x0[q];return V+H}function hK(K){if(isNaN(K))return{jy:NaN,jm:NaN,jd:NaN};var G,X,B=K-N0,U=1+m(33*B+3,12053);if(X=B-(365*(U-1)+m(8*U+21,33)),X<0)U--,X=B-(365*(U-1)+m(8*U+21,33));if(X<216)G=m(X,31);else G=m(X-6,30);var Z=X-x0[G]+1;X++;var J=U,q=G+1,Q=Z;return{jy:J,jm:q,jd:Q}}function kK(K,G,X){var B=j0(K,G),U=z(B,2),Z=U[0],J=U[1];return K=Z,G=J,m(1461*(K+4800+m(G-14,12)),4)+m(367*(G-2-12*m(G-14,12)),12)-m(3*m(K+4900+m(G-14,12),100),4)+X-32075}function yK(K){if(isNaN(K))return{gy:NaN,gm:NaN,gd:NaN};var G=K+68569,X=m(4*G,146097);G=G-m(146097*X+3,4);var B=m(4000*(G+1),1461001);G=G-m(1461*B,4)+31;var U=m(80*G,2447),Z=G-m(2447*U,80);G=m(U,11);var J=U+2-12*G,q=100*(X-49)+B+G;return{gy:q,gm:J,gd:Z}}function j0(K,G){if(G=G-1,G<0){var X=G;G=gK(G,12),K-=m(G-X,12)}if(G>11)K+=m(G,12),G=sG(G,12);return[K,G+1]}function m(K,G){return~~(K/G)}function sG(K,G){return K-~~(K/G)*G}function gK(K,G){return sG(sG(K,G)+G,G)}var N0=1948320,x0=[0,31,62,93,124,155,186,216,246,276,306,336];function DG(){for(var K=arguments.length,G=new Array(K),X=0;X<K;X++)G[X]=arguments[X];if(G.length>1){var B=G[0],U=G[1],Z=G[2],J=Z===void 0?1:Z,q=G.slice(3),Q=iG(B,U+1,J);return oX(Date,[Q.gy,Q.gm-1,Q.gd].concat(uG(q)))}return oX(Date,G)}function L(K,G){if(typeof K==="function")return K(G);if(K&&KG(K)==="object"&&V0 in K)return K[V0](G);if(K instanceof Date)return new K.constructor(G);return DG(G)}function j(K,G){return L(G||K,K)}function h(K){var G=K.getDate(),X=K.getMonth()+1,B=K.getFullYear();return TG(B,X,G).jd}function i(K){var G=K.getDate(),X=K.getMonth()+1,B=K.getFullYear(),U=TG(B,X,G);for(var Z=arguments.length,J=new Array(Z>1?Z-1:0),q=1;q<Z;q++)J[q-1]=arguments[q];var Q=J[0],H=iG(U.jy,U.jm,Q);return K.setFullYear(H.gy,H.gm-1,H.gd)}function UG(K,G,X){var B=j(K,X===null||X===void 0?void 0:X.in);if(isNaN(G))return L((X===null||X===void 0?void 0:X.in)||K,NaN);if(!G)return B;return i(B,h(B)+G),B}function v(K){var G=K.getDate(),X=K.getMonth()+1,B=K.getFullYear();return TG(B,X,G).jm-1}function e(K){var G=K.getDate(),X=K.getMonth()+1,B=K.getFullYear(),U=TG(B,X,G);for(var Z=arguments.length,J=new Array(Z>1?Z-1:0),q=1;q<Z;q++)J[q-1]=arguments[q];var Q=J[0],H=J[1],V=H===void 0?U.jd:H,N=iG(U.jy,Q+1,V);return K.setFullYear(N.gy,N.gm-1,N.gd)}function F(K){var G=K.getDate(),X=K.getMonth()+1,B=K.getFullYear();return TG(B,X,G).jy}function O(K){var G=K.getDate(),X=K.getMonth()+1,B=K.getFullYear(),U=TG(B,X,G);for(var Z=arguments.length,J=new Array(Z>1?Z-1:0),q=1;q<Z;q++)J[q-1]=arguments[q];var Q=J[0],H=J[1],V=H===void 0?U.jm-1:H,N=J[2],x=N===void 0?U.jd:N,C=iG(Q,V+1,x);return K.setFullYear(C.gy,C.gm-1,C.gd)}function hG(K,G,X){var B=j(K,X===null||X===void 0?void 0:X.in);if(isNaN(G))return L((X===null||X===void 0?void 0:X.in)||K,NaN);if(!G)return B;var U=h(B),Z=L((X===null||X===void 0?void 0:X.in)||K,B.getTime());e(Z,v(B)+G+1,0);var J=h(Z);if(U>=J)return Z;else return O(B,F(Z),v(Z),U),B}function MG(K,G,X){var B=G.years,U=B===void 0?0:B,Z=G.months,J=Z===void 0?0:Z,q=G.weeks,Q=q===void 0?0:q,H=G.days,V=H===void 0?0:H,N=G.hours,x=N===void 0?0:N,C=G.minutes,b=C===void 0?0:C,I=G.seconds,A=I===void 0?0:I,Y=j(K,X===null||X===void 0?void 0:X.in),W=J||U?hG(Y,J+U*12):Y,D=V||Q?UG(W,V+Q*7):W,y=b+x*60,n=A+y*60,o=n*1000;return L((X===null||X===void 0?void 0:X.in)||K,+D+o)}function E0(K,G){return j(K,G===null||G===void 0?void 0:G.in).getDay()===5}function YG(K,G){var X=j(K,G===null||G===void 0?void 0:G.in).getDay();return X===5}function R0(K,G,X){var B=j(K,X===null||X===void 0?void 0:X.in),U=YG(B,X);if(isNaN(G))return L(X===null||X===void 0?void 0:X.in,NaN);var Z=B.getHours(),J=G<0?-1:1,q=Math.trunc(G/6);i(B,h(B)+q*7);var Q=Math.abs(G%6);while(Q>0)if(i(B,h(B)+J),!YG(B,X))Q-=1;if(U&&YG(B,X)&&G!==0){if(E0(B,X))i(B,h(B)+(J<0?1:-2))}return B.setHours(Z),B}function nG(K,G,X){return L((X===null||X===void 0?void 0:X.in)||K,+j(K)+G)}function A0(K,G,X){return nG(K,G*bG,X)}function a(){return L0}function fK(K){L0=K}var L0={};function s(K,G){var X,B,U,Z,J,q,Q=a(),H=(X=(B=(U=(Z=G===null||G===void 0?void 0:G.weekStartsOn)!==null&&Z!==void 0?Z:G===null||G===void 0||(J=G.locale)===null||J===void 0||(J=J.options)===null||J===void 0?void 0:J.weekStartsOn)!==null&&U!==void 0?U:Q.weekStartsOn)!==null&&B!==void 0?B:(q=Q.locale)===null||q===void 0||(q=q.options)===null||q===void 0?void 0:q.weekStartsOn)!==null&&X!==void 0?X:6,V=j(K,G===null||G===void 0?void 0:G.in),N=V.getDay(),x=(N<H?7:0)+N-H;return i(V,h(V)-x),V.setHours(0,0,0,0),V}function BG(K,G){return s(K,XG(XG({},G),{},{weekStartsOn:1}))}function RG(K,G){var X=j(K,G===null||G===void 0?void 0:G.in),B=X.getFullYear(),U=L(X,0);U.setFullYear(B+1,0,4),U.setHours(0,0,0,0);var Z=BG(U),J=L(X,0);J.setFullYear(B,0,4),J.setHours(0,0,0,0);var q=BG(J);if(X.getTime()>=Z.getTime())return B+1;else if(X.getTime()>=q.getTime())return B;else return B-1}function t(K){var G=j(K),X=new Date(Date.UTC(G.getFullYear(),G.getMonth(),G.getDate(),G.getHours(),G.getMinutes(),G.getSeconds(),G.getMilliseconds()));return X.setUTCFullYear(G.getFullYear()),+K-+X}function k(K){for(var G=arguments.length,X=new Array(G>1?G-1:0),B=1;B<G;B++)X[B-1]=arguments[B];var U=L.bind(null,K||X.find(function(Z){return KG(Z)==="object"}));return X.map(U)}function zG(K,G){var X=j(K,G===null||G===void 0?void 0:G.in);return X.setHours(0,0,0,0),X}function ZG(K,G,X){var B=k(X===null||X===void 0?void 0:X.in,K,G),U=z(B,2),Z=U[0],J=U[1],q=zG(Z),Q=zG(J),H=+q-t(q),V=+Q-t(Q);return Math.round((H-V)/K0)}function AG(K,G){var X=RG(K,G),B=L((G===null||G===void 0?void 0:G.in)||K,0);return B.setFullYear(X,0,4),B.setHours(0,0,0,0),BG(B)}function w0(K,G,X){var B=j(K,X===null||X===void 0?void 0:X.in),U=ZG(B,AG(B,X)),Z=L((X===null||X===void 0?void 0:X.in)||K,0);return Z.setFullYear(G,0,4),Z.setHours(0,0,0,0),B=AG(Z),B.setDate(B.getDate()+U),B}function C0(K,G,X){return w0(K,RG(K,X)+G,X)}function IX(K,G,X){var B=j(K,X===null||X===void 0?void 0:X.in);return B.setTime(B.getTime()+G*VG),B}function FX(K,G,X){return hG(K,G*3,X)}function b0(K,G,X){return nG(K,G*1000,X)}function aG(K,G,X){return UG(K,G*7,X)}function I0(K,G,X){return hG(K,G*12,X)}function mK(K,G,X){var B=[+j(K.start,X===null||X===void 0?void 0:X.in),+j(K.end,X===null||X===void 0?void 0:X.in)].sort(function(N,x){return N-x}),U=z(B,2),Z=U[0],J=U[1],q=[+j(G.start,X===null||X===void 0?void 0:X.in),+j(G.end,X===null||X===void 0?void 0:X.in)].sort(function(N,x){return N-x}),Q=z(q,2),H=Q[0],V=Q[1];if(X!==null&&X!==void 0&&X.inclusive)return Z<=V&&H<=J;return Z<V&&H<J}function F0(K,G){var X,B=G===null||G===void 0?void 0:G.in;return K.forEach(function(U){if(!B&&KG(U)==="object")B=L.bind(null,U);var Z=j(U,B);if(!X||X<Z||isNaN(+Z))X=Z}),L(B,X||NaN)}function W0(K,G){var X,B=G===null||G===void 0?void 0:G.in;return K.forEach(function(U){if(!B&&KG(U)==="object")B=L.bind(null,U);var Z=j(U,B);if(!X||X>Z||isNaN(+Z))X=Z}),L(B,X||NaN)}function cK(K,G,X){var B=k(X===null||X===void 0?void 0:X.in,K,G.start,G.end),U=z(B,3),Z=U[0],J=U[1],q=U[2];return W0([F0([Z,J],X),q],X)}function T0(K,G){var X=+j(K);if(isNaN(X))return NaN;var B,U;return G.forEach(function(Z,J){var q=j(Z);if(isNaN(+q)){B=NaN,U=NaN;return}var Q=Math.abs(X-+q);if(B==null||Q<U)B=J,U=Q}),B}function uK(K,G,X){var B=k.apply(void 0,[X===null||X===void 0?void 0:X.in,K].concat(uG(G))),U=FK(B),Z=U[0],J=U.slice(1),q=T0(Z,J);if(typeof q==="number"&&isNaN(q))return L(Z,NaN);if(q!==void 0)return J[q]}function JG(K,G){var X=+j(K)-+j(G);if(X<0)return-1;else if(X>0)return 1;return X}function _K(K,G){var X=+j(K)-+j(G);if(X>0)return-1;else if(X<0)return 1;return X}function l(K){return L(K,Date.now())}function lK(K){var G=Math.trunc(K/X0);return G===0?0:G}function kG(K,G,X){var B=k(X===null||X===void 0?void 0:X.in,K,G),U=z(B,2),Z=U[0],J=U[1];return+zG(Z)===+zG(J)}function M0(K){return K instanceof Date||KG(K)==="object"&&Object.prototype.toString.call(K)==="[object Date]"}function jG(K){return!(!M0(K)&&typeof K!=="number"||isNaN(+j(K)))}function pK(K,G,X){var B=k(X===null||X===void 0?void 0:X.in,K,G),U=z(B,2),Z=U[0],J=U[1];if(!jG(Z)||!jG(J))return NaN;var q=ZG(Z,J),Q=q<0?-1:1,H=Math.trunc(q/7),V=H*6,N=UG(J,H*7);while(!kG(Z,N))V+=YG(N,X)?0:Q,N=UG(N,Q);return V===0?0:V}function Y0(K,G,X){var B=k(X===null||X===void 0?void 0:X.in,K,G),U=z(B,2),Z=U[0],J=U[1];return RG(Z,X)-RG(J,X)}function dK(K,G,X){var B=k(X===null||X===void 0?void 0:X.in,K,G),U=z(B,2),Z=U[0],J=U[1],q=BG(Z),Q=BG(J),H=+q-t(q),V=+Q-t(Q);return Math.round((H-V)/SG)}function oG(K,G,X){var B=k(X===null||X===void 0?void 0:X.in,K,G),U=z(B,2),Z=U[0],J=U[1],q=F(Z)-F(J),Q=v(Z)-v(J);return q*12+Q}function WX(K,G){var X=j(K,G===null||G===void 0?void 0:G.in),B=Math.trunc(v(X)/3)+1;return B}function eG(K,G,X){var B=k(X===null||X===void 0?void 0:X.in,K,G),U=z(B,2),Z=U[0],J=U[1],q=F(Z)-F(J),Q=WX(Z)-WX(J);return q*4+Q}function tG(K,G,X){var B=k(X===null||X===void 0?void 0:X.in,K,G),U=z(B,2),Z=U[0],J=U[1],q=s(Z,X),Q=s(J,X),H=+q-t(q),V=+Q-t(Q);return Math.round((H-V)/SG)}function yG(K,G,X){var B=k(X===null||X===void 0?void 0:X.in,K,G),U=z(B,2),Z=U[0],J=U[1];return F(Z)-F(J)}function TX(K,G,X){var B=k(X===null||X===void 0?void 0:X.in,K,G),U=z(B,2),Z=U[0],J=U[1],q=z0(Z,J),Q=Math.abs(ZG(Z,J));i(Z,h(Z)-q*Q);var H=Number(z0(Z,J)===-q),V=q*(Q-H);return V===0?0:V}function z0(K,G){var X=F(K)-F(G)||v(K)-v(G)||h(K)-h(G)||K.getHours()-G.getHours()||K.getMinutes()-G.getMinutes()||K.getSeconds()-G.getSeconds()||K.getMilliseconds()-G.getMilliseconds();if(X<0)return-1;if(X>0)return 1;return X}function LG(K){return function(G){var X=K?Math[K]:Math.trunc,B=X(G);return B===0?0:B}}function GX(K,G,X){var B=k(X===null||X===void 0?void 0:X.in,K,G),U=z(B,2),Z=U[0],J=U[1],q=(+Z-+J)/bG;return LG(X===null||X===void 0?void 0:X.roundingMethod)(q)}function $0(K,G,X){return C0(K,-G,X)}function rK(K,G,X){var B=k(X===null||X===void 0?void 0:X.in,K,G),U=z(B,2),Z=U[0],J=U[1],q=JG(Z,J),Q=Math.abs(Y0(Z,J,X)),H=$0(Z,q*Q,X),V=Number(JG(H,J)===-q),N=q*(Q-V);return N===0?0:N}function MX(K,G){return+j(K)-+j(G)}function XX(K,G,X){var B=MX(K,G)/VG;return LG(X===null||X===void 0?void 0:X.roundingMethod)(B)}function YX(K,G){var X=j(K,G===null||G===void 0?void 0:G.in);return X.setHours(23,59,59,999),X}function zX(K,G){var X=j(K,G===null||G===void 0?void 0:G.in),B=v(X);return O(X,F(X),B+1,0),X.setHours(23,59,59,999),X}function P0(K,G){var X=j(K,G===null||G===void 0?void 0:G.in);return+YX(X,G)===+zX(X,G)}function KX(K,G,X){var B=k(X===null||X===void 0?void 0:X.in,K,K,G),U=z(B,3),Z=U[0],J=U[1],q=U[2],Q=JG(J,q),H=Math.abs(oG(J,q));if(H<1)return 0;if(v(J)===1&&h(J)>27)i(J,30);e(J,v(J)-Q*H);var V=JG(J,q)===-Q;if(P0(Z)&&H===1&&JG(Z,q)===1)V=!1;var N=Q*(H-+V);return N===0?0:N}function iK(K,G,X){var B=KX(K,G,X)/3;return LG(X===null||X===void 0?void 0:X.roundingMethod)(B)}function $G(K,G,X){var B=MX(K,G)/1000;return LG(X===null||X===void 0?void 0:X.roundingMethod)(B)}function sK(K,G,X){var B=TX(K,G,X)/7;return LG(X===null||X===void 0?void 0:X.roundingMethod)(B)}function v0(K,G,X){var B=k(X===null||X===void 0?void 0:X.in,K,G),U=z(B,2),Z=U[0],J=U[1],q=JG(Z,J),Q=Math.abs(yG(Z,J));O(Z,1399),O(J,1399);var H=JG(Z,J)===-q,V=q*(Q-+H);return V===0?0:V}function NG(K,G){var X=k(K,G.start,G.end),B=z(X,2),U=B[0],Z=B[1];return{start:U,end:Z}}function O0(K,G){var X,B=NG(G===null||G===void 0?void 0:G.in,K),U=B.start,Z=B.end,J=+U>+Z,q=J?+U:+Z,Q=J?Z:U;Q.setHours(0,0,0,0);var H=(X=G===null||G===void 0?void 0:G.step)!==null&&X!==void 0?X:1;if(!H)return[];if(H<0)H=-H,J=!J;var V=[];while(+Q<=q)V.push(L(U,Q)),i(Q,h(Q)+H),Q.setHours(0,0,0,0);return J?V.reverse():V}function nK(K,G){var X,B=NG(G===null||G===void 0?void 0:G.in,K),U=B.start,Z=B.end,J=+U>+Z,q=J?+U:+Z,Q=J?Z:U;Q.setMinutes(0,0,0);var H=(X=G===null||G===void 0?void 0:G.step)!==null&&X!==void 0?X:1;if(!H)return[];if(H<0)H=-H,J=!J;var V=[];while(+Q<=q)V.push(L(U,Q)),Q.setHours(Q.getHours()+H);return J?V.reverse():V}function aK(K,G){var X,B=NG(G===null||G===void 0?void 0:G.in,K),U=B.start,Z=B.end;U.setSeconds(0,0);var J=+U>+Z,q=J?+U:+Z,Q=J?Z:U,H=(X=G===null||G===void 0?void 0:G.step)!==null&&X!==void 0?X:1;if(!H)return[];if(H<0)H=-H,J=!J;var V=[];while(+Q<=q)V.push(L(U,Q)),Q=IX(Q,H);return J?V.reverse():V}function oK(K,G){var X,B=NG(G===null||G===void 0?void 0:G.in,K),U=B.start,Z=B.end,J=+U>+Z,q=J?+U:+Z,Q=J?Z:U;Q.setHours(0,0,0,0),i(Q,1);var H=(X=G===null||G===void 0?void 0:G.step)!==null&&X!==void 0?X:1;if(!H)return[];if(H<0)H=-H,J=!J;var V=[];while(+Q<=q)V.push(L(U,Q)),e(Q,v(Q)+H);return J?V.reverse():V}function IG(K,G){var X=j(K,G===null||G===void 0?void 0:G.in),B=v(X),U=B-B%3;return e(X,U,1),X.setHours(0,0,0,0),X}function eK(K,G){var X,B=NG(G===null||G===void 0?void 0:G.in,K),U=B.start,Z=B.end,J=+U>+Z,q=J?+IG(U):+IG(Z),Q=J?IG(Z):IG(U),H=(X=G===null||G===void 0?void 0:G.step)!==null&&X!==void 0?X:1;if(!H)return[];if(H<0)H=-H,J=!J;var V=[];while(+Q<=q)V.push(L(U,Q)),Q=FX(Q,H);return J?V.reverse():V}function tK(K,G){var X,B=NG(G===null||G===void 0?void 0:G.in,K),U=B.start,Z=B.end,J=+U>+Z,q=J?s(Z,G):s(U,G),Q=J?s(U,G):s(Z,G);q.setHours(15),Q.setHours(15);var H=+Q.getTime(),V=q,N=(X=G===null||G===void 0?void 0:G.step)!==null&&X!==void 0?X:1;if(!N)return[];if(N<0)N=-N,J=!J;var x=[];while(+V<=H)V.setHours(0),x.push(L(U,V)),V=aG(V,N),V.setHours(15);return J?x.reverse():x}function $X(K,G){var X=NG(G===null||G===void 0?void 0:G.in,K),B=X.start,U=X.end,Z=O0({start:B,end:U},G),J=[],q=0;while(q<Z.length){var Q=Z[q++];if(YG(Q))J.push(L(B,Q))}return J}function BX(K,G){var X=j(K,G===null||G===void 0?void 0:G.in);return i(X,1),X.setHours(0,0,0,0),X}function GB(K,G){var X=BX(K,G),B=zX(K,G);return $X({start:X,end:B},G)}function S0(K,G){var X=j(K,G===null||G===void 0?void 0:G.in),B=F(X);return O(X,B+1,0,0),X.setHours(23,59,59,999),X}function PX(K,G){var X=j(K,G===null||G===void 0?void 0:G.in);return O(X,F(X),0,1),X.setHours(0,0,0,0),X}function XB(K,G){var X=PX(K,G),B=S0(K,G);return $X({start:X,end:B},G)}function KB(K,G){var X,B=NG(G===null||G===void 0?void 0:G.in,K),U=B.start,Z=B.end,J=+U>+Z,q=J?+U:+Z,Q=J?Z:U;Q.setHours(0,0,0,0),e(Q,0,1);var H=(X=G===null||G===void 0?void 0:G.step)!==null&&X!==void 0?X:1;if(!H)return[];if(H<0)H=-H,J=!J;var V=[];while(+Q<=q)V.push(L(U,Q)),O(Q,F(Q)+H);return J?V.reverse():V}function BB(K,G){var X=j(K,G===null||G===void 0?void 0:G.in),B=F(X),U=9+Math.floor(B/10)*10;return O(X,U+1,0,0),X.setHours(23,59,59,999),X}function UB(K,G){var X=j(K,G===null||G===void 0?void 0:G.in);return X.setMinutes(59,59,999),X}function D0(K,G){var X,B,U,Z,J,q,Q=a(),H=(X=(B=(U=(Z=G===null||G===void 0?void 0:G.weekStartsOn)!==null&&Z!==void 0?Z:G===null||G===void 0||(J=G.locale)===null||J===void 0||(J=J.options)===null||J===void 0?void 0:J.weekStartsOn)!==null&&U!==void 0?U:Q.weekStartsOn)!==null&&B!==void 0?B:(q=Q.locale)===null||q===void 0||(q=q.options)===null||q===void 0?void 0:q.weekStartsOn)!==null&&X!==void 0?X:6,V=j(K,G===null||G===void 0?void 0:G.in),N=V.getDay(),x=(N<H?-7:0)+6-(N-H);return i(V,h(V)+x),V.setHours(23,59,59,999),V}function ZB(K,G){return D0(K,XG(XG({},G),{},{weekStartsOn:1}))}function JB(K,G){var X=RG(K,G),B=L((G===null||G===void 0?void 0:G.in)||K,0);B.setFullYear(X+1,0,4),B.setHours(0,0,0,0);var U=BG(B,G);return U.setMilliseconds(U.getMilliseconds()-1),U}function qB(K,G){var X=j(K,G===null||G===void 0?void 0:G.in);return X.setSeconds(59,999),X}function QB(K,G){var X=j(K,G===null||G===void 0?void 0:G.in),B=v(X),U=B-B%3+3;return e(X,U,0),X.setHours(23,59,59,999),X}function HB(K,G){var X=j(K,G===null||G===void 0?void 0:G.in);return X.setMilliseconds(999),X}function VB(K){return YX(Date.now(),K)}function jB(K){var G=l(K===null||K===void 0?void 0:K.in),X=F(G),B=v(G),U=h(G),Z=l(K===null||K===void 0?void 0:K.in);return O(Z,X,B,U+1),Z.setHours(23,59,59,999),K!==null&&K!==void 0&&K.in?K.in(Z):Z}function NB(K){var G=l(K===null||K===void 0?void 0:K.in),X=L(K===null||K===void 0?void 0:K.in,0);return O(X,F(G),v(G),h(G)-1),X.setHours(23,59,59,999),X}var xB={lessThanXSeconds:{one:"\u06A9\u0645\u062A\u0631 \u0627\u0632 \u06CC\u06A9 \u062B\u0627\u0646\u06CC\u0647",other:"\u06A9\u0645\u062A\u0631 \u0627\u0632 {{count}} \u062B\u0627\u0646\u06CC\u0647"},xSeconds:{one:"1 \u062B\u0627\u0646\u06CC\u0647",other:"{{count}} \u062B\u0627\u0646\u06CC\u0647"},halfAMinute:"\u0646\u06CC\u0645 \u062F\u0642\u06CC\u0642\u0647",lessThanXMinutes:{one:"\u06A9\u0645\u062A\u0631 \u0627\u0632 \u06CC\u06A9 \u062F\u0642\u06CC\u0642\u0647",other:"\u06A9\u0645\u062A\u0631 \u0627\u0632 {{count}} \u062F\u0642\u06CC\u0642\u0647"},xMinutes:{one:"1 \u062F\u0642\u06CC\u0642\u0647",other:"{{count}} \u062F\u0642\u06CC\u0642\u0647"},aboutXHours:{one:"\u062D\u062F\u0648\u062F 1 \u0633\u0627\u0639\u062A",other:"\u062D\u062F\u0648\u062F {{count}} \u0633\u0627\u0639\u062A"},xHours:{one:"1 \u0633\u0627\u0639\u062A",other:"{{count}} \u0633\u0627\u0639\u062A"},xDays:{one:"1 \u0631\u0648\u0632",other:"{{count}} \u0631\u0648\u0632"},aboutXWeeks:{one:"\u062D\u062F\u0648\u062F 1 \u0647\u0641\u062A\u0647",other:"\u062D\u062F\u0648\u062F {{count}} \u0647\u0641\u062A\u0647"},xWeeks:{one:"1 \u0647\u0641\u062A\u0647",other:"{{count}} \u0647\u0641\u062A\u0647"},aboutXMonths:{one:"\u062D\u062F\u0648\u062F 1 \u0645\u0627\u0647",other:"\u062D\u062F\u0648\u062F {{count}} \u0645\u0627\u0647"},xMonths:{one:"1 \u0645\u0627\u0647",other:"{{count}} \u0645\u0627\u0647"},aboutXYears:{one:"\u062D\u062F\u0648\u062F 1 \u0633\u0627\u0644",other:"\u062D\u062F\u0648\u062F {{count}} \u0633\u0627\u0644"},xYears:{one:"1 \u0633\u0627\u0644",other:"{{count}} \u0633\u0627\u0644"},overXYears:{one:"\u0628\u06CC\u0634\u062A\u0631 \u0627\u0632 1 \u0633\u0627\u0644",other:"\u0628\u06CC\u0634\u062A\u0631 \u0627\u0632 {{count}} \u0633\u0627\u0644"},almostXYears:{one:"\u0646\u0632\u062F\u06CC\u06A9 1 \u0633\u0627\u0644",other:"\u0646\u0632\u062F\u06CC\u06A9 {{count}} \u0633\u0627\u0644"}},EB=function K(G,X,B){var U,Z=xB[G];if(typeof Z==="string")U=Z;else if(X===1)U=Z.one;else U=Z.other.replace("{{count}}",X.toString());if(B!==null&&B!==void 0&&B.addSuffix)if(B.comparison&&B.comparison>0)return"\u062F\u0631 "+U;else return U+" \u0642\u0628\u0644";return U};function vX(K){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},X=G.width?String(G.width):K.defaultWidth,B=K.formats[X]||K.formats[K.defaultWidth];return B}}var RB={full:"EEEE do MMMM y",long:"do MMMM y",medium:"d MMM y",short:"yyyy/MM/dd"},AB={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},LB={full:"{{date}} '\u062F\u0631' {{time}}",long:"{{date}} '\u062F\u0631' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},wB={date:vX({formats:RB,defaultWidth:"full"}),time:vX({formats:AB,defaultWidth:"full"}),dateTime:vX({formats:LB,defaultWidth:"full"})},CB={lastWeek:"eeee '\u06AF\u0630\u0634\u062A\u0647 \u062F\u0631' p",yesterday:"'\u062F\u06CC\u0631\u0648\u0632 \u062F\u0631' p",today:"'\u0627\u0645\u0631\u0648\u0632 \u062F\u0631' p",tomorrow:"'\u0641\u0631\u062F\u0627 \u062F\u0631' p",nextWeek:"eeee '\u062F\u0631' p",other:"P"},bB=function K(G,X,B,U){return CB[G]};function gG(K){return function(G,X){var B=X!==null&&X!==void 0&&X.context?String(X.context):"standalone",U;if(B==="formatting"&&K.formattingValues){var Z=K.defaultFormattingWidth||K.defaultWidth,J=X!==null&&X!==void 0&&X.width?String(X.width):Z;U=K.formattingValues[J]||K.formattingValues[Z]}else{var q=K.defaultWidth,Q=X!==null&&X!==void 0&&X.width?String(X.width):K.defaultWidth;U=K.values[Q]||K.values[q]}var H=K.argumentCallback?K.argumentCallback(G):G;return U[H]}}var IB={narrow:["\u0642","\u0628"],abbreviated:["\u0642.\u0647.","\u0628.\u0647."],wide:["\u0642\u0628\u0644 \u0627\u0632 \u0647\u062C\u0631\u062A","\u0628\u0639\u062F \u0627\u0632 \u0647\u062C\u0631\u062A"]},FB={narrow:["1","2","3","4"],abbreviated:["\u0633\u200C\u06451","\u0633\u200C\u06452","\u0633\u200C\u06453","\u0633\u200C\u06454"],wide:["\u0633\u0647\u200C\u0645\u0627\u0647\u0647 1","\u0633\u0647\u200C\u0645\u0627\u0647\u0647 2","\u0633\u0647\u200C\u0645\u0627\u0647\u0647 3","\u0633\u0647\u200C\u0645\u0627\u0647\u0647 4"]},WB={narrow:["\u0641\u0631","\u0627\u0631","\u062E\u0631","\u062A\u06CC","\u0645\u0631","\u0634\u0647","\u0645\u0647","\u0622\u0628","\u0622\u0630","\u062F\u06CC","\u0628\u0647","\u0627\u0633"],abbreviated:["\u0641\u0631\u0648","\u0627\u0631\u062F","\u062E\u0631\u062F","\u062A\u06CC\u0631","\u0645\u0631\u062F","\u0634\u0647\u0631","\u0645\u0647\u0631","\u0622\u0628\u0627","\u0622\u0630\u0631","\u062F\u06CC","\u0628\u0647\u0645","\u0627\u0633\u0641"],wide:["\u0641\u0631\u0648\u0631\u062F\u06CC\u0646","\u0627\u0631\u062F\u06CC\u0628\u0647\u0634\u062A","\u062E\u0631\u062F\u0627\u062F","\u062A\u06CC\u0631","\u0645\u0631\u062F\u0627\u062F","\u0634\u0647\u0631\u06CC\u0648\u0631","\u0645\u0647\u0631","\u0622\u0628\u0627\u0646","\u0622\u0630\u0631","\u062F\u06CC","\u0628\u0647\u0645\u0646","\u0627\u0633\u0641\u0646\u062F"]},TB={narrow:["\u06CC","\u062F","\u0633","\u0686","\u067E","\u062C","\u0634"],short:["1\u0634","2\u0634","3\u0634","4\u0634","5\u0634","\u062C","\u0634"],abbreviated:["\u06CC\u06A9\u200C\u0634\u0646\u0628\u0647","\u062F\u0648\u0634\u0646\u0628\u0647","\u0633\u0647\u200C\u0634\u0646\u0628\u0647","\u0686\u0647\u0627\u0631\u0634\u0646\u0628\u0647","\u067E\u0646\u062C\u200C\u0634\u0646\u0628\u0647","\u062C\u0645\u0639\u0647","\u0634\u0646\u0628\u0647"],wide:["\u06CC\u06A9\u200C\u0634\u0646\u0628\u0647","\u062F\u0648\u0634\u0646\u0628\u0647","\u0633\u0647\u200C\u0634\u0646\u0628\u0647","\u0686\u0647\u0627\u0631\u0634\u0646\u0628\u0647","\u067E\u0646\u062C\u200C\u0634\u0646\u0628\u0647","\u062C\u0645\u0639\u0647","\u0634\u0646\u0628\u0647"]},MB={narrow:{am:"\u0642",pm:"\u0628",midnight:"\u0646",noon:"\u0638",morning:"\u0635",afternoon:"\u0628.\u0638.",evening:"\u0639",night:"\u0634"},abbreviated:{am:"\u0642.\u0638.",pm:"\u0628.\u0638.",midnight:"\u0646\u06CC\u0645\u0647\u200C\u0634\u0628",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u062D",afternoon:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",evening:"\u0639\u0635\u0631",night:"\u0634\u0628"},wide:{am:"\u0642\u0628\u0644\u200C\u0627\u0632\u0638\u0647\u0631",pm:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",midnight:"\u0646\u06CC\u0645\u0647\u200C\u0634\u0628",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u062D",afternoon:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",evening:"\u0639\u0635\u0631",night:"\u0634\u0628"}},YB={narrow:{am:"\u0642",pm:"\u0628",midnight:"\u0646",noon:"\u0638",morning:"\u0635",afternoon:"\u0628.\u0638.",evening:"\u0639",night:"\u0634"},abbreviated:{am:"\u0642.\u0638.",pm:"\u0628.\u0638.",midnight:"\u0646\u06CC\u0645\u0647\u200C\u0634\u0628",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u062D",afternoon:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",evening:"\u0639\u0635\u0631",night:"\u0634\u0628"},wide:{am:"\u0642\u0628\u0644\u200C\u0627\u0632\u0638\u0647\u0631",pm:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",midnight:"\u0646\u06CC\u0645\u0647\u200C\u0634\u0628",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u062D",afternoon:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",evening:"\u0639\u0635\u0631",night:"\u0634\u0628"}},zB=function K(G,X){var B=Number(G);return B+"-\u0627\u0645"},$B={ordinalNumber:zB,era:gG({values:IB,defaultWidth:"wide"}),quarter:gG({values:FB,defaultWidth:"wide",argumentCallback:function K(G){return G-1}}),month:gG({values:WB,defaultWidth:"wide"}),day:gG({values:TB,defaultWidth:"wide"}),dayPeriod:gG({values:MB,defaultWidth:"wide",formattingValues:YB,defaultFormattingWidth:"wide"})};function fG(K){return function(G){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=X.width,U=B&&K.matchPatterns[B]||K.matchPatterns[K.defaultMatchWidth],Z=G.match(U);if(!Z)return null;var J=Z[0],q=B&&K.parsePatterns[B]||K.parsePatterns[K.defaultParseWidth],Q=Array.isArray(q)?vB(q,function(N){return N.test(J)}):PB(q,function(N){return N.test(J)}),H;H=K.valueCallback?K.valueCallback(Q):Q,H=X.valueCallback?X.valueCallback(H):H;var V=G.slice(J.length);return{value:H,rest:V}}}function PB(K,G){for(var X in K)if(Object.prototype.hasOwnProperty.call(K,X)&&G(K[X]))return X;return}function vB(K,G){for(var X=0;X<K.length;X++)if(G(K[X]))return X;return}function OB(K){return function(G){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=G.match(K.matchPattern);if(!B)return null;var U=B[0],Z=G.match(K.parsePattern);if(!Z)return null;var J=K.valueCallback?K.valueCallback(Z[0]):Z[0];J=X.valueCallback?X.valueCallback(J):J;var q=G.slice(U.length);return{value:J,rest:q}}}var SB=/^(\d+)(-?ام)?/i,DB=/\d+/i,hB={narrow:/^(ق|ب)/i,abbreviated:/^(ق\.?\s?ه\.?|ب\.?\s?ه\.?|ه\.?)/i,wide:/^(قبل از هجرت|هجری شمسی|بعد از هجرت)/i},kB={any:[/^قبل/i,/^بعد/i]},yB={narrow:/^[1234]/i,abbreviated:/^(ف|Q|س‌م)[1234]/i,wide:/^(فصل|quarter|سه‌ماهه) [1234](-ام|ام)?/i},gB={any:[/1/i,/2/i,/3/i,/4/i]},fB={narrow:/^(فر|ار|خر|تی|مر|شه|مه|آب|آذ|دی|به|اس)/i,abbreviated:/^(فرو|ارد|خرد|تیر|مرد|شهر|مهر|آبا|آذر|دی|بهم|اسف)/i,wide:/^(فروردین|اردیبهشت|خرداد|تیر|مرداد|شهریور|مهر|آبان|آذر|دی|بهمن|اسفند)/i},mB={narrow:[/^فر/i,/^ار/i,/^خر/i,/^تی/i,/^مر/i,/^شه/i,/^مه/i,/^آب/i,/^آذ/i,/^دی/i,/^به/i,/^اس/i],any:[/^فر/i,/^ار/i,/^خر/i,/^تی/i,/^مر/i,/^شه/i,/^مه/i,/^آب/i,/^آذ/i,/^دی/i,/^به/i,/^اس/i]},cB={narrow:/^[شیدسچپج]/i,short:/^(ش|ج|1ش|2ش|3ش|4ش|5ش)/i,abbreviated:/^(یکشنبه|دوشنبه|سه‌شنبه|چهارشنبه|پنج‌شنبه|جمعه|شنبه)/i,wide:/^(یکشنبه|دوشنبه|سه‌شنبه|چهارشنبه|پنج‌شنبه|جمعه|شنبه)/i},uB={narrow:[/^ی/i,/^دو/i,/^س/i,/^چ/i,/^پ/i,/^ج/i,/^ش/i],any:[/^(ی|1ش|یکشنبه)/i,/^(د|2ش|دوشنبه)/i,/^(س|3ش|سه‌شنبه)/i,/^(چ|4ش|چهارشنبه)/i,/^(پ|5ش|پنجشنبه)/i,/^(ج|جمعه)/i,/^(ش|شنبه)/i]},_B={narrow:/^(ب|ق|ن|ظ|ص|ب.ظ.|ع|ش)/i,any:/^(ق.ظ.|ب.ظ.|قبل‌ازظهر|نیمه‌شب|ظهر|صبح|بعدازظهر|عصر|شب)/i},lB={any:{am:/^(ق|ق.ظ.|قبل‌ازظهر)/i,pm:/^(ب|ب.ظ.|بعدازظهر)/i,midnight:/^(‌نیمه‌شب|ن)/i,noon:/^(ظ|ظهر)/i,morning:/^(ص|صبح)/i,afternoon:/^(ب|ب.ظ.|بعدازظهر)/i,evening:/^(ع|عصر)/i,night:/^(ش|شب)/i}},pB={ordinalNumber:OB({matchPattern:SB,parsePattern:DB,valueCallback:function K(G){return parseInt(G,10)}}),era:fG({matchPatterns:hB,defaultMatchWidth:"wide",parsePatterns:kB,defaultParseWidth:"any"}),quarter:fG({matchPatterns:yB,defaultMatchWidth:"wide",parsePatterns:gB,defaultParseWidth:"any",valueCallback:function K(G){return G+1}}),month:fG({matchPatterns:fB,defaultMatchWidth:"wide",parsePatterns:mB,defaultParseWidth:"any"}),day:fG({matchPatterns:cB,defaultMatchWidth:"wide",parsePatterns:uB,defaultParseWidth:"any"}),dayPeriod:fG({matchPatterns:_B,defaultMatchWidth:"any",parsePatterns:lB,defaultParseWidth:"any"})},PG={code:"fa-IR",formatDistance:EB,formatLong:wB,formatRelative:bB,localize:$B,match:pB,options:{weekStartsOn:6,firstWeekContainsDate:1}};function h0(K,G){var X=j(K,G===null||G===void 0?void 0:G.in),B=ZG(X,PX(X)),U=B+1;return U}function OX(K,G){var X=j(K,G===null||G===void 0?void 0:G.in),B=+BG(X)-+AG(X);return Math.round(B/SG)+1}function UX(K,G){var X,B,U,Z,J,q,Q=j(K,G===null||G===void 0?void 0:G.in),H=F(Q),V=a(),N=(X=(B=(U=(Z=G===null||G===void 0?void 0:G.firstWeekContainsDate)!==null&&Z!==void 0?Z:G===null||G===void 0||(J=G.locale)===null||J===void 0||(J=J.options)===null||J===void 0?void 0:J.firstWeekContainsDate)!==null&&U!==void 0?U:V.firstWeekContainsDate)!==null&&B!==void 0?B:(q=V.locale)===null||q===void 0||(q=q.options)===null||q===void 0?void 0:q.firstWeekContainsDate)!==null&&X!==void 0?X:1,x=L((G===null||G===void 0?void 0:G.in)||K,0);O(x,H+1,0,N),x.setHours(0,0,0,0);var C=s(x,G),b=L((G===null||G===void 0?void 0:G.in)||K,0);O(b,H,0,N),b.setHours(0,0,0,0);var I=s(b,G);if(+Q>=+C)return H+1;else if(+Q>=+I)return H;else return H-1}function ZX(K,G){var X,B,U,Z,J,q,Q=a(),H=(X=(B=(U=(Z=G===null||G===void 0?void 0:G.firstWeekContainsDate)!==null&&Z!==void 0?Z:G===null||G===void 0||(J=G.locale)===null||J===void 0||(J=J.options)===null||J===void 0?void 0:J.firstWeekContainsDate)!==null&&U!==void 0?U:Q.firstWeekContainsDate)!==null&&B!==void 0?B:(q=Q.locale)===null||q===void 0||(q=q.options)===null||q===void 0?void 0:q.firstWeekContainsDate)!==null&&X!==void 0?X:1,V=UX(K,G),N=L((G===null||G===void 0?void 0:G.in)||K,0);O(N,V,0,H),N.setHours(0,0,0,0);var x=s(N,G);return x}function SX(K,G){var X=j(K,G===null||G===void 0?void 0:G.in),B=+s(X,G)-+ZX(X,G);return Math.round(B/SG)+1}function w(K,G){var X=K<0?"-":"",B=Math.abs(K).toString().padStart(G,"0");return X+B}var qG={y:function K(G,X){var B=F(G),U=B>0?B:1-B;return w(X==="yy"?U%100:U,X.length)},M:function K(G,X){var B=v(G);return X==="M"?String(B+1):w(B+1,2)},d:function K(G,X){return w(h(G),X.length)},a:function K(G,X){var B=G.getHours()/12>=1?"pm":"am";switch(X){case"a":case"aa":return B.toUpperCase();case"aaa":return B;case"aaaaa":return B[0];case"aaaa":default:return B==="am"?"a.m.":"p.m."}},h:function K(G,X){return w(G.getHours()%12||12,X.length)},H:function K(G,X){return w(G.getHours(),X.length)},m:function K(G,X){return w(G.getMinutes(),X.length)},s:function K(G,X){return w(G.getSeconds(),X.length)},S:function K(G,X){var B=X.length,U=G.getMilliseconds(),Z=Math.trunc(U*Math.pow(10,B-3));return w(Z,X.length)}};function k0(K){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",X=K>0?"-":"+",B=Math.abs(K),U=Math.trunc(B/60),Z=B%60;if(Z===0)return X+String(U);return X+String(U)+G+w(Z,2)}function y0(K,G){if(K%60===0){var X=K>0?"-":"+";return X+w(Math.abs(K)/60,2)}return FG(K,G)}function FG(K){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",X=K>0?"-":"+",B=Math.abs(K),U=w(Math.trunc(B/60),2),Z=w(B%60,2);return X+U+G+Z}var vG={am:"am",pm:"pm",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},DX={G:function K(G,X,B){var U=F(G)>0?1:0;switch(X){case"G":case"GG":case"GGG":return B.era(U,{width:"abbreviated"});case"GGGGG":return B.era(U,{width:"narrow"});case"GGGG":default:return B.era(U,{width:"wide"})}},y:function K(G,X,B){if(X==="yo"){var U=F(G),Z=U>0?U:1-U;return B.ordinalNumber(Z,{unit:"year"})}return qG.y(G,X)},Y:function K(G,X,B,U){var Z=UX(G,U),J=Z>0?Z:1-Z;if(X==="YY"){var q=J%100;return w(q,2)}if(X==="Yo")return B.ordinalNumber(J,{unit:"year"});return w(J,X.length)},R:function K(G,X){var B=RG(G);return w(B,X.length)},u:function K(G,X){var B=F(G);return w(B,X.length)},Q:function K(G,X,B){var U=Math.ceil((v(G)+1)/3);switch(X){case"Q":return String(U);case"QQ":return w(U,2);case"Qo":return B.ordinalNumber(U,{unit:"quarter"});case"QQQ":return B.quarter(U,{width:"abbreviated",context:"formatting"});case"QQQQQ":return B.quarter(U,{width:"narrow",context:"formatting"});case"QQQQ":default:return B.quarter(U,{width:"wide",context:"formatting"})}},q:function K(G,X,B){var U=Math.ceil((v(G)+1)/3);switch(X){case"q":return String(U);case"qq":return w(U,2);case"qo":return B.ordinalNumber(U,{unit:"quarter"});case"qqq":return B.quarter(U,{width:"abbreviated",context:"standalone"});case"qqqqq":return B.quarter(U,{width:"narrow",context:"standalone"});case"qqqq":default:return B.quarter(U,{width:"wide",context:"standalone"})}},M:function K(G,X,B){var U=v(G);switch(X){case"M":case"MM":return qG.M(G,X);case"Mo":return B.ordinalNumber(U+1,{unit:"month"});case"MMM":return B.month(U,{width:"abbreviated",context:"formatting"});case"MMMMM":return B.month(U,{width:"narrow",context:"formatting"});case"MMMM":default:return B.month(U,{width:"wide",context:"formatting"})}},L:function K(G,X,B){var U=v(G);switch(X){case"L":return String(U+1);case"LL":return w(U+1,2);case"Lo":return B.ordinalNumber(U+1,{unit:"month"});case"LLL":return B.month(U,{width:"abbreviated",context:"standalone"});case"LLLLL":return B.month(U,{width:"narrow",context:"standalone"});case"LLLL":default:return B.month(U,{width:"wide",context:"standalone"})}},w:function K(G,X,B,U){var Z=SX(G,U);if(X==="wo")return B.ordinalNumber(Z,{unit:"week"});return w(Z,X.length)},I:function K(G,X,B){var U=OX(G);if(X==="Io")return B.ordinalNumber(U,{unit:"week"});return w(U,X.length)},d:function K(G,X,B){if(X==="do")return B.ordinalNumber(h(G),{unit:"date"});return qG.d(G,X)},D:function K(G,X,B){var U=h0(G);if(X==="Do")return B.ordinalNumber(U,{unit:"dayOfYear"});return w(U,X.length)},E:function K(G,X,B){var U=G.getDay();switch(X){case"E":case"EE":case"EEE":return B.day(U,{width:"abbreviated",context:"formatting"});case"EEEEE":return B.day(U,{width:"narrow",context:"formatting"});case"EEEEEE":return B.day(U,{width:"short",context:"formatting"});case"EEEE":default:return B.day(U,{width:"wide",context:"formatting"})}},e:function K(G,X,B,U){var Z=G.getDay(),J=(Z-U.weekStartsOn+8)%7||7;switch(X){case"e":return String(J);case"ee":return w(J,2);case"eo":return B.ordinalNumber(J,{unit:"day"});case"eee":return B.day(Z,{width:"abbreviated",context:"formatting"});case"eeeee":return B.day(Z,{width:"narrow",context:"formatting"});case"eeeeee":return B.day(Z,{width:"short",context:"formatting"});case"eeee":default:return B.day(Z,{width:"wide",context:"formatting"})}},c:function K(G,X,B,U){var Z=G.getDay(),J=(Z-U.weekStartsOn+8)%7||7;switch(X){case"c":return String(J);case"cc":return w(J,X.length);case"co":return B.ordinalNumber(J,{unit:"day"});case"ccc":return B.day(Z,{width:"abbreviated",context:"standalone"});case"ccccc":return B.day(Z,{width:"narrow",context:"standalone"});case"cccccc":return B.day(Z,{width:"short",context:"standalone"});case"cccc":default:return B.day(Z,{width:"wide",context:"standalone"})}},i:function K(G,X,B){var U=G.getDay(),Z=U===0?7:U;switch(X){case"i":return String(Z);case"ii":return w(Z,X.length);case"io":return B.ordinalNumber(Z,{unit:"day"});case"iii":return B.day(U,{width:"abbreviated",context:"formatting"});case"iiiii":return B.day(U,{width:"narrow",context:"formatting"});case"iiiiii":return B.day(U,{width:"short",context:"formatting"});case"iiii":default:return B.day(U,{width:"wide",context:"formatting"})}},a:function K(G,X,B){var U=G.getHours(),Z=U/12>=1?"pm":"am";switch(X){case"a":case"aa":return B.dayPeriod(Z,{width:"abbreviated",context:"formatting"});case"aaa":return B.dayPeriod(Z,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return B.dayPeriod(Z,{width:"narrow",context:"formatting"});case"aaaa":default:return B.dayPeriod(Z,{width:"wide",context:"formatting"})}},b:function K(G,X,B){var U=G.getHours(),Z;if(U===12)Z=vG.noon;else if(U===0)Z=vG.midnight;else Z=U/12>=1?"pm":"am";switch(X){case"b":case"bb":return B.dayPeriod(Z,{width:"abbreviated",context:"formatting"});case"bbb":return B.dayPeriod(Z,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return B.dayPeriod(Z,{width:"narrow",context:"formatting"});case"bbbb":default:return B.dayPeriod(Z,{width:"wide",context:"formatting"})}},B:function K(G,X,B){var U=G.getHours(),Z;if(U>=17)Z=vG.evening;else if(U>=12)Z=vG.afternoon;else if(U>=4)Z=vG.morning;else Z=vG.night;switch(X){case"B":case"BB":case"BBB":return B.dayPeriod(Z,{width:"abbreviated",context:"formatting"});case"BBBBB":return B.dayPeriod(Z,{width:"narrow",context:"formatting"});case"BBBB":default:return B.dayPeriod(Z,{width:"wide",context:"formatting"})}},h:function K(G,X,B){if(X==="ho"){var U=G.getHours()%12;if(U===0)U=12;return B.ordinalNumber(U,{unit:"hour"})}return qG.h(G,X)},H:function K(G,X,B){if(X==="Ho")return B.ordinalNumber(G.getHours(),{unit:"hour"});return qG.H(G,X)},K:function K(G,X,B){var U=G.getHours()%12;if(X==="Ko")return B.ordinalNumber(U,{unit:"hour"});return w(U,X.length)},k:function K(G,X,B){var U=G.getHours();if(U===0)U=24;if(X==="ko")return B.ordinalNumber(U,{unit:"hour"});return w(U,X.length)},m:function K(G,X,B){if(X==="mo")return B.ordinalNumber(G.getMinutes(),{unit:"minute"});return qG.m(G,X)},s:function K(G,X,B){if(X==="so")return B.ordinalNumber(G.getSeconds(),{unit:"second"});return qG.s(G,X)},S:function K(G,X){return qG.S(G,X)},X:function K(G,X,B){var U=G.getTimezoneOffset();if(U===0)return"Z";switch(X){case"X":return y0(U);case"XXXX":case"XX":return FG(U);case"XXXXX":case"XXX":default:return FG(U,":")}},x:function K(G,X,B){var U=G.getTimezoneOffset();switch(X){case"x":return y0(U);case"xxxx":case"xx":return FG(U);case"xxxxx":case"xxx":default:return FG(U,":")}},O:function K(G,X,B){var U=G.getTimezoneOffset();switch(X){case"O":case"OO":case"OOO":return"GMT"+k0(U,":");case"OOOO":default:return"GMT"+FG(U,":")}},z:function K(G,X,B){var U=G.getTimezoneOffset();switch(X){case"z":case"zz":case"zzz":return"GMT"+k0(U,":");case"zzzz":default:return"GMT"+FG(U,":")}},t:function K(G,X,B){var U=Math.trunc(+G/1000);return w(U,X.length)},T:function K(G,X,B){return w(+G,X.length)}},g0=function K(G,X){switch(G){case"P":return X.date({width:"short"});case"PP":return X.date({width:"medium"});case"PPP":return X.date({width:"long"});case"PPPP":default:return X.date({width:"full"})}},f0=function K(G,X){switch(G){case"p":return X.time({width:"short"});case"pp":return X.time({width:"medium"});case"ppp":return X.time({width:"long"});case"pppp":default:return X.time({width:"full"})}},dB=function K(G,X){var B=G.match(/(P+)(p+)?/)||[],U=B[1],Z=B[2];if(!Z)return g0(G,X);var J;switch(U){case"P":J=X.dateTime({width:"short"});break;case"PP":J=X.dateTime({width:"medium"});break;case"PPP":J=X.dateTime({width:"long"});break;case"PPPP":default:J=X.dateTime({width:"full"});break}return J.replace("{{date}}",g0(U,X)).replace("{{time}}",f0(Z,X))},JX={p:f0,P:dB};function m0(K){return iB.test(K)}function c0(K){return sB.test(K)}function hX(K,G,X){var B=rB(K,G,X);if(console.warn(B),nB.includes(K))throw new RangeError(B)}function rB(K,G,X){var B=K[0]==="Y"?"years":"days of the month";return"Use `".concat(K.toLowerCase(),"` instead of `").concat(K,"` (in `").concat(G,"`) for formatting ").concat(B," to the input `").concat(X,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md")}var iB=/^D+$/,sB=/^Y+$/,nB=["D","DD","YY","YYYY"];function kX(K,G,X){var B,U,Z,J,q,Q,H,V,N,x,C,b,I,A,Y=a(),W=(B=(U=X===null||X===void 0?void 0:X.locale)!==null&&U!==void 0?U:Y.locale)!==null&&B!==void 0?B:PG,D=(Z=(J=(q=(Q=X===null||X===void 0?void 0:X.firstWeekContainsDate)!==null&&Q!==void 0?Q:X===null||X===void 0||(H=X.locale)===null||H===void 0||(H=H.options)===null||H===void 0?void 0:H.firstWeekContainsDate)!==null&&q!==void 0?q:Y.firstWeekContainsDate)!==null&&J!==void 0?J:(V=Y.locale)===null||V===void 0||(V=V.options)===null||V===void 0?void 0:V.firstWeekContainsDate)!==null&&Z!==void 0?Z:1,y=(N=(x=(C=(b=X===null||X===void 0?void 0:X.weekStartsOn)!==null&&b!==void 0?b:X===null||X===void 0||(I=X.locale)===null||I===void 0||(I=I.options)===null||I===void 0?void 0:I.weekStartsOn)!==null&&C!==void 0?C:Y.weekStartsOn)!==null&&x!==void 0?x:(A=Y.locale)===null||A===void 0||(A=A.options)===null||A===void 0?void 0:A.weekStartsOn)!==null&&N!==void 0?N:6,n=j(K,X===null||X===void 0?void 0:X.in);if(!jG(n))throw new RangeError("Invalid time value");var o=G.match(eB).map(function(p){var d=p[0];if(d==="p"||d==="P"){var EG=JX[d];return EG(p,W.formatLong)}return p}).join("").match(oB).map(function(p){if(p==="''")return{isToken:!1,value:"'"};var d=p[0];if(d==="'")return{isToken:!1,value:aB(p)};if(DX[d])return{isToken:!0,value:p};if(d.match(XU))throw new RangeError("Format string contains an unescaped latin alphabet character `"+d+"`");return{isToken:!1,value:p}});if(W.localize.preprocessor)o=W.localize.preprocessor(n,o);var xG={firstWeekContainsDate:D,weekStartsOn:y,locale:W};return o.map(function(p){if(!p.isToken)return p.value;var d=p.value;if(!(X!==null&&X!==void 0&&X.useAdditionalWeekYearTokens)&&c0(d)||!(X!==null&&X!==void 0&&X.useAdditionalDayOfYearTokens)&&m0(d))hX(d,G,String(K));var EG=DX[d[0]];return EG(n,d,W.localize,xG)}).join("")}function aB(K){var G=K.match(tB);if(!G)return K;return G[1].replace(GU,"'")}var oB=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,eB=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,tB=/^'([^]*?)'?$/,GU=/''/g,XU=/[a-zA-Z]/;function u0(K,G,X){var B,U,Z=a(),J=(B=(U=X===null||X===void 0?void 0:X.locale)!==null&&U!==void 0?U:Z.locale)!==null&&B!==void 0?B:PG,q=2520,Q=JG(K,G);if(isNaN(Q))throw new RangeError("Invalid time value");var H=Object.assign({},X,{addSuffix:X===null||X===void 0?void 0:X.addSuffix,comparison:Q}),V=k.apply(void 0,[X===null||X===void 0?void 0:X.in].concat(uG(Q>0?[G,K]:[K,G]))),N=z(V,2),x=N[0],C=N[1],b=$G(C,x),I=(t(C)-t(x))/1000,A=Math.round((b-I)/60),Y;if(A<2)if(X!==null&&X!==void 0&&X.includeSeconds)if(b<5)return J.formatDistance("lessThanXSeconds",5,H);else if(b<10)return J.formatDistance("lessThanXSeconds",10,H);else if(b<20)return J.formatDistance("lessThanXSeconds",20,H);else if(b<40)return J.formatDistance("halfAMinute",0,H);else if(b<60)return J.formatDistance("lessThanXMinutes",1,H);else return J.formatDistance("xMinutes",1,H);else if(A===0)return J.formatDistance("lessThanXMinutes",1,H);else return J.formatDistance("xMinutes",A,H);else if(A<45)return J.formatDistance("xMinutes",A,H);else if(A<90)return J.formatDistance("aboutXHours",1,H);else if(A<dG){var W=Math.round(A/60);return J.formatDistance("aboutXHours",W,H)}else if(A<q)return J.formatDistance("xDays",1,H);else if(A<WG){var D=Math.round(A/dG);return J.formatDistance("xDays",D,H)}else if(A<WG*2)return Y=Math.round(A/WG),J.formatDistance("aboutXMonths",Y,H);if(Y=KX(C,x),Y<12){var y=Math.round(A/WG);return J.formatDistance("xMonths",y,H)}else{var n=Y%12,o=Math.trunc(Y/12);if(n<3)return J.formatDistance("aboutXYears",o,H);else if(n<9)return J.formatDistance("overXYears",o,H);else return J.formatDistance("almostXYears",o+1,H)}}function _0(K,G,X){var B,U,Z,J=a(),q=(B=(U=X===null||X===void 0?void 0:X.locale)!==null&&U!==void 0?U:J.locale)!==null&&B!==void 0?B:PG,Q=JG(K,G);if(isNaN(Q))throw new RangeError("Invalid time value");var H=Object.assign({},X,{addSuffix:X===null||X===void 0?void 0:X.addSuffix,comparison:Q}),V=k.apply(void 0,[X===null||X===void 0?void 0:X.in].concat(uG(Q>0?[G,K]:[K,G]))),N=z(V,2),x=N[0],C=N[1],b=LG((Z=X===null||X===void 0?void 0:X.roundingMethod)!==null&&Z!==void 0?Z:"round"),I=C.getTime()-x.getTime(),A=I/VG,Y=t(C)-t(x),W=(I-Y)/VG,D=X===null||X===void 0?void 0:X.unit,y;if(!D)if(A<1)y="second";else if(A<60)y="minute";else if(A<dG)y="hour";else if(W<WG)y="day";else if(W<B0)y="month";else y="year";else y=D;if(y==="second"){var n=b(I/1000);return q.formatDistance("xSeconds",n,H)}else if(y==="minute"){var o=b(A);return q.formatDistance("xMinutes",o,H)}else if(y==="hour"){var xG=b(A/60);return q.formatDistance("xHours",xG,H)}else if(y==="day"){var p=b(W/dG);return q.formatDistance("xDays",p,H)}else if(y==="month"){var d=b(W/WG);return d===12&&D!=="month"?q.formatDistance("xYears",1,H):q.formatDistance("xMonths",d,H)}else{var EG=b(W/B0);return q.formatDistance("xYears",EG,H)}}function KU(K,G){return u0(K,l(K),G)}function BU(K,G){return _0(K,l(K),G)}function UU(K,G){var X,B,U,Z,J,q=a(),Q=(X=(B=G===null||G===void 0?void 0:G.locale)!==null&&B!==void 0?B:q.locale)!==null&&X!==void 0?X:PG,H=(U=G===null||G===void 0?void 0:G.format)!==null&&U!==void 0?U:ZU,V=(Z=G===null||G===void 0?void 0:G.zero)!==null&&Z!==void 0?Z:!1,N=(J=G===null||G===void 0?void 0:G.delimiter)!==null&&J!==void 0?J:" ";if(!Q.formatDistance)return"";var x=H.reduce(function(C,b){var I="x".concat(b.replace(/(^.)/,function(Y){return Y.toUpperCase()})),A=K[b];if(A!==void 0&&(V||K[b]))return C.concat(Q.formatDistance(I,A));return C},[]).join(N);return x}var ZU=["years","months","weeks","days","hours","minutes","seconds"];function JU(K,G){var X,B,U=j(K,G===null||G===void 0?void 0:G.in);if(isNaN(+U))throw new RangeError("Invalid time value");var Z=(X=G===null||G===void 0?void 0:G.format)!==null&&X!==void 0?X:"extended",J=(B=G===null||G===void 0?void 0:G.representation)!==null&&B!==void 0?B:"complete",q="",Q="",H=Z==="extended"?"-":"",V=Z==="extended"?":":"";if(J!=="time"){var N=w(U.getDate(),2),x=w(U.getMonth()+1,2),C=w(U.getFullYear(),4);q="".concat(C).concat(H).concat(x).concat(H).concat(N)}if(J!=="date"){var b=U.getTimezoneOffset();if(b!==0){var I=Math.abs(b),A=w(Math.trunc(I/60),2),Y=w(I%60,2),W=b<0?"+":"-";Q="".concat(W).concat(A,":").concat(Y)}else Q="Z";var D=w(U.getHours(),2),y=w(U.getMinutes(),2),n=w(U.getSeconds(),2),o=q===""?"":"T",xG=[D,y,n].join(V);q="".concat(q).concat(o).concat(xG).concat(Q)}return q}function qU(K,G){var X,B,U=j(K,G===null||G===void 0?void 0:G.in);if(!jG(U))throw new RangeError("Invalid time value");var Z=(X=G===null||G===void 0?void 0:G.format)!==null&&X!==void 0?X:"extended",J=(B=G===null||G===void 0?void 0:G.representation)!==null&&B!==void 0?B:"complete",q="",Q=Z==="extended"?"-":"",H=Z==="extended"?":":"";if(J!=="time"){var V=w(U.getDate(),2),N=w(U.getMonth()+1,2),x=w(U.getFullYear(),4);q="".concat(x).concat(Q).concat(N).concat(Q).concat(V)}if(J!=="date"){var C=w(U.getHours(),2),b=w(U.getMinutes(),2),I=w(U.getSeconds(),2),A=q===""?"":" ";q="".concat(q).concat(A).concat(C).concat(H).concat(b).concat(H).concat(I)}return q}function QU(K){var G=K.years,X=G===void 0?0:G,B=K.months,U=B===void 0?0:B,Z=K.days,J=Z===void 0?0:Z,q=K.hours,Q=q===void 0?0:q,H=K.minutes,V=H===void 0?0:H,N=K.seconds,x=N===void 0?0:N;return"P".concat(X,"Y").concat(U,"M").concat(J,"DT").concat(Q,"H").concat(V,"M").concat(x,"S")}function HU(K,G){var X,B=j(K,G===null||G===void 0?void 0:G.in);if(!jG(B))throw new RangeError("Invalid time value");var U=(X=G===null||G===void 0?void 0:G.fractionDigits)!==null&&X!==void 0?X:0,Z=w(B.getDate(),2),J=w(B.getMonth()+1,2),q=B.getFullYear(),Q=w(B.getHours(),2),H=w(B.getMinutes(),2),V=w(B.getSeconds(),2),N="";if(U>0){var x=B.getMilliseconds(),C=Math.trunc(x*Math.pow(10,U-3));N="."+w(C,U)}var b="",I=B.getTimezoneOffset();if(I!==0){var A=Math.abs(I),Y=w(Math.trunc(A/60),2),W=w(A%60,2),D=I<0?"+":"-";b="".concat(D).concat(Y,":").concat(W)}else b="Z";return"".concat(q,"-").concat(J,"-").concat(Z,"T").concat(Q,":").concat(H,":").concat(V).concat(N).concat(b)}function VU(K){var G=j(K);if(!jG(G))throw new RangeError("Invalid time value");var X=jU[G.getUTCDay()],B=w(G.getUTCDate(),2),U=NU[G.getUTCMonth()],Z=G.getUTCFullYear(),J=w(G.getUTCHours(),2),q=w(G.getUTCMinutes(),2),Q=w(G.getUTCSeconds(),2);return"".concat(X,", ").concat(B," ").concat(U," ").concat(Z," ").concat(J,":").concat(q,":").concat(Q," GMT")}var jU=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],NU=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function xU(K,G,X){var B,U,Z,J,q,Q,H,V,N=k(X===null||X===void 0?void 0:X.in,K,G),x=z(N,2),C=x[0],b=x[1],I=a(),A=(B=(U=X===null||X===void 0?void 0:X.locale)!==null&&U!==void 0?U:I.locale)!==null&&B!==void 0?B:PG,Y=(Z=(J=(q=(Q=X===null||X===void 0?void 0:X.weekStartsOn)!==null&&Q!==void 0?Q:X===null||X===void 0||(H=X.locale)===null||H===void 0||(H=H.options)===null||H===void 0?void 0:H.weekStartsOn)!==null&&q!==void 0?q:I.weekStartsOn)!==null&&J!==void 0?J:(V=I.locale)===null||V===void 0||(V=V.options)===null||V===void 0?void 0:V.weekStartsOn)!==null&&Z!==void 0?Z:0,W=ZG(C,b);if(isNaN(W))throw new RangeError("Invalid time value");var D;if(W<-6)D="other";else if(W<-1)D="lastWeek";else if(W<0)D="yesterday";else if(W<1)D="today";else if(W<2)D="tomorrow";else if(W<7)D="nextWeek";else D="other";var y=A.formatRelative(D,C,b,{locale:A,weekStartsOn:Y});return kX(C,y,{locale:A,weekStartsOn:Y})}function EU(K,G){return j(K*1000,G===null||G===void 0?void 0:G.in)}function l0(K,G){return h(j(K,G===null||G===void 0?void 0:G.in))}function qX(K,G){return j(K,G===null||G===void 0?void 0:G.in).getDay()}function p0(K,G){var X=j(K,G===null||G===void 0?void 0:G.in),B=F(X),U=v(X),Z=L(X,0);return O(Z,B,U+1,0),Z.setHours(0,0,0,0),h(Z)}function RU(K){return SK(K)}function yX(K,G){var X=j(K,G===null||G===void 0?void 0:G.in),B=F(X);return RU(B)}function AU(K,G){var X=j(K,G===null||G===void 0?void 0:G.in);if(Number.isNaN(+X))return NaN;return yX(X)?366:365}function LU(K,G){var X=j(K,G===null||G===void 0?void 0:G.in),B=F(X),U=Math.floor(B/10)*10;return U}function d0(){return Object.assign({},a())}function wU(K,G){return j(K,G===null||G===void 0?void 0:G.in).getHours()}function r0(K,G){var X=j(K,G===null||G===void 0?void 0:G.in).getDay();return X===0?7:X}function CU(K,G){var X=AG(K,G),B=AG(aG(X,60)),U=+B-+X;return Math.round(U/SG)}function bU(K){return j(K).getMilliseconds()}function IU(K,G){return j(K,G===null||G===void 0?void 0:G.in).getMinutes()}function FU(K,G){return v(j(K,G===null||G===void 0?void 0:G.in))}function WU(K,G){var X=[+j(K.start),+j(K.end)].sort(function(I,A){return I-A}),B=z(X,2),U=B[0],Z=B[1],J=[+j(G.start),+j(G.end)].sort(function(I,A){return I-A}),q=z(J,2),Q=q[0],H=q[1],V=U<H&&Q<Z;if(!V)return 0;var N=Q<U?U:Q,x=N-t(N),C=H>Z?Z:H,b=C-t(C);return Math.ceil((b-x)/K0)}function TU(K){return j(K).getSeconds()}function MU(K){return+j(K)}function YU(K){return Math.trunc(+j(K)/1000)}function zU(K,G){var X,B,U,Z,J,q,Q=a(),H=(X=(B=(U=(Z=G===null||G===void 0?void 0:G.weekStartsOn)!==null&&Z!==void 0?Z:G===null||G===void 0||(J=G.locale)===null||J===void 0||(J=J.options)===null||J===void 0?void 0:J.weekStartsOn)!==null&&U!==void 0?U:Q.weekStartsOn)!==null&&B!==void 0?B:(q=Q.locale)===null||q===void 0||(q=q.options)===null||q===void 0?void 0:q.weekStartsOn)!==null&&X!==void 0?X:6,V=l0(j(K,G===null||G===void 0?void 0:G.in));if(isNaN(V))return NaN;var N=qX(BX(K,G)),x=H-N;if(x<=0)x+=7;var C=V-x;return Math.ceil(C/7)+1}function i0(K,G){var X=j(K,G===null||G===void 0?void 0:G.in),B=v(X);return O(X,F(X),B+1,0),X.setHours(0,0,0,0),j(X,G===null||G===void 0?void 0:G.in)}function $U(K,G){var X=j(K,G===null||G===void 0?void 0:G.in);return tG(i0(X,G),BX(X,G),G)+1}function PU(K,G){return F(j(K,G===null||G===void 0?void 0:G.in))}function vU(K){return Math.trunc(K*bG)}function OU(K){return Math.trunc(K*U0)}function SU(K){return Math.trunc(K*rG)}function DU(K,G,X){var B=k(X===null||X===void 0?void 0:X.in,K,G),U=z(B,2),Z=U[0],J=U[1];if(isNaN(+Z))throw new TypeError("Start date is invalid");if(isNaN(+J))throw new TypeError("End date is invalid");if(X!==null&&X!==void 0&&X.assertPositive&&+Z>+J)throw new TypeError("End date must be after start date");return{start:Z,end:J}}function hU(K,G){var X=NG(G===null||G===void 0?void 0:G.in,K),B=X.start,U=X.end,Z={},J=v0(U,B);if(J)Z.years=J;var q=MG(B,{years:Z.years}),Q=KX(U,q);if(Q)Z.months=Q;var H=MG(q,{months:Z.months}),V=TX(U,H);if(V)Z.days=V;var N=MG(H,{days:Z.days}),x=GX(U,N);if(x)Z.hours=x;var C=MG(N,{hours:Z.hours}),b=XX(U,C);if(b)Z.minutes=b;var I=MG(C,{minutes:Z.minutes}),A=$G(U,I);if(A)Z.seconds=A;return Z}function kU(K,G,X){var B,U;if(yU(G))U=G;else X=G;return new Intl.DateTimeFormat((B=X)===null||B===void 0?void 0:B.locale,U).format(j(K))}function yU(K){return K!==void 0&&!("locale"in K)}function gU(K,G,X){var B=0,U,Z=k(X===null||X===void 0?void 0:X.in,K,G),J=z(Z,2),q=J[0],Q=J[1];if(!(X!==null&&X!==void 0&&X.unit)){var H=$G(q,Q);if(Math.abs(H)<CX)B=$G(q,Q),U="second";else if(Math.abs(H)<rG)B=XX(q,Q),U="minute";else if(Math.abs(H)<bX&&Math.abs(ZG(q,Q))<1)B=GX(q,Q),U="hour";else if(Math.abs(H)<vK&&(B=ZG(q,Q))&&Math.abs(B)<7)U="day";else if(Math.abs(H)<H0)B=tG(q,Q),U="week";else if(Math.abs(H)<OK)B=oG(q,Q),U="month";else if(Math.abs(H)<Q0)if(eG(q,Q)<4)B=eG(q,Q),U="quarter";else B=yG(q,Q),U="year";else B=yG(q,Q),U="year"}else if(U=X===null||X===void 0?void 0:X.unit,U==="second")B=$G(q,Q);else if(U==="minute")B=XX(q,Q);else if(U==="hour")B=GX(q,Q);else if(U==="day")B=ZG(q,Q);else if(U==="week")B=tG(q,Q);else if(U==="month")B=oG(q,Q);else if(U==="quarter")B=eG(q,Q);else if(U==="year")B=yG(q,Q);var V=new Intl.RelativeTimeFormat(X===null||X===void 0?void 0:X.locale,XG({numeric:"auto"},X));return V.format(B,U)}function fU(K,G){return+j(K)>+j(G)}function mU(K,G){return+j(K)<+j(G)}function cU(K,G){return+j(K)===+j(G)}function uU(K,G,X){var B=DG(K,G,X);return F(B)===K&&v(B)===G&&h(B)===X}function _U(K,G){return h(j(K,G===null||G===void 0?void 0:G.in))===1}function lU(K){return+j(K)>Date.now()}function s0(K,G){var X=pU(G)?new G(0):L(G,0);return O(X,F(K),v(K),h(K)),X.setHours(K.getHours(),K.getMinutes(),K.getSeconds(),K.getMilliseconds()),X}function pU(K){var G;return typeof K==="function"&&((G=K.prototype)===null||G===void 0?void 0:G.constructor)===K}var dU=10,n0=function(){function K(){T(this,K),E(this,"subPriority",0)}return M(K,[{key:"validate",value:function G(X,B){return!0}}]),K}(),rU=function(K){P(G,K);function G(X,B,U,Z,J){var q;if(T(this,G),q=$(this,G),q.value=X,q.validateValue=B,q.setValue=U,q.priority=Z,J)q.subPriority=J;return q}return M(G,[{key:"validate",value:function X(B,U){return this.validateValue(B,this.value,U)}},{key:"set",value:function X(B,U,Z){return this.setValue(B,U,this.value,Z)}}]),G}(n0),iU=function(K){P(G,K);function G(X,B){var U;return T(this,G),U=$(this,G),E(R(U),"priority",dU),E(R(U),"subPriority",-1),U.context=X||function(Z){return L(B,Z)},U}return M(G,[{key:"set",value:function X(B,U){if(U.timestampIsSet)return B;return L(B,s0(B,this.context))}}]),G}(n0),S=function(){function K(){T(this,K)}return M(K,[{key:"run",value:function G(X,B,U,Z){var J=this.parse(X,B,U,Z);if(!J)return null;return{setter:new rU(J.value,this.validate,this.set,this.priority,this.subPriority),rest:J.rest}}},{key:"validate",value:function G(X,B,U){return!0}}]),K}(),sU=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",140),E(R(X),"incompatibleTokens",["R","u","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z){switch(U){case"G":case"GG":case"GGG":return Z.era(B,{width:"abbreviated"})||Z.era(B,{width:"narrow"});case"GGGGG":return Z.era(B,{width:"narrow"});case"GGGG":default:return Z.era(B,{width:"wide"})||Z.era(B,{width:"abbreviated"})||Z.era(B,{width:"narrow"})}}},{key:"set",value:function X(B,U,Z){return U.era=Z,O(B,Z,0,1),B.setHours(0,0,0,0),B}}]),G}(S),u={month:/^(1[0-2]|0?\d)/,date:/^(3[0-1]|[0-2]?\d)/,dayOfYear:/^(36[0-6]|3[0-5]\d|[0-2]?\d?\d)/,week:/^(5[0-3]|[0-4]?\d)/,hour23h:/^(2[0-3]|[0-1]?\d)/,hour24h:/^(2[0-4]|[0-1]?\d)/,hour11h:/^(1[0-1]|0?\d)/,hour12h:/^(1[0-2]|0?\d)/,minute:/^[0-5]?\d/,second:/^[0-5]?\d/,singleDigit:/^\d/,twoDigits:/^\d{1,2}/,threeDigits:/^\d{1,3}/,fourDigits:/^\d{1,4}/,anyDigitsSigned:/^-?\d+/,singleDigitSigned:/^-?\d/,twoDigitsSigned:/^-?\d{1,2}/,threeDigitsSigned:/^-?\d{1,3}/,fourDigitsSigned:/^-?\d{1,4}/},QG={basicOptionalMinutes:/^([+-])(\d{2})(\d{2})?|Z/,basic:/^([+-])(\d{2})(\d{2})|Z/,basicOptionalSeconds:/^([+-])(\d{2})(\d{2})((\d{2}))?|Z/,extended:/^([+-])(\d{2}):(\d{2})|Z/,extendedOptionalSeconds:/^([+-])(\d{2}):(\d{2})(:(\d{2}))?|Z/};function _(K,G){if(!K)return K;return{value:G(K.value),rest:K.rest}}function f(K,G){var X=G.match(K);if(!X)return null;return{value:parseInt(X[0],10),rest:G.slice(X[0].length)}}function HG(K,G){var X=G.match(K);if(!X)return null;if(X[0]==="Z")return{value:0,rest:G.slice(1)};var B=X[1]==="+"?1:-1,U=X[2]?parseInt(X[2],10):0,Z=X[3]?parseInt(X[3],10):0,J=X[5]?parseInt(X[5],10):0;return{value:B*(U*bG+Z*VG+J*wX),rest:G.slice(X[0].length)}}function a0(K){return f(u.anyDigitsSigned,K)}function c(K,G){switch(K){case 1:return f(u.singleDigit,G);case 2:return f(u.twoDigits,G);case 3:return f(u.threeDigits,G);case 4:return f(u.fourDigits,G);default:return f(new RegExp("^\\d{1,"+K+"}"),G)}}function QX(K,G){switch(K){case 1:return f(u.singleDigitSigned,G);case 2:return f(u.twoDigitsSigned,G);case 3:return f(u.threeDigitsSigned,G);case 4:return f(u.fourDigitsSigned,G);default:return f(new RegExp("^-?\\d{1,"+K+"}"),G)}}function gX(K){switch(K){case"morning":return 4;case"evening":return 17;case"pm":case"noon":case"afternoon":return 12;case"am":case"midnight":case"night":default:return 0}}function o0(K,G){var X=G>0,B=X?G:1-G,U;if(B<=50)U=K||100;else{var Z=B+50,J=Math.trunc(Z/100)*100,q=K>=Z%100;U=K+J-(q?100:0)}return X?U:1-U}function e0(K){return yX(DG(K,0))}var nU=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",130),E(R(X),"incompatibleTokens",["Y","R","u","w","I","i","e","c","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z){var J=function q(Q){return{year:Q,isTwoDigitYear:U==="yy"}};switch(U){case"y":return _(c(4,B),J);case"yo":return _(Z.ordinalNumber(B,{unit:"year"}),J);default:return _(c(U.length,B),J)}}},{key:"validate",value:function X(B,U){return U.isTwoDigitYear||U.year>0}},{key:"set",value:function X(B,U,Z){var J=F(B);if(Z.isTwoDigitYear){var q=o0(Z.year,J);return O(B,q,0,1),B.setHours(0,0,0,0),B}var Q=!("era"in U)||U.era===1?Z.year:1-Z.year;return O(B,Q,0,1),B.setHours(0,0,0,0),B}}]),G}(S),aU=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",130),E(R(X),"incompatibleTokens",["y","R","u","Q","q","M","L","I","d","D","i","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z){var J=function q(Q){return{year:Q,isTwoDigitYear:U==="YY"}};switch(U){case"Y":return _(c(4,B),J);case"Yo":return _(Z.ordinalNumber(B,{unit:"year"}),J);default:return _(c(U.length,B),J)}}},{key:"validate",value:function X(B,U){return U.isTwoDigitYear||U.year>0}},{key:"set",value:function X(B,U,Z,J){var q=UX(B,J);if(Z.isTwoDigitYear){var Q=o0(Z.year,q);return O(B,Q,0,J.firstWeekContainsDate),B.setHours(0,0,0,0),s(B,J)}var H=!("era"in U)||U.era===1?Z.year:1-Z.year;return O(B,H,0,J.firstWeekContainsDate),B.setHours(0,0,0,0),s(B,J)}}]),G}(S),oU=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",130),E(R(X),"incompatibleTokens",["G","y","Y","u","Q","q","M","L","w","d","D","e","c","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U){if(U==="R")return QX(4,B);return QX(U.length,B)}},{key:"set",value:function X(B,U,Z){var J=L(B,0);return O(J,Z,0,4),J.setHours(0,0,0,0),BG(J)}}]),G}(S),eU=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",130),E(R(X),"incompatibleTokens",["G","y","Y","R","w","I","i","e","c","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U){if(U==="u")return QX(4,B);return QX(U.length,B)}},{key:"set",value:function X(B,U,Z){return O(B,Z,0,1),B.setHours(0,0,0,0),B}}]),G}(S),tU=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",120),E(R(X),"incompatibleTokens",["Y","R","q","M","L","w","I","d","D","i","e","c","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z){switch(U){case"Q":case"QQ":return c(U.length,B);case"Qo":return Z.ordinalNumber(B,{unit:"quarter"});case"QQQ":return Z.quarter(B,{width:"abbreviated",context:"formatting"})||Z.quarter(B,{width:"narrow",context:"formatting"});case"QQQQQ":return Z.quarter(B,{width:"narrow",context:"formatting"});case"QQQQ":default:return Z.quarter(B,{width:"wide",context:"formatting"})||Z.quarter(B,{width:"abbreviated",context:"formatting"})||Z.quarter(B,{width:"narrow",context:"formatting"})}}},{key:"validate",value:function X(B,U){return U>=1&&U<=4}},{key:"set",value:function X(B,U,Z){return e(B,(Z-1)*3,1),B.setHours(0,0,0,0),B}}]),G}(S),GZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",120),E(R(X),"incompatibleTokens",["Y","R","Q","M","L","w","I","d","D","i","e","c","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z){switch(U){case"q":case"qq":return c(U.length,B);case"qo":return Z.ordinalNumber(B,{unit:"quarter"});case"qqq":return Z.quarter(B,{width:"abbreviated",context:"standalone"})||Z.quarter(B,{width:"narrow",context:"standalone"});case"qqqqq":return Z.quarter(B,{width:"narrow",context:"standalone"});case"qqqq":default:return Z.quarter(B,{width:"wide",context:"standalone"})||Z.quarter(B,{width:"abbreviated",context:"standalone"})||Z.quarter(B,{width:"narrow",context:"standalone"})}}},{key:"validate",value:function X(B,U){return U>=1&&U<=4}},{key:"set",value:function X(B,U,Z){return e(B,(Z-1)*3,1),B.setHours(0,0,0,0),B}}]),G}(S),XZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"incompatibleTokens",["Y","R","q","Q","L","w","I","D","i","e","c","t","T"]),E(R(X),"priority",110),X}return M(G,[{key:"parse",value:function X(B,U,Z){var J=function q(Q){return Q-1};switch(U){case"M":return _(f(u.month,B),J);case"MM":return _(c(2,B),J);case"Mo":return _(Z.ordinalNumber(B,{unit:"month"}),J);case"MMM":return Z.month(B,{width:"abbreviated",context:"formatting"})||Z.month(B,{width:"narrow",context:"formatting"});case"MMMMM":return Z.month(B,{width:"narrow",context:"formatting"});case"MMMM":default:return Z.month(B,{width:"wide",context:"formatting"})||Z.month(B,{width:"abbreviated",context:"formatting"})||Z.month(B,{width:"narrow",context:"formatting"})}}},{key:"validate",value:function X(B,U){return U>=0&&U<=11}},{key:"set",value:function X(B,U,Z){return e(B,Z,1),B.setHours(0,0,0,0),B}}]),G}(S),KZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",110),E(R(X),"incompatibleTokens",["Y","R","q","Q","M","w","I","D","i","e","c","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z){var J=function q(Q){return Q-1};switch(U){case"L":return _(f(u.month,B),J);case"LL":return _(c(2,B),J);case"Lo":return _(Z.ordinalNumber(B,{unit:"month"}),J);case"LLL":return Z.month(B,{width:"abbreviated",context:"standalone"})||Z.month(B,{width:"narrow",context:"standalone"});case"LLLLL":return Z.month(B,{width:"narrow",context:"standalone"});case"LLLL":default:return Z.month(B,{width:"wide",context:"standalone"})||Z.month(B,{width:"abbreviated",context:"standalone"})||Z.month(B,{width:"narrow",context:"standalone"})}}},{key:"validate",value:function X(B,U){return U>=0&&U<=11}},{key:"set",value:function X(B,U,Z){return e(B,Z,1),B.setHours(0,0,0,0),B}}]),G}(S);function t0(K,G,X){var B=j(K,X===null||X===void 0?void 0:X.in),U=SX(B,X)-G;return i(B,h(B)-U*7),j(B,X===null||X===void 0?void 0:X.in)}var BZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",100),E(R(X),"incompatibleTokens",["y","R","u","q","Q","M","L","I","d","D","i","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z){switch(U){case"w":return f(u.week,B);case"wo":return Z.ordinalNumber(B,{unit:"week"});default:return c(U.length,B)}}},{key:"validate",value:function X(B,U){return U>=1&&U<=53}},{key:"set",value:function X(B,U,Z,J){return s(t0(B,Z,J),J)}}]),G}(S);function GK(K,G,X){var B=j(K,X===null||X===void 0?void 0:X.in),U=OX(B,X)-G;return B.setDate(B.getDate()-U*7),B}var UZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",100),E(R(X),"incompatibleTokens",["y","Y","u","q","Q","M","L","w","d","D","e","c","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z){switch(U){case"I":return f(u.week,B);case"Io":return Z.ordinalNumber(B,{unit:"week"});default:return c(U.length,B)}}},{key:"validate",value:function X(B,U){return U>=1&&U<=53}},{key:"set",value:function X(B,U,Z){return BG(GK(B,Z))}}]),G}(S),ZZ=[31,31,31,31,31,31,30,30,30,30,30,29],JZ=[31,31,31,31,31,31,30,30,30,30,30,30],qZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",90),E(R(X),"subPriority",1),E(R(X),"incompatibleTokens",["Y","R","q","Q","w","I","D","i","e","c","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z){switch(U){case"d":return f(u.date,B);case"do":return Z.ordinalNumber(B,{unit:"date"});default:return c(U.length,B)}}},{key:"validate",value:function X(B,U){var Z=F(B),J=e0(Z),q=v(B);if(J)return U>=1&&U<=JZ[q];else return U>=1&&U<=ZZ[q]}},{key:"set",value:function X(B,U,Z){return i(B,Z),B.setHours(0,0,0,0),B}}]),G}(S),QZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",90),E(R(X),"subpriority",1),E(R(X),"incompatibleTokens",["Y","R","q","Q","M","L","w","I","d","E","i","e","c","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z){switch(U){case"D":case"DD":return f(u.dayOfYear,B);case"Do":return Z.ordinalNumber(B,{unit:"date"});default:return c(U.length,B)}}},{key:"validate",value:function X(B,U){var Z=F(B),J=e0(Z);if(J)return U>=1&&U<=366;else return U>=1&&U<=365}},{key:"set",value:function X(B,U,Z){return e(B,0,Z),B.setHours(0,0,0,0),B}}]),G}(S);function HX(K,G,X){var B,U,Z,J,q,Q,H=a(),V=(B=(U=(Z=(J=X===null||X===void 0?void 0:X.weekStartsOn)!==null&&J!==void 0?J:X===null||X===void 0||(q=X.locale)===null||q===void 0||(q=q.options)===null||q===void 0?void 0:q.weekStartsOn)!==null&&Z!==void 0?Z:H.weekStartsOn)!==null&&U!==void 0?U:(Q=H.locale)===null||Q===void 0||(Q=Q.options)===null||Q===void 0?void 0:Q.weekStartsOn)!==null&&B!==void 0?B:6,N=j(K,X===null||X===void 0?void 0:X.in),x=N.getDay(),C=G%7,b=(C+7)%7,I=7-V,A=G<0||G>6?G-(x+I)%7:(b+I)%7-(x+I)%7;return UG(N,A,X)}var HZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",90),E(R(X),"incompatibleTokens",["D","i","e","c","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z){switch(U){case"E":case"EE":case"EEE":return Z.day(B,{width:"abbreviated",context:"formatting"})||Z.day(B,{width:"short",context:"formatting"})||Z.day(B,{width:"narrow",context:"formatting"});case"EEEEE":return Z.day(B,{width:"narrow",context:"formatting"});case"EEEEEE":return Z.day(B,{width:"short",context:"formatting"})||Z.day(B,{width:"narrow",context:"formatting"});case"EEEE":default:return Z.day(B,{width:"wide",context:"formatting"})||Z.day(B,{width:"abbreviated",context:"formatting"})||Z.day(B,{width:"short",context:"formatting"})||Z.day(B,{width:"narrow",context:"formatting"})}}},{key:"validate",value:function X(B,U){return U>=0&&U<=6}},{key:"set",value:function X(B,U,Z,J){return B=HX(B,Z,J),B.setHours(0,0,0,0),B}}]),G}(S),VZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",90),E(R(X),"incompatibleTokens",["y","R","u","q","Q","M","L","I","d","D","E","i","c","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z,J){var q=function Q(H){var V=Math.floor((H-1)/7)*7;return(H+J.weekStartsOn+6+1)%7+V};switch(U){case"e":case"ee":return _(c(U.length,B),q);case"eo":return _(Z.ordinalNumber(B,{unit:"day"}),q);case"eee":return Z.day(B,{width:"abbreviated",context:"formatting"})||Z.day(B,{width:"short",context:"formatting"})||Z.day(B,{width:"narrow",context:"formatting"});case"eeeee":return Z.day(B,{width:"narrow",context:"formatting"});case"eeeeee":return Z.day(B,{width:"short",context:"formatting"})||Z.day(B,{width:"narrow",context:"formatting"});case"eeee":default:return Z.day(B,{width:"wide",context:"formatting"})||Z.day(B,{width:"abbreviated",context:"formatting"})||Z.day(B,{width:"short",context:"formatting"})||Z.day(B,{width:"narrow",context:"formatting"})}}},{key:"validate",value:function X(B,U){return U>=0&&U<=6}},{key:"set",value:function X(B,U,Z,J){return B=HX(B,Z,J),B.setHours(0,0,0,0),B}}]),G}(S),jZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",90),E(R(X),"incompatibleTokens",["y","R","u","q","Q","M","L","I","d","D","E","i","e","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z,J){var q=function Q(H){var V=Math.floor((H-1)/7)*7;return(H+J.weekStartsOn+6+1)%7+V};switch(U){case"c":case"cc":return _(c(U.length,B),q);case"co":return _(Z.ordinalNumber(B,{unit:"day"}),q);case"ccc":return Z.day(B,{width:"abbreviated",context:"standalone"})||Z.day(B,{width:"short",context:"standalone"})||Z.day(B,{width:"narrow",context:"standalone"});case"ccccc":return Z.day(B,{width:"narrow",context:"standalone"});case"cccccc":return Z.day(B,{width:"short",context:"standalone"})||Z.day(B,{width:"narrow",context:"standalone"});case"cccc":default:return Z.day(B,{width:"wide",context:"standalone"})||Z.day(B,{width:"abbreviated",context:"standalone"})||Z.day(B,{width:"short",context:"standalone"})||Z.day(B,{width:"narrow",context:"standalone"})}}},{key:"validate",value:function X(B,U){return U>=0&&U<=6}},{key:"set",value:function X(B,U,Z,J){return B=HX(B,Z,J),B.setHours(0,0,0,0),B}}]),G}(S);function XK(K,G,X){var B=j(K,X===null||X===void 0?void 0:X.in),U=r0(B,X),Z=G-U;return UG(B,Z,X)}var NZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",90),E(R(X),"incompatibleTokens",["y","Y","u","q","Q","M","L","w","d","D","E","e","c","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z){var J=function q(Q){if(Q===0)return 7;return Q};switch(U){case"i":case"ii":return c(U.length,B);case"io":return Z.ordinalNumber(B,{unit:"day"});case"iii":return _(Z.day(B,{width:"abbreviated",context:"formatting"})||Z.day(B,{width:"short",context:"formatting"})||Z.day(B,{width:"narrow",context:"formatting"}),J);case"iiiii":return _(Z.day(B,{width:"narrow",context:"formatting"}),J);case"iiiiii":return _(Z.day(B,{width:"short",context:"formatting"})||Z.day(B,{width:"narrow",context:"formatting"}),J);case"iiii":default:return _(Z.day(B,{width:"wide",context:"formatting"})||Z.day(B,{width:"abbreviated",context:"formatting"})||Z.day(B,{width:"short",context:"formatting"})||Z.day(B,{width:"narrow",context:"formatting"}),J)}}},{key:"validate",value:function X(B,U){return U>=1&&U<=7}},{key:"set",value:function X(B,U,Z){return B=XK(B,Z),B.setHours(0,0,0,0),B}}]),G}(S),xZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",80),E(R(X),"incompatibleTokens",["b","B","H","k","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z){switch(U){case"a":case"aa":case"aaa":return Z.dayPeriod(B,{width:"abbreviated",context:"formatting"})||Z.dayPeriod(B,{width:"narrow",context:"formatting"});case"aaaaa":return Z.dayPeriod(B,{width:"narrow",context:"formatting"});case"aaaa":default:return Z.dayPeriod(B,{width:"wide",context:"formatting"})||Z.dayPeriod(B,{width:"abbreviated",context:"formatting"})||Z.dayPeriod(B,{width:"narrow",context:"formatting"})}}},{key:"set",value:function X(B,U,Z){return B.setHours(gX(Z),0,0,0),B}}]),G}(S),EZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",80),E(R(X),"incompatibleTokens",["a","B","H","k","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z){switch(U){case"b":case"bb":case"bbb":return Z.dayPeriod(B,{width:"abbreviated",context:"formatting"})||Z.dayPeriod(B,{width:"narrow",context:"formatting"});case"bbbbb":return Z.dayPeriod(B,{width:"narrow",context:"formatting"});case"bbbb":default:return Z.dayPeriod(B,{width:"wide",context:"formatting"})||Z.dayPeriod(B,{width:"abbreviated",context:"formatting"})||Z.dayPeriod(B,{width:"narrow",context:"formatting"})}}},{key:"set",value:function X(B,U,Z){return B.setHours(gX(Z),0,0,0),B}}]),G}(S),RZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",80),E(R(X),"incompatibleTokens",["a","b","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z){switch(U){case"B":case"BB":case"BBB":return Z.dayPeriod(B,{width:"abbreviated",context:"formatting"})||Z.dayPeriod(B,{width:"narrow",context:"formatting"});case"BBBBB":return Z.dayPeriod(B,{width:"narrow",context:"formatting"});case"BBBB":default:return Z.dayPeriod(B,{width:"wide",context:"formatting"})||Z.dayPeriod(B,{width:"abbreviated",context:"formatting"})||Z.dayPeriod(B,{width:"narrow",context:"formatting"})}}},{key:"set",value:function X(B,U,Z){return B.setHours(gX(Z),0,0,0),B}}]),G}(S),AZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",70),E(R(X),"incompatibleTokens",["H","K","k","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z){switch(U){case"h":return f(u.hour12h,B);case"ho":return Z.ordinalNumber(B,{unit:"hour"});default:return c(U.length,B)}}},{key:"validate",value:function X(B,U){return U>=1&&U<=12}},{key:"set",value:function X(B,U,Z){var J=B.getHours()>=12;if(J&&Z<12)B.setHours(Z+12,0,0,0);else if(!J&&Z===12)B.setHours(0,0,0,0);else B.setHours(Z,0,0,0);return B}}]),G}(S),LZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",70),E(R(X),"incompatibleTokens",["a","b","h","K","k","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z){switch(U){case"H":return f(u.hour23h,B);case"Ho":return Z.ordinalNumber(B,{unit:"hour"});default:return c(U.length,B)}}},{key:"validate",value:function X(B,U){return U>=0&&U<=23}},{key:"set",value:function X(B,U,Z){return B.setHours(Z,0,0,0),B}}]),G}(S),wZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",70),E(R(X),"incompatibleTokens",["h","H","k","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z){switch(U){case"K":return f(u.hour11h,B);case"Ko":return Z.ordinalNumber(B,{unit:"hour"});default:return c(U.length,B)}}},{key:"validate",value:function X(B,U){return U>=0&&U<=11}},{key:"set",value:function X(B,U,Z){var J=B.getHours()>=12;if(J&&Z<12)B.setHours(Z+12,0,0,0);else B.setHours(Z,0,0,0);return B}}]),G}(S),CZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",70),E(R(X),"incompatibleTokens",["a","b","h","H","K","t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z){switch(U){case"k":return f(u.hour24h,B);case"ko":return Z.ordinalNumber(B,{unit:"hour"});default:return c(U.length,B)}}},{key:"validate",value:function X(B,U){return U>=1&&U<=24}},{key:"set",value:function X(B,U,Z){var J=Z<=24?Z%24:Z;return B.setHours(J,0,0,0),B}}]),G}(S),bZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",60),E(R(X),"incompatibleTokens",["t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z){switch(U){case"m":return f(u.minute,B);case"mo":return Z.ordinalNumber(B,{unit:"minute"});default:return c(U.length,B)}}},{key:"validate",value:function X(B,U){return U>=0&&U<=59}},{key:"set",value:function X(B,U,Z){return B.setMinutes(Z,0,0),B}}]),G}(S),IZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",50),E(R(X),"incompatibleTokens",["t","T"]),X}return M(G,[{key:"parse",value:function X(B,U,Z){switch(U){case"s":return f(u.second,B);case"so":return Z.ordinalNumber(B,{unit:"second"});default:return c(U.length,B)}}},{key:"validate",value:function X(B,U){return U>=0&&U<=59}},{key:"set",value:function X(B,U,Z){return B.setSeconds(Z,0),B}}]),G}(S),FZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",30),E(R(X),"incompatibleTokens",["t","T"]),X}return M(G,[{key:"parse",value:function X(B,U){var Z=function J(q){return Math.trunc(q*Math.pow(10,-U.length+3))};return _(c(U.length,B),Z)}},{key:"set",value:function X(B,U,Z){return B.setMilliseconds(Z),B}}]),G}(S),WZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",10),E(R(X),"incompatibleTokens",["t","T","x"]),X}return M(G,[{key:"parse",value:function X(B,U){switch(U){case"X":return HG(QG.basicOptionalMinutes,B);case"XX":return HG(QG.basic,B);case"XXXX":return HG(QG.basicOptionalSeconds,B);case"XXXXX":return HG(QG.extendedOptionalSeconds,B);case"XXX":default:return HG(QG.extended,B)}}},{key:"set",value:function X(B,U,Z){if(U.timestampIsSet)return B;return L(B,B.getTime()-t(B)-Z)}}]),G}(S),TZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",10),E(R(X),"incompatibleTokens",["t","T","X"]),X}return M(G,[{key:"parse",value:function X(B,U){switch(U){case"x":return HG(QG.basicOptionalMinutes,B);case"xx":return HG(QG.basic,B);case"xxxx":return HG(QG.basicOptionalSeconds,B);case"xxxxx":return HG(QG.extendedOptionalSeconds,B);case"xxx":default:return HG(QG.extended,B)}}},{key:"set",value:function X(B,U,Z){if(U.timestampIsSet)return B;return L(B,B.getTime()-t(B)-Z)}}]),G}(S),MZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",40),E(R(X),"incompatibleTokens","*"),X}return M(G,[{key:"parse",value:function X(B){return a0(B)}},{key:"set",value:function X(B,U,Z){return[L(B,Z*1000),{timestampIsSet:!0}]}}]),G}(S),YZ=function(K){P(G,K);function G(){var X;T(this,G);for(var B=arguments.length,U=new Array(B),Z=0;Z<B;Z++)U[Z]=arguments[Z];return X=$(this,G,[].concat(U)),E(R(X),"priority",20),E(R(X),"incompatibleTokens","*"),X}return M(G,[{key:"parse",value:function X(B){return a0(B)}},{key:"set",value:function X(B,U,Z){return[L(B,Z),{timestampIsSet:!0}]}}]),G}(S),KK={G:new sU,y:new nU,Y:new aU,R:new oU,u:new eU,Q:new tU,q:new GZ,M:new XZ,L:new KZ,w:new BZ,I:new UZ,d:new qZ,D:new QZ,E:new HZ,e:new VZ,c:new jZ,i:new NZ,a:new xZ,b:new EZ,B:new RZ,h:new AZ,H:new LZ,K:new wZ,k:new CZ,m:new bZ,s:new IZ,S:new FZ,X:new WZ,x:new TZ,t:new MZ,T:new YZ};function BK(K,G,X,B){var U,Z,J,q,Q,H,V,N,x,C,b,I,A,Y,W=function r(){return L((B===null||B===void 0?void 0:B.in)||X,NaN)},D=d0(),y=(U=(Z=B===null||B===void 0?void 0:B.locale)!==null&&Z!==void 0?Z:D.locale)!==null&&U!==void 0?U:PG,n=(J=(q=(Q=(H=B===null||B===void 0?void 0:B.firstWeekContainsDate)!==null&&H!==void 0?H:B===null||B===void 0||(V=B.locale)===null||V===void 0||(V=V.options)===null||V===void 0?void 0:V.firstWeekContainsDate)!==null&&Q!==void 0?Q:D.firstWeekContainsDate)!==null&&q!==void 0?q:(N=D.locale)===null||N===void 0||(N=N.options)===null||N===void 0?void 0:N.firstWeekContainsDate)!==null&&J!==void 0?J:1,o=(x=(C=(b=(I=B===null||B===void 0?void 0:B.weekStartsOn)!==null&&I!==void 0?I:B===null||B===void 0||(A=B.locale)===null||A===void 0||(A=A.options)===null||A===void 0?void 0:A.weekStartsOn)!==null&&b!==void 0?b:D.weekStartsOn)!==null&&C!==void 0?C:(Y=D.locale)===null||Y===void 0||(Y=Y.options)===null||Y===void 0?void 0:Y.weekStartsOn)!==null&&x!==void 0?x:6;if(!G)return K?W():j(X,B===null||B===void 0?void 0:B.in);var xG={firstWeekContainsDate:n,weekStartsOn:o,locale:y},p=[new iU(B===null||B===void 0?void 0:B.in,X)],d=G.match(PZ).map(function(r){var g=r[0];if(g in JX){var GG=JX[g];return GG(r,y.formatLong)}return r}).join("").match($Z),EG=[],NX=rX(d),EK;try{var hq=function r(){var g=EK.value;if(!(B!==null&&B!==void 0&&B.useAdditionalWeekYearTokens)&&c0(g))hX(g,G,K);if(!(B!==null&&B!==void 0&&B.useAdditionalDayOfYearTokens)&&m0(g))hX(g,G,K);var GG=g[0],RX=KK[GG];if(RX){var wK=RX.incompatibleTokens;if(Array.isArray(wK)){var CK=EG.find(function(bK){return wK.includes(bK.token)||bK.token===GG});if(CK)throw new RangeError("The format string mustn't contain `".concat(CK.fullToken,"` and `").concat(g,"` at the same time"))}else if(RX.incompatibleTokens==="*"&&EG.length>0)throw new RangeError("The format string mustn't contain `".concat(g,"` and any other token at the same time"));EG.push({token:GG,fullToken:g});var dX=RX.run(K,g,y.match,xG);if(!dX)return{v:W()};p.push(dX.setter),K=dX.rest}else{if(GG.match(DZ))throw new RangeError("Format string contains an unescaped latin alphabet character `"+GG+"`");if(g==="''")g="'";else if(GG==="'")g=zZ(g);if(K.indexOf(g)===0)K=K.slice(g.length);else return{v:W()}}},pX;for(NX.s();!(EK=NX.n()).done;)if(pX=hq(),pX)return pX.v}catch(r){NX.e(r)}finally{NX.f()}if(K.length>0&&SZ.test(K))return W();var kq=p.map(function(r){return r.priority}).sort(function(r,g){return g-r}).filter(function(r,g,GG){return GG.indexOf(r)===g}).map(function(r){return p.filter(function(g){return g.priority===r}).sort(function(g,GG){return GG.subPriority-g.subPriority})}).map(function(r){return r[0]}),OG=j(X,B===null||B===void 0?void 0:B.in);if(isNaN(+OG))return W();var RK={},xX=rX(kq),AK;try{for(xX.s();!(AK=xX.n()).done;){var LK=AK.value;if(!LK.validate(OG,xG))return W();var EX=LK.set(OG,RK,xG);if(Array.isArray(EX))OG=EX[0],Object.assign(RK,EX[1]);else OG=EX}}catch(r){xX.e(r)}finally{xX.f()}return OG}function zZ(K){return K.match(vZ)[1].replace(OZ,"'")}var $Z=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,PZ=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,vZ=/^'([^]*?)'?$/,OZ=/''/g,SZ=/\S/,DZ=/[a-zA-Z]/;function hZ(K,G,X){return jG(BK(K,G,DG(),X))}function kZ(K,G){return j(K,G===null||G===void 0?void 0:G.in).getDay()===1}function yZ(K){return+j(K)<Date.now()}function fX(K,G){var X=j(K,G===null||G===void 0?void 0:G.in);return X.setMinutes(0,0,0),X}function UK(K,G,X){var B=k(X===null||X===void 0?void 0:X.in,K,G),U=z(B,2),Z=U[0],J=U[1];return+fX(Z)===+fX(J)}function mX(K,G,X){var B=k(X===null||X===void 0?void 0:X.in,K,G),U=z(B,2),Z=U[0],J=U[1];return+s(Z,X)===+s(J,X)}function ZK(K,G,X){return mX(K,G,XG(XG({},X),{},{weekStartsOn:1}))}function gZ(K,G,X){var B=k(X===null||X===void 0?void 0:X.in,K,G),U=z(B,2),Z=U[0],J=U[1];return+AG(Z)===+AG(J)}function cX(K,G){var X=j(K,G===null||G===void 0?void 0:G.in);return X.setSeconds(0,0),X}function JK(K,G){return+cX(K)===+cX(G)}function qK(K,G,X){var B=k(X===null||X===void 0?void 0:X.in,K,G),U=z(B,2),Z=U[0],J=U[1];return F(Z)===F(J)&&v(Z)===v(J)}function QK(K,G,X){var B=k(X===null||X===void 0?void 0:X.in,K,G),U=z(B,2),Z=U[0],J=U[1];return+IG(Z)===+IG(J)}function uX(K,G){var X=j(K,G===null||G===void 0?void 0:G.in);return X.setMilliseconds(0),X}function HK(K,G){return+uX(K)===+uX(G)}function VK(K,G,X){var B=k(X===null||X===void 0?void 0:X.in,K,G),U=z(B,2),Z=U[0],J=U[1];return F(Z)===F(J)}function fZ(K,G){return j(K,G===null||G===void 0?void 0:G.in).getDay()===6}function mZ(K,G){return j(K,G===null||G===void 0?void 0:G.in).getDay()===0}function cZ(K,G){return UK(j(K,G===null||G===void 0?void 0:G.in),l((G===null||G===void 0?void 0:G.in)||K))}function uZ(K,G){return ZK(L((G===null||G===void 0?void 0:G.in)||K,K),l((G===null||G===void 0?void 0:G.in)||K))}function _Z(K){return JK(K,l(K))}function lZ(K,G){return qK(L((G===null||G===void 0?void 0:G.in)||K,K),l((G===null||G===void 0?void 0:G.in)||K))}function pZ(K,G){return QK(L((G===null||G===void 0?void 0:G.in)||K,K),l((G===null||G===void 0?void 0:G.in)||K))}function dZ(K){return HK(K,l(K))}function rZ(K,G){return mX(L((G===null||G===void 0?void 0:G.in)||K,K),l((G===null||G===void 0?void 0:G.in)||K),G)}function iZ(K,G){return VK(L((G===null||G===void 0?void 0:G.in)||K,K),l((G===null||G===void 0?void 0:G.in)||K))}function sZ(K,G){return j(K,G===null||G===void 0?void 0:G.in).getDay()===4}function nZ(K,G){return kG(L((G===null||G===void 0?void 0:G.in)||K,K),l((G===null||G===void 0?void 0:G.in)||K))}function aZ(K,G){return kG(K,UG(l((G===null||G===void 0?void 0:G.in)||K),1),G)}function oZ(K,G){return j(K,G===null||G===void 0?void 0:G.in).getDay()===2}function eZ(K,G){return j(K,G===null||G===void 0?void 0:G.in).getDay()===3}function tZ(K,G,X){var B=+j(K,X===null||X===void 0?void 0:X.in),U=[+j(G.start,X===null||X===void 0?void 0:X.in),+j(G.end,X===null||X===void 0?void 0:X.in)].sort(function(Q,H){return Q-H}),Z=z(U,2),J=Z[0],q=Z[1];return B>=J&&B<=q}function VX(K,G,X){return UG(K,-G,X)}function GJ(K,G){return kG(L((G===null||G===void 0?void 0:G.in)||K,K),VX(l((G===null||G===void 0?void 0:G.in)||K),1))}function XJ(K,G){var X=j(K,G===null||G===void 0?void 0:G.in),B=F(X),U=9+Math.floor(B/10)*10;return O(X,U+1,0,0),X.setHours(0,0,0,0),j(X,G===null||G===void 0?void 0:G.in)}function jK(K,G){var X,B,U,Z,J,q,Q=a(),H=(X=(B=(U=(Z=G===null||G===void 0?void 0:G.weekStartsOn)!==null&&Z!==void 0?Z:G===null||G===void 0||(J=G.locale)===null||J===void 0||(J=J.options)===null||J===void 0?void 0:J.weekStartsOn)!==null&&U!==void 0?U:Q.weekStartsOn)!==null&&B!==void 0?B:(q=Q.locale)===null||q===void 0||(q=q.options)===null||q===void 0?void 0:q.weekStartsOn)!==null&&X!==void 0?X:6,V=j(K,G===null||G===void 0?void 0:G.in),N=V.getDay(),x=(N<H?-7:0)+6-(N-H);return V.setHours(0,0,0,0),i(V,h(V)+x),V}function KJ(K,G){return jK(K,XG(XG({},G),{},{weekStartsOn:1}))}function BJ(K,G){var X=RG(K,G),B=L((G===null||G===void 0?void 0:G.in)||K,0);B.setFullYear(X+1,0,4),B.setHours(0,0,0,0);var U=BG(B,G);return U.setDate(U.getDate()-1),U}function UJ(K,G){var X=j(K,G===null||G===void 0?void 0:G.in),B=v(X),U=B-B%3+3;return e(X,U,0),X.setHours(0,0,0,0),X}function ZJ(K,G){var X=j(K,G===null||G===void 0?void 0:G.in),B=F(X);return O(X,B+1,0,0),X.setHours(0,0,0,0),X}function JJ(K,G){var X=j(K);if(!jG(X))throw new RangeError("Invalid time value");var B=G.match(QJ);if(!B)return"";var U=B.map(function(Z){if(Z==="''")return"'";var J=Z[0];if(J==="'")return qJ(Z);var q=qG[J];if(q)return q(X,Z);if(J.match(jJ))throw new RangeError("Format string contains an unescaped latin alphabet character `"+J+"`");return Z}).join("");return U}function qJ(K){var G=K.match(HJ);if(!G)return K;return G[1].replace(VJ,"'")}var QJ=/(\w)\1*|''|'(''|[^'])+('|$)|./g,HJ=/^'([^]*?)'?$/,VJ=/''/g,jJ=/[a-zA-Z]/;function NJ(K){var{years:G,months:X,weeks:B,days:U,hours:Z,minutes:J,seconds:q}=K,Q=0;if(G)Q+=G*pG;if(X)Q+=X*(pG/12);if(B)Q+=B*7;if(U)Q+=U;var H=Q*24*60*60;if(Z)H+=Z*60*60;if(J)H+=J*60;if(q)H+=q;return Math.trunc(H*1000)}function xJ(K){var G=K/bG;return Math.trunc(G)}function EJ(K){var G=K/VG;return Math.trunc(G)}function RJ(K){var G=K/wX;return Math.trunc(G)}function AJ(K){var G=K/U0;return Math.trunc(G)}function LJ(K){return Math.trunc(K*VG)}function wJ(K){return Math.trunc(K*CX)}function CJ(K){var G=K/Z0;return Math.trunc(G)}function bJ(K){var G=K/J0;return Math.trunc(G)}function IJ(K,G,X){var B=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0,U=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0,Z=arguments.length>5&&arguments[5]!==void 0?arguments[5]:0,J=arguments.length>6&&arguments[6]!==void 0?arguments[6]:0;return DG(K,G,X,B,U,Z,J)}function wG(K,G,X){var B=G-qX(K,X);if(B<=0)B+=7;return UG(K,B,X)}function FJ(K,G){return wG(K,5,G)}function WJ(K,G){return wG(K,1,G)}function TJ(K,G){return wG(K,6,G)}function MJ(K,G){return wG(K,0,G)}function YJ(K,G){return wG(K,4,G)}function zJ(K,G){return wG(K,2,G)}function $J(K,G){return wG(K,3,G)}function PJ(K,G){var X,B=function C(){return L(G===null||G===void 0?void 0:G.in,NaN)},U=(X=G===null||G===void 0?void 0:G.additionalDigits)!==null&&X!==void 0?X:2,Z=vJ(K),J;if(Z.date){var q=OJ(Z.date,U);J=SJ(q.restDateString,q.year)}if(!J||isNaN(+J))return B();var Q=+J,H=0,V;if(Z.time){if(H=DJ(Z.time),isNaN(H))return B()}if(Z.timezone){if(V=hJ(Z.timezone),isNaN(V))return B()}else{var N=new Date(Q+H),x=j(0,G===null||G===void 0?void 0:G.in);return x.setFullYear(N.getUTCFullYear(),N.getUTCMonth(),N.getUTCDate()),x.setHours(N.getUTCHours(),N.getUTCMinutes(),N.getUTCSeconds(),N.getUTCMilliseconds()),x}return j(Q+H+V,G===null||G===void 0?void 0:G.in)}function vJ(K){var G={},X=K.split(jX.dateTimeDelimiter),B;if(X.length>2)return G;if(/:/.test(X[0]))B=X[0];else if(G.date=X[0],B=X[1],jX.timeZoneDelimiter.test(G.date))G.date=K.split(jX.timeZoneDelimiter)[0],B=K.substr(G.date.length,K.length);if(B){var U=jX.timezone.exec(B);if(U)G.time=B.replace(U[1],""),G.timezone=U[1];else G.time=B}return G}function OJ(K,G){var X=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+G)+"})|(\\d{2}|[+-]\\d{"+(2+G)+"})$)"),B=K.match(X);if(!B)return{year:NaN,restDateString:""};var U=B[1]?parseInt(B[1]):null,Z=B[2]?parseInt(B[2]):null;return{year:Z===null?U:Z*100,restDateString:K.slice((B[1]||B[2]).length)}}function SJ(K,G){if(G===null)return new Date(NaN);var X=K.match(uJ);if(!X)return new Date(NaN);var B=!!X[4],U=mG(X[1]),Z=mG(X[2])-1,J=mG(X[3]),q=mG(X[4]),Q=mG(X[5])-1;if(B){if(!fJ(G,q,Q))return new Date(NaN);return kJ(G,q,Q)}else{var H=new Date(0);if(!yJ(G,Z,J)||!gJ(G,U))return new Date(NaN);return H.setUTCFullYear(G,Z,Math.max(U,J)),H}}function mG(K){return K?parseInt(K):1}function DJ(K){var G=K.match(_J);if(!G)return NaN;var X=_X(G[1]),B=_X(G[2]),U=_X(G[3]);if(!mJ(X,B,U))return NaN;return X*bG+B*VG+U*1000}function _X(K){return K&&parseFloat(K.replace(",","."))||0}function hJ(K){if(K==="Z")return 0;var G=K.match(lJ);if(!G)return 0;var X=G[1]==="+"?-1:1,B=parseInt(G[2]),U=G[3]&&parseInt(G[3])||0;if(!cJ(B,U))return NaN;return X*(B*bG+U*VG)}function kJ(K,G,X){var B=new Date(0);B.setUTCFullYear(K,0,4);var U=B.getUTCDay()||7,Z=(G-1)*7+X+1-U;return B.setUTCDate(B.getUTCDate()+Z),B}function NK(K){return K%400===0||K%4===0&&K%100!==0}function yJ(K,G,X){return G>=0&&G<=11&&X>=1&&X<=(pJ[G]||(NK(K)?29:28))}function gJ(K,G){return G>=1&&G<=(NK(K)?366:365)}function fJ(K,G,X){return G>=1&&G<=53&&X>=0&&X<=6}function mJ(K,G,X){if(K===24)return G===0&&X===0;return X>=0&&X<60&&G>=0&&G<60&&K>=0&&K<25}function cJ(K,G){return G>=0&&G<=59}var jX={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},uJ=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,_J=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,lJ=/^([+-])(\d{2})(?::?(\d{2}))?$/,pJ=[31,null,31,30,31,30,31,31,30,31,30,31];function dJ(K,G){var X=K.match(/(\d{4})-(\d{2})-(\d{2})[T ](\d{2}):(\d{2}):(\d{2})(?:\.(\d{0,7}))?(?:Z|(.)(\d{2}):?(\d{2})?)?/);if(!X)return j(NaN,G===null||G===void 0?void 0:G.in);return j(Date.UTC(+X[1],+X[2]-1,+X[3],+X[4]-(+X[9]||0)*(X[8]=="-"?-1:1),+X[5]-(+X[10]||0)*(X[8]=="-"?-1:1),+X[6],+((X[7]||"0")+"00").substring(0,3)),G===null||G===void 0?void 0:G.in)}function CG(K,G,X){var B=qX(K,X)-G;if(B<=0)B+=7;return VX(K,B,X)}function rJ(K,G){return CG(K,5,G)}function iJ(K,G){return CG(K,1,G)}function sJ(K,G){return CG(K,6,G)}function nJ(K,G){return CG(K,0,G)}function aJ(K,G){return CG(K,4,G)}function oJ(K,G){return CG(K,2,G)}function eJ(K,G){return CG(K,3,G)}function tJ(K){return Math.trunc(K*Z0)}function Gq(K){var G=K/q0;return Math.trunc(G)}function Xq(K,G){var X,B,U=(X=G===null||G===void 0?void 0:G.nearestTo)!==null&&X!==void 0?X:1;if(U<1||U>12)return L((G===null||G===void 0?void 0:G.in)||K,NaN);var Z=j(K,G===null||G===void 0?void 0:G.in),J=Z.getMinutes()/60,q=Z.getSeconds()/60/60,Q=Z.getMilliseconds()/1000/60/60,H=Z.getHours()+J+q+Q,V=(B=G===null||G===void 0?void 0:G.roundingMethod)!==null&&B!==void 0?B:"round",N=LG(V),x=N(H/U)*U;return Z.setHours(x,0,0,0),Z}function Kq(K,G){var X,B,U=(X=G===null||G===void 0?void 0:G.nearestTo)!==null&&X!==void 0?X:1;if(U<1||U>30)return L(K,NaN);var Z=j(K,G===null||G===void 0?void 0:G.in),J=Z.getSeconds()/60,q=Z.getMilliseconds()/1000/60,Q=Z.getMinutes()+J+q,H=(B=G===null||G===void 0?void 0:G.roundingMethod)!==null&&B!==void 0?B:"round",V=LG(H),N=V(Q/U)*U;return Z.setMinutes(N,0,0),Z}function Bq(K){var G=K/rG;return Math.trunc(G)}function Uq(K){return K*wX}function Zq(K){var G=K/CX;return Math.trunc(G)}function lX(K,G,X){var B=j(K,X===null||X===void 0?void 0:X.in),U=F(B),Z=h(B),J=L((X===null||X===void 0?void 0:X.in)||K,0);O(J,U,G,15),J.setHours(0,0,0,0);var q=p0(J);return e(B,G,Math.min(Z,q)),B}function Jq(K,G,X){var B=j(K,X===null||X===void 0?void 0:X.in);if(isNaN(+B))return L((X===null||X===void 0?void 0:X.in)||K,NaN);if(G.year!=null)O(B,G.year);if(G.month!=null)B=lX(B,G.month);if(G.date!=null)i(B,G.date);if(G.hours!=null)B.setHours(G.hours);if(G.minutes!=null)B.setMinutes(G.minutes);if(G.seconds!=null)B.setSeconds(G.seconds);if(G.milliseconds!=null)B.setMilliseconds(G.milliseconds);return B}function qq(K,G,X){var B=j(K,X===null||X===void 0?void 0:X.in);return i(B,G),B}function Qq(K,G,X){var B=j(K,X===null||X===void 0?void 0:X.in);return e(B,0),i(B,G),B}function Hq(K){var G={},X=a();for(var B in X)if(Object.prototype.hasOwnProperty.call(X,B))G[B]=X[B];for(var U in K)if(Object.prototype.hasOwnProperty.call(K,U))if(K[U]===void 0)delete G[U];else G[U]=K[U];fK(G)}function Vq(K,G,X){var B=j(K,X===null||X===void 0?void 0:X.in);return B.setHours(G),B}function jq(K,G,X){var B=j(K,X===null||X===void 0?void 0:X.in);return B.setMilliseconds(G),B}function Nq(K,G,X){var B=j(K,X===null||X===void 0?void 0:X.in);return B.setMinutes(G),B}function xq(K,G,X){var B=j(K,X===null||X===void 0?void 0:X.in),U=Math.trunc(v(B)/3)+1,Z=G-U;return lX(B,v(B)+Z*3)}function Eq(K,G,X){var B=j(K,X===null||X===void 0?void 0:X.in);return B.setSeconds(G),B}function Rq(K,G,X){var B,U,Z,J,q,Q,H=a(),V=(B=(U=(Z=(J=X===null||X===void 0?void 0:X.firstWeekContainsDate)!==null&&J!==void 0?J:X===null||X===void 0||(q=X.locale)===null||q===void 0||(q=q.options)===null||q===void 0?void 0:q.firstWeekContainsDate)!==null&&Z!==void 0?Z:H.firstWeekContainsDate)!==null&&U!==void 0?U:(Q=H.locale)===null||Q===void 0||(Q=Q.options)===null||Q===void 0?void 0:Q.firstWeekContainsDate)!==null&&B!==void 0?B:1,N=ZG(j(K,X===null||X===void 0?void 0:X.in),ZX(K,X),X),x=L((X===null||X===void 0?void 0:X.in)||K,0);O(x,G,0,V),x.setHours(0,0,0,0);var C=ZX(x,X);return i(C,h(C)+N),C}function Aq(K,G,X){var B=j(K,X===null||X===void 0?void 0:X.in);if(isNaN(+B))return L((X===null||X===void 0?void 0:X.in)||K,NaN);return O(B,G),B}function Lq(K,G){var X=j(K,G===null||G===void 0?void 0:G.in),B=F(X),U=Math.floor(B/10)*10;return O(X,U,0,1),X.setHours(0,0,0,0),X}function wq(K){return zG(Date.now(),K)}function Cq(K){var G=l(K===null||K===void 0?void 0:K.in),X=F(G),B=v(G),U=h(G),Z=L(K===null||K===void 0?void 0:K.in,0);return O(Z,X,B,U+1),Z.setHours(0,0,0,0),Z}function bq(K){var G=l(K===null||K===void 0?void 0:K.in),X=F(G),B=v(G),U=h(G),Z=l(K===null||K===void 0?void 0:K.in);return O(Z,X,B,U-1),Z.setHours(0,0,0,0),Z}function xK(K,G,X){return hG(K,-G,X)}function Iq(K,G,X){var B=G.years,U=B===void 0?0:B,Z=G.months,J=Z===void 0?0:Z,q=G.weeks,Q=q===void 0?0:q,H=G.days,V=H===void 0?0:H,N=G.hours,x=N===void 0?0:N,C=G.minutes,b=C===void 0?0:C,I=G.seconds,A=I===void 0?0:I,Y=xK(K,J+U*12,X),W=VX(Y,V+Q*7,X),D=b+x*60,y=A+D*60,n=y*1000;return L((X===null||X===void 0?void 0:X.in)||K,+W-n)}function Fq(K,G,X){return R0(K,-G,X)}function Wq(K,G,X){return A0(K,-G,X)}function Tq(K,G,X){return nG(K,-G,X)}function Mq(K,G,X){return IX(K,-G,X)}function Yq(K,G,X){return FX(K,-G,X)}function zq(K,G,X){return b0(K,-G,X)}function $q(K,G,X){return aG(K,-G,X)}function Pq(K,G,X){return I0(K,-G,X)}function vq(K){return Math.trunc(K*X0)}function Oq(K){return Math.trunc(K*pG)}function Sq(K){return Math.trunc(K*J0)}function Dq(K){return Math.trunc(K*q0)}window.dateFnsJalali=XG(XG({},window.dateFnsJalali),G0)})();

//# debugId=2BD66C514230A85E64756E2164756E21
