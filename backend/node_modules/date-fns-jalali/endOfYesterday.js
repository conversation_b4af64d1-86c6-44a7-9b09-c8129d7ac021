import { constructFrom } from "./constructFrom.js";
import { constructNow } from "./constructNow.js";

import { getMonth as coreGetMonth } from "./_core/getMonth.js";
import { getDate as coreGetDate } from "./_core/getDate.js";
import { getFullYear as coreGetFullYear } from "./_core/getFullYear.js";
import { setFullYear as coreSetFullYear } from "./_core/setFullYear.js";

/**
 * The {@link endOfYesterday} function options.
 */

/**
 * @name endOfYesterday
 * @category Day Helpers
 * @summary Return the end of yesterday.
 * @pure false
 *
 * @description
 * Return the end of yesterday.
 *
 * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).
 * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.
 *
 * @returns The end of yesterday
 *
 * @example
 * // If today is 6 October 2014:
 * const result = endOfYesterday()
 * //=> Sun Oct 5 2014 23:59:59.999
 */
export function endOfYesterday(options) {
  const now = constructNow(options?.in);
  const date = constructFrom(options?.in, 0);
  coreSetFullYear(
    date,
    coreGetFullYear(now),
    coreGetMonth(now),
    coreGetDate(now) - 1,
  );
  date.setHours(23, 59, 59, 999);
  return date;
}

// Fallback for modularized imports:
export default endOfYesterday;
