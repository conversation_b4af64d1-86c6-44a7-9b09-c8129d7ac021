(()=>{var q;function K(B){return K=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},K(B)}function W(B,G){var H=Object.keys(B);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(B);G&&(X=X.filter(function(Y){return Object.getOwnPropertyDescriptor(B,Y).enumerable})),H.push.apply(H,X)}return H}function O(B){for(var G=1;G<arguments.length;G++){var H=arguments[G]!=null?arguments[G]:{};G%2?W(Object(H),!0).forEach(function(X){x(B,X,H[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(H)):W(Object(H)).forEach(function(X){Object.defineProperty(B,X,Object.getOwnPropertyDescriptor(H,X))})}return B}function x(B,G,H){if(G=N(G),G in B)Object.defineProperty(B,G,{value:H,enumerable:!0,configurable:!0,writable:!0});else B[G]=H;return B}function N(B){var G=z(B,"string");return K(G)=="symbol"?G:String(G)}function z(B,G){if(K(B)!="object"||!B)return B;var H=B[Symbol.toPrimitive];if(H!==void 0){var X=H.call(B,G||"default");if(K(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(B)}var R=Object.defineProperty,D=function B(G,H){for(var X in H)R(G,X,{get:H[X],enumerable:!0,configurable:!0,set:function Y(Z){return H[X]=function(){return Z}}})},E={};D(E,{faIR:function B(){return vB},enUS:function B(){return HB}});var M={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},j=function B(G,H,X){var Y,Z=M[G];if(typeof Z==="string")Y=Z;else if(H===1)Y=Z.one;else Y=Z.other.replace("{{count}}",H.toString());if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return"in "+Y;else return Y+" ago";return Y};function $(B){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=G.width?String(G.width):B.defaultWidth,X=B.formats[H]||B.formats[B.defaultWidth];return X}}var w={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},V={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},L={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},_={date:$({formats:w,defaultWidth:"full"}),time:$({formats:V,defaultWidth:"full"}),dateTime:$({formats:L,defaultWidth:"full"})},f={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},v=function B(G,H,X,Y){return f[G]};function C(B){return function(G,H){var X=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",Y;if(X==="formatting"&&B.formattingValues){var Z=B.defaultFormattingWidth||B.defaultWidth,U=H!==null&&H!==void 0&&H.width?String(H.width):Z;Y=B.formattingValues[U]||B.formattingValues[Z]}else{var I=B.defaultWidth,Q=H!==null&&H!==void 0&&H.width?String(H.width):B.defaultWidth;Y=B.values[Q]||B.values[I]}var J=B.argumentCallback?B.argumentCallback(G):G;return Y[J]}}var k={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},P={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},F={narrow:["F","O","K","T","M","S","M","A","A","D","B","E"],abbreviated:["Far","Ord","Kho","Tir","Mor","Sha","Meh","Aba","Aza","Day","Bah","Esf"],wide:["Farvardin","Ordibehesht","Khordad","Tir","Mordad","Sharivar","Mehr","Aban","Azar","Day","Bahman","Esfand"]},h={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},y={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},b={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},m=function B(G,H){var X=Number(G),Y=X%100;if(Y>20||Y<10)switch(Y%10){case 1:return X+"st";case 2:return X+"nd";case 3:return X+"rd"}return X+"th"},c={ordinalNumber:m,era:C({values:k,defaultWidth:"wide"}),quarter:C({values:P,defaultWidth:"wide",argumentCallback:function B(G){return G-1}}),month:C({values:F,defaultWidth:"wide"}),day:C({values:h,defaultWidth:"wide"}),dayPeriod:C({values:y,defaultWidth:"wide",formattingValues:b,defaultFormattingWidth:"wide"})};function T(B){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=H.width,Y=X&&B.matchPatterns[X]||B.matchPatterns[B.defaultMatchWidth],Z=G.match(Y);if(!Z)return null;var U=Z[0],I=X&&B.parsePatterns[X]||B.parsePatterns[B.defaultParseWidth],Q=Array.isArray(I)?p(I,function(A){return A.test(U)}):g(I,function(A){return A.test(U)}),J;J=B.valueCallback?B.valueCallback(Q):Q,J=H.valueCallback?H.valueCallback(J):J;var kB=G.slice(U.length);return{value:J,rest:kB}}}function g(B,G){for(var H in B)if(Object.prototype.hasOwnProperty.call(B,H)&&G(B[H]))return H;return}function p(B,G){for(var H=0;H<B.length;H++)if(G(B[H]))return H;return}function S(B){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=G.match(B.matchPattern);if(!X)return null;var Y=X[0],Z=G.match(B.parsePattern);if(!Z)return null;var U=B.valueCallback?B.valueCallback(Z[0]):Z[0];U=H.valueCallback?H.valueCallback(U):U;var I=G.slice(Y.length);return{value:U,rest:I}}}var d=/^(\d+)(th|st|nd|rd)?/i,u=/\d+/i,l={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},i={any:[/^b/i,/^(a|c)/i]},s={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},n={any:[/1/i,/2/i,/3/i,/4/i]},o={narrow:/^[foktmsadbe]/i,abbreviated:/^(far|ord|kho|tir|mor|sha|meh|aba|aza|day|bah|esf)/i,wide:/^(farvardin|ordibehesht|khordad|tir|mordad|sharivar|mehr|aban|azar|day|bahman|esfand)/i},r={narrow:[/^f/i,/^o/i,/^k/i,/^t/i,/^m/i,/^s/i,/^m/i,/^a/i,/^a/i,/^d/i,/^b/i,/^e/i],any:[/^f/i,/^o/i,/^kh/i,/^t/i,/^mo/i,/^s/i,/^me/i,/^ab/i,/^az/i,/^d/i,/^b/i,/^e/i]},e={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},a={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},t={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},BB={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},GB={ordinalNumber:S({matchPattern:d,parsePattern:u,valueCallback:function B(G){return parseInt(G,10)}}),era:T({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),quarter:T({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any",valueCallback:function B(G){return G+1}}),month:T({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),day:T({matchPatterns:e,defaultMatchWidth:"wide",parsePatterns:a,defaultParseWidth:"any"}),dayPeriod:T({matchPatterns:t,defaultMatchWidth:"any",parsePatterns:BB,defaultParseWidth:"any"})},HB={code:"en-US",formatDistance:j,formatLong:_,formatRelative:v,localize:c,match:GB,options:{weekStartsOn:0,firstWeekContainsDate:1}},XB={lessThanXSeconds:{one:"\u06A9\u0645\u062A\u0631 \u0627\u0632 \u06CC\u06A9 \u062B\u0627\u0646\u06CC\u0647",other:"\u06A9\u0645\u062A\u0631 \u0627\u0632 {{count}} \u062B\u0627\u0646\u06CC\u0647"},xSeconds:{one:"1 \u062B\u0627\u0646\u06CC\u0647",other:"{{count}} \u062B\u0627\u0646\u06CC\u0647"},halfAMinute:"\u0646\u06CC\u0645 \u062F\u0642\u06CC\u0642\u0647",lessThanXMinutes:{one:"\u06A9\u0645\u062A\u0631 \u0627\u0632 \u06CC\u06A9 \u062F\u0642\u06CC\u0642\u0647",other:"\u06A9\u0645\u062A\u0631 \u0627\u0632 {{count}} \u062F\u0642\u06CC\u0642\u0647"},xMinutes:{one:"1 \u062F\u0642\u06CC\u0642\u0647",other:"{{count}} \u062F\u0642\u06CC\u0642\u0647"},aboutXHours:{one:"\u062D\u062F\u0648\u062F 1 \u0633\u0627\u0639\u062A",other:"\u062D\u062F\u0648\u062F {{count}} \u0633\u0627\u0639\u062A"},xHours:{one:"1 \u0633\u0627\u0639\u062A",other:"{{count}} \u0633\u0627\u0639\u062A"},xDays:{one:"1 \u0631\u0648\u0632",other:"{{count}} \u0631\u0648\u0632"},aboutXWeeks:{one:"\u062D\u062F\u0648\u062F 1 \u0647\u0641\u062A\u0647",other:"\u062D\u062F\u0648\u062F {{count}} \u0647\u0641\u062A\u0647"},xWeeks:{one:"1 \u0647\u0641\u062A\u0647",other:"{{count}} \u0647\u0641\u062A\u0647"},aboutXMonths:{one:"\u062D\u062F\u0648\u062F 1 \u0645\u0627\u0647",other:"\u062D\u062F\u0648\u062F {{count}} \u0645\u0627\u0647"},xMonths:{one:"1 \u0645\u0627\u0647",other:"{{count}} \u0645\u0627\u0647"},aboutXYears:{one:"\u062D\u062F\u0648\u062F 1 \u0633\u0627\u0644",other:"\u062D\u062F\u0648\u062F {{count}} \u0633\u0627\u0644"},xYears:{one:"1 \u0633\u0627\u0644",other:"{{count}} \u0633\u0627\u0644"},overXYears:{one:"\u0628\u06CC\u0634\u062A\u0631 \u0627\u0632 1 \u0633\u0627\u0644",other:"\u0628\u06CC\u0634\u062A\u0631 \u0627\u0632 {{count}} \u0633\u0627\u0644"},almostXYears:{one:"\u0646\u0632\u062F\u06CC\u06A9 1 \u0633\u0627\u0644",other:"\u0646\u0632\u062F\u06CC\u06A9 {{count}} \u0633\u0627\u0644"}},YB=function B(G,H,X){var Y,Z=XB[G];if(typeof Z==="string")Y=Z;else if(H===1)Y=Z.one;else Y=Z.other.replace("{{count}}",H.toString());if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return"\u062F\u0631 "+Y;else return Y+" \u0642\u0628\u0644";return Y},ZB={full:"EEEE do MMMM y",long:"do MMMM y",medium:"d MMM y",short:"yyyy/MM/dd"},CB={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},TB={full:"{{date}} '\u062F\u0631' {{time}}",long:"{{date}} '\u062F\u0631' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},UB={date:$({formats:ZB,defaultWidth:"full"}),time:$({formats:CB,defaultWidth:"full"}),dateTime:$({formats:TB,defaultWidth:"full"})},IB={lastWeek:"eeee '\u06AF\u0630\u0634\u062A\u0647 \u062F\u0631' p",yesterday:"'\u062F\u06CC\u0631\u0648\u0632 \u062F\u0631' p",today:"'\u0627\u0645\u0631\u0648\u0632 \u062F\u0631' p",tomorrow:"'\u0641\u0631\u062F\u0627 \u062F\u0631' p",nextWeek:"eeee '\u062F\u0631' p",other:"P"},JB=function B(G,H,X,Y){return IB[G]},$B={narrow:["\u0642","\u0628"],abbreviated:["\u0642.\u0647.","\u0628.\u0647."],wide:["\u0642\u0628\u0644 \u0627\u0632 \u0647\u062C\u0631\u062A","\u0628\u0639\u062F \u0627\u0632 \u0647\u062C\u0631\u062A"]},KB={narrow:["1","2","3","4"],abbreviated:["\u0633\u200C\u06451","\u0633\u200C\u06452","\u0633\u200C\u06453","\u0633\u200C\u06454"],wide:["\u0633\u0647\u200C\u0645\u0627\u0647\u0647 1","\u0633\u0647\u200C\u0645\u0627\u0647\u0647 2","\u0633\u0647\u200C\u0645\u0627\u0647\u0647 3","\u0633\u0647\u200C\u0645\u0627\u0647\u0647 4"]},OB={narrow:["\u0641\u0631","\u0627\u0631","\u062E\u0631","\u062A\u06CC","\u0645\u0631","\u0634\u0647","\u0645\u0647","\u0622\u0628","\u0622\u0630","\u062F\u06CC","\u0628\u0647","\u0627\u0633"],abbreviated:["\u0641\u0631\u0648","\u0627\u0631\u062F","\u062E\u0631\u062F","\u062A\u06CC\u0631","\u0645\u0631\u062F","\u0634\u0647\u0631","\u0645\u0647\u0631","\u0622\u0628\u0627","\u0622\u0630\u0631","\u062F\u06CC","\u0628\u0647\u0645","\u0627\u0633\u0641"],wide:["\u0641\u0631\u0648\u0631\u062F\u06CC\u0646","\u0627\u0631\u062F\u06CC\u0628\u0647\u0634\u062A","\u062E\u0631\u062F\u0627\u062F","\u062A\u06CC\u0631","\u0645\u0631\u062F\u0627\u062F","\u0634\u0647\u0631\u06CC\u0648\u0631","\u0645\u0647\u0631","\u0622\u0628\u0627\u0646","\u0622\u0630\u0631","\u062F\u06CC","\u0628\u0647\u0645\u0646","\u0627\u0633\u0641\u0646\u062F"]},QB={narrow:["\u06CC","\u062F","\u0633","\u0686","\u067E","\u062C","\u0634"],short:["1\u0634","2\u0634","3\u0634","4\u0634","5\u0634","\u062C","\u0634"],abbreviated:["\u06CC\u06A9\u200C\u0634\u0646\u0628\u0647","\u062F\u0648\u0634\u0646\u0628\u0647","\u0633\u0647\u200C\u0634\u0646\u0628\u0647","\u0686\u0647\u0627\u0631\u0634\u0646\u0628\u0647","\u067E\u0646\u062C\u200C\u0634\u0646\u0628\u0647","\u062C\u0645\u0639\u0647","\u0634\u0646\u0628\u0647"],wide:["\u06CC\u06A9\u200C\u0634\u0646\u0628\u0647","\u062F\u0648\u0634\u0646\u0628\u0647","\u0633\u0647\u200C\u0634\u0646\u0628\u0647","\u0686\u0647\u0627\u0631\u0634\u0646\u0628\u0647","\u067E\u0646\u062C\u200C\u0634\u0646\u0628\u0647","\u062C\u0645\u0639\u0647","\u0634\u0646\u0628\u0647"]},qB={narrow:{am:"\u0642",pm:"\u0628",midnight:"\u0646",noon:"\u0638",morning:"\u0635",afternoon:"\u0628.\u0638.",evening:"\u0639",night:"\u0634"},abbreviated:{am:"\u0642.\u0638.",pm:"\u0628.\u0638.",midnight:"\u0646\u06CC\u0645\u0647\u200C\u0634\u0628",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u062D",afternoon:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",evening:"\u0639\u0635\u0631",night:"\u0634\u0628"},wide:{am:"\u0642\u0628\u0644\u200C\u0627\u0632\u0638\u0647\u0631",pm:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",midnight:"\u0646\u06CC\u0645\u0647\u200C\u0634\u0628",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u062D",afternoon:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",evening:"\u0639\u0635\u0631",night:"\u0634\u0628"}},AB={narrow:{am:"\u0642",pm:"\u0628",midnight:"\u0646",noon:"\u0638",morning:"\u0635",afternoon:"\u0628.\u0638.",evening:"\u0639",night:"\u0634"},abbreviated:{am:"\u0642.\u0638.",pm:"\u0628.\u0638.",midnight:"\u0646\u06CC\u0645\u0647\u200C\u0634\u0628",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u062D",afternoon:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",evening:"\u0639\u0635\u0631",night:"\u0634\u0628"},wide:{am:"\u0642\u0628\u0644\u200C\u0627\u0632\u0638\u0647\u0631",pm:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",midnight:"\u0646\u06CC\u0645\u0647\u200C\u0634\u0628",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u062D",afternoon:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",evening:"\u0639\u0635\u0631",night:"\u0634\u0628"}},WB=function B(G,H){var X=Number(G);return X+"-\u0627\u0645"},EB={ordinalNumber:WB,era:C({values:$B,defaultWidth:"wide"}),quarter:C({values:KB,defaultWidth:"wide",argumentCallback:function B(G){return G-1}}),month:C({values:OB,defaultWidth:"wide"}),day:C({values:QB,defaultWidth:"wide"}),dayPeriod:C({values:qB,defaultWidth:"wide",formattingValues:AB,defaultFormattingWidth:"wide"})},SB=/^(\d+)(-?ام)?/i,xB=/\d+/i,NB={narrow:/^(ق|ب)/i,abbreviated:/^(ق\.?\s?ه\.?|ب\.?\s?ه\.?|ه\.?)/i,wide:/^(قبل از هجرت|هجری شمسی|بعد از هجرت)/i},zB={any:[/^قبل/i,/^بعد/i]},RB={narrow:/^[1234]/i,abbreviated:/^(ف|Q|س‌م)[1234]/i,wide:/^(فصل|quarter|سه‌ماهه) [1234](-ام|ام)?/i},DB={any:[/1/i,/2/i,/3/i,/4/i]},MB={narrow:/^(فر|ار|خر|تی|مر|شه|مه|آب|آذ|دی|به|اس)/i,abbreviated:/^(فرو|ارد|خرد|تیر|مرد|شهر|مهر|آبا|آذر|دی|بهم|اسف)/i,wide:/^(فروردین|اردیبهشت|خرداد|تیر|مرداد|شهریور|مهر|آبان|آذر|دی|بهمن|اسفند)/i},jB={narrow:[/^فر/i,/^ار/i,/^خر/i,/^تی/i,/^مر/i,/^شه/i,/^مه/i,/^آب/i,/^آذ/i,/^دی/i,/^به/i,/^اس/i],any:[/^فر/i,/^ار/i,/^خر/i,/^تی/i,/^مر/i,/^شه/i,/^مه/i,/^آب/i,/^آذ/i,/^دی/i,/^به/i,/^اس/i]},wB={narrow:/^[شیدسچپج]/i,short:/^(ش|ج|1ش|2ش|3ش|4ش|5ش)/i,abbreviated:/^(یکشنبه|دوشنبه|سه‌شنبه|چهارشنبه|پنج‌شنبه|جمعه|شنبه)/i,wide:/^(یکشنبه|دوشنبه|سه‌شنبه|چهارشنبه|پنج‌شنبه|جمعه|شنبه)/i},VB={narrow:[/^ی/i,/^دو/i,/^س/i,/^چ/i,/^پ/i,/^ج/i,/^ش/i],any:[/^(ی|1ش|یکشنبه)/i,/^(د|2ش|دوشنبه)/i,/^(س|3ش|سه‌شنبه)/i,/^(چ|4ش|چهارشنبه)/i,/^(پ|5ش|پنجشنبه)/i,/^(ج|جمعه)/i,/^(ش|شنبه)/i]},LB={narrow:/^(ب|ق|ن|ظ|ص|ب.ظ.|ع|ش)/i,any:/^(ق.ظ.|ب.ظ.|قبل‌ازظهر|نیمه‌شب|ظهر|صبح|بعدازظهر|عصر|شب)/i},_B={any:{am:/^(ق|ق.ظ.|قبل‌ازظهر)/i,pm:/^(ب|ب.ظ.|بعدازظهر)/i,midnight:/^(‌نیمه‌شب|ن)/i,noon:/^(ظ|ظهر)/i,morning:/^(ص|صبح)/i,afternoon:/^(ب|ب.ظ.|بعدازظهر)/i,evening:/^(ع|عصر)/i,night:/^(ش|شب)/i}},fB={ordinalNumber:S({matchPattern:SB,parsePattern:xB,valueCallback:function B(G){return parseInt(G,10)}}),era:T({matchPatterns:NB,defaultMatchWidth:"wide",parsePatterns:zB,defaultParseWidth:"any"}),quarter:T({matchPatterns:RB,defaultMatchWidth:"wide",parsePatterns:DB,defaultParseWidth:"any",valueCallback:function B(G){return G+1}}),month:T({matchPatterns:MB,defaultMatchWidth:"wide",parsePatterns:jB,defaultParseWidth:"any"}),day:T({matchPatterns:wB,defaultMatchWidth:"wide",parsePatterns:VB,defaultParseWidth:"any"}),dayPeriod:T({matchPatterns:LB,defaultMatchWidth:"any",parsePatterns:_B,defaultParseWidth:"any"})},vB={code:"fa-IR",formatDistance:YB,formatLong:UB,formatRelative:JB,localize:EB,match:fB,options:{weekStartsOn:6,firstWeekContainsDate:1}};window.dateFnsJalali=O(O({},window.dateFnsJalali),{},{locale:O(O({},(q=window.dateFnsJalali)===null||q===void 0?void 0:q.locale),E)})})();

//# debugId=BC162DC85BE4D9A364756E2164756E21
