"use strict";
exports.setWeekYear = setWeekYear;
var _index = require("./_lib/defaultOptions.cjs");
var _index2 = require("./constructFrom.cjs");
var _index3 = require("./differenceInCalendarDays.cjs");
var _index4 = require("./startOfWeekYear.cjs");
var _index5 = require("./toDate.cjs");

var _index6 = require("./_core/getDate.cjs");
var _index7 = require("./_core/setDate.cjs");
var _index8 = require("./_core/setFullYear.cjs");

/**
 * The {@link setWeekYear} function options.
 */

/**
 * @name setWeekYear
 * @category Week-Numbering Year Helpers
 * @summary Set the local week-numbering year to the given date.
 *
 * @description
 * Set the local week-numbering year to the given date,
 * saving the week number and the weekday number.
 * The exact calculation depends on the values of
 * `options.weekStartsOn` (which is the index of the first day of the week)
 * and `options.firstWeekContainsDate` (which is the day of January, which is always in
 * the first week of the week-numbering year)
 *
 * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system
 *
 * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).
 * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.
 *
 * @param date - The date to be changed
 * @param weekYear - The local week-numbering year of the new date
 * @param options - An object with options
 *
 * @returns The new date with the local week-numbering year set
 *
 * @example
 * // Set the local week-numbering year 2004 to 2 January 2010 with default options:
 * const result = setWeekYear(new Date(2010, 0, 2), 2004)
 * //=> Sat Jan 03 2004 00:00:00
 *
 * @example
 * // Set the local week-numbering year 2004 to 2 January 2010,
 * // if Monday is the first day of week
 * // and 4 January is always in the first week of the year:
 * const result = setWeekYear(new Date(2010, 0, 2), 2004, {
 *   weekStartsOn: 1,
 *   firstWeekContainsDate: 4
 * })
 * //=> Sat Jan 01 2005 00:00:00
 */
function setWeekYear(date, weekYear, options) {
  const defaultOptions = (0, _index.getDefaultOptions)();
  const firstWeekContainsDate =
    options?.firstWeekContainsDate ??
    options?.locale?.options?.firstWeekContainsDate ??
    defaultOptions.firstWeekContainsDate ??
    defaultOptions.locale?.options?.firstWeekContainsDate ??
    1;

  const diff = (0, _index3.differenceInCalendarDays)(
    (0, _index5.toDate)(date, options?.in),
    (0, _index4.startOfWeekYear)(date, options),
    options,
  );

  const firstWeek = (0, _index2.constructFrom)(options?.in || date, 0);
  (0, _index8.setFullYear)(firstWeek, weekYear, 0, firstWeekContainsDate);
  firstWeek.setHours(0, 0, 0, 0);

  const date_ = (0, _index4.startOfWeekYear)(firstWeek, options);
  (0, _index7.setDate)(date_, (0, _index6.getDate)(date_) + diff);
  return date_;
}
