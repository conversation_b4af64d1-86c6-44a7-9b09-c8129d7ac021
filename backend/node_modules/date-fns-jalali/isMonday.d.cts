import type { ContextOptions, DateArg } from "./types.js";
/**
 * The {@link isMonday} function options.
 */
export interface IsMondayOptions extends ContextOptions<Date> {}
/**
 * @name isMonday
 * @category Weekday Helpers
 * @summary Is the given date Monday?
 *
 * @description
 * Is the given date Monday?
 *
 * @param date - The date to check
 * @param options - An object with options
 *
 * @returns The date is Monday
 *
 * @example
 * // Is 22 September 2014 Monday?
 * const result = isMonday(new Date(2014, 8, 22))
 * //=> true
 */
export declare function isMonday(
  date: DateArg<Date> & {},
  options?: IsMondayOptions | undefined,
): boolean;
