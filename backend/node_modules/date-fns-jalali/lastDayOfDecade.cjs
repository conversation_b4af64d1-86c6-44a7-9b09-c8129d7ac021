"use strict";
exports.lastDayOfDecade = lastDayOfDecade;
var _index = require("./toDate.cjs");

var _index2 = require("./_core/getFullYear.cjs");
var _index3 = require("./_core/setFullYear.cjs");

/**
 * The {@link lastDayOfDecade} function options.
 */

/**
 * @name lastDayOfDecade
 * @category Decade Helpers
 * @summary Return the last day of a decade for the given date.
 *
 * @description
 * Return the last day of a decade for the given date.
 *
 * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows using extensions like [`UTCDate`](https://github.com/date-fns/utc).
 * @typeParam ResultDate - The result `Date` type; inferred from arguments or specified by context.
 *
 * @param date - The original date
 * @param options - The options
 *
 * @returns The last day of a decade
 *
 * @example
 * // The last day of a decade for 21 December 2012 21:12:00:
 * const result = lastDayOfDecade(new Date(2012, 11, 21, 21, 12, 00))
 * //=> Wed Dec 31 2019 00:00:00
 */
function lastDayOfDecade(date, options) {
  const _date = (0, _index.toDate)(date, options?.in);
  const year = (0, _index2.getFullYear)(_date);
  const decade = 9 + Math.floor(year / 10) * 10;
  (0, _index3.setFullYear)(_date, decade + 1, 0, 0);
  _date.setHours(0, 0, 0, 0);
  return (0, _index.toDate)(_date, options?.in);
}
