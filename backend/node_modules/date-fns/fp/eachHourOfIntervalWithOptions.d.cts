export declare const eachHourOfIntervalWithOptions: import("./types.js").FPFn2<
  import("../eachHourOfInterval.js").EachHourOfIntervalResult<
    import("../fp.js").Interval<
      import("../fp.js").DateArg<Date>,
      import("../fp.js").DateArg<Date>
    >,
    | import("../eachHourOfInterval.js").EachHourOfIntervalOptions<Date>
    | undefined
  >,
  | import("../eachHourOfInterval.js").EachHourOfIntervalOptions<Date>
  | undefined,
  import("../fp.js").Interval<
    import("../fp.js").DateArg<Date>,
    import("../fp.js").DateArg<Date>
  >
>;
