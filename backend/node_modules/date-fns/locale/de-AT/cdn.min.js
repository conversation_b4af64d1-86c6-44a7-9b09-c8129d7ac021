(()=>{var $;function I(B){return I=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},I(B)}function E(B,G){var H=Object.keys(B);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(B);G&&(J=J.filter(function(X){return Object.getOwnPropertyDescriptor(B,X).enumerable})),H.push.apply(H,J)}return H}function T(B){for(var G=1;G<arguments.length;G++){var H=arguments[G]!=null?arguments[G]:{};G%2?E(Object(H),!0).forEach(function(J){z(B,J,H[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(H)):E(Object(H)).forEach(function(J){Object.defineProperty(B,J,Object.getOwnPropertyDescriptor(H,J))})}return B}function z(B,G,H){if(G=W(G),G in B)Object.defineProperty(B,G,{value:H,enumerable:!0,configurable:!0,writable:!0});else B[G]=H;return B}function W(B){var G=S(B,"string");return I(G)=="symbol"?G:String(G)}function S(B,G){if(I(B)!="object"||!B)return B;var H=B[Symbol.toPrimitive];if(H!==void 0){var J=H.call(B,G||"default");if(I(J)!="object")return J;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(B)}var D=Object.defineProperty,JB=function B(G,H){for(var J in H)D(G,J,{get:H[J],enumerable:!0,configurable:!0,set:function X(Y){return H[J]=function(){return Y}}})},N={lessThanXSeconds:{standalone:{one:"weniger als 1 Sekunde",other:"weniger als {{count}} Sekunden"},withPreposition:{one:"weniger als 1 Sekunde",other:"weniger als {{count}} Sekunden"}},xSeconds:{standalone:{one:"1 Sekunde",other:"{{count}} Sekunden"},withPreposition:{one:"1 Sekunde",other:"{{count}} Sekunden"}},halfAMinute:{standalone:"eine halbe Minute",withPreposition:"einer halben Minute"},lessThanXMinutes:{standalone:{one:"weniger als 1 Minute",other:"weniger als {{count}} Minuten"},withPreposition:{one:"weniger als 1 Minute",other:"weniger als {{count}} Minuten"}},xMinutes:{standalone:{one:"1 Minute",other:"{{count}} Minuten"},withPreposition:{one:"1 Minute",other:"{{count}} Minuten"}},aboutXHours:{standalone:{one:"etwa 1 Stunde",other:"etwa {{count}} Stunden"},withPreposition:{one:"etwa 1 Stunde",other:"etwa {{count}} Stunden"}},xHours:{standalone:{one:"1 Stunde",other:"{{count}} Stunden"},withPreposition:{one:"1 Stunde",other:"{{count}} Stunden"}},xDays:{standalone:{one:"1 Tag",other:"{{count}} Tage"},withPreposition:{one:"1 Tag",other:"{{count}} Tagen"}},aboutXWeeks:{standalone:{one:"etwa 1 Woche",other:"etwa {{count}} Wochen"},withPreposition:{one:"etwa 1 Woche",other:"etwa {{count}} Wochen"}},xWeeks:{standalone:{one:"1 Woche",other:"{{count}} Wochen"},withPreposition:{one:"1 Woche",other:"{{count}} Wochen"}},aboutXMonths:{standalone:{one:"etwa 1 Monat",other:"etwa {{count}} Monate"},withPreposition:{one:"etwa 1 Monat",other:"etwa {{count}} Monaten"}},xMonths:{standalone:{one:"1 Monat",other:"{{count}} Monate"},withPreposition:{one:"1 Monat",other:"{{count}} Monaten"}},aboutXYears:{standalone:{one:"etwa 1 Jahr",other:"etwa {{count}} Jahre"},withPreposition:{one:"etwa 1 Jahr",other:"etwa {{count}} Jahren"}},xYears:{standalone:{one:"1 Jahr",other:"{{count}} Jahre"},withPreposition:{one:"1 Jahr",other:"{{count}} Jahren"}},overXYears:{standalone:{one:"mehr als 1 Jahr",other:"mehr als {{count}} Jahre"},withPreposition:{one:"mehr als 1 Jahr",other:"mehr als {{count}} Jahren"}},almostXYears:{standalone:{one:"fast 1 Jahr",other:"fast {{count}} Jahre"},withPreposition:{one:"fast 1 Jahr",other:"fast {{count}} Jahren"}}},M=function B(G,H,J){var X,Y=J!==null&&J!==void 0&&J.addSuffix?N[G].withPreposition:N[G].standalone;if(typeof Y==="string")X=Y;else if(H===1)X=Y.one;else X=Y.other.replace("{{count}}",String(H));if(J!==null&&J!==void 0&&J.addSuffix)if(J.comparison&&J.comparison>0)return"in "+X;else return"vor "+X;return X};function K(B){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=G.width?String(G.width):B.defaultWidth,J=B.formats[H]||B.formats[B.defaultWidth];return J}}var R={full:"EEEE, do MMMM y",long:"do MMMM y",medium:"do MMM y",short:"dd.MM.y"},L={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},V={full:"{{date}} 'um' {{time}}",long:"{{date}} 'um' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},j={date:K({formats:R,defaultWidth:"full"}),time:K({formats:L,defaultWidth:"full"}),dateTime:K({formats:V,defaultWidth:"full"})},w={lastWeek:"'letzten' eeee 'um' p",yesterday:"'gestern um' p",today:"'heute um' p",tomorrow:"'morgen um' p",nextWeek:"eeee 'um' p",other:"P"},_=function B(G,H,J,X){return w[G]};function O(B){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=H.width,X=J&&B.matchPatterns[J]||B.matchPatterns[B.defaultMatchWidth],Y=G.match(X);if(!Y)return null;var Z=Y[0],C=J&&B.parsePatterns[J]||B.parsePatterns[B.defaultParseWidth],q=Array.isArray(C)?f(C,function(A){return A.test(Z)}):F(C,function(A){return A.test(Z)}),U;U=B.valueCallback?B.valueCallback(q):q,U=H.valueCallback?H.valueCallback(U):U;var HB=G.slice(Z.length);return{value:U,rest:HB}}}function F(B,G){for(var H in B)if(Object.prototype.hasOwnProperty.call(B,H)&&G(B[H]))return H;return}function f(B,G){for(var H=0;H<B.length;H++)if(G(B[H]))return H;return}function v(B){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=G.match(B.matchPattern);if(!J)return null;var X=J[0],Y=G.match(B.parsePattern);if(!Y)return null;var Z=B.valueCallback?B.valueCallback(Y[0]):Y[0];Z=H.valueCallback?H.valueCallback(Z):Z;var C=G.slice(X.length);return{value:Z,rest:C}}}var P=/^(\d+)(\.)?/i,k=/\d+/i,b={narrow:/^(v\.? ?Chr\.?|n\.? ?Chr\.?)/i,abbreviated:/^(v\.? ?Chr\.?|n\.? ?Chr\.?)/i,wide:/^(vor Christus|vor unserer Zeitrechnung|nach Christus|unserer Zeitrechnung)/i},h={any:[/^v/i,/^n/i]},m={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](\.)? Quartal/i},y={any:[/1/i,/2/i,/3/i,/4/i]},c={narrow:/^[jfmasond]/i,abbreviated:/^(j[aä]n|feb|mär[z]?|apr|mai|jun[i]?|jul[i]?|aug|sep|okt|nov|dez)\.?/i,wide:/^(januar|februar|märz|april|mai|juni|juli|august|september|oktober|november|dezember)/i},d={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^j[aä]/i,/^f/i,/^mär/i,/^ap/i,/^mai/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},g={narrow:/^[smdmf]/i,short:/^(so|mo|di|mi|do|fr|sa)/i,abbreviated:/^(son?|mon?|die?|mit?|don?|fre?|sam?)\.?/i,wide:/^(sonntag|montag|dienstag|mittwoch|donnerstag|freitag|samstag)/i},p={any:[/^so/i,/^mo/i,/^di/i,/^mi/i,/^do/i,/^f/i,/^sa/i]},u={narrow:/^(vm\.?|nm\.?|Mitternacht|Mittag|morgens|nachm\.?|abends|nachts)/i,abbreviated:/^(vorm\.?|nachm\.?|Mitternacht|Mittag|morgens|nachm\.?|abends|nachts)/i,wide:/^(vormittags|nachmittags|Mitternacht|Mittag|morgens|nachmittags|abends|nachts)/i},l={any:{am:/^v/i,pm:/^n/i,midnight:/^Mitte/i,noon:/^Mitta/i,morning:/morgens/i,afternoon:/nachmittags/i,evening:/abends/i,night:/nachts/i}},i={ordinalNumber:v({matchPattern:P,parsePattern:k,valueCallback:function B(G){return parseInt(G)}}),era:O({matchPatterns:b,defaultMatchWidth:"wide",parsePatterns:h,defaultParseWidth:"any"}),quarter:O({matchPatterns:m,defaultMatchWidth:"wide",parsePatterns:y,defaultParseWidth:"any",valueCallback:function B(G){return G+1}}),month:O({matchPatterns:c,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any"}),day:O({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any"})};function Q(B){return function(G,H){var J=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",X;if(J==="formatting"&&B.formattingValues){var Y=B.defaultFormattingWidth||B.defaultWidth,Z=H!==null&&H!==void 0&&H.width?String(H.width):Y;X=B.formattingValues[Z]||B.formattingValues[Y]}else{var C=B.defaultWidth,q=H!==null&&H!==void 0&&H.width?String(H.width):B.defaultWidth;X=B.values[q]||B.values[C]}var U=B.argumentCallback?B.argumentCallback(G):G;return X[U]}}var n={narrow:["v.Chr.","n.Chr."],abbreviated:["v.Chr.","n.Chr."],wide:["vor Christus","nach Christus"]},s={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. Quartal","2. Quartal","3. Quartal","4. Quartal"]},x={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["J\xE4n","Feb","M\xE4r","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez"],wide:["J\xE4nner","Februar","M\xE4rz","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember"]},o={narrow:x.narrow,abbreviated:["J\xE4n.","Feb.","M\xE4rz","Apr.","Mai","Juni","Juli","Aug.","Sep.","Okt.","Nov.","Dez."],wide:x.wide},r={narrow:["S","M","D","M","D","F","S"],short:["So","Mo","Di","Mi","Do","Fr","Sa"],abbreviated:["So.","Mo.","Di.","Mi.","Do.","Fr.","Sa."],wide:["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"]},a={narrow:{am:"vm.",pm:"nm.",midnight:"Mitternacht",noon:"Mittag",morning:"Morgen",afternoon:"Nachm.",evening:"Abend",night:"Nacht"},abbreviated:{am:"vorm.",pm:"nachm.",midnight:"Mitternacht",noon:"Mittag",morning:"Morgen",afternoon:"Nachmittag",evening:"Abend",night:"Nacht"},wide:{am:"vormittags",pm:"nachmittags",midnight:"Mitternacht",noon:"Mittag",morning:"Morgen",afternoon:"Nachmittag",evening:"Abend",night:"Nacht"}},e={narrow:{am:"vm.",pm:"nm.",midnight:"Mitternacht",noon:"Mittag",morning:"morgens",afternoon:"nachm.",evening:"abends",night:"nachts"},abbreviated:{am:"vorm.",pm:"nachm.",midnight:"Mitternacht",noon:"Mittag",morning:"morgens",afternoon:"nachmittags",evening:"abends",night:"nachts"},wide:{am:"vormittags",pm:"nachmittags",midnight:"Mitternacht",noon:"Mittag",morning:"morgens",afternoon:"nachmittags",evening:"abends",night:"nachts"}},t=function B(G){var H=Number(G);return H+"."},BB={ordinalNumber:t,era:Q({values:n,defaultWidth:"wide"}),quarter:Q({values:s,defaultWidth:"wide",argumentCallback:function B(G){return G-1}}),month:Q({values:x,formattingValues:o,defaultWidth:"wide"}),day:Q({values:r,defaultWidth:"wide"}),dayPeriod:Q({values:a,defaultWidth:"wide",formattingValues:e,defaultFormattingWidth:"wide"})},GB={code:"de-AT",formatDistance:M,formatLong:j,formatRelative:_,localize:BB,match:i,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=T(T({},window.dateFns),{},{locale:T(T({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{deAT:GB})})})();

//# debugId=87375CD3185F7C6564756E2164756E21
