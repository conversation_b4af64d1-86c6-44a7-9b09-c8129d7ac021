import { formatDistance } from "./bg/_lib/formatDistance.js";
import { formatLong } from "./bg/_lib/formatLong.js";
import { formatRelative } from "./bg/_lib/formatRelative.js";
import { localize } from "./bg/_lib/localize.js";
import { match } from "./bg/_lib/match.js";

/**
 * @category Locales
 * @summary Bulgarian locale.
 * @language Bulgarian
 * @iso-639-2 bul
 * <AUTHOR> [@arvigeus](https://github.com/arvigeus)
 * <AUTHOR> [@fintara](https://github.com/fintara)
 */
export const bg = {
  code: "bg",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default bg;
