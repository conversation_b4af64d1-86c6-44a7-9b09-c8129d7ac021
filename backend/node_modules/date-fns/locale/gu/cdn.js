(() => {
var _window$dateFns;function _typeof(o) {"@babel/helpers - typeof";return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) {return typeof o;} : function (o) {return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o;}, _typeof(o);}function ownKeys(e, r) {var t = Object.keys(e);if (Object.getOwnPropertySymbols) {var o = Object.getOwnPropertySymbols(e);r && (o = o.filter(function (r) {return Object.getOwnPropertyDescriptor(e, r).enumerable;})), t.push.apply(t, o);}return t;}function _objectSpread(e) {for (var r = 1; r < arguments.length; r++) {var t = null != arguments[r] ? arguments[r] : {};r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {_defineProperty(e, r, t[r]);}) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));});}return e;}function _defineProperty(obj, key, value) {key = _toPropertyKey(key);if (key in obj) {Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });} else {obj[key] = value;}return obj;}function _toPropertyKey(t) {var i = _toPrimitive(t, "string");return "symbol" == _typeof(i) ? i : String(i);}function _toPrimitive(t, r) {if ("object" != _typeof(t) || !t) return t;var e = t[Symbol.toPrimitive];if (void 0 !== e) {var i = e.call(t, r || "default");if ("object" != _typeof(i)) return i;throw new TypeError("@@toPrimitive must return a primitive value.");}return ("string" === r ? String : Number)(t);}var __defProp = Object.defineProperty;
var __export = function __export(target, all) {
  for (var name in all)
  __defProp(target, name, {
    get: all[name],
    enumerable: true,
    configurable: true,
    set: function set(newValue) {return all[name] = function () {return newValue;};}
  });
};

// lib/locale/gu/_lib/formatDistance.js
var formatDistanceLocale = {
  lessThanXSeconds: {
    one: "\u0AB9\u0AAE\u0AA3\u0ABE\u0A82",
    other: "\u200B\u0A86\u0AB6\u0AB0\u0AC7 {{count}} \u0AB8\u0AC7\u0A95\u0A82\u0AA1"
  },
  xSeconds: {
    one: "1 \u0AB8\u0AC7\u0A95\u0A82\u0AA1",
    other: "{{count}} \u0AB8\u0AC7\u0A95\u0A82\u0AA1"
  },
  halfAMinute: "\u0A85\u0AA1\u0AA7\u0AC0 \u0AAE\u0ABF\u0AA8\u0ABF\u0A9F",
  lessThanXMinutes: {
    one: "\u0A86 \u0AAE\u0ABF\u0AA8\u0ABF\u0A9F",
    other: "\u200B\u0A86\u0AB6\u0AB0\u0AC7 {{count}} \u0AAE\u0ABF\u0AA8\u0ABF\u0A9F"
  },
  xMinutes: {
    one: "1 \u0AAE\u0ABF\u0AA8\u0ABF\u0A9F",
    other: "{{count}} \u0AAE\u0ABF\u0AA8\u0ABF\u0A9F"
  },
  aboutXHours: {
    one: "\u200B\u0A86\u0AB6\u0AB0\u0AC7 1 \u0A95\u0AB2\u0ABE\u0A95",
    other: "\u200B\u0A86\u0AB6\u0AB0\u0AC7 {{count}} \u0A95\u0AB2\u0ABE\u0A95"
  },
  xHours: {
    one: "1 \u0A95\u0AB2\u0ABE\u0A95",
    other: "{{count}} \u0A95\u0AB2\u0ABE\u0A95"
  },
  xDays: {
    one: "1 \u0AA6\u0ABF\u0AB5\u0AB8",
    other: "{{count}} \u0AA6\u0ABF\u0AB5\u0AB8"
  },
  aboutXWeeks: {
    one: "\u0A86\u0AB6\u0AB0\u0AC7 1 \u0A85\u0AA0\u0AB5\u0ABE\u0AA1\u0ABF\u0AAF\u0AC1\u0A82",
    other: "\u0A86\u0AB6\u0AB0\u0AC7 {{count}} \u0A85\u0AA0\u0AB5\u0ABE\u0AA1\u0ABF\u0AAF\u0ABE"
  },
  xWeeks: {
    one: "1 \u0A85\u0AA0\u0AB5\u0ABE\u0AA1\u0ABF\u0AAF\u0AC1\u0A82",
    other: "{{count}} \u0A85\u0AA0\u0AB5\u0ABE\u0AA1\u0ABF\u0AAF\u0ABE"
  },
  aboutXMonths: {
    one: "\u0A86\u0AB6\u0AB0\u0AC7 1 \u0AAE\u0AB9\u0ABF\u0AA8\u0ACB",
    other: "\u0A86\u0AB6\u0AB0\u0AC7 {{count}} \u0AAE\u0AB9\u0ABF\u0AA8\u0ABE"
  },
  xMonths: {
    one: "1 \u0AAE\u0AB9\u0ABF\u0AA8\u0ACB",
    other: "{{count}} \u0AAE\u0AB9\u0ABF\u0AA8\u0ABE"
  },
  aboutXYears: {
    one: "\u0A86\u0AB6\u0AB0\u0AC7 1 \u0AB5\u0AB0\u0ACD\u0AB7",
    other: "\u0A86\u0AB6\u0AB0\u0AC7 {{count}} \u0AB5\u0AB0\u0ACD\u0AB7"
  },
  xYears: {
    one: "1 \u0AB5\u0AB0\u0ACD\u0AB7",
    other: "{{count}} \u0AB5\u0AB0\u0ACD\u0AB7"
  },
  overXYears: {
    one: "1 \u0AB5\u0AB0\u0ACD\u0AB7\u0AA5\u0AC0 \u0AB5\u0AA7\u0AC1",
    other: "{{count}} \u0AB5\u0AB0\u0ACD\u0AB7\u0AA5\u0AC0 \u0AB5\u0AA7\u0AC1"
  },
  almostXYears: {
    one: "\u0AB2\u0A97\u0AAD\u0A97 1 \u0AB5\u0AB0\u0ACD\u0AB7",
    other: "\u0AB2\u0A97\u0AAD\u0A97 {{count}} \u0AB5\u0AB0\u0ACD\u0AB7"
  }
};
var formatDistance = function formatDistance(token, count, options) {
  var result;
  var tokenValue = formatDistanceLocale[token];
  if (typeof tokenValue === "string") {
    result = tokenValue;
  } else if (count === 1) {
    result = tokenValue.one;
  } else {
    result = tokenValue.other.replace("{{count}}", String(count));
  }
  if (options !== null && options !== void 0 && options.addSuffix) {
    if (options.comparison && options.comparison > 0) {
      return result + "\u0AAE\u0ABE\u0A82";
    } else {
      return result + " \u0AAA\u0AB9\u0AC7\u0AB2\u0ABE\u0A82";
    }
  }
  return result;
};

// lib/locale/_lib/buildFormatLongFn.js
function buildFormatLongFn(args) {
  return function () {var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    var width = options.width ? String(options.width) : args.defaultWidth;
    var format = args.formats[width] || args.formats[args.defaultWidth];
    return format;
  };
}

// lib/locale/gu/_lib/formatLong.js
var dateFormats = {
  full: "EEEE, d MMMM, y",
  long: "d MMMM, y",
  medium: "d MMM, y",
  short: "d/M/yy"
};
var timeFormats = {
  full: "hh:mm:ss a zzzz",
  long: "hh:mm:ss a z",
  medium: "hh:mm:ss a",
  short: "hh:mm a"
};
var dateTimeFormats = {
  full: "{{date}} {{time}}",
  long: "{{date}} {{time}}",
  medium: "{{date}} {{time}}",
  short: "{{date}} {{time}}"
};
var formatLong = {
  date: buildFormatLongFn({
    formats: dateFormats,
    defaultWidth: "full"
  }),
  time: buildFormatLongFn({
    formats: timeFormats,
    defaultWidth: "full"
  }),
  dateTime: buildFormatLongFn({
    formats: dateTimeFormats,
    defaultWidth: "full"
  })
};

// lib/locale/gu/_lib/formatRelative.js
var formatRelativeLocale = {
  lastWeek: "'\u0AAA\u0ABE\u0A9B\u0AB2\u0ABE' eeee p",
  yesterday: "'\u0A97\u0A88\u0A95\u0ABE\u0AB2\u0AC7' p",
  today: "'\u0A86\u0A9C\u0AC7' p",
  tomorrow: "'\u0A86\u0AB5\u0AA4\u0AC0\u0A95\u0ABE\u0AB2\u0AC7' p",
  nextWeek: "eeee p",
  other: "P"
};
var formatRelative = function formatRelative(token, _date, _baseDate, _options) {return formatRelativeLocale[token];};

// lib/locale/_lib/buildLocalizeFn.js
function buildLocalizeFn(args) {
  return function (value, options) {
    var context = options !== null && options !== void 0 && options.context ? String(options.context) : "standalone";
    var valuesArray;
    if (context === "formatting" && args.formattingValues) {
      var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;
      var width = options !== null && options !== void 0 && options.width ? String(options.width) : defaultWidth;
      valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];
    } else {
      var _defaultWidth = args.defaultWidth;
      var _width = options !== null && options !== void 0 && options.width ? String(options.width) : args.defaultWidth;
      valuesArray = args.values[_width] || args.values[_defaultWidth];
    }
    var index = args.argumentCallback ? args.argumentCallback(value) : value;
    return valuesArray[index];
  };
}

// lib/locale/gu/_lib/localize.js
var eraValues = {
  narrow: ["\u0A88\u0AB8\u0AAA\u0AC2", "\u0A88\u0AB8"],
  abbreviated: ["\u0A88.\u0AB8.\u0AAA\u0AC2\u0AB0\u0ACD\u0AB5\u0AC7", "\u0A88.\u0AB8."],
  wide: ["\u0A88\u0AB8\u0AB5\u0AC0\u0AB8\u0AA8 \u0AAA\u0AC2\u0AB0\u0ACD\u0AB5\u0AC7", "\u0A88\u0AB8\u0AB5\u0AC0\u0AB8\u0AA8"]
};
var quarterValues = {
  narrow: ["1", "2", "3", "4"],
  abbreviated: ["Q1", "Q2", "Q3", "Q4"],
  wide: ["1\u0AB2\u0ACB \u0AA4\u0ACD\u0AB0\u0ABF\u0AAE\u0ABE\u0AB8", "2\u0A9C\u0ACB \u0AA4\u0ACD\u0AB0\u0ABF\u0AAE\u0ABE\u0AB8", "3\u0A9C\u0ACB \u0AA4\u0ACD\u0AB0\u0ABF\u0AAE\u0ABE\u0AB8", "4\u0AA5\u0ACB \u0AA4\u0ACD\u0AB0\u0ABF\u0AAE\u0ABE\u0AB8"]
};
var monthValues = {
  narrow: ["\u0A9C\u0ABE", "\u0AAB\u0AC7", "\u0AAE\u0ABE", "\u0A8F", "\u0AAE\u0AC7", "\u0A9C\u0AC2", "\u0A9C\u0AC1", "\u0A93", "\u0AB8", "\u0A93", "\u0AA8", "\u0AA1\u0ABF"],
  abbreviated: [
  "\u0A9C\u0ABE\u0AA8\u0ACD\u0AAF\u0AC1",
  "\u0AAB\u0AC7\u0AAC\u0ACD\u0AB0\u0AC1",
  "\u0AAE\u0ABE\u0AB0\u0ACD\u0A9A",
  "\u0A8F\u0AAA\u0ACD\u0AB0\u0ABF\u0AB2",
  "\u0AAE\u0AC7",
  "\u0A9C\u0AC2\u0AA8",
  "\u0A9C\u0AC1\u0AB2\u0ABE\u0A88",
  "\u0A91\u0A97\u0AB8\u0ACD\u0A9F",
  "\u0AB8\u0AAA\u0ACD\u0A9F\u0AC7",
  "\u0A93\u0A95\u0ACD\u0A9F\u0ACB",
  "\u0AA8\u0AB5\u0AC7",
  "\u0AA1\u0ABF\u0AB8\u0AC7"],

  wide: [
  "\u0A9C\u0ABE\u0AA8\u0ACD\u0AAF\u0AC1\u0A86\u0AB0\u0AC0",
  "\u0AAB\u0AC7\u0AAC\u0ACD\u0AB0\u0AC1\u0A86\u0AB0\u0AC0",
  "\u0AAE\u0ABE\u0AB0\u0ACD\u0A9A",
  "\u0A8F\u0AAA\u0ACD\u0AB0\u0ABF\u0AB2",
  "\u0AAE\u0AC7",
  "\u0A9C\u0AC2\u0AA8",
  "\u0A9C\u0AC1\u0AB2\u0ABE\u0A87",
  "\u0A93\u0A97\u0AB8\u0ACD\u0A9F",
  "\u0AB8\u0AAA\u0ACD\u0A9F\u0AC7\u0AAE\u0ACD\u0AAC\u0AB0",
  "\u0A93\u0A95\u0ACD\u0A9F\u0ACB\u0AAC\u0AB0",
  "\u0AA8\u0AB5\u0AC7\u0AAE\u0ACD\u0AAC\u0AB0",
  "\u0AA1\u0ABF\u0AB8\u0AC7\u0AAE\u0ACD\u0AAC\u0AB0"]

};
var dayValues = {
  narrow: ["\u0AB0", "\u0AB8\u0ACB", "\u0AAE\u0A82", "\u0AAC\u0AC1", "\u0A97\u0AC1", "\u0AB6\u0AC1", "\u0AB6"],
  short: ["\u0AB0", "\u0AB8\u0ACB", "\u0AAE\u0A82", "\u0AAC\u0AC1", "\u0A97\u0AC1", "\u0AB6\u0AC1", "\u0AB6"],
  abbreviated: ["\u0AB0\u0AB5\u0ABF", "\u0AB8\u0ACB\u0AAE", "\u0AAE\u0A82\u0A97\u0AB3", "\u0AAC\u0AC1\u0AA7", "\u0A97\u0AC1\u0AB0\u0AC1", "\u0AB6\u0AC1\u0A95\u0ACD\u0AB0", "\u0AB6\u0AA8\u0ABF"],
  wide: [
  "\u0AB0\u0AB5\u0ABF\u0AB5\u0ABE\u0AB0",
  "\u0AB8\u0ACB\u0AAE\u0AB5\u0ABE\u0AB0",
  "\u0AAE\u0A82\u0A97\u0AB3\u0AB5\u0ABE\u0AB0",
  "\u0AAC\u0AC1\u0AA7\u0AB5\u0ABE\u0AB0",
  "\u0A97\u0AC1\u0AB0\u0AC1\u0AB5\u0ABE\u0AB0",
  "\u0AB6\u0AC1\u0A95\u0ACD\u0AB0\u0AB5\u0ABE\u0AB0",
  "\u0AB6\u0AA8\u0ABF\u0AB5\u0ABE\u0AB0"]

};
var dayPeriodValues = {
  narrow: {
    am: "AM",
    pm: "PM",
    midnight: "\u0AAE.\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0ABF",
    noon: "\u0AAC.",
    morning: "\u0AB8\u0AB5\u0ABE\u0AB0\u0AC7",
    afternoon: "\u0AAC\u0AAA\u0ACB\u0AB0\u0AC7",
    evening: "\u0AB8\u0ABE\u0A82\u0A9C\u0AC7",
    night: "\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0AC7"
  },
  abbreviated: {
    am: "AM",
    pm: "PM",
    midnight: "\u200B\u0AAE\u0AA7\u0ACD\u0AAF\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0ABF",
    noon: "\u0AAC\u0AAA\u0ACB\u0AB0\u0AC7",
    morning: "\u0AB8\u0AB5\u0ABE\u0AB0\u0AC7",
    afternoon: "\u0AAC\u0AAA\u0ACB\u0AB0\u0AC7",
    evening: "\u0AB8\u0ABE\u0A82\u0A9C\u0AC7",
    night: "\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0AC7"
  },
  wide: {
    am: "AM",
    pm: "PM",
    midnight: "\u200B\u0AAE\u0AA7\u0ACD\u0AAF\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0ABF",
    noon: "\u0AAC\u0AAA\u0ACB\u0AB0\u0AC7",
    morning: "\u0AB8\u0AB5\u0ABE\u0AB0\u0AC7",
    afternoon: "\u0AAC\u0AAA\u0ACB\u0AB0\u0AC7",
    evening: "\u0AB8\u0ABE\u0A82\u0A9C\u0AC7",
    night: "\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0AC7"
  }
};
var formattingDayPeriodValues = {
  narrow: {
    am: "AM",
    pm: "PM",
    midnight: "\u0AAE.\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0ABF",
    noon: "\u0AAC\u0AAA\u0ACB\u0AB0\u0AC7",
    morning: "\u0AB8\u0AB5\u0ABE\u0AB0\u0AC7",
    afternoon: "\u0AAC\u0AAA\u0ACB\u0AB0\u0AC7",
    evening: "\u0AB8\u0ABE\u0A82\u0A9C\u0AC7",
    night: "\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0AC7"
  },
  abbreviated: {
    am: "AM",
    pm: "PM",
    midnight: "\u0AAE\u0AA7\u0ACD\u0AAF\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0ABF",
    noon: "\u0AAC\u0AAA\u0ACB\u0AB0\u0AC7",
    morning: "\u0AB8\u0AB5\u0ABE\u0AB0\u0AC7",
    afternoon: "\u0AAC\u0AAA\u0ACB\u0AB0\u0AC7",
    evening: "\u0AB8\u0ABE\u0A82\u0A9C\u0AC7",
    night: "\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0AC7"
  },
  wide: {
    am: "AM",
    pm: "PM",
    midnight: "\u200B\u0AAE\u0AA7\u0ACD\u0AAF\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0ABF",
    noon: "\u0AAC\u0AAA\u0ACB\u0AB0\u0AC7",
    morning: "\u0AB8\u0AB5\u0ABE\u0AB0\u0AC7",
    afternoon: "\u0AAC\u0AAA\u0ACB\u0AB0\u0AC7",
    evening: "\u0AB8\u0ABE\u0A82\u0A9C\u0AC7",
    night: "\u0AB0\u0ABE\u0AA4\u0ACD\u0AB0\u0AC7"
  }
};
var ordinalNumber = function ordinalNumber(dirtyNumber, _options) {
  return String(dirtyNumber);
};
var localize = {
  ordinalNumber: ordinalNumber,
  era: buildLocalizeFn({
    values: eraValues,
    defaultWidth: "wide"
  }),
  quarter: buildLocalizeFn({
    values: quarterValues,
    defaultWidth: "wide",
    argumentCallback: function argumentCallback(quarter) {return quarter - 1;}
  }),
  month: buildLocalizeFn({
    values: monthValues,
    defaultWidth: "wide"
  }),
  day: buildLocalizeFn({
    values: dayValues,
    defaultWidth: "wide"
  }),
  dayPeriod: buildLocalizeFn({
    values: dayPeriodValues,
    defaultWidth: "wide",
    formattingValues: formattingDayPeriodValues,
    defaultFormattingWidth: "wide"
  })
};

// lib/locale/_lib/buildMatchFn.js
function buildMatchFn(args) {
  return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    var width = options.width;
    var matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];
    var matchResult = string.match(matchPattern);
    if (!matchResult) {
      return null;
    }
    var matchedString = matchResult[0];
    var parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];
    var key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, function (pattern) {return pattern.test(matchedString);}) : findKey(parsePatterns, function (pattern) {return pattern.test(matchedString);});
    var value;
    value = args.valueCallback ? args.valueCallback(key) : key;
    value = options.valueCallback ? options.valueCallback(value) : value;
    var rest = string.slice(matchedString.length);
    return { value: value, rest: rest };
  };
}
function findKey(object, predicate) {
  for (var key in object) {
    if (Object.prototype.hasOwnProperty.call(object, key) && predicate(object[key])) {
      return key;
    }
  }
  return;
}
function findIndex(array, predicate) {
  for (var key = 0; key < array.length; key++) {
    if (predicate(array[key])) {
      return key;
    }
  }
  return;
}

// lib/locale/_lib/buildMatchPatternFn.js
function buildMatchPatternFn(args) {
  return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    var matchResult = string.match(args.matchPattern);
    if (!matchResult)
    return null;
    var matchedString = matchResult[0];
    var parseResult = string.match(args.parsePattern);
    if (!parseResult)
    return null;
    var value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];
    value = options.valueCallback ? options.valueCallback(value) : value;
    var rest = string.slice(matchedString.length);
    return { value: value, rest: rest };
  };
}

// lib/locale/gu/_lib/match.js
var matchOrdinalNumberPattern = /^(\d+)(લ|જ|થ|ઠ્ઠ|મ)?/i;
var parseOrdinalNumberPattern = /\d+/i;
var matchEraPatterns = {
  narrow: /^(ઈસપૂ|ઈસ)/i,
  abbreviated: /^(ઈ\.સ\.પૂર્વે|ઈ\.સ\.)/i,
  wide: /^(ઈસવીસન\sપૂર્વે|ઈસવીસન)/i
};
var parseEraPatterns = {
  any: [/^ઈસપૂ/i, /^ઈસ/i]
};
var matchQuarterPatterns = {
  narrow: /^[1234]/i,
  abbreviated: /^q[1234]/i,
  wide: /^[1234](લો|જો|થો)? ત્રિમાસ/i
};
var parseQuarterPatterns = {
  any: [/1/i, /2/i, /3/i, /4/i]
};
var matchMonthPatterns = {
  narrow: /^[જાફેમાએમેજૂજુઓસઓનડિ]/i,
  abbreviated: /^(જાન્યુ|ફેબ્રુ|માર્ચ|એપ્રિલ|મે|જૂન|જુલાઈ|ઑગસ્ટ|સપ્ટે|ઓક્ટો|નવે|ડિસે)/i,
  wide: /^(જાન્યુઆરી|ફેબ્રુઆરી|માર્ચ|એપ્રિલ|મે|જૂન|જુલાઇ|ઓગસ્ટ|સપ્ટેમ્બર|ઓક્ટોબર|નવેમ્બર|ડિસેમ્બર)/i
};
var parseMonthPatterns = {
  narrow: [
  /^જા/i,
  /^ફે/i,
  /^મા/i,
  /^એ/i,
  /^મે/i,
  /^જૂ/i,
  /^જુ/i,
  /^ઑગ/i,
  /^સ/i,
  /^ઓક્ટો/i,
  /^ન/i,
  /^ડિ/i],

  any: [
  /^જા/i,
  /^ફે/i,
  /^મા/i,
  /^એ/i,
  /^મે/i,
  /^જૂ/i,
  /^જુ/i,
  /^ઑગ/i,
  /^સ/i,
  /^ઓક્ટો/i,
  /^ન/i,
  /^ડિ/i]

};
var matchDayPatterns = {
  narrow: /^(ર|સો|મં|બુ|ગુ|શુ|શ)/i,
  short: /^(ર|સો|મં|બુ|ગુ|શુ|શ)/i,
  abbreviated: /^(રવિ|સોમ|મંગળ|બુધ|ગુરુ|શુક્ર|શનિ)/i,
  wide: /^(રવિવાર|સોમવાર|મંગળવાર|બુધવાર|ગુરુવાર|શુક્રવાર|શનિવાર)/i
};
var parseDayPatterns = {
  narrow: [/^ર/i, /^સો/i, /^મં/i, /^બુ/i, /^ગુ/i, /^શુ/i, /^શ/i],
  any: [/^ર/i, /^સો/i, /^મં/i, /^બુ/i, /^ગુ/i, /^શુ/i, /^શ/i]
};
var matchDayPeriodPatterns = {
  narrow: /^(a|p|મ\.?|સ|બ|સાં|રા)/i,
  any: /^(a|p|મ\.?|સ|બ|સાં|રા)/i
};
var parseDayPeriodPatterns = {
  any: {
    am: /^a/i,
    pm: /^p/i,
    midnight: /^મ\.?/i,
    noon: /^બ/i,
    morning: /સ/i,
    afternoon: /બ/i,
    evening: /સાં/i,
    night: /રા/i
  }
};
var match = {
  ordinalNumber: buildMatchPatternFn({
    matchPattern: matchOrdinalNumberPattern,
    parsePattern: parseOrdinalNumberPattern,
    valueCallback: function valueCallback(value) {return parseInt(value, 10);}
  }),
  era: buildMatchFn({
    matchPatterns: matchEraPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseEraPatterns,
    defaultParseWidth: "any"
  }),
  quarter: buildMatchFn({
    matchPatterns: matchQuarterPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseQuarterPatterns,
    defaultParseWidth: "any",
    valueCallback: function valueCallback(index) {return index + 1;}
  }),
  month: buildMatchFn({
    matchPatterns: matchMonthPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseMonthPatterns,
    defaultParseWidth: "any"
  }),
  day: buildMatchFn({
    matchPatterns: matchDayPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseDayPatterns,
    defaultParseWidth: "any"
  }),
  dayPeriod: buildMatchFn({
    matchPatterns: matchDayPeriodPatterns,
    defaultMatchWidth: "any",
    parsePatterns: parseDayPeriodPatterns,
    defaultParseWidth: "any"
  })
};

// lib/locale/gu.js
var gu = {
  code: "gu",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1,
    firstWeekContainsDate: 4
  }
};

// lib/locale/gu/cdn.js
window.dateFns = _objectSpread(_objectSpread({},
window.dateFns), {}, {
  locale: _objectSpread(_objectSpread({}, (_window$dateFns =
  window.dateFns) === null || _window$dateFns === void 0 ? void 0 : _window$dateFns.locale), {}, {
    gu: gu }) });



//# debugId=2F4D0D626181F30264756E2164756E21

//# sourceMappingURL=cdn.js.map
})();