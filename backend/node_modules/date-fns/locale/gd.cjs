"use strict";
exports.gd = void 0;
var _index = require("./gd/_lib/formatDistance.cjs");
var _index2 = require("./gd/_lib/formatLong.cjs");
var _index3 = require("./gd/_lib/formatRelative.cjs");
var _index4 = require("./gd/_lib/localize.cjs");
var _index5 = require("./gd/_lib/match.cjs");

/**
 * @category Locales
 * @summary Scottish Gaelic.
 * @language Scottish Gaelic
 * @iso-639-2 gla
 * <AUTHOR> [@leedriscoll](https://github.com/leedriscoll)
 */
const gd = (exports.gd = {
  code: "gd",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
});
