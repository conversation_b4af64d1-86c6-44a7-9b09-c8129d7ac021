(()=>{var V;function T(B,J){var X=Object.keys(B);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(B);J&&(Z=Z.filter(function(U){return Object.getOwnPropertyDescriptor(B,U).enumerable})),X.push.apply(X,Z)}return X}function M(B){for(var J=1;J<arguments.length;J++){var X=arguments[J]!=null?arguments[J]:{};J%2?T(Object(X),!0).forEach(function(Z){D(B,Z,X[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(X)):T(Object(X)).forEach(function(Z){Object.defineProperty(B,Z,Object.getOwnPropertyDescriptor(X,Z))})}return B}function D(B,J,X){if(J=P(J),J in B)Object.defineProperty(B,J,{value:X,enumerable:!0,configurable:!0,writable:!0});else B[J]=X;return B}function P(B){var J=w(B,"string");return K(J)=="symbol"?J:String(J)}function w(B,J){if(K(B)!="object"||!B)return B;var X=B[Symbol.toPrimitive];if(X!==void 0){var Z=X.call(B,J||"default");if(K(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(B)}function j(B,J){return f(B)||b(B,J)||v(B,J)||F()}function F(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function v(B,J){if(!B)return;if(typeof B==="string")return x(B,J);var X=Object.prototype.toString.call(B).slice(8,-1);if(X==="Object"&&B.constructor)X=B.constructor.name;if(X==="Map"||X==="Set")return Array.from(B);if(X==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(X))return x(B,J)}function x(B,J){if(J==null||J>B.length)J=B.length;for(var X=0,Z=new Array(J);X<J;X++)Z[X]=B[X];return Z}function b(B,J){var X=B==null?null:typeof Symbol!="undefined"&&B[Symbol.iterator]||B["@@iterator"];if(X!=null){var Z,U,C,H,Q=[],q=!0,Y=!1;try{if(C=(X=X.call(B)).next,J===0){if(Object(X)!==X)return;q=!1}else for(;!(q=(Z=C.call(X)).done)&&(Q.push(Z.value),Q.length!==J);q=!0);}catch(G){Y=!0,U=G}finally{try{if(!q&&X.return!=null&&(H=X.return(),Object(H)!==H))return}finally{if(Y)throw U}}return Q}}function f(B){if(Array.isArray(B))return B}function K(B){return K=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},K(B)}var h=Object.defineProperty,vB=function B(J,X){for(var Z in X)h(J,Z,{get:X[Z],enumerable:!0,configurable:!0,set:function U(C){return X[Z]=function(){return C}}})};function k(B,J){if(J===1)return B.one;var X=J%100;if(X<=20&&X>10)return B.other;var Z=X%10;if(Z>=2&&Z<=4)return B.twoFour;return B.other}function z(B,J,X){var Z=k(B,J),U=typeof Z==="string"?Z:Z[X];return U.replace("{{count}}",String(J))}var _={lessThanXSeconds:{one:{regular:"mniej ni\u017C sekunda",past:"mniej ni\u017C sekund\u0119",future:"mniej ni\u017C sekund\u0119"},twoFour:"mniej ni\u017C {{count}} sekundy",other:"mniej ni\u017C {{count}} sekund"},xSeconds:{one:{regular:"sekunda",past:"sekund\u0119",future:"sekund\u0119"},twoFour:"{{count}} sekundy",other:"{{count}} sekund"},halfAMinute:{one:"p\xF3\u0142 minuty",twoFour:"p\xF3\u0142 minuty",other:"p\xF3\u0142 minuty"},lessThanXMinutes:{one:{regular:"mniej ni\u017C minuta",past:"mniej ni\u017C minut\u0119",future:"mniej ni\u017C minut\u0119"},twoFour:"mniej ni\u017C {{count}} minuty",other:"mniej ni\u017C {{count}} minut"},xMinutes:{one:{regular:"minuta",past:"minut\u0119",future:"minut\u0119"},twoFour:"{{count}} minuty",other:"{{count}} minut"},aboutXHours:{one:{regular:"oko\u0142o godziny",past:"oko\u0142o godziny",future:"oko\u0142o godzin\u0119"},twoFour:"oko\u0142o {{count}} godziny",other:"oko\u0142o {{count}} godzin"},xHours:{one:{regular:"godzina",past:"godzin\u0119",future:"godzin\u0119"},twoFour:"{{count}} godziny",other:"{{count}} godzin"},xDays:{one:{regular:"dzie\u0144",past:"dzie\u0144",future:"1 dzie\u0144"},twoFour:"{{count}} dni",other:"{{count}} dni"},aboutXWeeks:{one:"oko\u0142o tygodnia",twoFour:"oko\u0142o {{count}} tygodni",other:"oko\u0142o {{count}} tygodni"},xWeeks:{one:"tydzie\u0144",twoFour:"{{count}} tygodnie",other:"{{count}} tygodni"},aboutXMonths:{one:"oko\u0142o miesi\u0105c",twoFour:"oko\u0142o {{count}} miesi\u0105ce",other:"oko\u0142o {{count}} miesi\u0119cy"},xMonths:{one:"miesi\u0105c",twoFour:"{{count}} miesi\u0105ce",other:"{{count}} miesi\u0119cy"},aboutXYears:{one:"oko\u0142o rok",twoFour:"oko\u0142o {{count}} lata",other:"oko\u0142o {{count}} lat"},xYears:{one:"rok",twoFour:"{{count}} lata",other:"{{count}} lat"},overXYears:{one:"ponad rok",twoFour:"ponad {{count}} lata",other:"ponad {{count}} lat"},almostXYears:{one:"prawie rok",twoFour:"prawie {{count}} lata",other:"prawie {{count}} lat"}},y=function B(J,X,Z){var U=_[J];if(!(Z!==null&&Z!==void 0&&Z.addSuffix))return z(U,X,"regular");if(Z.comparison&&Z.comparison>0)return"za "+z(U,X,"future");else return z(U,X,"past")+" temu"};function R(B){return function(){var J=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},X=J.width?String(J.width):B.defaultWidth,Z=B.formats[X]||B.formats[B.defaultWidth];return Z}}var g={full:"EEEE, do MMMM y",long:"do MMMM y",medium:"do MMM y",short:"dd.MM.y"},m={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},c={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},u={date:R({formats:g,defaultWidth:"full"}),time:R({formats:m,defaultWidth:"full"}),dateTime:R({formats:c,defaultWidth:"full"})},bB=7,p=365.2425,d=Math.pow(10,8)*24*60*60*1000,fB=-d,hB=604800000,kB=86400000,_B=60000,yB=3600000,gB=1000,mB=525600,cB=43200,uB=1440,pB=60,dB=3,lB=12,iB=4,l=3600,sB=60,A=l*24,nB=A*7,i=A*p,s=i/12,rB=s*3,W=Symbol.for("constructDateFrom");function S(B,J){if(typeof B==="function")return B(J);if(B&&K(B)==="object"&&W in B)return B[W](J);if(B instanceof Date)return new B.constructor(J);return new Date(J)}function n(B){for(var J=arguments.length,X=new Array(J>1?J-1:0),Z=1;Z<J;Z++)X[Z-1]=arguments[Z];var U=S.bind(null,B||X.find(function(C){return K(C)==="object"}));return X.map(U)}function r(){return $}function oB(B){$=B}var $={};function o(B,J){return S(J||B,B)}function L(B,J){var X,Z,U,C,H,Q,q=r(),Y=(X=(Z=(U=(C=J===null||J===void 0?void 0:J.weekStartsOn)!==null&&C!==void 0?C:J===null||J===void 0||(H=J.locale)===null||H===void 0||(H=H.options)===null||H===void 0?void 0:H.weekStartsOn)!==null&&U!==void 0?U:q.weekStartsOn)!==null&&Z!==void 0?Z:(Q=q.locale)===null||Q===void 0||(Q=Q.options)===null||Q===void 0?void 0:Q.weekStartsOn)!==null&&X!==void 0?X:0,G=o(B,J===null||J===void 0?void 0:J.in),N=G.getDay(),FB=(N<Y?7:0)+N-Y;return G.setDate(G.getDate()-FB),G.setHours(0,0,0,0),G}function a(B,J,X){var Z=n(X===null||X===void 0?void 0:X.in,B,J),U=j(Z,2),C=U[0],H=U[1];return+L(C,X)===+L(H,X)}function O(B,J,X,Z){var U;if(a(J,X,Z))U=e;else if(B==="lastWeek")U=t;else if(B==="nextWeek")U=BB;else throw new Error("Cannot determine adjectives for token ".concat(B));var C=J.getDay(),H=JB[C],Q=U[H];return"'".concat(Q,"' eeee 'o' p")}var t={masculine:"ostatni",feminine:"ostatnia"},e={masculine:"ten",feminine:"ta"},BB={masculine:"nast\u0119pny",feminine:"nast\u0119pna"},JB={0:"feminine",1:"masculine",2:"masculine",3:"feminine",4:"masculine",5:"masculine",6:"feminine"},XB={lastWeek:O,yesterday:"'wczoraj o' p",today:"'dzisiaj o' p",tomorrow:"'jutro o' p",nextWeek:O,other:"P"},ZB=function B(J,X,Z,U){var C=XB[J];if(typeof C==="function")return C(J,X,Z,U);return C};function E(B){return function(J,X){var Z=X!==null&&X!==void 0&&X.context?String(X.context):"standalone",U;if(Z==="formatting"&&B.formattingValues){var C=B.defaultFormattingWidth||B.defaultWidth,H=X!==null&&X!==void 0&&X.width?String(X.width):C;U=B.formattingValues[H]||B.formattingValues[C]}else{var Q=B.defaultWidth,q=X!==null&&X!==void 0&&X.width?String(X.width):B.defaultWidth;U=B.values[q]||B.values[Q]}var Y=B.argumentCallback?B.argumentCallback(J):J;return U[Y]}}var UB={narrow:["p.n.e.","n.e."],abbreviated:["p.n.e.","n.e."],wide:["przed nasz\u0105 er\u0105","naszej ery"]},CB={narrow:["1","2","3","4"],abbreviated:["I kw.","II kw.","III kw.","IV kw."],wide:["I kwarta\u0142","II kwarta\u0142","III kwarta\u0142","IV kwarta\u0142"]},HB={narrow:["S","L","M","K","M","C","L","S","W","P","L","G"],abbreviated:["sty","lut","mar","kwi","maj","cze","lip","sie","wrz","pa\u017A","lis","gru"],wide:["stycze\u0144","luty","marzec","kwiecie\u0144","maj","czerwiec","lipiec","sierpie\u0144","wrzesie\u0144","pa\u017Adziernik","listopad","grudzie\u0144"]},QB={narrow:["s","l","m","k","m","c","l","s","w","p","l","g"],abbreviated:["sty","lut","mar","kwi","maj","cze","lip","sie","wrz","pa\u017A","lis","gru"],wide:["stycznia","lutego","marca","kwietnia","maja","czerwca","lipca","sierpnia","wrze\u015Bnia","pa\u017Adziernika","listopada","grudnia"]},YB={narrow:["N","P","W","\u015A","C","P","S"],short:["nie","pon","wto","\u015Bro","czw","pi\u0105","sob"],abbreviated:["niedz.","pon.","wt.","\u015Br.","czw.","pt.","sob."],wide:["niedziela","poniedzia\u0142ek","wtorek","\u015Broda","czwartek","pi\u0105tek","sobota"]},qB={narrow:["n","p","w","\u015B","c","p","s"],short:["nie","pon","wto","\u015Bro","czw","pi\u0105","sob"],abbreviated:["niedz.","pon.","wt.","\u015Br.","czw.","pt.","sob."],wide:["niedziela","poniedzia\u0142ek","wtorek","\u015Broda","czwartek","pi\u0105tek","sobota"]},GB={narrow:{am:"a",pm:"p",midnight:"p\xF3\u0142n.",noon:"po\u0142",morning:"rano",afternoon:"popo\u0142.",evening:"wiecz.",night:"noc"},abbreviated:{am:"AM",pm:"PM",midnight:"p\xF3\u0142noc",noon:"po\u0142udnie",morning:"rano",afternoon:"popo\u0142udnie",evening:"wiecz\xF3r",night:"noc"},wide:{am:"AM",pm:"PM",midnight:"p\xF3\u0142noc",noon:"po\u0142udnie",morning:"rano",afternoon:"popo\u0142udnie",evening:"wiecz\xF3r",night:"noc"}},KB={narrow:{am:"a",pm:"p",midnight:"o p\xF3\u0142n.",noon:"w po\u0142.",morning:"rano",afternoon:"po po\u0142.",evening:"wiecz.",night:"w nocy"},abbreviated:{am:"AM",pm:"PM",midnight:"o p\xF3\u0142nocy",noon:"w po\u0142udnie",morning:"rano",afternoon:"po po\u0142udniu",evening:"wieczorem",night:"w nocy"},wide:{am:"AM",pm:"PM",midnight:"o p\xF3\u0142nocy",noon:"w po\u0142udnie",morning:"rano",afternoon:"po po\u0142udniu",evening:"wieczorem",night:"w nocy"}},NB=function B(J,X){return String(J)},EB={ordinalNumber:NB,era:E({values:UB,defaultWidth:"wide"}),quarter:E({values:CB,defaultWidth:"wide",argumentCallback:function B(J){return J-1}}),month:E({values:HB,defaultWidth:"wide",formattingValues:QB,defaultFormattingWidth:"wide"}),day:E({values:YB,defaultWidth:"wide",formattingValues:qB,defaultFormattingWidth:"wide"}),dayPeriod:E({values:GB,defaultWidth:"wide",formattingValues:KB,defaultFormattingWidth:"wide"})};function I(B){return function(J){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Z=X.width,U=Z&&B.matchPatterns[Z]||B.matchPatterns[B.defaultMatchWidth],C=J.match(U);if(!C)return null;var H=C[0],Q=Z&&B.parsePatterns[Z]||B.parsePatterns[B.defaultParseWidth],q=Array.isArray(Q)?MB(Q,function(N){return N.test(H)}):IB(Q,function(N){return N.test(H)}),Y;Y=B.valueCallback?B.valueCallback(q):q,Y=X.valueCallback?X.valueCallback(Y):Y;var G=J.slice(H.length);return{value:Y,rest:G}}}function IB(B,J){for(var X in B)if(Object.prototype.hasOwnProperty.call(B,X)&&J(B[X]))return X;return}function MB(B,J){for(var X=0;X<B.length;X++)if(J(B[X]))return X;return}function VB(B){return function(J){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Z=J.match(B.matchPattern);if(!Z)return null;var U=Z[0],C=J.match(B.parsePattern);if(!C)return null;var H=B.valueCallback?B.valueCallback(C[0]):C[0];H=X.valueCallback?X.valueCallback(H):H;var Q=J.slice(U.length);return{value:H,rest:Q}}}var zB=/^(\d+)?/i,RB=/\d+/i,TB={narrow:/^(p\.?\s*n\.?\s*e\.?\s*|n\.?\s*e\.?\s*)/i,abbreviated:/^(p\.?\s*n\.?\s*e\.?\s*|n\.?\s*e\.?\s*)/i,wide:/^(przed\s*nasz(ą|a)\s*er(ą|a)|naszej\s*ery)/i},xB={any:[/^p/i,/^n/i]},AB={narrow:/^[1234]/i,abbreviated:/^(I|II|III|IV)\s*kw\.?/i,wide:/^(I|II|III|IV)\s*kwarta(ł|l)/i},WB={narrow:[/1/i,/2/i,/3/i,/4/i],any:[/^I kw/i,/^II kw/i,/^III kw/i,/^IV kw/i]},SB={narrow:/^[slmkcwpg]/i,abbreviated:/^(sty|lut|mar|kwi|maj|cze|lip|sie|wrz|pa(ź|z)|lis|gru)/i,wide:/^(stycznia|stycze(ń|n)|lutego|luty|marca|marzec|kwietnia|kwiecie(ń|n)|maja|maj|czerwca|czerwiec|lipca|lipiec|sierpnia|sierpie(ń|n)|wrze(ś|s)nia|wrzesie(ń|n)|pa(ź|z)dziernika|pa(ź|z)dziernik|listopada|listopad|grudnia|grudzie(ń|n))/i},$B={narrow:[/^s/i,/^l/i,/^m/i,/^k/i,/^m/i,/^c/i,/^l/i,/^s/i,/^w/i,/^p/i,/^l/i,/^g/i],any:[/^st/i,/^lu/i,/^mar/i,/^k/i,/^maj/i,/^c/i,/^lip/i,/^si/i,/^w/i,/^p/i,/^lis/i,/^g/i]},LB={narrow:/^[npwścs]/i,short:/^(nie|pon|wto|(ś|s)ro|czw|pi(ą|a)|sob)/i,abbreviated:/^(niedz|pon|wt|(ś|s)r|czw|pt|sob)\.?/i,wide:/^(niedziela|poniedzia(ł|l)ek|wtorek|(ś|s)roda|czwartek|pi(ą|a)tek|sobota)/i},OB={narrow:[/^n/i,/^p/i,/^w/i,/^ś/i,/^c/i,/^p/i,/^s/i],abbreviated:[/^n/i,/^po/i,/^w/i,/^(ś|s)r/i,/^c/i,/^pt/i,/^so/i],any:[/^n/i,/^po/i,/^w/i,/^(ś|s)r/i,/^c/i,/^pi/i,/^so/i]},DB={narrow:/^(^a$|^p$|pó(ł|l)n\.?|o\s*pó(ł|l)n\.?|po(ł|l)\.?|w\s*po(ł|l)\.?|po\s*po(ł|l)\.?|rano|wiecz\.?|noc|w\s*nocy)/i,any:/^(am|pm|pó(ł|l)noc|o\s*pó(ł|l)nocy|po(ł|l)udnie|w\s*po(ł|l)udnie|popo(ł|l)udnie|po\s*po(ł|l)udniu|rano|wieczór|wieczorem|noc|w\s*nocy)/i},PB={narrow:{am:/^a$/i,pm:/^p$/i,midnight:/pó(ł|l)n/i,noon:/po(ł|l)/i,morning:/rano/i,afternoon:/po\s*po(ł|l)/i,evening:/wiecz/i,night:/noc/i},any:{am:/^am/i,pm:/^pm/i,midnight:/pó(ł|l)n/i,noon:/po(ł|l)/i,morning:/rano/i,afternoon:/po\s*po(ł|l)/i,evening:/wiecz/i,night:/noc/i}},wB={ordinalNumber:VB({matchPattern:zB,parsePattern:RB,valueCallback:function B(J){return parseInt(J,10)}}),era:I({matchPatterns:TB,defaultMatchWidth:"wide",parsePatterns:xB,defaultParseWidth:"any"}),quarter:I({matchPatterns:AB,defaultMatchWidth:"wide",parsePatterns:WB,defaultParseWidth:"any",valueCallback:function B(J){return J+1}}),month:I({matchPatterns:SB,defaultMatchWidth:"wide",parsePatterns:$B,defaultParseWidth:"any"}),day:I({matchPatterns:LB,defaultMatchWidth:"wide",parsePatterns:OB,defaultParseWidth:"any"}),dayPeriod:I({matchPatterns:DB,defaultMatchWidth:"any",parsePatterns:PB,defaultParseWidth:"any"})},jB={code:"pl",formatDistance:y,formatLong:u,formatRelative:ZB,localize:EB,match:wB,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=M(M({},window.dateFns),{},{locale:M(M({},(V=window.dateFns)===null||V===void 0?void 0:V.locale),{},{pl:jB})})})();

//# debugId=F0123D5E15A2B26F64756E2164756E21
