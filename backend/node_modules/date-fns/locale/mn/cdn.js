(() => {
var _window$dateFns;function _typeof(o) {"@babel/helpers - typeof";return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) {return typeof o;} : function (o) {return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o;}, _typeof(o);}function ownKeys(e, r) {var t = Object.keys(e);if (Object.getOwnPropertySymbols) {var o = Object.getOwnPropertySymbols(e);r && (o = o.filter(function (r) {return Object.getOwnPropertyDescriptor(e, r).enumerable;})), t.push.apply(t, o);}return t;}function _objectSpread(e) {for (var r = 1; r < arguments.length; r++) {var t = null != arguments[r] ? arguments[r] : {};r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {_defineProperty(e, r, t[r]);}) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));});}return e;}function _defineProperty(obj, key, value) {key = _toPropertyKey(key);if (key in obj) {Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });} else {obj[key] = value;}return obj;}function _toPropertyKey(t) {var i = _toPrimitive(t, "string");return "symbol" == _typeof(i) ? i : String(i);}function _toPrimitive(t, r) {if ("object" != _typeof(t) || !t) return t;var e = t[Symbol.toPrimitive];if (void 0 !== e) {var i = e.call(t, r || "default");if ("object" != _typeof(i)) return i;throw new TypeError("@@toPrimitive must return a primitive value.");}return ("string" === r ? String : Number)(t);}var __defProp = Object.defineProperty;
var __export = function __export(target, all) {
  for (var name in all)
  __defProp(target, name, {
    get: all[name],
    enumerable: true,
    configurable: true,
    set: function set(newValue) {return all[name] = function () {return newValue;};}
  });
};

// lib/locale/mn/_lib/formatDistance.js
var formatDistanceLocale = {
  lessThanXSeconds: {
    one: "\u0441\u0435\u043A\u0443\u043D\u0434 \u0445\u04AF\u0440\u044D\u0445\u0433\u04AF\u0439",
    other: "{{count}} \u0441\u0435\u043A\u0443\u043D\u0434 \u0445\u04AF\u0440\u044D\u0445\u0433\u04AF\u0439"
  },
  xSeconds: {
    one: "1 \u0441\u0435\u043A\u0443\u043D\u0434",
    other: "{{count}} \u0441\u0435\u043A\u0443\u043D\u0434"
  },
  halfAMinute: "\u0445\u0430\u0433\u0430\u0441 \u043C\u0438\u043D\u0443\u0442",
  lessThanXMinutes: {
    one: "\u043C\u0438\u043D\u0443\u0442 \u0445\u04AF\u0440\u044D\u0445\u0433\u04AF\u0439",
    other: "{{count}} \u043C\u0438\u043D\u0443\u0442 \u0445\u04AF\u0440\u044D\u0445\u0433\u04AF\u0439"
  },
  xMinutes: {
    one: "1 \u043C\u0438\u043D\u0443\u0442",
    other: "{{count}} \u043C\u0438\u043D\u0443\u0442"
  },
  aboutXHours: {
    one: "\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 1 \u0446\u0430\u0433",
    other: "\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 {{count}} \u0446\u0430\u0433"
  },
  xHours: {
    one: "1 \u0446\u0430\u0433",
    other: "{{count}} \u0446\u0430\u0433"
  },
  xDays: {
    one: "1 \u04E9\u0434\u04E9\u0440",
    other: "{{count}} \u04E9\u0434\u04E9\u0440"
  },
  aboutXWeeks: {
    one: "\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 1 \u0434\u043E\u043B\u043E\u043E \u0445\u043E\u043D\u043E\u0433",
    other: "\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 {{count}} \u0434\u043E\u043B\u043E\u043E \u0445\u043E\u043D\u043E\u0433"
  },
  xWeeks: {
    one: "1 \u0434\u043E\u043B\u043E\u043E \u0445\u043E\u043D\u043E\u0433",
    other: "{{count}} \u0434\u043E\u043B\u043E\u043E \u0445\u043E\u043D\u043E\u0433"
  },
  aboutXMonths: {
    one: "\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 1 \u0441\u0430\u0440",
    other: "\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 {{count}} \u0441\u0430\u0440"
  },
  xMonths: {
    one: "1 \u0441\u0430\u0440",
    other: "{{count}} \u0441\u0430\u0440"
  },
  aboutXYears: {
    one: "\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 1 \u0436\u0438\u043B",
    other: "\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 {{count}} \u0436\u0438\u043B"
  },
  xYears: {
    one: "1 \u0436\u0438\u043B",
    other: "{{count}} \u0436\u0438\u043B"
  },
  overXYears: {
    one: "1 \u0436\u0438\u043B \u0433\u0430\u0440\u0430\u043D",
    other: "{{count}} \u0436\u0438\u043B \u0433\u0430\u0440\u0430\u043D"
  },
  almostXYears: {
    one: "\u0431\u0430\u0440\u0430\u0433 1 \u0436\u0438\u043B",
    other: "\u0431\u0430\u0440\u0430\u0433 {{count}} \u0436\u0438\u043B"
  }
};
var formatDistance = function formatDistance(token, count, options) {
  var result;
  var tokenValue = formatDistanceLocale[token];
  if (typeof tokenValue === "string") {
    result = tokenValue;
  } else if (count === 1) {
    result = tokenValue.one;
  } else {
    result = tokenValue.other.replace("{{count}}", String(count));
  }
  if (options !== null && options !== void 0 && options.addSuffix) {
    var words = result.split(" ");
    var lastword = words.pop();
    result = words.join(" ");
    switch (lastword) {
      case "\u0441\u0435\u043A\u0443\u043D\u0434":
        result += " \u0441\u0435\u043A\u0443\u043D\u0434\u0438\u0439\u043D";
        break;
      case "\u043C\u0438\u043D\u0443\u0442":
        result += " \u043C\u0438\u043D\u0443\u0442\u044B\u043D";
        break;
      case "\u0446\u0430\u0433":
        result += " \u0446\u0430\u0433\u0438\u0439\u043D";
        break;
      case "\u04E9\u0434\u04E9\u0440":
        result += " \u04E9\u0434\u0440\u0438\u0439\u043D";
        break;
      case "\u0441\u0430\u0440":
        result += " \u0441\u0430\u0440\u044B\u043D";
        break;
      case "\u0436\u0438\u043B":
        result += " \u0436\u0438\u043B\u0438\u0439\u043D";
        break;
      case "\u0445\u043E\u043D\u043E\u0433":
        result += " \u0445\u043E\u043D\u043E\u0433\u0438\u0439\u043D";
        break;
      case "\u0433\u0430\u0440\u0430\u043D":
        result += " \u0433\u0430\u0440\u0430\u043D\u044B";
        break;
      case "\u0445\u04AF\u0440\u044D\u0445\u0433\u04AF\u0439":
        result += " \u0445\u04AF\u0440\u044D\u0445\u0433\u04AF\u0439 \u0445\u0443\u0433\u0430\u0446\u0430\u0430\u043D\u044B";
        break;
      default:
        result += lastword + "-\u043D";
    }
    if (options.comparison && options.comparison > 0) {
      return result + " \u0434\u0430\u0440\u0430\u0430";
    } else {
      return result + " \u04E9\u043C\u043D\u04E9";
    }
  }
  return result;
};

// lib/locale/_lib/buildFormatLongFn.js
function buildFormatLongFn(args) {
  return function () {var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    var width = options.width ? String(options.width) : args.defaultWidth;
    var format = args.formats[width] || args.formats[args.defaultWidth];
    return format;
  };
}

// lib/locale/mn/_lib/formatLong.js
var dateFormats = {
  full: "y '\u043E\u043D\u044B' MMMM'\u044B\u043D' d, EEEE '\u0433\u0430\u0440\u0430\u0433'",
  long: "y '\u043E\u043D\u044B' MMMM'\u044B\u043D' d",
  medium: "y '\u043E\u043D\u044B' MMM'\u044B\u043D' d",
  short: "y.MM.dd"
};
var timeFormats = {
  full: "H:mm:ss zzzz",
  long: "H:mm:ss z",
  medium: "H:mm:ss",
  short: "H:mm"
};
var dateTimeFormats = {
  full: "{{date}} {{time}}",
  long: "{{date}} {{time}}",
  medium: "{{date}} {{time}}",
  short: "{{date}} {{time}}"
};
var formatLong = {
  date: buildFormatLongFn({
    formats: dateFormats,
    defaultWidth: "full"
  }),
  time: buildFormatLongFn({
    formats: timeFormats,
    defaultWidth: "full"
  }),
  dateTime: buildFormatLongFn({
    formats: dateTimeFormats,
    defaultWidth: "full"
  })
};

// lib/locale/mn/_lib/formatRelative.js
var formatRelativeLocale = {
  lastWeek: "'\u04E9\u043D\u0433\u04E9\u0440\u0441\u04E9\u043D' eeee '\u0433\u0430\u0440\u0430\u0433\u0438\u0439\u043D' p '\u0446\u0430\u0433\u0442'",
  yesterday: "'\u04E9\u0447\u0438\u0433\u0434\u04E9\u0440' p '\u0446\u0430\u0433\u0442'",
  today: "'\u04E9\u043D\u04E9\u04E9\u0434\u04E9\u0440' p '\u0446\u0430\u0433\u0442'",
  tomorrow: "'\u043C\u0430\u0440\u0433\u0430\u0430\u0448' p '\u0446\u0430\u0433\u0442'",
  nextWeek: "'\u0438\u0440\u044D\u0445' eeee '\u0433\u0430\u0440\u0430\u0433\u0438\u0439\u043D' p '\u0446\u0430\u0433\u0442'",
  other: "P"
};
var formatRelative = function formatRelative(token, _date, _baseDate, _options) {return formatRelativeLocale[token];};

// lib/locale/_lib/buildLocalizeFn.js
function buildLocalizeFn(args) {
  return function (value, options) {
    var context = options !== null && options !== void 0 && options.context ? String(options.context) : "standalone";
    var valuesArray;
    if (context === "formatting" && args.formattingValues) {
      var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;
      var width = options !== null && options !== void 0 && options.width ? String(options.width) : defaultWidth;
      valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];
    } else {
      var _defaultWidth = args.defaultWidth;
      var _width = options !== null && options !== void 0 && options.width ? String(options.width) : args.defaultWidth;
      valuesArray = args.values[_width] || args.values[_defaultWidth];
    }
    var index = args.argumentCallback ? args.argumentCallback(value) : value;
    return valuesArray[index];
  };
}

// lib/locale/mn/_lib/localize.js
var eraValues = {
  narrow: ["\u041D\u0422\u04E8", "\u041D\u0422"],
  abbreviated: ["\u041D\u0422\u04E8", "\u041D\u0422"],
  wide: ["\u043D\u0438\u0439\u0442\u0438\u0439\u043D \u0442\u043E\u043E\u043B\u043B\u044B\u043D \u04E9\u043C\u043D\u04E9\u0445", "\u043D\u0438\u0439\u0442\u0438\u0439\u043D \u0442\u043E\u043E\u043B\u043B\u044B\u043D"]
};
var quarterValues = {
  narrow: ["I", "II", "III", "IV"],
  abbreviated: ["I \u0443\u043B\u0438\u0440\u0430\u043B", "II \u0443\u043B\u0438\u0440\u0430\u043B", "III \u0443\u043B\u0438\u0440\u0430\u043B", "IV \u0443\u043B\u0438\u0440\u0430\u043B"],
  wide: ["1-\u0440 \u0443\u043B\u0438\u0440\u0430\u043B", "2-\u0440 \u0443\u043B\u0438\u0440\u0430\u043B", "3-\u0440 \u0443\u043B\u0438\u0440\u0430\u043B", "4-\u0440 \u0443\u043B\u0438\u0440\u0430\u043B"]
};
var monthValues = {
  narrow: [
  "I",
  "II",
  "III",
  "IV",
  "V",
  "VI",
  "VII",
  "VIII",
  "IX",
  "X",
  "XI",
  "XII"],

  abbreviated: [
  "1-\u0440 \u0441\u0430\u0440",
  "2-\u0440 \u0441\u0430\u0440",
  "3-\u0440 \u0441\u0430\u0440",
  "4-\u0440 \u0441\u0430\u0440",
  "5-\u0440 \u0441\u0430\u0440",
  "6-\u0440 \u0441\u0430\u0440",
  "7-\u0440 \u0441\u0430\u0440",
  "8-\u0440 \u0441\u0430\u0440",
  "9-\u0440 \u0441\u0430\u0440",
  "10-\u0440 \u0441\u0430\u0440",
  "11-\u0440 \u0441\u0430\u0440",
  "12-\u0440 \u0441\u0430\u0440"],

  wide: [
  "\u041D\u044D\u0433\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440",
  "\u0425\u043E\u0451\u0440\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440",
  "\u0413\u0443\u0440\u0430\u0432\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440",
  "\u0414\u04E9\u0440\u04E9\u0432\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440",
  "\u0422\u0430\u0432\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440",
  "\u0417\u0443\u0440\u0433\u0430\u0430\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440",
  "\u0414\u043E\u043B\u043E\u043E\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440",
  "\u041D\u0430\u0439\u043C\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440",
  "\u0415\u0441\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440",
  "\u0410\u0440\u0430\u0432\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440",
  "\u0410\u0440\u0432\u0430\u043D\u043D\u044D\u0433\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440",
  "\u0410\u0440\u0432\u0430\u043D \u0445\u043E\u0451\u0440\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440"]

};
var formattingMonthValues = {
  narrow: [
  "I",
  "II",
  "III",
  "IV",
  "V",
  "VI",
  "VII",
  "VIII",
  "IX",
  "X",
  "XI",
  "XII"],

  abbreviated: [
  "1-\u0440 \u0441\u0430\u0440",
  "2-\u0440 \u0441\u0430\u0440",
  "3-\u0440 \u0441\u0430\u0440",
  "4-\u0440 \u0441\u0430\u0440",
  "5-\u0440 \u0441\u0430\u0440",
  "6-\u0440 \u0441\u0430\u0440",
  "7-\u0440 \u0441\u0430\u0440",
  "8-\u0440 \u0441\u0430\u0440",
  "9-\u0440 \u0441\u0430\u0440",
  "10-\u0440 \u0441\u0430\u0440",
  "11-\u0440 \u0441\u0430\u0440",
  "12-\u0440 \u0441\u0430\u0440"],

  wide: [
  "\u043D\u044D\u0433\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440",
  "\u0445\u043E\u0451\u0440\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440",
  "\u0433\u0443\u0440\u0430\u0432\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440",
  "\u0434\u04E9\u0440\u04E9\u0432\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440",
  "\u0442\u0430\u0432\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440",
  "\u0437\u0443\u0440\u0433\u0430\u0430\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440",
  "\u0434\u043E\u043B\u043E\u043E\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440",
  "\u043D\u0430\u0439\u043C\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440",
  "\u0435\u0441\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440",
  "\u0430\u0440\u0430\u0432\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440",
  "\u0430\u0440\u0432\u0430\u043D\u043D\u044D\u0433\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440",
  "\u0430\u0440\u0432\u0430\u043D \u0445\u043E\u0451\u0440\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440"]

};
var dayValues = {
  narrow: ["\u041D", "\u0414", "\u041C", "\u041B", "\u041F", "\u0411", "\u0411"],
  short: ["\u041D\u044F", "\u0414\u0430", "\u041C\u044F", "\u041B\u0445", "\u041F\u04AF", "\u0411\u0430", "\u0411\u044F"],
  abbreviated: ["\u041D\u044F\u043C", "\u0414\u0430\u0432", "\u041C\u044F\u0433", "\u041B\u0445\u0430", "\u041F\u04AF\u0440", "\u0411\u0430\u0430", "\u0411\u044F\u043C"],
  wide: ["\u041D\u044F\u043C", "\u0414\u0430\u0432\u0430\u0430", "\u041C\u044F\u0433\u043C\u0430\u0440", "\u041B\u0445\u0430\u0433\u0432\u0430", "\u041F\u04AF\u0440\u044D\u0432", "\u0411\u0430\u0430\u0441\u0430\u043D", "\u0411\u044F\u043C\u0431\u0430"]
};
var formattingDayValues = {
  narrow: ["\u041D", "\u0414", "\u041C", "\u041B", "\u041F", "\u0411", "\u0411"],
  short: ["\u041D\u044F", "\u0414\u0430", "\u041C\u044F", "\u041B\u0445", "\u041F\u04AF", "\u0411\u0430", "\u0411\u044F"],
  abbreviated: ["\u041D\u044F\u043C", "\u0414\u0430\u0432", "\u041C\u044F\u0433", "\u041B\u0445\u0430", "\u041F\u04AF\u0440", "\u0411\u0430\u0430", "\u0411\u044F\u043C"],
  wide: ["\u043D\u044F\u043C", "\u0434\u0430\u0432\u0430\u0430", "\u043C\u044F\u0433\u043C\u0430\u0440", "\u043B\u0445\u0430\u0433\u0432\u0430", "\u043F\u04AF\u0440\u044D\u0432", "\u0431\u0430\u0430\u0441\u0430\u043D", "\u0431\u044F\u043C\u0431\u0430"]
};
var dayPeriodValues = {
  narrow: {
    am: "\u04AF.\u04E9.",
    pm: "\u04AF.\u0445.",
    midnight: "\u0448\u04E9\u043D\u04E9 \u0434\u0443\u043D\u0434",
    noon: "\u04AF\u0434 \u0434\u0443\u043D\u0434",
    morning: "\u04E9\u0433\u043B\u04E9\u04E9",
    afternoon: "\u04E9\u0434\u04E9\u0440",
    evening: "\u043E\u0440\u043E\u0439",
    night: "\u0448\u04E9\u043D\u04E9"
  },
  abbreviated: {
    am: "\u04AF.\u04E9.",
    pm: "\u04AF.\u0445.",
    midnight: "\u0448\u04E9\u043D\u04E9 \u0434\u0443\u043D\u0434",
    noon: "\u04AF\u0434 \u0434\u0443\u043D\u0434",
    morning: "\u04E9\u0433\u043B\u04E9\u04E9",
    afternoon: "\u04E9\u0434\u04E9\u0440",
    evening: "\u043E\u0440\u043E\u0439",
    night: "\u0448\u04E9\u043D\u04E9"
  },
  wide: {
    am: "\u04AF.\u04E9.",
    pm: "\u04AF.\u0445.",
    midnight: "\u0448\u04E9\u043D\u04E9 \u0434\u0443\u043D\u0434",
    noon: "\u04AF\u0434 \u0434\u0443\u043D\u0434",
    morning: "\u04E9\u0433\u043B\u04E9\u04E9",
    afternoon: "\u04E9\u0434\u04E9\u0440",
    evening: "\u043E\u0440\u043E\u0439",
    night: "\u0448\u04E9\u043D\u04E9"
  }
};
var ordinalNumber = function ordinalNumber(dirtyNumber, _options) {
  return String(dirtyNumber);
};
var localize = {
  ordinalNumber: ordinalNumber,
  era: buildLocalizeFn({
    values: eraValues,
    defaultWidth: "wide"
  }),
  quarter: buildLocalizeFn({
    values: quarterValues,
    defaultWidth: "wide",
    argumentCallback: function argumentCallback(quarter) {return quarter - 1;}
  }),
  month: buildLocalizeFn({
    values: monthValues,
    defaultWidth: "wide",
    formattingValues: formattingMonthValues,
    defaultFormattingWidth: "wide"
  }),
  day: buildLocalizeFn({
    values: dayValues,
    defaultWidth: "wide",
    formattingValues: formattingDayValues,
    defaultFormattingWidth: "wide"
  }),
  dayPeriod: buildLocalizeFn({
    values: dayPeriodValues,
    defaultWidth: "wide"
  })
};

// lib/locale/_lib/buildMatchFn.js
function buildMatchFn(args) {
  return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    var width = options.width;
    var matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];
    var matchResult = string.match(matchPattern);
    if (!matchResult) {
      return null;
    }
    var matchedString = matchResult[0];
    var parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];
    var key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, function (pattern) {return pattern.test(matchedString);}) : findKey(parsePatterns, function (pattern) {return pattern.test(matchedString);});
    var value;
    value = args.valueCallback ? args.valueCallback(key) : key;
    value = options.valueCallback ? options.valueCallback(value) : value;
    var rest = string.slice(matchedString.length);
    return { value: value, rest: rest };
  };
}
function findKey(object, predicate) {
  for (var key in object) {
    if (Object.prototype.hasOwnProperty.call(object, key) && predicate(object[key])) {
      return key;
    }
  }
  return;
}
function findIndex(array, predicate) {
  for (var key = 0; key < array.length; key++) {
    if (predicate(array[key])) {
      return key;
    }
  }
  return;
}

// lib/locale/_lib/buildMatchPatternFn.js
function buildMatchPatternFn(args) {
  return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    var matchResult = string.match(args.matchPattern);
    if (!matchResult)
    return null;
    var matchedString = matchResult[0];
    var parseResult = string.match(args.parsePattern);
    if (!parseResult)
    return null;
    var value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];
    value = options.valueCallback ? options.valueCallback(value) : value;
    var rest = string.slice(matchedString.length);
    return { value: value, rest: rest };
  };
}

// lib/locale/mn/_lib/match.js
var matchOrdinalNumberPattern = /\d+/i;
var parseOrdinalNumberPattern = /\d+/i;
var matchEraPatterns = {
  narrow: /^(нтө|нт)/i,
  abbreviated: /^(нтө|нт)/i,
  wide: /^(нийтийн тооллын өмнө|нийтийн тооллын)/i
};
var parseEraPatterns = {
  any: [/^(нтө|нийтийн тооллын өмнө)/i, /^(нт|нийтийн тооллын)/i]
};
var matchQuarterPatterns = {
  narrow: /^(iv|iii|ii|i)/i,
  abbreviated: /^(iv|iii|ii|i) улирал/i,
  wide: /^[1-4]-р улирал/i
};
var parseQuarterPatterns = {
  any: [/^(i(\s|$)|1)/i, /^(ii(\s|$)|2)/i, /^(iii(\s|$)|3)/i, /^(iv(\s|$)|4)/i]
};
var matchMonthPatterns = {
  narrow: /^(xii|xi|x|ix|viii|vii|vi|v|iv|iii|ii|i)/i,
  abbreviated: /^(1-р сар|2-р сар|3-р сар|4-р сар|5-р сар|6-р сар|7-р сар|8-р сар|9-р сар|10-р сар|11-р сар|12-р сар)/i,
  wide: /^(нэгдүгээр сар|хоёрдугаар сар|гуравдугаар сар|дөрөвдүгээр сар|тавдугаар сар|зургаадугаар сар|долоодугаар сар|наймдугаар сар|есдүгээр сар|аравдугаар сар|арван нэгдүгээр сар|арван хоёрдугаар сар)/i
};
var parseMonthPatterns = {
  narrow: [
  /^i$/i,
  /^ii$/i,
  /^iii$/i,
  /^iv$/i,
  /^v$/i,
  /^vi$/i,
  /^vii$/i,
  /^viii$/i,
  /^ix$/i,
  /^x$/i,
  /^xi$/i,
  /^xii$/i],

  any: [
  /^(1|нэгдүгээр)/i,
  /^(2|хоёрдугаар)/i,
  /^(3|гуравдугаар)/i,
  /^(4|дөрөвдүгээр)/i,
  /^(5|тавдугаар)/i,
  /^(6|зургаадугаар)/i,
  /^(7|долоодугаар)/i,
  /^(8|наймдугаар)/i,
  /^(9|есдүгээр)/i,
  /^(10|аравдугаар)/i,
  /^(11|арван нэгдүгээр)/i,
  /^(12|арван хоёрдугаар)/i]

};
var matchDayPatterns = {
  narrow: /^[ндмлпбб]/i,
  short: /^(ня|да|мя|лх|пү|ба|бя)/i,
  abbreviated: /^(ням|дав|мяг|лха|пүр|баа|бям)/i,
  wide: /^(ням|даваа|мягмар|лхагва|пүрэв|баасан|бямба)/i
};
var parseDayPatterns = {
  narrow: [/^н/i, /^д/i, /^м/i, /^л/i, /^п/i, /^б/i, /^б/i],
  any: [/^ня/i, /^да/i, /^мя/i, /^лх/i, /^пү/i, /^ба/i, /^бя/i]
};
var matchDayPeriodPatterns = {
  narrow: /^(ү\.ө\.|ү\.х\.|шөнө дунд|үд дунд|өглөө|өдөр|орой|шөнө)/i,
  any: /^(ү\.ө\.|ү\.х\.|шөнө дунд|үд дунд|өглөө|өдөр|орой|шөнө)/i
};
var parseDayPeriodPatterns = {
  any: {
    am: /^ү\.ө\./i,
    pm: /^ү\.х\./i,
    midnight: /^шөнө дунд/i,
    noon: /^үд дунд/i,
    morning: /өглөө/i,
    afternoon: /өдөр/i,
    evening: /орой/i,
    night: /шөнө/i
  }
};
var match = {
  ordinalNumber: buildMatchPatternFn({
    matchPattern: matchOrdinalNumberPattern,
    parsePattern: parseOrdinalNumberPattern,
    valueCallback: function valueCallback(value) {return parseInt(value, 10);}
  }),
  era: buildMatchFn({
    matchPatterns: matchEraPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseEraPatterns,
    defaultParseWidth: "any"
  }),
  quarter: buildMatchFn({
    matchPatterns: matchQuarterPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseQuarterPatterns,
    defaultParseWidth: "any",
    valueCallback: function valueCallback(index) {return index + 1;}
  }),
  month: buildMatchFn({
    matchPatterns: matchMonthPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseMonthPatterns,
    defaultParseWidth: "any"
  }),
  day: buildMatchFn({
    matchPatterns: matchDayPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseDayPatterns,
    defaultParseWidth: "any"
  }),
  dayPeriod: buildMatchFn({
    matchPatterns: matchDayPeriodPatterns,
    defaultMatchWidth: "any",
    parsePatterns: parseDayPeriodPatterns,
    defaultParseWidth: "any"
  })
};

// lib/locale/mn.js
var mn = {
  code: "mn",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1,
    firstWeekContainsDate: 1
  }
};

// lib/locale/mn/cdn.js
window.dateFns = _objectSpread(_objectSpread({},
window.dateFns), {}, {
  locale: _objectSpread(_objectSpread({}, (_window$dateFns =
  window.dateFns) === null || _window$dateFns === void 0 ? void 0 : _window$dateFns.locale), {}, {
    mn: mn }) });



//# debugId=059C7E0D906C8FDE64756E2164756E21

//# sourceMappingURL=cdn.js.map
})();