import { formatDistance } from "./bn/_lib/formatDistance.js";
import { formatLong } from "./bn/_lib/formatLong.js";
import { formatRelative } from "./bn/_lib/formatRelative.js";
import { localize } from "./bn/_lib/localize.js";
import { match } from "./bn/_lib/match.js";

/**
 * @category Locales
 * @summary Bengali locale.
 * @language Bengali
 * @iso-639-2 ben
 * <AUTHOR> [@touhid<PERSON><PERSON>](https://github.com/touhidrahman)
 * <AUTHOR> [@nutboltu](https://github.com/nutboltu)
 */
export const bn = {
  code: "bn",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default bn;
