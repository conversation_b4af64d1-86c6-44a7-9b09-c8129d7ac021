"use strict";
exports.lv = void 0;
var _index = require("./lv/_lib/formatDistance.cjs");
var _index2 = require("./lv/_lib/formatLong.cjs");
var _index3 = require("./lv/_lib/formatRelative.cjs");
var _index4 = require("./lv/_lib/localize.cjs");
var _index5 = require("./lv/_lib/match.cjs");

/**
 * @category Locales
 * @summary Latvian locale (Latvia).
 * @language Latvian
 * @iso-639-2 lav
 * <AUTHOR> Puķītis [@prudolfs](https://github.com/prudolfs)
 */
const lv = (exports.lv = {
  code: "lv",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
