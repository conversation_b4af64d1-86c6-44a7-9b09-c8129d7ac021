import { formatDistance } from "./se/_lib/formatDistance.js";
import { formatLong } from "./se/_lib/formatLong.js";
import { formatRelative } from "./se/_lib/formatRelative.js";
import { localize } from "./se/_lib/localize.js";
import { match } from "./se/_lib/match.js";

/**
 * @category Locales
 * @summary Northern Sámi locale.
 * @language Northern Sámi
 * @iso-639-2 sme
 * <AUTHOR> [@audunru](https://github.com/audunru)
 */
export const se = {
  code: "se",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default se;
