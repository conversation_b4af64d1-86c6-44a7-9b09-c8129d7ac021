import type { DateLibOptions } from "../classes/DateLib.js";

/**
 * Generates the ARIA label for the week number cell (the first cell in a row).
 *
 * @defaultValue `Week ${weekNumber}`
 * @param weekNumber - The number of the week.
 * @param options - Optional configuration for the date formatting library.
 * @returns The ARIA label for the week number cell.
 * @group Labels
 * @see https://daypicker.dev/docs/translation#aria-labels
 */
export function labelWeekNumber(
  weekNumber: number,
  options?: DateLibOptions
): string {
  return `Week ${weekNumber}`;
}
