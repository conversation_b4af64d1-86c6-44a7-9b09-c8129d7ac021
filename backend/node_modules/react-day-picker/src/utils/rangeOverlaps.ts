import { defaultDateLib } from "../classes/index.js";

import { rangeIncludesDate } from "./rangeIncludesDate.js";

/**
 * Determines if two date ranges overlap.
 *
 * @since 9.2.2
 * @param rangeLeft - The first date range.
 * @param rangeRight - The second date range.
 * @param dateLib - The date utility library instance.
 * @returns `true` if the ranges overlap, otherwise `false`.
 * @group Utilities
 */
export function rangeOverlaps(
  rangeLeft: { from: Date; to: Date },
  rangeRight: { from: Date; to: Date },
  dateLib = defaultDateLib
): boolean {
  return (
    rangeIncludesDate(rangeLeft, rangeRight.from, false, dateLib) ||
    rangeIncludesDate(rangeLeft, rangeRight.to, false, dateLib) ||
    rangeIncludesDate(rangeRight, rangeLeft.from, false, dateLib) ||
    rangeIncludesDate(rangeRight, rangeLeft.to, false, dateLib)
  );
}
