/**
 * Returns the start date of the week in the broadcast calendar.
 *
 * The broadcast week starts on Monday. If the first day of the month is not a
 * Monday, this function calculates the previous Monday as the start of the
 * broadcast week.
 *
 * @since 9.4.0
 * @param date The date for which to calculate the start of the broadcast week.
 * @param dateLib The date library to use for date manipulation.
 * @returns The start date of the broadcast week.
 */
export function startOfBroadcastWeek(date, dateLib) {
    const firstOfMonth = dateLib.startOfMonth(date);
    const dayOfWeek = firstOfMonth.getDay();
    if (dayOfWeek === 1) {
        return firstOfMonth;
    }
    else if (dayOfWeek === 0) {
        return dateLib.addDays(firstOfMonth, -1 * 6);
    }
    else {
        return dateLib.addDays(firstOfMonth, -1 * (dayOfWeek - 1));
    }
}
//# sourceMappingURL=startOfBroadcastWeek.js.map