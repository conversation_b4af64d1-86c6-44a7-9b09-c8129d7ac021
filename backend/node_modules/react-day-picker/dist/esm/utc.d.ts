import React from "react";
import { type DayPickerProps } from "./index.js";
/**
 * @deprecated Use the `timeZone` prop instead of this function. This function
 *   wraps the DayPicker component and sets the `timeZone` to "utc".
 * @param props - The props to pass to the DayPicker component.
 * @returns The DayPicker component with the `timeZone` set to "utc".
 */
export declare function DayPicker(props: DayPickerProps): React.JSX.Element;
