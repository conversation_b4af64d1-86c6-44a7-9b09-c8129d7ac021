"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Select = Select;
const react_1 = __importDefault(require("react"));
/**
 * Render a `select` element.
 *
 * @group Components
 * @see https://daypicker.dev/guides/custom-components
 */
function Select(props) {
    return react_1.default.createElement("select", { ...props });
}
//# sourceMappingURL=Select.js.map