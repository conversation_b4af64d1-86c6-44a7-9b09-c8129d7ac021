"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MonthGrid = MonthGrid;
const react_1 = __importDefault(require("react"));
/**
 * Render the grid of days for a specific month.
 *
 * @group Components
 * @see https://daypicker.dev/guides/custom-components
 */
function MonthGrid(props) {
    return react_1.default.createElement("table", { ...props });
}
//# sourceMappingURL=MonthGrid.js.map