{"version": 3, "file": "useSelection.js", "sourceRoot": "", "sources": ["../../src/useSelection.ts"], "names": [], "mappings": ";;AAiBA,oCAkBC;AAlCD,yDAAmD;AACnD,yDAAmD;AACnD,2DAAqD;AAIrD;;;;;;;;;GASG;AACH,SAAgB,YAAY,CAC1B,KAAQ,EACR,OAAgB;IAEhB,MAAM,MAAM,GAAG,IAAA,wBAAS,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACzC,MAAM,KAAK,GAAG,IAAA,sBAAQ,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACvC,MAAM,KAAK,GAAG,IAAA,sBAAQ,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAEvC,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;QACnB,KAAK,QAAQ;YACX,OAAO,MAAM,CAAC;QAChB,KAAK,UAAU;YACb,OAAO,KAAK,CAAC;QACf,KAAK,OAAO;YACV,OAAO,KAAK,CAAC;QACf;YACE,OAAO,SAAS,CAAC;IACrB,CAAC;AACH,CAAC"}