"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DayPicker = DayPicker;
const react_1 = __importDefault(require("react"));
const index_js_1 = require("./index.js");
/**
 * @deprecated Use the `timeZone` prop instead of this function. This function
 *   wraps the DayPicker component and sets the `timeZone` to "utc".
 * @param props - The props to pass to the DayPicker component.
 * @returns The DayPicker component with the `timeZone` set to "utc".
 */
function DayPicker(props) {
    return react_1.default.createElement(index_js_1.DayPicker, { timeZone: "utc", ...props });
}
//# sourceMappingURL=utc.js.map