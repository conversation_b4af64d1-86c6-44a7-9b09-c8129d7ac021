import type { DateRange } from "../types/index.js";
/**
 * Checks if a given date is within a specified date range.
 *
 * @since 9.0.0
 * @param range - The date range to check against.
 * @param date - The date to check.
 * @param excludeEnds - If `true`, the range's start and end dates are excluded.
 * @param dateLib - The date utility library instance.
 * @returns `true` if the date is within the range, otherwise `false`.
 * @group Utilities
 */
export declare function rangeIncludesDate(range: DateRange, date: Date, excludeEnds?: boolean, dateLib?: import("../classes/DateLib.js").DateLib): boolean;
/**
 * @private
 * @deprecated Use {@link rangeIncludesDate} instead.
 */
export declare const isDateInRange: (range: DateRange, date: Date) => boolean;
