{"version": 3, "file": "rangeContainsModifiers.js", "sourceRoot": "", "sources": ["../../../src/utils/rangeContainsModifiers.ts"], "names": [], "mappings": ";;AA0BA,wDAyFC;AAnHD,sDAAqE;AAGrE,mEAA6D;AAC7D,2EAAqE;AACrE,iEAA2D;AAC3D,yDAAmD;AACnD,mDAOyB;AAEzB;;;;;;;;;GASG;AACH,SAAgB,sBAAsB,CACpC,KAA+B,EAC/B,SAA8B,EAC9B,UAAmB,2BAAc;IAEjC,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;IAEpE,uEAAuE;IACvE,MAAM,mBAAmB,GAAG,QAAQ,CAAC,MAAM,CACzC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,OAAO,KAAK,UAAU,CAC3C,CAAC;IAEF,MAAM,yBAAyB,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;QACrE,IAAI,OAAO,OAAO,KAAK,SAAS;YAAE,OAAO,OAAO,CAAC;QAEjD,IAAI,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,OAAO,IAAA,wCAAiB,EAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,IAAA,4BAAY,EAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC;YACnC,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAC3B,IAAA,wCAAiB,EAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAC/C,CAAC;QACJ,CAAC;QAED,IAAI,IAAA,2BAAW,EAAC,OAAO,CAAC,EAAE,CAAC;YACzB,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC;gBAC/B,OAAO,IAAA,gCAAa,EAClB,KAAK,EACL,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,EACtC,OAAO,CACR,CAAC;YACJ,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAA,+BAAe,EAAC,OAAO,CAAC,EAAE,CAAC;YAC7B,OAAO,IAAA,kDAAsB,EAAC,KAAK,EAAE,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,IAAA,8BAAc,EAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,MAAM,gBAAgB,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YACxE,IAAI,gBAAgB,EAAE,CAAC;gBACrB,OAAO,IAAA,gCAAa,EAClB,KAAK,EACL;oBACE,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;oBACvC,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;iBACxC,EACD,OAAO,CACR,CAAC;YACJ,CAAC;YACD,OAAO,CACL,IAAA,0CAAkB,EAAC,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC;gBAChD,IAAA,0CAAkB,EAAC,KAAK,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,CAC/C,CAAC;QACJ,CAAC;QAED,IAAI,IAAA,+BAAe,EAAC,OAAO,CAAC,IAAI,IAAA,gCAAgB,EAAC,OAAO,CAAC,EAAE,CAAC;YAC1D,OAAO,CACL,IAAA,0CAAkB,EAAC,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC;gBAChD,IAAA,0CAAkB,EAAC,KAAK,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,CAC/C,CAAC;QACJ,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC,CAAC,CAAC;IAEH,IAAI,yBAAyB,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CACtC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,OAAO,KAAK,UAAU,CAC3C,CAAC;IAEF,IAAI,gBAAgB,CAAC,MAAM,EAAE,CAAC;QAC5B,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACtB,MAAM,SAAS,GAAG,OAAO,CAAC,wBAAwB,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QAEzE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;gBACtD,OAAO,IAAI,CAAC;YACd,CAAC;YACD,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC"}