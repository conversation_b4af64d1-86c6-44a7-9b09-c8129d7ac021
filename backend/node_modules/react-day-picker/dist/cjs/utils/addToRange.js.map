{"version": 3, "file": "addToRange.js", "sourceRoot": "", "sources": ["../../../src/utils/addToRange.ts"], "names": [], "mappings": ";;AAgBA,gCAwEC;AAxFD,sDAAqE;AAGrE;;;;;;;;;;;;GAYG;AACH,SAAgB,UAAU,CACxB,IAAU,EACV,YAAmC,EACnC,GAAG,GAAG,CAAC,EACP,GAAG,GAAG,CAAC,EACP,QAAQ,GAAG,KAAK,EAChB,UAAmB,2BAAc;IAEjC,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,YAAY,IAAI,EAAE,CAAC;IACxC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;IAEjD,IAAI,KAA4B,CAAC;IAEjC,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;QACjB,mCAAmC;QACnC,KAAK,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IACzD,CAAC;SAAM,IAAI,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;QACvB,qCAAqC;QACrC,IAAI,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAC1B,gDAAgD;YAChD,IAAI,QAAQ,EAAE,CAAC;gBACb,KAAK,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC;YAClC,CAAC;iBAAM,CAAC;gBACN,KAAK,GAAG,SAAS,CAAC;YACpB,CAAC;QACH,CAAC;aAAM,IAAI,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAChC,8CAA8C;YAC9C,KAAK,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC;QACnC,CAAC;aAAM,CAAC;YACN,6CAA6C;YAC7C,KAAK,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC;QAC7B,CAAC;IACH,CAAC;SAAM,IAAI,IAAI,IAAI,EAAE,EAAE,CAAC;QACtB,kCAAkC;QAClC,IAAI,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC;YACjD,iEAAiE;YACjE,IAAI,QAAQ,EAAE,CAAC;gBACb,KAAK,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,KAAK,GAAG,SAAS,CAAC;YACpB,CAAC;QACH,CAAC;aAAM,IAAI,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YACjC,oDAAoD;YACpD,KAAK,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACnD,CAAC;aAAM,IAAI,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC;YAC/B,8CAA8C;YAC9C,KAAK,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACzD,CAAC;aAAM,IAAI,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAChC,8CAA8C;YAC9C,KAAK,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QACjC,CAAC;aAAM,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAC/B,6CAA6C;YAC7C,KAAK,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC;QAC7B,CAAC;aAAM,IAAI,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC;YAC7B,2CAA2C;YAC3C,KAAK,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC;QAC7B,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAED,sBAAsB;IACtB,IAAI,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE,EAAE,CAAC;QAC7B,MAAM,IAAI,GAAG,OAAO,CAAC,wBAAwB,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QACpE,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,GAAG,GAAG,EAAE,CAAC;YAC1B,KAAK,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC;QACxC,CAAC;aAAM,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,GAAG,GAAG,EAAE,CAAC;YACjC,KAAK,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC;QACxC,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC"}