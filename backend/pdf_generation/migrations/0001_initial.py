# Generated by Django 4.2.7 on 2025-06-09 04:17

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PDFTemplate',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.Char<PERSON>ield(help_text='Arabic name', max_length=200)),
                ('template_type', models.CharField(choices=[('hr_report', 'HR Report'), ('hr_report_ar', 'HR Report (Arabic)'), ('employee_report', 'Employee Report'), ('employee_report_ar', 'Employee Report (Arabic)'), ('attendance_report', 'Attendance Report'), ('attendance_report_ar', 'Attendance Report (Arabic)'), ('leave_report', 'Leave Report'), ('leave_report_ar', 'Leave Report (Arabic)'), ('payroll_report', 'Payroll Report'), ('payroll_report_ar', 'Payroll Report (Arabic)'), ('financial_report', 'Financial Report'), ('financial_report_ar', 'Financial Report (Arabic)'), ('budget_report', 'Budget Report'), ('budget_report_ar', 'Budget Report (Arabic)'), ('expense_report', 'Expense Report'), ('expense_report_ar', 'Expense Report (Arabic)'), ('project_report', 'Project Report'), ('project_report_ar', 'Project Report (Arabic)'), ('task_report', 'Task Report'), ('task_report_ar', 'Task Report (Arabic)'), ('asset_report', 'Asset Report'), ('asset_report_ar', 'Asset Report (Arabic)'), ('inventory_report', 'Inventory Report'), ('inventory_report_ar', 'Inventory Report (Arabic)'), ('supplier_report', 'Supplier Report'), ('supplier_report_ar', 'Supplier Report (Arabic)'), ('purchase_order', 'Purchase Order'), ('purchase_order_ar', 'Purchase Order (Arabic)'), ('sales_report', 'Sales Report'), ('sales_report_ar', 'Sales Report (Arabic)'), ('customer_report', 'Customer Report'), ('customer_report_ar', 'Customer Report (Arabic)'), ('quotation', 'Quotation'), ('quotation_ar', 'Quotation (Arabic)'), ('invoice', 'Invoice'), ('invoice_ar', 'Invoice (Arabic)'), ('kpi_report', 'KPI Report'), ('kpi_report_ar', 'KPI Report (Arabic)'), ('performance_report', 'Performance Report'), ('performance_report_ar', 'Performance Report (Arabic)'), ('department_report', 'Department Report'), ('department_report_ar', 'Department Report (Arabic)'), ('meeting_report', 'Meeting Report'), ('meeting_report_ar', 'Meeting Report (Arabic)'), ('announcement_report', 'Announcement Report'), ('announcement_report_ar', 'Announcement Report (Arabic)'), ('document_report', 'Document Report'), ('document_report_ar', 'Document Report (Arabic)'), ('support_ticket_report', 'Support Ticket Report'), ('support_ticket_report_ar', 'Support Ticket Report (Arabic)'), ('customer_feedback_report', 'Customer Feedback Report'), ('customer_feedback_report_ar', 'Customer Feedback Report (Arabic)'), ('analytics_report', 'Analytics Report'), ('analytics_report_ar', 'Analytics Report (Arabic)'), ('custom_report', 'Custom Report'), ('custom_report_ar', 'Custom Report (Arabic)')], max_length=50)),
                ('language', models.CharField(choices=[('en', 'English'), ('ar', 'Arabic')], default='en', max_length=2)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True, help_text='Arabic description')),
                ('html_template', models.TextField(help_text='HTML template content')),
                ('css_styles', models.TextField(blank=True, help_text='Custom CSS styles')),
                ('page_size', models.CharField(choices=[('A4', 'A4'), ('A3', 'A3'), ('Letter', 'Letter'), ('Legal', 'Legal')], default='A4', max_length=10)),
                ('orientation', models.CharField(choices=[('portrait', 'Portrait'), ('landscape', 'Landscape')], default='portrait', max_length=10)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('version', models.PositiveIntegerField(default=1)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_pdf_templates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['template_type', 'language', 'name'],
                'unique_together': {('template_type', 'language')},
            },
        ),
        migrations.CreateModel(
            name='PDFSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('default_font_en', models.CharField(choices=[('dejavu', 'DejaVu Sans'), ('noto', 'Noto Sans'), ('amiri', 'Amiri (Arabic)'), ('cairo', 'Cairo (Arabic)'), ('tajawal', 'Tajawal (Arabic)')], default='dejavu', max_length=50)),
                ('default_font_ar', models.CharField(choices=[('dejavu', 'DejaVu Sans'), ('noto', 'Noto Sans'), ('amiri', 'Amiri (Arabic)'), ('cairo', 'Cairo (Arabic)'), ('tajawal', 'Tajawal (Arabic)')], default='amiri', max_length=50)),
                ('font_size', models.PositiveIntegerField(default=12, validators=[django.core.validators.MinValueValidator(8), django.core.validators.MaxValueValidator(24)])),
                ('margin_top', models.FloatField(default=2.0, help_text='Top margin in cm')),
                ('margin_bottom', models.FloatField(default=2.0, help_text='Bottom margin in cm')),
                ('margin_left', models.FloatField(default=2.0, help_text='Left margin in cm')),
                ('margin_right', models.FloatField(default=2.0, help_text='Right margin in cm')),
                ('include_header', models.BooleanField(default=True)),
                ('include_footer', models.BooleanField(default=True)),
                ('header_text', models.CharField(default='Enterprise Management System', max_length=200)),
                ('header_text_ar', models.CharField(default='نظام إدارة المؤسسات', max_length=200)),
                ('footer_text', models.CharField(default='Generated on {date}', max_length=200)),
                ('footer_text_ar', models.CharField(default='تم الإنشاء في {date}', max_length=200)),
                ('company_logo', models.ImageField(blank=True, null=True, upload_to='pdf_assets/')),
                ('company_name', models.CharField(default='Your Company', max_length=200)),
                ('company_name_ar', models.CharField(default='شركتكم', max_length=200)),
                ('company_address', models.TextField(blank=True)),
                ('company_address_ar', models.TextField(blank=True)),
                ('max_records_per_pdf', models.PositiveIntegerField(default=1000, help_text='Maximum records per PDF')),
                ('enable_compression', models.BooleanField(default=True)),
                ('quality_level', models.PositiveIntegerField(default=85, help_text='PDF quality level (1-100)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(100)])),
                ('storage_path', models.CharField(default='generated_pdfs/', max_length=500)),
                ('auto_cleanup_days', models.PositiveIntegerField(default=30, help_text='Auto-delete PDFs after N days')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('updated_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pdf_settings_updates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'PDF Settings',
                'verbose_name_plural': 'PDF Settings',
            },
        ),
        migrations.CreateModel(
            name='PDFGenerationHistory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('filename', models.CharField(max_length=255)),
                ('file_path', models.CharField(blank=True, max_length=500)),
                ('file_size', models.PositiveIntegerField(blank=True, help_text='File size in bytes', null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('progress', models.PositiveIntegerField(default=0, help_text='Generation progress percentage', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('processing_time', models.DurationField(blank=True, null=True)),
                ('generation_parameters', models.JSONField(default=dict, help_text='Parameters used for generation')),
                ('data_source', models.CharField(help_text="Source of data (e.g., 'employees', 'projects')", max_length=100)),
                ('record_count', models.PositiveIntegerField(default=0, help_text='Number of records processed')),
                ('error_message', models.TextField(blank=True)),
                ('error_details', models.JSONField(blank=True, default=dict)),
                ('download_count', models.PositiveIntegerField(default=0)),
                ('last_downloaded', models.DateTimeField(blank=True, null=True)),
                ('generated_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pdf_generations', to=settings.AUTH_USER_MODEL)),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='generations', to='pdf_generation.pdftemplate')),
            ],
            options={
                'verbose_name': 'PDF Generation History',
                'verbose_name_plural': 'PDF Generation Histories',
                'ordering': ['-started_at'],
            },
        ),
    ]
