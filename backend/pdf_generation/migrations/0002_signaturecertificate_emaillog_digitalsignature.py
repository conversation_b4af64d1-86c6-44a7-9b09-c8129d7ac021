# Generated by Django 4.2.7 on 2025-06-10 07:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('pdf_generation', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SignatureCertificate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('certificate_name', models.CharField(max_length=200)),
                ('organization', models.CharField(max_length=200)),
                ('country', models.Char<PERSON>ield(default='SA', max_length=2)),
                ('state', models.CharField(max_length=100)),
                ('city', models.Char<PERSON>ield(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('certificate_file', models.<PERSON>r<PERSON><PERSON>(help_text='Path to certificate file', max_length=500)),
                ('private_key_file', models.CharField(help_text='Path to private key file', max_length=500)),
                ('p12_file', models.CharField(help_text='Path to PKCS#12 file', max_length=500)),
                ('p12_password', models.CharField(help_text='PKCS#12 password', max_length=100)),
                ('serial_number', models.CharField(max_length=100, unique=True)),
                ('fingerprint', models.CharField(max_length=200, unique=True)),
                ('valid_from', models.DateTimeField()),
                ('valid_until', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
                ('revoked_at', models.DateTimeField(blank=True, null=True)),
                ('revocation_reason', models.CharField(blank=True, max_length=200)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='signature_certificates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Signature Certificate',
                'verbose_name_plural': 'Signature Certificates',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='EmailLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('recipients', models.TextField(help_text='Comma-separated list of recipients')),
                ('cc_recipients', models.TextField(blank=True, help_text='Comma-separated list of CC recipients')),
                ('bcc_recipients', models.TextField(blank=True, help_text='Comma-separated list of BCC recipients')),
                ('subject', models.CharField(max_length=500)),
                ('status', models.CharField(choices=[('sent', 'Sent'), ('failed', 'Failed'), ('pending', 'Pending')], default='pending', max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('normal', 'Normal'), ('high', 'High'), ('urgent', 'Urgent')], default='normal', max_length=10)),
                ('error_message', models.TextField(blank=True)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('pdf_history', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='email_logs', to='pdf_generation.pdfgenerationhistory')),
                ('sender', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sent_pdf_emails', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Email Log',
                'verbose_name_plural': 'Email Logs',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DigitalSignature',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_path', models.CharField(help_text='Path to original document', max_length=500)),
                ('signed_document_path', models.CharField(help_text='Path to signed document', max_length=500)),
                ('signature_data', models.TextField(help_text='Base64 encoded signature data')),
                ('document_hash', models.CharField(help_text='SHA256 hash of original document', max_length=64)),
                ('reason', models.CharField(default='Document approval', max_length=200)),
                ('location', models.CharField(default='Saudi Arabia', max_length=200)),
                ('contact_info', models.CharField(blank=True, max_length=200)),
                ('signed_at', models.DateTimeField()),
                ('is_valid', models.BooleanField(default=True)),
                ('certificate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='signatures', to='pdf_generation.signaturecertificate')),
            ],
            options={
                'verbose_name': 'Digital Signature',
                'verbose_name_plural': 'Digital Signatures',
                'ordering': ['-signed_at'],
            },
        ),
    ]
