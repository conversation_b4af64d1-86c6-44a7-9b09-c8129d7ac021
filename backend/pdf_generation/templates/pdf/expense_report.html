{% extends "pdf/base.html" %}

{% block content %}
<!-- Summary Statistics -->
<div class="section">
    <h2 class="section-title">
        {% if is_rtl %}
        ملخص المصروفات
        {% else %}
        Expense Summary
        {% endif %}
    </h2>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value">{{ expenses|length|default:0 }}</div>
            <div class="stat-label">
                {% if is_rtl %}
                إجمالي المصروفات
                {% else %}
                Total Expenses
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value">
                {% if expenses %}
                    {{ expenses|length|add:0 }}
                {% else %}
                    0
                {% endif %}
            </div>
            <div class="stat-label">
                {% if is_rtl %}
                المبلغ الإجمالي
                {% else %}
                Total Amount
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value">{{ budgets|length|default:0 }}</div>
            <div class="stat-label">
                {% if is_rtl %}
                الميزانيات المتأثرة
                {% else %}
                Affected Budgets
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Expenses List -->
{% if expenses %}
<div class="section no-break">
    <h2 class="section-title">
        {% if is_rtl %}
        تفاصيل المصروفات
        {% else %}
        Expense Details
        {% endif %}
    </h2>
    
    <table class="table">
        <thead>
            <tr>
                <th>
                    {% if is_rtl %}
                    الوصف
                    {% else %}
                    Description
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    المبلغ
                    {% else %}
                    Amount
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    التاريخ
                    {% else %}
                    Date
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الفئة
                    {% else %}
                    Category
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الموظف
                    {% else %}
                    Employee
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الحالة
                    {% else %}
                    Status
                    {% endif %}
                </th>
            </tr>
        </thead>
        <tbody>
            {% for expense in expenses %}
            <tr>
                <td>
                    {% if is_rtl and expense.description_ar %}
                    {{ expense.description_ar }}
                    {% else %}
                    {{ expense.description }}
                    {% endif %}
                </td>
                <td class="text-center font-semibold">{{ expense.amount|default:0 }}</td>
                <td>{{ expense.date|date:"Y-m-d" }}</td>
                <td>
                    {% if is_rtl and expense.category_ar %}
                    {{ expense.category_ar }}
                    {% else %}
                    {{ expense.category|default:"-" }}
                    {% endif %}
                </td>
                <td>{{ expense.employee.user.get_full_name|default:expense.employee.user.username|default:"-" }}</td>
                <td class="text-center">
                    {% if expense.status == 'APPROVED' %}
                        <span class="text-green font-semibold">
                            {% if is_rtl %}
                            موافق عليه
                            {% else %}
                            Approved
                            {% endif %}
                        </span>
                    {% elif expense.status == 'REJECTED' %}
                        <span class="text-red font-semibold">
                            {% if is_rtl %}
                            مرفوض
                            {% else %}
                            Rejected
                            {% endif %}
                        </span>
                    {% else %}
                        <span class="text-yellow font-semibold">
                            {% if is_rtl %}
                            قيد المراجعة
                            {% else %}
                            Pending
                            {% endif %}
                        </span>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

<!-- Budget Analysis -->
{% if budgets %}
<div class="section page-break">
    <h2 class="section-title">
        {% if is_rtl %}
        تحليل الميزانية
        {% else %}
        Budget Analysis
        {% endif %}
    </h2>
    
    <table class="table">
        <thead>
            <tr>
                <th>
                    {% if is_rtl %}
                    اسم الميزانية
                    {% else %}
                    Budget Name
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    المبلغ المخصص
                    {% else %}
                    Allocated Amount
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    المبلغ المستخدم
                    {% else %}
                    Used Amount
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    المتبقي
                    {% else %}
                    Remaining
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    النسبة المئوية
                    {% else %}
                    Percentage Used
                    {% endif %}
                </th>
            </tr>
        </thead>
        <tbody>
            {% for budget in budgets %}
            <tr>
                <td>
                    {% if is_rtl and budget.name_ar %}
                    {{ budget.name_ar }}
                    {% else %}
                    {{ budget.name }}
                    {% endif %}
                </td>
                <td class="text-center">{{ budget.allocated_amount|default:0 }}</td>
                <td class="text-center">{{ budget.used_amount|default:0 }}</td>
                <td class="text-center">{{ budget.remaining_amount|default:budget.allocated_amount }}</td>
                <td class="text-center">
                    {% if budget.allocated_amount and budget.allocated_amount > 0 %}
                        {% widthratio budget.used_amount budget.allocated_amount 100 %}%
                    {% else %}
                        0%
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

<!-- Monthly Breakdown -->
<div class="section page-break">
    <h2 class="section-title">
        {% if is_rtl %}
        التوزيع الشهري
        {% else %}
        Monthly Breakdown
        {% endif %}
    </h2>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value text-green">
                {% if is_rtl %}
                ريال
                {% else %}
                SAR
                {% endif %}
                0
            </div>
            <div class="stat-label">
                {% if is_rtl %}
                هذا الشهر
                {% else %}
                This Month
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value text-blue">
                {% if is_rtl %}
                ريال
                {% else %}
                SAR
                {% endif %}
                0
            </div>
            <div class="stat-label">
                {% if is_rtl %}
                الشهر الماضي
                {% else %}
                Last Month
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value text-purple">
                {% if is_rtl %}
                ريال
                {% else %}
                SAR
                {% endif %}
                0
            </div>
            <div class="stat-label">
                {% if is_rtl %}
                متوسط شهري
                {% else %}
                Monthly Average
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Expense Categories -->
<div class="section">
    <h2 class="section-title">
        {% if is_rtl %}
        فئات المصروفات
        {% else %}
        Expense Categories
        {% endif %}
    </h2>
    
    <div class="grid grid-cols-2 gap-4">
        <div class="stat-card">
            <div class="stat-label">
                {% if is_rtl %}
                مصروفات تشغيلية
                {% else %}
                Operational Expenses
                {% endif %}
            </div>
            <div class="stat-value text-blue">0</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-label">
                {% if is_rtl %}
                مصروفات إدارية
                {% else %}
                Administrative Expenses
                {% endif %}
            </div>
            <div class="stat-value text-green">0</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-label">
                {% if is_rtl %}
                مصروفات تسويقية
                {% else %}
                Marketing Expenses
                {% endif %}
            </div>
            <div class="stat-value text-purple">0</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-label">
                {% if is_rtl %}
                مصروفات أخرى
                {% else %}
                Other Expenses
                {% endif %}
            </div>
            <div class="stat-value text-orange">0</div>
        </div>
    </div>
</div>
{% endblock %}
