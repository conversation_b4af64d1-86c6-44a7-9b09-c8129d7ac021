{% extends "pdf/base.html" %}

{% block content %}
<!-- KPI Summary -->
<div class="section">
    <h2 class="section-title">
        {% if is_rtl %}
        ملخص مؤشرات الأداء الرئيسية
        {% else %}
        Key Performance Indicators Summary
        {% endif %}
    </h2>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value">{{ kpis|length|default:0 }}</div>
            <div class="stat-label">
                {% if is_rtl %}
                إجمالي المؤشرات
                {% else %}
                Total KPIs
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value text-green">0</div>
            <div class="stat-label">
                {% if is_rtl %}
                مؤشرات تحقق الهدف
                {% else %}
                KPIs Meeting Target
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value text-yellow">0</div>
            <div class="stat-label">
                {% if is_rtl %}
                مؤشرات تحتاج تحسين
                {% else %}
                KPIs Need Improvement
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value text-red">0</div>
            <div class="stat-label">
                {% if is_rtl %}
                مؤشرات دون الهدف
                {% else %}
                KPIs Below Target
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- KPI Performance -->
{% if kpis %}
<div class="section no-break">
    <h2 class="section-title">
        {% if is_rtl %}
        أداء المؤشرات
        {% else %}
        KPI Performance
        {% endif %}
    </h2>
    
    <table class="table">
        <thead>
            <tr>
                <th>
                    {% if is_rtl %}
                    اسم المؤشر
                    {% else %}
                    KPI Name
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الفئة
                    {% else %}
                    Category
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    القيمة الحالية
                    {% else %}
                    Current Value
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الهدف
                    {% else %}
                    Target
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    نسبة الإنجاز
                    {% else %}
                    Achievement %
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الحالة
                    {% else %}
                    Status
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    المسؤول
                    {% else %}
                    Owner
                    {% endif %}
                </th>
            </tr>
        </thead>
        <tbody>
            {% for kpi in kpis %}
            <tr>
                <td>
                    {% if is_rtl and kpi.name_ar %}
                    {{ kpi.name_ar }}
                    {% else %}
                    {{ kpi.name }}
                    {% endif %}
                </td>
                <td>
                    {% if is_rtl and kpi.category_ar %}
                    {{ kpi.category_ar }}
                    {% else %}
                    {{ kpi.category.name|default:"-" }}
                    {% endif %}
                </td>
                <td class="text-center font-semibold">{{ kpi.current_value|default:0 }}</td>
                <td class="text-center">{{ kpi.target_value|default:0 }}</td>
                <td class="text-center">
                    {% if kpi.target_value and kpi.target_value > 0 %}
                        {% widthratio kpi.current_value kpi.target_value 100 %}%
                    {% else %}
                        0%
                    {% endif %}
                </td>
                <td class="text-center">
                    {% if kpi.current_value >= kpi.target_value %}
                        <span class="text-green font-semibold">
                            {% if is_rtl %}
                            تحقق الهدف
                            {% else %}
                            Target Met
                            {% endif %}
                        </span>
                    {% elif kpi.current_value >= kpi.target_value|mul:0.8 %}
                        <span class="text-yellow font-semibold">
                            {% if is_rtl %}
                            قريب من الهدف
                            {% else %}
                            Near Target
                            {% endif %}
                        </span>
                    {% else %}
                        <span class="text-red font-semibold">
                            {% if is_rtl %}
                            دون الهدف
                            {% else %}
                            Below Target
                            {% endif %}
                        </span>
                    {% endif %}
                </td>
                <td>{{ kpi.owner.user.get_full_name|default:kpi.owner.user.username|default:"-" }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

<!-- KPI Categories Performance -->
<div class="section page-break">
    <h2 class="section-title">
        {% if is_rtl %}
        أداء الفئات
        {% else %}
        Category Performance
        {% endif %}
    </h2>
    
    <table class="table">
        <thead>
            <tr>
                <th>
                    {% if is_rtl %}
                    الفئة
                    {% else %}
                    Category
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    عدد المؤشرات
                    {% else %}
                    KPI Count
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    متوسط الإنجاز
                    {% else %}
                    Average Achievement
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الحالة العامة
                    {% else %}
                    Overall Status
                    {% endif %}
                </th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>
                    {% if is_rtl %}
                    المبيعات
                    {% else %}
                    Sales
                    {% endif %}
                </td>
                <td class="text-center">0</td>
                <td class="text-center">0%</td>
                <td class="text-center">
                    <span class="text-green font-semibold">
                        {% if is_rtl %}
                        جيد
                        {% else %}
                        Good
                        {% endif %}
                    </span>
                </td>
            </tr>
            <tr>
                <td>
                    {% if is_rtl %}
                    الموارد البشرية
                    {% else %}
                    Human Resources
                    {% endif %}
                </td>
                <td class="text-center">0</td>
                <td class="text-center">0%</td>
                <td class="text-center">
                    <span class="text-yellow font-semibold">
                        {% if is_rtl %}
                        متوسط
                        {% else %}
                        Average
                        {% endif %}
                    </span>
                </td>
            </tr>
            <tr>
                <td>
                    {% if is_rtl %}
                    المالية
                    {% else %}
                    Finance
                    {% endif %}
                </td>
                <td class="text-center">0</td>
                <td class="text-center">0%</td>
                <td class="text-center">
                    <span class="text-green font-semibold">
                        {% if is_rtl %}
                        ممتاز
                        {% else %}
                        Excellent
                        {% endif %}
                    </span>
                </td>
            </tr>
        </tbody>
    </table>
</div>

<!-- Trending KPIs -->
{% if kpi_values %}
<div class="section">
    <h2 class="section-title">
        {% if is_rtl %}
        اتجاهات المؤشرات
        {% else %}
        KPI Trends
        {% endif %}
    </h2>
    
    <table class="table">
        <thead>
            <tr>
                <th>
                    {% if is_rtl %}
                    المؤشر
                    {% else %}
                    KPI
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    التاريخ
                    {% else %}
                    Date
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    القيمة
                    {% else %}
                    Value
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    التغيير
                    {% else %}
                    Change
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الاتجاه
                    {% else %}
                    Trend
                    {% endif %}
                </th>
            </tr>
        </thead>
        <tbody>
            {% for value in kpi_values %}
            <tr>
                <td>
                    {% if is_rtl and value.kpi.name_ar %}
                    {{ value.kpi.name_ar }}
                    {% else %}
                    {{ value.kpi.name }}
                    {% endif %}
                </td>
                <td>{{ value.date|date:"Y-m-d" }}</td>
                <td class="text-center font-semibold">{{ value.value }}</td>
                <td class="text-center">{{ value.change|default:0 }}%</td>
                <td class="text-center">
                    {% if value.change > 0 %}
                        <span class="text-green font-semibold">↗</span>
                    {% elif value.change < 0 %}
                        <span class="text-red font-semibold">↘</span>
                    {% else %}
                        <span class="text-gray font-semibold">→</span>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

<!-- Performance Insights -->
<div class="section page-break">
    <h2 class="section-title">
        {% if is_rtl %}
        رؤى الأداء
        {% else %}
        Performance Insights
        {% endif %}
    </h2>
    
    <div class="grid grid-cols-1 gap-4">
        <div class="stat-card bg-green">
            <div class="stat-label text-white">
                {% if is_rtl %}
                أفضل أداء
                {% else %}
                Best Performing
                {% endif %}
            </div>
            <div class="stat-value text-white">
                {% if is_rtl %}
                مؤشر المبيعات الشهرية
                {% else %}
                Monthly Sales KPI
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card bg-yellow">
            <div class="stat-label text-white">
                {% if is_rtl %}
                يحتاج تحسين
                {% else %}
                Needs Improvement
                {% endif %}
            </div>
            <div class="stat-value text-white">
                {% if is_rtl %}
                مؤشر رضا العملاء
                {% else %}
                Customer Satisfaction KPI
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card bg-red">
            <div class="stat-label text-white">
                {% if is_rtl %}
                يتطلب اهتمام عاجل
                {% else %}
                Requires Immediate Attention
                {% endif %}
            </div>
            <div class="stat-value text-white">
                {% if is_rtl %}
                مؤشر الجودة
                {% else %}
                Quality KPI
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recommendations -->
<div class="section">
    <h2 class="section-title">
        {% if is_rtl %}
        التوصيات
        {% else %}
        Recommendations
        {% endif %}
    </h2>
    
    <div class="space-y-4">
        <div class="p-4 border border-blue-200 rounded">
            <h4 class="font-semibold text-blue-800 mb-2">
                {% if is_rtl %}
                تحسين المبيعات
                {% else %}
                Sales Improvement
                {% endif %}
            </h4>
            <p class="text-sm">
                {% if is_rtl %}
                ركز على زيادة معدل التحويل من خلال تحسين عملية المبيعات وتدريب الفريق.
                {% else %}
                Focus on increasing conversion rates through improved sales processes and team training.
                {% endif %}
            </p>
        </div>
        
        <div class="p-4 border border-green-200 rounded">
            <h4 class="font-semibold text-green-800 mb-2">
                {% if is_rtl %}
                الحفاظ على الأداء الجيد
                {% else %}
                Maintain Good Performance
                {% endif %}
            </h4>
            <p class="text-sm">
                {% if is_rtl %}
                استمر في الممارسات الحالية للمؤشرات التي تحقق أهدافها.
                {% else %}
                Continue current practices for KPIs that are meeting their targets.
                {% endif %}
            </p>
        </div>
    </div>
</div>
{% endblock %}
