{% extends "pdf/base.html" %}

{% block content %}
<!-- Sales Summary -->
<div class="section">
    <h2 class="section-title">
        {% if is_rtl %}
        ملخص المبيعات
        {% else %}
        Sales Summary
        {% endif %}
    </h2>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value">{{ orders|length|default:0 }}</div>
            <div class="stat-label">
                {% if is_rtl %}
                إجمالي الطلبات
                {% else %}
                Total Orders
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value">{{ customers|length|default:0 }}</div>
            <div class="stat-label">
                {% if is_rtl %}
                إجمالي العملاء
                {% else %}
                Total Customers
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value text-green">
                {% if is_rtl %}
                ريال
                {% else %}
                SAR
                {% endif %}
                0
            </div>
            <div class="stat-label">
                {% if is_rtl %}
                إجمالي المبيعات
                {% else %}
                Total Revenue
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value text-blue">
                {% if is_rtl %}
                ريال
                {% else %}
                SAR
                {% endif %}
                0
            </div>
            <div class="stat-label">
                {% if is_rtl %}
                متوسط قيمة الطلب
                {% else %}
                Average Order Value
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Sales Orders -->
{% if orders %}
<div class="section no-break">
    <h2 class="section-title">
        {% if is_rtl %}
        طلبات المبيعات
        {% else %}
        Sales Orders
        {% endif %}
    </h2>
    
    <table class="table">
        <thead>
            <tr>
                <th>
                    {% if is_rtl %}
                    رقم الطلب
                    {% else %}
                    Order ID
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    العميل
                    {% else %}
                    Customer
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    التاريخ
                    {% else %}
                    Date
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    المبلغ الإجمالي
                    {% else %}
                    Total Amount
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الحالة
                    {% else %}
                    Status
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الأولوية
                    {% else %}
                    Priority
                    {% endif %}
                </th>
            </tr>
        </thead>
        <tbody>
            {% for order in orders %}
            <tr>
                <td class="font-semibold">{{ order.order_number|default:order.id }}</td>
                <td>
                    {% if is_rtl and order.customer_name_ar %}
                    {{ order.customer_name_ar }}
                    {% else %}
                    {{ order.customer_name|default:"-" }}
                    {% endif %}
                </td>
                <td>{{ order.order_date|date:"Y-m-d"|default:order.created_at|date:"Y-m-d" }}</td>
                <td class="text-center font-semibold text-green">{{ order.total_amount|default:0 }}</td>
                <td class="text-center">
                    {% if order.status == 'COMPLETED' %}
                        <span class="text-green font-semibold">
                            {% if is_rtl %}
                            مكتمل
                            {% else %}
                            Completed
                            {% endif %}
                        </span>
                    {% elif order.status == 'PENDING' %}
                        <span class="text-yellow font-semibold">
                            {% if is_rtl %}
                            قيد الانتظار
                            {% else %}
                            Pending
                            {% endif %}
                        </span>
                    {% elif order.status == 'CANCELLED' %}
                        <span class="text-red font-semibold">
                            {% if is_rtl %}
                            ملغي
                            {% else %}
                            Cancelled
                            {% endif %}
                        </span>
                    {% else %}
                        <span class="text-blue font-semibold">
                            {% if is_rtl %}
                            قيد المعالجة
                            {% else %}
                            Processing
                            {% endif %}
                        </span>
                    {% endif %}
                </td>
                <td class="text-center">
                    {% if order.priority == 'HIGH' %}
                        <span class="text-red font-semibold">
                            {% if is_rtl %}
                            عالية
                            {% else %}
                            High
                            {% endif %}
                        </span>
                    {% elif order.priority == 'MEDIUM' %}
                        <span class="text-yellow font-semibold">
                            {% if is_rtl %}
                            متوسطة
                            {% else %}
                            Medium
                            {% endif %}
                        </span>
                    {% else %}
                        <span class="text-green font-semibold">
                            {% if is_rtl %}
                            منخفضة
                            {% else %}
                            Low
                            {% endif %}
                        </span>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

<!-- Customer Analysis -->
{% if customers %}
<div class="section page-break">
    <h2 class="section-title">
        {% if is_rtl %}
        تحليل العملاء
        {% else %}
        Customer Analysis
        {% endif %}
    </h2>
    
    <table class="table">
        <thead>
            <tr>
                <th>
                    {% if is_rtl %}
                    اسم العميل
                    {% else %}
                    Customer Name
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    البريد الإلكتروني
                    {% else %}
                    Email
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الهاتف
                    {% else %}
                    Phone
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    عدد الطلبات
                    {% else %}
                    Orders Count
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    إجمالي المشتريات
                    {% else %}
                    Total Purchases
                    {% endif %}
                </th>
            </tr>
        </thead>
        <tbody>
            {% for customer in customers %}
            <tr>
                <td>
                    {% if is_rtl and customer.name_ar %}
                    {{ customer.name_ar }}
                    {% else %}
                    {{ customer.name }}
                    {% endif %}
                </td>
                <td>{{ customer.email|default:"-" }}</td>
                <td>{{ customer.phone|default:"-" }}</td>
                <td class="text-center">{{ customer.orders_count|default:0 }}</td>
                <td class="text-center font-semibold text-green">{{ customer.total_purchases|default:0 }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

<!-- Sales Performance -->
<div class="section page-break">
    <h2 class="section-title">
        {% if is_rtl %}
        أداء المبيعات
        {% else %}
        Sales Performance
        {% endif %}
    </h2>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value text-green">0</div>
            <div class="stat-label">
                {% if is_rtl %}
                مبيعات هذا الشهر
                {% else %}
                This Month Sales
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value text-blue">0</div>
            <div class="stat-label">
                {% if is_rtl %}
                مبيعات الشهر الماضي
                {% else %}
                Last Month Sales
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value text-purple">0%</div>
            <div class="stat-label">
                {% if is_rtl %}
                نسبة النمو
                {% else %}
                Growth Rate
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value text-orange">0</div>
            <div class="stat-label">
                {% if is_rtl %}
                الهدف الشهري
                {% else %}
                Monthly Target
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Top Products -->
<div class="section">
    <h2 class="section-title">
        {% if is_rtl %}
        أفضل المنتجات مبيعاً
        {% else %}
        Top Selling Products
        {% endif %}
    </h2>
    
    <div class="grid grid-cols-1 gap-4">
        <div class="stat-card">
            <div class="flex justify-between items-center">
                <div>
                    <div class="stat-label">
                        {% if is_rtl %}
                        منتج 1
                        {% else %}
                        Product 1
                        {% endif %}
                    </div>
                    <div class="stat-value text-sm">0 
                        {% if is_rtl %}
                        وحدة
                        {% else %}
                        units
                        {% endif %}
                    </div>
                </div>
                <div class="text-green font-semibold">
                    {% if is_rtl %}
                    ريال
                    {% else %}
                    SAR
                    {% endif %}
                    0
                </div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="flex justify-between items-center">
                <div>
                    <div class="stat-label">
                        {% if is_rtl %}
                        منتج 2
                        {% else %}
                        Product 2
                        {% endif %}
                    </div>
                    <div class="stat-value text-sm">0 
                        {% if is_rtl %}
                        وحدة
                        {% else %}
                        units
                        {% endif %}
                    </div>
                </div>
                <div class="text-green font-semibold">
                    {% if is_rtl %}
                    ريال
                    {% else %}
                    SAR
                    {% endif %}
                    0
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
