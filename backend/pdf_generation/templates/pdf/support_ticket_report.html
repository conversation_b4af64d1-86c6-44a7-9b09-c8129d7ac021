{% extends "pdf/base.html" %}

{% block content %}
<!-- Support Tickets Summary -->
<div class="section">
    <h2 class="section-title">
        {% if is_rtl %}
        ملخص تذاكر الدعم
        {% else %}
        Support Tickets Summary
        {% endif %}
    </h2>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value">{{ tickets|length|default:0 }}</div>
            <div class="stat-label">
                {% if is_rtl %}
                إجمالي التذاكر
                {% else %}
                Total Tickets
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value text-green">0</div>
            <div class="stat-label">
                {% if is_rtl %}
                تذاكر محلولة
                {% else %}
                Resolved Tickets
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value text-yellow">0</div>
            <div class="stat-label">
                {% if is_rtl %}
                تذاكر قيد المعالجة
                {% else %}
                Pending Tickets
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value text-red">0</div>
            <div class="stat-label">
                {% if is_rtl %}
                تذاكر عالية الأولوية
                {% else %}
                High Priority Tickets
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Tickets List -->
{% if tickets %}
<div class="section no-break">
    <h2 class="section-title">
        {% if is_rtl %}
        تفاصيل التذاكر
        {% else %}
        Ticket Details
        {% endif %}
    </h2>
    
    <table class="table">
        <thead>
            <tr>
                <th>
                    {% if is_rtl %}
                    رقم التذكرة
                    {% else %}
                    Ticket ID
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    العنوان
                    {% else %}
                    Subject
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    العميل
                    {% else %}
                    Customer
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الأولوية
                    {% else %}
                    Priority
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الحالة
                    {% else %}
                    Status
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الوكيل المسؤول
                    {% else %}
                    Assigned Agent
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    تاريخ الإنشاء
                    {% else %}
                    Created Date
                    {% endif %}
                </th>
            </tr>
        </thead>
        <tbody>
            {% for ticket in tickets %}
            <tr>
                <td class="font-semibold">#{{ ticket.id }}</td>
                <td>
                    {% if is_rtl and ticket.subject_ar %}
                    {{ ticket.subject_ar }}
                    {% else %}
                    {{ ticket.subject }}
                    {% endif %}
                </td>
                <td>
                    {% if is_rtl and ticket.customerNameAr %}
                    {{ ticket.customerNameAr }}
                    {% else %}
                    {{ ticket.customerName }}
                    {% endif %}
                </td>
                <td class="text-center">
                    {% if ticket.priority == 'critical' %}
                        <span class="text-red font-semibold">
                            {% if is_rtl %}
                            حرجة
                            {% else %}
                            Critical
                            {% endif %}
                        </span>
                    {% elif ticket.priority == 'high' %}
                        <span class="text-orange font-semibold">
                            {% if is_rtl %}
                            عالية
                            {% else %}
                            High
                            {% endif %}
                        </span>
                    {% elif ticket.priority == 'medium' %}
                        <span class="text-yellow font-semibold">
                            {% if is_rtl %}
                            متوسطة
                            {% else %}
                            Medium
                            {% endif %}
                        </span>
                    {% else %}
                        <span class="text-green font-semibold">
                            {% if is_rtl %}
                            منخفضة
                            {% else %}
                            Low
                            {% endif %}
                        </span>
                    {% endif %}
                </td>
                <td class="text-center">
                    {% if ticket.status == 'resolved' %}
                        <span class="text-green font-semibold">
                            {% if is_rtl %}
                            محلول
                            {% else %}
                            Resolved
                            {% endif %}
                        </span>
                    {% elif ticket.status == 'in_progress' %}
                        <span class="text-blue font-semibold">
                            {% if is_rtl %}
                            قيد المعالجة
                            {% else %}
                            In Progress
                            {% endif %}
                        </span>
                    {% elif ticket.status == 'closed' %}
                        <span class="text-gray font-semibold">
                            {% if is_rtl %}
                            مغلق
                            {% else %}
                            Closed
                            {% endif %}
                        </span>
                    {% else %}
                        <span class="text-yellow font-semibold">
                            {% if is_rtl %}
                            جديد
                            {% else %}
                            New
                            {% endif %}
                        </span>
                    {% endif %}
                </td>
                <td>
                    {% if is_rtl and ticket.assignedAgentAr %}
                    {{ ticket.assignedAgentAr }}
                    {% else %}
                    {{ ticket.assignedAgent|default:"-" }}
                    {% endif %}
                </td>
                <td>{{ ticket.createdAt|date:"Y-m-d H:i"|default:ticket.created_at|date:"Y-m-d H:i" }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

<!-- Performance Metrics -->
<div class="section page-break">
    <h2 class="section-title">
        {% if is_rtl %}
        مقاييس الأداء
        {% else %}
        Performance Metrics
        {% endif %}
    </h2>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value text-green">0</div>
            <div class="stat-label">
                {% if is_rtl %}
                متوسط وقت الاستجابة (ساعات)
                {% else %}
                Avg Response Time (hours)
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value text-blue">0</div>
            <div class="stat-label">
                {% if is_rtl %}
                متوسط وقت الحل (ساعات)
                {% else %}
                Avg Resolution Time (hours)
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value text-purple">0%</div>
            <div class="stat-label">
                {% if is_rtl %}
                معدل رضا العملاء
                {% else %}
                Customer Satisfaction Rate
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value text-orange">0%</div>
            <div class="stat-label">
                {% if is_rtl %}
                معدل الحل من المرة الأولى
                {% else %}
                First Contact Resolution Rate
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Category Analysis -->
<div class="section">
    <h2 class="section-title">
        {% if is_rtl %}
        تحليل الفئات
        {% else %}
        Category Analysis
        {% endif %}
    </h2>
    
    <table class="table">
        <thead>
            <tr>
                <th>
                    {% if is_rtl %}
                    الفئة
                    {% else %}
                    Category
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    عدد التذاكر
                    {% else %}
                    Ticket Count
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    متوسط وقت الحل
                    {% else %}
                    Avg Resolution Time
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    معدل الرضا
                    {% else %}
                    Satisfaction Rate
                    {% endif %}
                </th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>
                    {% if is_rtl %}
                    دعم تقني
                    {% else %}
                    Technical Support
                    {% endif %}
                </td>
                <td class="text-center">0</td>
                <td class="text-center">0 
                    {% if is_rtl %}
                    ساعة
                    {% else %}
                    hours
                    {% endif %}
                </td>
                <td class="text-center">0%</td>
            </tr>
            <tr>
                <td>
                    {% if is_rtl %}
                    استفسارات عامة
                    {% else %}
                    General Inquiries
                    {% endif %}
                </td>
                <td class="text-center">0</td>
                <td class="text-center">0 
                    {% if is_rtl %}
                    ساعة
                    {% else %}
                    hours
                    {% endif %}
                </td>
                <td class="text-center">0%</td>
            </tr>
            <tr>
                <td>
                    {% if is_rtl %}
                    شكاوى
                    {% else %}
                    Complaints
                    {% endif %}
                </td>
                <td class="text-center">0</td>
                <td class="text-center">0 
                    {% if is_rtl %}
                    ساعة
                    {% else %}
                    hours
                    {% endif %}
                </td>
                <td class="text-center">0%</td>
            </tr>
        </tbody>
    </table>
</div>

<!-- Agent Performance -->
<div class="section page-break">
    <h2 class="section-title">
        {% if is_rtl %}
        أداء الوكلاء
        {% else %}
        Agent Performance
        {% endif %}
    </h2>
    
    <table class="table">
        <thead>
            <tr>
                <th>
                    {% if is_rtl %}
                    اسم الوكيل
                    {% else %}
                    Agent Name
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    التذاكر المعينة
                    {% else %}
                    Assigned Tickets
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    التذاكر المحلولة
                    {% else %}
                    Resolved Tickets
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    معدل الحل
                    {% else %}
                    Resolution Rate
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    تقييم العملاء
                    {% else %}
                    Customer Rating
                    {% endif %}
                </th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>
                    {% if is_rtl %}
                    وكيل 1
                    {% else %}
                    Agent 1
                    {% endif %}
                </td>
                <td class="text-center">0</td>
                <td class="text-center">0</td>
                <td class="text-center">0%</td>
                <td class="text-center">0/5</td>
            </tr>
            <tr>
                <td>
                    {% if is_rtl %}
                    وكيل 2
                    {% else %}
                    Agent 2
                    {% endif %}
                </td>
                <td class="text-center">0</td>
                <td class="text-center">0</td>
                <td class="text-center">0%</td>
                <td class="text-center">0/5</td>
            </tr>
        </tbody>
    </table>
</div>
{% endblock %}
