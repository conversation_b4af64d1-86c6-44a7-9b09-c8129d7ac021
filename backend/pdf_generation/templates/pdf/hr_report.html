{% extends "pdf/base.html" %}

{% block content %}
<!-- Summary Statistics -->
<div class="section">
    <h2 class="section-title">
        {% if is_rtl %}
        إحصائيات عامة
        {% else %}
        Summary Statistics
        {% endif %}
    </h2>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value">{{ total_employees|default:0 }}</div>
            <div class="stat-label">
                {% if is_rtl %}
                إجمالي الموظفين
                {% else %}
                Total Employees
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value">{{ total_departments|default:0 }}</div>
            <div class="stat-label">
                {% if is_rtl %}
                إجمالي الأقسام
                {% else %}
                Total Departments
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value">{{ attendance_data|length|default:0 }}</div>
            <div class="stat-label">
                {% if is_rtl %}
                سجلات الحضور (30 يوم)
                {% else %}
                Attendance Records (30 days)
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value">{{ leave_requests|length|default:0 }}</div>
            <div class="stat-label">
                {% if is_rtl %}
                طلبات الإجازة (30 يوم)
                {% else %}
                Leave Requests (30 days)
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Employees List -->
{% if employees %}
<div class="section no-break">
    <h2 class="section-title">
        {% if is_rtl %}
        قائمة الموظفين
        {% else %}
        Employee List
        {% endif %}
    </h2>
    
    <table class="table">
        <thead>
            <tr>
                <th>
                    {% if is_rtl %}
                    رقم الموظف
                    {% else %}
                    Employee ID
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الاسم
                    {% else %}
                    Name
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    المنصب
                    {% else %}
                    Position
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    القسم
                    {% else %}
                    Department
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    تاريخ التوظيف
                    {% else %}
                    Hire Date
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الحالة
                    {% else %}
                    Status
                    {% endif %}
                </th>
            </tr>
        </thead>
        <tbody>
            {% for employee in employees %}
            <tr>
                <td>{{ employee.employee_id }}</td>
                <td>{{ employee.user.get_full_name|default:employee.user.username }}</td>
                <td>
                    {% if is_rtl and employee.position_ar %}
                    {{ employee.position_ar }}
                    {% else %}
                    {{ employee.position }}
                    {% endif %}
                </td>
                <td>
                    {% if employee.department %}
                        {% if is_rtl and employee.department.name_ar %}
                        {{ employee.department.name_ar }}
                        {% else %}
                        {{ employee.department.name }}
                        {% endif %}
                    {% else %}
                    -
                    {% endif %}
                </td>
                <td>{{ employee.hire_date|date:"Y-m-d" }}</td>
                <td>
                    {% if employee.employment_status == 'FULL_TIME' %}
                        {% if is_rtl %}
                        دوام كامل
                        {% else %}
                        Full Time
                        {% endif %}
                    {% elif employee.employment_status == 'PART_TIME' %}
                        {% if is_rtl %}
                        دوام جزئي
                        {% else %}
                        Part Time
                        {% endif %}
                    {% elif employee.employment_status == 'CONTRACT' %}
                        {% if is_rtl %}
                        عقد
                        {% else %}
                        Contract
                        {% endif %}
                    {% else %}
                        {{ employee.employment_status }}
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

<!-- Departments Summary -->
{% if departments %}
<div class="section page-break">
    <h2 class="section-title">
        {% if is_rtl %}
        ملخص الأقسام
        {% else %}
        Departments Summary
        {% endif %}
    </h2>
    
    <table class="table">
        <thead>
            <tr>
                <th>
                    {% if is_rtl %}
                    اسم القسم
                    {% else %}
                    Department Name
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    عدد الموظفين
                    {% else %}
                    Employee Count
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الوصف
                    {% else %}
                    Description
                    {% endif %}
                </th>
            </tr>
        </thead>
        <tbody>
            {% for department in departments %}
            <tr>
                <td>
                    {% if is_rtl and department.name_ar %}
                    {{ department.name_ar }}
                    {% else %}
                    {{ department.name }}
                    {% endif %}
                </td>
                <td class="text-center">{{ department.employee_count }}</td>
                <td>
                    {% if is_rtl and department.description_ar %}
                    {{ department.description_ar }}
                    {% else %}
                    {{ department.description|default:"-" }}
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

<!-- Recent Attendance -->
{% if attendance_data %}
<div class="section page-break">
    <h2 class="section-title">
        {% if is_rtl %}
        سجلات الحضور الحديثة
        {% else %}
        Recent Attendance Records
        {% endif %}
    </h2>
    
    <table class="table">
        <thead>
            <tr>
                <th>
                    {% if is_rtl %}
                    الموظف
                    {% else %}
                    Employee
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    التاريخ
                    {% else %}
                    Date
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    وقت الدخول
                    {% else %}
                    Check In
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    وقت الخروج
                    {% else %}
                    Check Out
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    إجمالي الساعات
                    {% else %}
                    Total Hours
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الحالة
                    {% else %}
                    Status
                    {% endif %}
                </th>
            </tr>
        </thead>
        <tbody>
            {% for attendance in attendance_data %}
            <tr>
                <td>{{ attendance.employee.user.get_full_name|default:attendance.employee.user.username }}</td>
                <td>{{ attendance.date|date:"Y-m-d" }}</td>
                <td>{{ attendance.check_in_time|time:"H:i"|default:"-" }}</td>
                <td>{{ attendance.check_out_time|time:"H:i"|default:"-" }}</td>
                <td class="text-center">{{ attendance.total_hours|default:"-" }}</td>
                <td class="text-center">
                    {% if attendance.is_present %}
                        <span class="text-green font-semibold">
                            {% if is_rtl %}
                            حاضر
                            {% else %}
                            Present
                            {% endif %}
                        </span>
                    {% else %}
                        <span class="text-red font-semibold">
                            {% if is_rtl %}
                            غائب
                            {% else %}
                            Absent
                            {% endif %}
                        </span>
                    {% endif %}
                    {% if attendance.is_late %}
                        <span class="text-yellow">
                            {% if is_rtl %}
                            (متأخر)
                            {% else %}
                            (Late)
                            {% endif %}
                        </span>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

<!-- Recent Leave Requests -->
{% if leave_requests %}
<div class="section page-break">
    <h2 class="section-title">
        {% if is_rtl %}
        طلبات الإجازة الحديثة
        {% else %}
        Recent Leave Requests
        {% endif %}
    </h2>
    
    <table class="table">
        <thead>
            <tr>
                <th>
                    {% if is_rtl %}
                    الموظف
                    {% else %}
                    Employee
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    نوع الإجازة
                    {% else %}
                    Leave Type
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    تاريخ البداية
                    {% else %}
                    Start Date
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    تاريخ النهاية
                    {% else %}
                    End Date
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الأيام
                    {% else %}
                    Days
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الحالة
                    {% else %}
                    Status
                    {% endif %}
                </th>
            </tr>
        </thead>
        <tbody>
            {% for leave in leave_requests %}
            <tr>
                <td>{{ leave.employee.user.get_full_name|default:leave.employee.user.username }}</td>
                <td>
                    {% if is_rtl and leave.leave_type.name_ar %}
                    {{ leave.leave_type.name_ar }}
                    {% else %}
                    {{ leave.leave_type.name }}
                    {% endif %}
                </td>
                <td>{{ leave.start_date|date:"Y-m-d" }}</td>
                <td>{{ leave.end_date|date:"Y-m-d" }}</td>
                <td class="text-center">{{ leave.days_requested }}</td>
                <td class="text-center">
                    {% if leave.status == 'APPROVED' %}
                        <span class="text-green font-semibold">
                            {% if is_rtl %}
                            موافق عليها
                            {% else %}
                            Approved
                            {% endif %}
                        </span>
                    {% elif leave.status == 'REJECTED' %}
                        <span class="text-red font-semibold">
                            {% if is_rtl %}
                            مرفوضة
                            {% else %}
                            Rejected
                            {% endif %}
                        </span>
                    {% else %}
                        <span class="text-yellow font-semibold">
                            {% if is_rtl %}
                            قيد المراجعة
                            {% else %}
                            Pending
                            {% endif %}
                        </span>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}
{% endblock %}
