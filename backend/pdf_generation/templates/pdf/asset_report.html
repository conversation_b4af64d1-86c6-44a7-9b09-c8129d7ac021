{% extends "pdf/base.html" %}

{% block content %}
<!-- Asset Summary -->
<div class="section">
    <h2 class="section-title">
        {% if is_rtl %}
        ملخص الأصول
        {% else %}
        Asset Summary
        {% endif %}
    </h2>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value">{{ assets|length|default:0 }}</div>
            <div class="stat-label">
                {% if is_rtl %}
                إجمالي الأصول
                {% else %}
                Total Assets
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value text-green">0</div>
            <div class="stat-label">
                {% if is_rtl %}
                أصول متاحة
                {% else %}
                Available Assets
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value text-blue">0</div>
            <div class="stat-label">
                {% if is_rtl %}
                أصول قيد الاستخدام
                {% else %}
                Assets In Use
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value text-yellow">0</div>
            <div class="stat-label">
                {% if is_rtl %}
                أصول تحت الصيانة
                {% else %}
                Assets Under Maintenance
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Assets List -->
{% if assets %}
<div class="section no-break">
    <h2 class="section-title">
        {% if is_rtl %}
        تفاصيل الأصول
        {% else %}
        Asset Details
        {% endif %}
    </h2>
    
    <table class="table">
        <thead>
            <tr>
                <th>
                    {% if is_rtl %}
                    رقم الأصل
                    {% else %}
                    Asset ID
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    اسم الأصل
                    {% else %}
                    Asset Name
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الفئة
                    {% else %}
                    Category
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الحالة
                    {% else %}
                    Status
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    تاريخ الشراء
                    {% else %}
                    Purchase Date
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    سعر الشراء
                    {% else %}
                    Purchase Price
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    القيمة الحالية
                    {% else %}
                    Current Value
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    المسؤول
                    {% else %}
                    Assigned To
                    {% endif %}
                </th>
            </tr>
        </thead>
        <tbody>
            {% for asset in assets %}
            <tr>
                <td class="font-semibold">{{ asset.asset_id|default:asset.id }}</td>
                <td>
                    {% if is_rtl and asset.name_ar %}
                    {{ asset.name_ar }}
                    {% else %}
                    {{ asset.name }}
                    {% endif %}
                </td>
                <td>
                    {% if is_rtl and asset.category_ar %}
                    {{ asset.category_ar }}
                    {% else %}
                    {{ asset.category }}
                    {% endif %}
                </td>
                <td class="text-center">
                    {% if asset.status == 'available' %}
                        <span class="text-green font-semibold">
                            {% if is_rtl %}
                            متاح
                            {% else %}
                            Available
                            {% endif %}
                        </span>
                    {% elif asset.status == 'inUse' %}
                        <span class="text-blue font-semibold">
                            {% if is_rtl %}
                            قيد الاستخدام
                            {% else %}
                            In Use
                            {% endif %}
                        </span>
                    {% elif asset.status == 'maintenance' %}
                        <span class="text-yellow font-semibold">
                            {% if is_rtl %}
                            تحت الصيانة
                            {% else %}
                            Maintenance
                            {% endif %}
                        </span>
                    {% else %}
                        <span class="text-red font-semibold">
                            {% if is_rtl %}
                            متقاعد
                            {% else %}
                            Retired
                            {% endif %}
                        </span>
                    {% endif %}
                </td>
                <td>{{ asset.purchaseDate|date:"Y-m-d"|default:"-" }}</td>
                <td class="text-center">{{ asset.purchasePrice|default:0 }}</td>
                <td class="text-center">{{ asset.currentValue|default:asset.purchasePrice|default:0 }}</td>
                <td>{{ asset.assignedTo|default:"-" }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

<!-- Asset Categories -->
<div class="section page-break">
    <h2 class="section-title">
        {% if is_rtl %}
        فئات الأصول
        {% else %}
        Asset Categories
        {% endif %}
    </h2>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value text-blue">0</div>
            <div class="stat-label">
                {% if is_rtl %}
                معدات تقنية
                {% else %}
                IT Equipment
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value text-green">0</div>
            <div class="stat-label">
                {% if is_rtl %}
                أثاث مكتبي
                {% else %}
                Office Furniture
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value text-purple">0</div>
            <div class="stat-label">
                {% if is_rtl %}
                مركبات
                {% else %}
                Vehicles
                {% endif %}
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value text-orange">0</div>
            <div class="stat-label">
                {% if is_rtl %}
                معدات أخرى
                {% else %}
                Other Equipment
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Asset Valuation -->
<div class="section">
    <h2 class="section-title">
        {% if is_rtl %}
        تقييم الأصول
        {% else %}
        Asset Valuation
        {% endif %}
    </h2>
    
    <table class="table">
        <thead>
            <tr>
                <th>
                    {% if is_rtl %}
                    الفئة
                    {% else %}
                    Category
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    عدد الأصول
                    {% else %}
                    Asset Count
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    إجمالي سعر الشراء
                    {% else %}
                    Total Purchase Price
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    إجمالي القيمة الحالية
                    {% else %}
                    Total Current Value
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الاستهلاك
                    {% else %}
                    Depreciation
                    {% endif %}
                </th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>
                    {% if is_rtl %}
                    معدات تقنية
                    {% else %}
                    IT Equipment
                    {% endif %}
                </td>
                <td class="text-center">0</td>
                <td class="text-center">0</td>
                <td class="text-center">0</td>
                <td class="text-center">0%</td>
            </tr>
            <tr>
                <td>
                    {% if is_rtl %}
                    أثاث مكتبي
                    {% else %}
                    Office Furniture
                    {% endif %}
                </td>
                <td class="text-center">0</td>
                <td class="text-center">0</td>
                <td class="text-center">0</td>
                <td class="text-center">0%</td>
            </tr>
        </tbody>
    </table>
</div>

<!-- Maintenance Schedule -->
<div class="section page-break">
    <h2 class="section-title">
        {% if is_rtl %}
        جدول الصيانة
        {% else %}
        Maintenance Schedule
        {% endif %}
    </h2>
    
    <table class="table">
        <thead>
            <tr>
                <th>
                    {% if is_rtl %}
                    الأصل
                    {% else %}
                    Asset
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    نوع الصيانة
                    {% else %}
                    Maintenance Type
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    التاريخ المجدول
                    {% else %}
                    Scheduled Date
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    الحالة
                    {% else %}
                    Status
                    {% endif %}
                </th>
                <th>
                    {% if is_rtl %}
                    التكلفة المقدرة
                    {% else %}
                    Estimated Cost
                    {% endif %}
                </th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>
                    {% if is_rtl %}
                    لا توجد صيانة مجدولة
                    {% else %}
                    No scheduled maintenance
                    {% endif %}
                </td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
            </tr>
        </tbody>
    </table>
</div>
{% endblock %}
