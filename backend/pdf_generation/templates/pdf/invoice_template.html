{% extends "pdf/base.html" %}

{% block content %}
<!-- Invoice Header -->
<div class="section">
    <div class="flex justify-between items-start mb-6">
        <div>
            {% if settings.company_logo %}
            <img src="{{ settings.company_logo.url }}" alt="Company Logo" style="height: 80px; margin-bottom: 20px;">
            {% endif %}
            <h1 class="text-3xl font-bold text-blue-800">
                {% if is_rtl %}
                فاتورة
                {% else %}
                INVOICE
                {% endif %}
            </h1>
        </div>
        <div class="text-right">
            <div class="text-lg font-semibold">
                {% if is_rtl %}
                رقم الفاتورة: {{ invoice.invoice_number|default:"INV-001" }}
                {% else %}
                Invoice #: {{ invoice.invoice_number|default:"INV-001" }}
                {% endif %}
            </div>
            <div class="text-sm text-gray-600">
                {% if is_rtl %}
                التاريخ: {{ invoice.date|date:"Y-m-d"|default:generated_at|date:"Y-m-d" }}
                {% else %}
                Date: {{ invoice.date|date:"Y-m-d"|default:generated_at|date:"Y-m-d" }}
                {% endif %}
            </div>
            <div class="text-sm text-gray-600">
                {% if is_rtl %}
                تاريخ الاستحقاق: {{ invoice.due_date|date:"Y-m-d"|default:"-" }}
                {% else %}
                Due Date: {{ invoice.due_date|date:"Y-m-d"|default:"-" }}
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Company & Customer Information -->
<div class="section">
    <div class="grid grid-cols-2 gap-8 mb-6">
        <!-- From (Company) -->
        <div>
            <h3 class="font-semibold text-gray-800 mb-3">
                {% if is_rtl %}
                من:
                {% else %}
                From:
                {% endif %}
            </h3>
            <div class="text-sm">
                <div class="font-semibold">
                    {% if is_rtl %}
                    {{ settings.company_name_ar|default:settings.company_name }}
                    {% else %}
                    {{ settings.company_name }}
                    {% endif %}
                </div>
                <div class="mt-2">
                    {% if is_rtl %}
                    {{ settings.company_address_ar|default:settings.company_address|linebreaks }}
                    {% else %}
                    {{ settings.company_address|linebreaks }}
                    {% endif %}
                </div>
                {% if invoice.company_tax_id %}
                <div class="mt-2">
                    {% if is_rtl %}
                    الرقم الضريبي: {{ invoice.company_tax_id }}
                    {% else %}
                    Tax ID: {{ invoice.company_tax_id }}
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- To (Customer) -->
        <div>
            <h3 class="font-semibold text-gray-800 mb-3">
                {% if is_rtl %}
                إلى:
                {% else %}
                To:
                {% endif %}
            </h3>
            <div class="text-sm">
                <div class="font-semibold">
                    {% if is_rtl and invoice.customer_name_ar %}
                    {{ invoice.customer_name_ar }}
                    {% else %}
                    {{ invoice.customer_name|default:"Customer Name" }}
                    {% endif %}
                </div>
                {% if invoice.customer_address %}
                <div class="mt-2">
                    {% if is_rtl and invoice.customer_address_ar %}
                    {{ invoice.customer_address_ar|linebreaks }}
                    {% else %}
                    {{ invoice.customer_address|linebreaks }}
                    {% endif %}
                </div>
                {% endif %}
                {% if invoice.customer_email %}
                <div class="mt-2">
                    {% if is_rtl %}
                    البريد الإلكتروني: {{ invoice.customer_email }}
                    {% else %}
                    Email: {{ invoice.customer_email }}
                    {% endif %}
                </div>
                {% endif %}
                {% if invoice.customer_phone %}
                <div class="mt-2">
                    {% if is_rtl %}
                    الهاتف: {{ invoice.customer_phone }}
                    {% else %}
                    Phone: {{ invoice.customer_phone }}
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Invoice Items -->
<div class="section">
    <table class="table">
        <thead>
            <tr class="bg-blue-50">
                <th class="text-left">
                    {% if is_rtl %}
                    الوصف
                    {% else %}
                    Description
                    {% endif %}
                </th>
                <th class="text-center">
                    {% if is_rtl %}
                    الكمية
                    {% else %}
                    Qty
                    {% endif %}
                </th>
                <th class="text-center">
                    {% if is_rtl %}
                    سعر الوحدة
                    {% else %}
                    Unit Price
                    {% endif %}
                </th>
                <th class="text-center">
                    {% if is_rtl %}
                    الضريبة
                    {% else %}
                    Tax
                    {% endif %}
                </th>
                <th class="text-right">
                    {% if is_rtl %}
                    المجموع
                    {% else %}
                    Total
                    {% endif %}
                </th>
            </tr>
        </thead>
        <tbody>
            {% for item in invoice.items|default:sample_items %}
            <tr>
                <td>
                    <div class="font-semibold">
                        {% if is_rtl and item.description_ar %}
                        {{ item.description_ar }}
                        {% else %}
                        {{ item.description|default:"Service/Product Description" }}
                        {% endif %}
                    </div>
                    {% if item.details %}
                    <div class="text-sm text-gray-600">
                        {% if is_rtl and item.details_ar %}
                        {{ item.details_ar }}
                        {% else %}
                        {{ item.details }}
                        {% endif %}
                    </div>
                    {% endif %}
                </td>
                <td class="text-center">{{ item.quantity|default:1 }}</td>
                <td class="text-center">{{ item.unit_price|default:0|floatformat:2 }}</td>
                <td class="text-center">{{ item.tax_rate|default:15 }}%</td>
                <td class="text-right font-semibold">{{ item.total|default:0|floatformat:2 }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Invoice Totals -->
<div class="section">
    <div class="flex justify-end">
        <div class="w-64">
            <table class="table">
                <tr>
                    <td class="font-semibold">
                        {% if is_rtl %}
                        المجموع الفرعي:
                        {% else %}
                        Subtotal:
                        {% endif %}
                    </td>
                    <td class="text-right">{{ invoice.subtotal|default:0|floatformat:2 }}</td>
                </tr>
                <tr>
                    <td class="font-semibold">
                        {% if is_rtl %}
                        الضريبة (15%):
                        {% else %}
                        Tax (15%):
                        {% endif %}
                    </td>
                    <td class="text-right">{{ invoice.tax_amount|default:0|floatformat:2 }}</td>
                </tr>
                {% if invoice.discount_amount %}
                <tr>
                    <td class="font-semibold text-green">
                        {% if is_rtl %}
                        الخصم:
                        {% else %}
                        Discount:
                        {% endif %}
                    </td>
                    <td class="text-right text-green">-{{ invoice.discount_amount|floatformat:2 }}</td>
                </tr>
                {% endif %}
                <tr class="bg-blue-50">
                    <td class="font-bold text-lg">
                        {% if is_rtl %}
                        المجموع الكلي:
                        {% else %}
                        Total Amount:
                        {% endif %}
                    </td>
                    <td class="text-right font-bold text-lg">{{ invoice.total_amount|default:0|floatformat:2 }} 
                        {% if is_rtl %}
                        ريال
                        {% else %}
                        SAR
                        {% endif %}
                    </td>
                </tr>
            </table>
        </div>
    </div>
</div>

<!-- Payment Information -->
{% if invoice.payment_terms or invoice.payment_method %}
<div class="section">
    <h3 class="section-title">
        {% if is_rtl %}
        معلومات الدفع
        {% else %}
        Payment Information
        {% endif %}
    </h3>
    
    {% if invoice.payment_terms %}
    <div class="mb-3">
        <span class="font-semibold">
            {% if is_rtl %}
            شروط الدفع:
            {% else %}
            Payment Terms:
            {% endif %}
        </span>
        {% if is_rtl and invoice.payment_terms_ar %}
        {{ invoice.payment_terms_ar }}
        {% else %}
        {{ invoice.payment_terms }}
        {% endif %}
    </div>
    {% endif %}
    
    {% if invoice.payment_method %}
    <div class="mb-3">
        <span class="font-semibold">
            {% if is_rtl %}
            طريقة الدفع:
            {% else %}
            Payment Method:
            {% endif %}
        </span>
        {% if is_rtl and invoice.payment_method_ar %}
        {{ invoice.payment_method_ar }}
        {% else %}
        {{ invoice.payment_method }}
        {% endif %}
    </div>
    {% endif %}
    
    {% if invoice.bank_details %}
    <div class="mb-3">
        <span class="font-semibold">
            {% if is_rtl %}
            تفاصيل البنك:
            {% else %}
            Bank Details:
            {% endif %}
        </span>
        <div class="text-sm mt-1">
            {% if is_rtl and invoice.bank_details_ar %}
            {{ invoice.bank_details_ar|linebreaks }}
            {% else %}
            {{ invoice.bank_details|linebreaks }}
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<!-- Notes -->
{% if invoice.notes %}
<div class="section">
    <h3 class="section-title">
        {% if is_rtl %}
        ملاحظات
        {% else %}
        Notes
        {% endif %}
    </h3>
    <div class="text-sm">
        {% if is_rtl and invoice.notes_ar %}
        {{ invoice.notes_ar|linebreaks }}
        {% else %}
        {{ invoice.notes|linebreaks }}
        {% endif %}
    </div>
</div>
{% endif %}

<!-- Terms and Conditions -->
<div class="section">
    <h3 class="section-title">
        {% if is_rtl %}
        الشروط والأحكام
        {% else %}
        Terms and Conditions
        {% endif %}
    </h3>
    <div class="text-xs text-gray-600">
        {% if is_rtl %}
        <p>1. يجب دفع الفاتورة خلال 30 يوماً من تاريخ الإصدار.</p>
        <p>2. في حالة التأخير في الدفع، سيتم تطبيق رسوم إضافية بنسبة 2% شهرياً.</p>
        <p>3. جميع الأسعار شاملة ضريبة القيمة المضافة.</p>
        <p>4. هذه الفاتورة صالحة لمدة 30 يوماً من تاريخ الإصدار.</p>
        {% else %}
        <p>1. Payment is due within 30 days of invoice date.</p>
        <p>2. Late payment charges of 2% per month will apply to overdue amounts.</p>
        <p>3. All prices are inclusive of applicable taxes.</p>
        <p>4. This invoice is valid for 30 days from the date of issue.</p>
        {% endif %}
    </div>
</div>
{% endblock %}
