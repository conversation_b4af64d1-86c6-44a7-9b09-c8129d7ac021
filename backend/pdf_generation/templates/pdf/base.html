<!DOCTYPE html>
<html lang="{{ language }}" dir="{{ is_rtl|yesno:'rtl,ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title|default:"PDF Report" }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@400;600;700&family=Tajawal:wght@400;500;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: {% if is_rtl %}'Amiri', 'Cairo', 'Tajawal', Arial, sans-serif{% else %}'DejaVu Sans', Arial, sans-serif{% endif %};
            font-size: 12px;
            line-height: 1.6;
            color: #333;
            direction: {{ is_rtl|yesno:'rtl,ltr' }};
            text-align: {{ is_rtl|yesno:'right,left' }};
        }
        
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            border-bottom: 2px solid #2563eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            color: #2563eb;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header .company-info {
            font-size: 14px;
            color: #666;
        }
        
        .content {
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e40af;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 11px;
        }
        
        .table th,
        .table td {
            border: 1px solid #d1d5db;
            padding: 8px;
            text-align: {{ is_rtl|yesno:'right,left' }};
        }
        
        .table th {
            background-color: #f3f4f6;
            font-weight: 600;
            color: #374151;
        }
        
        .table tr:nth-child(even) {
            background-color: #f9fafb;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 25px;
        }
        
        .stat-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #2563eb;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #64748b;
            font-weight: 500;
        }
        
        .footer {
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #6b7280;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        .no-break {
            page-break-inside: avoid;
        }
        
        .text-center { text-align: center; }
        .text-left { text-align: left; }
        .text-right { text-align: right; }
        
        .font-bold { font-weight: 700; }
        .font-semibold { font-weight: 600; }
        
        .text-blue { color: #2563eb; }
        .text-green { color: #059669; }
        .text-red { color: #dc2626; }
        .text-yellow { color: #d97706; }
        
        .bg-blue { background-color: #dbeafe; }
        .bg-green { background-color: #d1fae5; }
        .bg-red { background-color: #fee2e2; }
        .bg-yellow { background-color: #fef3c7; }
        
        .mb-1 { margin-bottom: 5px; }
        .mb-2 { margin-bottom: 10px; }
        .mb-3 { margin-bottom: 15px; }
        .mb-4 { margin-bottom: 20px; }
        
        .mt-1 { margin-top: 5px; }
        .mt-2 { margin-top: 10px; }
        .mt-3 { margin-top: 15px; }
        .mt-4 { margin-top: 20px; }
        
        /* Arabic-specific styles */
        {% if is_rtl %}
        .table th,
        .table td {
            text-align: right;
        }
        
        .table th:first-child,
        .table td:first-child {
            border-right: 2px solid #2563eb;
        }
        {% else %}
        .table th:first-child,
        .table td:first-child {
            border-left: 2px solid #2563eb;
        }
        {% endif %}
        
        /* Print styles */
        @media print {
            body {
                font-size: 11px;
            }
            
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 20px;
            }
            
            .section-title {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>{{ title|default:"PDF Report" }}</h1>
            <div class="company-info">
                <div>{{ settings.company_name|default:"Enterprise Management System" }}</div>
                {% if settings.company_address %}
                <div>{{ settings.company_address }}</div>
                {% endif %}
                <div>{{ generated_at|date:"Y-m-d H:i" }} | {{ generated_by|default:"System" }}</div>
            </div>
        </div>
        
        <!-- Content -->
        <div class="content">
            {% block content %}
            <!-- Content will be inserted here -->
            {% endblock %}
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div>
                {% if is_rtl %}
                تم إنشاء هذا التقرير بواسطة نظام إدارة المؤسسات
                {% else %}
                Generated by Enterprise Management System
                {% endif %}
            </div>
            <div>{{ generated_at|date:"Y-m-d H:i:s" }}</div>
        </div>
    </div>
</body>
</html>
