from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
import uuid
import json

class PDFTemplate(models.Model):
    """
    PDF Template model for storing reusable PDF templates
    """
    TEMPLATE_TYPES = [
        ('hr_report', 'HR Report'),
        ('hr_report_ar', 'HR Report (Arabic)'),
        ('employee_report', 'Employee Report'),
        ('employee_report_ar', 'Employee Report (Arabic)'),
        ('attendance_report', 'Attendance Report'),
        ('attendance_report_ar', 'Attendance Report (Arabic)'),
        ('leave_report', 'Leave Report'),
        ('leave_report_ar', 'Leave Report (Arabic)'),
        ('payroll_report', 'Payroll Report'),
        ('payroll_report_ar', 'Payroll Report (Arabic)'),
        ('financial_report', 'Financial Report'),
        ('financial_report_ar', 'Financial Report (Arabic)'),
        ('budget_report', 'Budget Report'),
        ('budget_report_ar', 'Budget Report (Arabic)'),
        ('expense_report', 'Expense Report'),
        ('expense_report_ar', 'Expense Report (Arabic)'),
        ('project_report', 'Project Report'),
        ('project_report_ar', 'Project Report (Arabic)'),
        ('task_report', 'Task Report'),
        ('task_report_ar', 'Task Report (Arabic)'),
        ('asset_report', 'Asset Report'),
        ('asset_report_ar', 'Asset Report (Arabic)'),
        ('inventory_report', 'Inventory Report'),
        ('inventory_report_ar', 'Inventory Report (Arabic)'),
        ('supplier_report', 'Supplier Report'),
        ('supplier_report_ar', 'Supplier Report (Arabic)'),
        ('purchase_order', 'Purchase Order'),
        ('purchase_order_ar', 'Purchase Order (Arabic)'),
        ('sales_report', 'Sales Report'),
        ('sales_report_ar', 'Sales Report (Arabic)'),
        ('customer_report', 'Customer Report'),
        ('customer_report_ar', 'Customer Report (Arabic)'),
        ('quotation', 'Quotation'),
        ('quotation_ar', 'Quotation (Arabic)'),
        ('invoice', 'Invoice'),
        ('invoice_ar', 'Invoice (Arabic)'),
        ('kpi_report', 'KPI Report'),
        ('kpi_report_ar', 'KPI Report (Arabic)'),
        ('performance_report', 'Performance Report'),
        ('performance_report_ar', 'Performance Report (Arabic)'),
        ('department_report', 'Department Report'),
        ('department_report_ar', 'Department Report (Arabic)'),
        ('meeting_report', 'Meeting Report'),
        ('meeting_report_ar', 'Meeting Report (Arabic)'),
        ('announcement_report', 'Announcement Report'),
        ('announcement_report_ar', 'Announcement Report (Arabic)'),
        ('document_report', 'Document Report'),
        ('document_report_ar', 'Document Report (Arabic)'),
        ('support_ticket_report', 'Support Ticket Report'),
        ('support_ticket_report_ar', 'Support Ticket Report (Arabic)'),
        ('customer_feedback_report', 'Customer Feedback Report'),
        ('customer_feedback_report_ar', 'Customer Feedback Report (Arabic)'),
        ('analytics_report', 'Analytics Report'),
        ('analytics_report_ar', 'Analytics Report (Arabic)'),
        ('custom_report', 'Custom Report'),
        ('custom_report_ar', 'Custom Report (Arabic)'),
    ]

    LANGUAGES = [
        ('en', 'English'),
        ('ar', 'Arabic'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, help_text="Arabic name")
    template_type = models.CharField(max_length=50, choices=TEMPLATE_TYPES)
    language = models.CharField(max_length=2, choices=LANGUAGES, default='en')
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True, help_text="Arabic description")

    # Template content
    html_template = models.TextField(help_text="HTML template content")
    css_styles = models.TextField(blank=True, help_text="Custom CSS styles")

    # Configuration
    page_size = models.CharField(max_length=10, default='A4', choices=[
        ('A4', 'A4'),
        ('A3', 'A3'),
        ('Letter', 'Letter'),
        ('Legal', 'Legal'),
    ])
    orientation = models.CharField(max_length=10, default='portrait', choices=[
        ('portrait', 'Portrait'),
        ('landscape', 'Landscape'),
    ])

    # Metadata
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_pdf_templates')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    version = models.PositiveIntegerField(default=1)

    class Meta:
        ordering = ['template_type', 'language', 'name']
        unique_together = ['template_type', 'language']

    def __str__(self):
        return f"{self.name} ({self.get_language_display()})"


class PDFGenerationHistory(models.Model):
    """
    Track PDF generation history and status
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    template = models.ForeignKey(PDFTemplate, on_delete=models.CASCADE, related_name='generations')
    generated_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='pdf_generations')

    # Generation details
    filename = models.CharField(max_length=255)
    file_path = models.CharField(max_length=500, blank=True)
    file_size = models.PositiveIntegerField(null=True, blank=True, help_text="File size in bytes")

    # Status tracking
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    progress = models.PositiveIntegerField(
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Generation progress percentage"
    )

    # Timing
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    processing_time = models.DurationField(null=True, blank=True)

    # Data and parameters
    generation_parameters = models.JSONField(default=dict, help_text="Parameters used for generation")
    data_source = models.CharField(max_length=100, help_text="Source of data (e.g., 'employees', 'projects')")
    record_count = models.PositiveIntegerField(default=0, help_text="Number of records processed")

    # Error handling
    error_message = models.TextField(blank=True)
    error_details = models.JSONField(default=dict, blank=True)

    # Download tracking
    download_count = models.PositiveIntegerField(default=0)
    last_downloaded = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-started_at']
        verbose_name = 'PDF Generation History'
        verbose_name_plural = 'PDF Generation Histories'

    def __str__(self):
        return f"{self.filename} - {self.status}"

    def mark_completed(self, file_path, file_size=None):
        """Mark generation as completed"""
        self.status = 'completed'
        self.progress = 100
        self.completed_at = timezone.now()
        self.file_path = file_path
        if file_size:
            self.file_size = file_size
        if self.started_at and self.completed_at:
            self.processing_time = self.completed_at - self.started_at
        self.save()

    def mark_failed(self, error_message, error_details=None):
        """Mark generation as failed"""
        self.status = 'failed'
        self.error_message = error_message
        if error_details:
            self.error_details = error_details
        self.completed_at = timezone.now()
        self.save()


class PDFSettings(models.Model):
    """
    Global PDF generation settings
    """
    FONT_CHOICES = [
        ('dejavu', 'DejaVu Sans'),
        ('noto', 'Noto Sans'),
        ('amiri', 'Amiri (Arabic)'),
        ('cairo', 'Cairo (Arabic)'),
        ('tajawal', 'Tajawal (Arabic)'),
    ]

    # Font settings
    default_font_en = models.CharField(max_length=50, choices=FONT_CHOICES, default='dejavu')
    default_font_ar = models.CharField(max_length=50, choices=FONT_CHOICES, default='amiri')
    font_size = models.PositiveIntegerField(default=12, validators=[MinValueValidator(8), MaxValueValidator(24)])

    # Page settings
    margin_top = models.FloatField(default=2.0, help_text="Top margin in cm")
    margin_bottom = models.FloatField(default=2.0, help_text="Bottom margin in cm")
    margin_left = models.FloatField(default=2.0, help_text="Left margin in cm")
    margin_right = models.FloatField(default=2.0, help_text="Right margin in cm")

    # Header/Footer
    include_header = models.BooleanField(default=True)
    include_footer = models.BooleanField(default=True)
    header_text = models.CharField(max_length=200, default="Enterprise Management System")
    header_text_ar = models.CharField(max_length=200, default="نظام إدارة المؤسسات")
    footer_text = models.CharField(max_length=200, default="Generated on {date}")
    footer_text_ar = models.CharField(max_length=200, default="تم الإنشاء في {date}")

    # Branding
    company_logo = models.ImageField(upload_to='pdf_assets/', blank=True, null=True)
    company_name = models.CharField(max_length=200, default="Your Company")
    company_name_ar = models.CharField(max_length=200, default="شركتكم")
    company_address = models.TextField(blank=True)
    company_address_ar = models.TextField(blank=True)

    # Performance
    max_records_per_pdf = models.PositiveIntegerField(default=1000, help_text="Maximum records per PDF")
    enable_compression = models.BooleanField(default=True)
    quality_level = models.PositiveIntegerField(
        default=85,
        validators=[MinValueValidator(1), MaxValueValidator(100)],
        help_text="PDF quality level (1-100)"
    )

    # Storage
    storage_path = models.CharField(max_length=500, default='generated_pdfs/')
    auto_cleanup_days = models.PositiveIntegerField(default=30, help_text="Auto-delete PDFs after N days")

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='pdf_settings_updates')

    class Meta:
        verbose_name = 'PDF Settings'
        verbose_name_plural = 'PDF Settings'

    def __str__(self):
        return f"PDF Settings (Updated: {self.updated_at.strftime('%Y-%m-%d %H:%M')})"


class EmailLog(models.Model):
    """Log of PDF emails sent"""

    pdf_history = models.ForeignKey(
        PDFGenerationHistory,
        on_delete=models.CASCADE,
        related_name='email_logs'
    )
    recipients = models.TextField(help_text="Comma-separated list of recipients")
    cc_recipients = models.TextField(blank=True, help_text="Comma-separated list of CC recipients")
    bcc_recipients = models.TextField(blank=True, help_text="Comma-separated list of BCC recipients")
    subject = models.CharField(max_length=500)
    sender = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='sent_pdf_emails'
    )
    status = models.CharField(
        max_length=20,
        choices=[
            ('sent', 'Sent'),
            ('failed', 'Failed'),
            ('pending', 'Pending'),
        ],
        default='pending'
    )
    priority = models.CharField(
        max_length=10,
        choices=[
            ('low', 'Low'),
            ('normal', 'Normal'),
            ('high', 'High'),
            ('urgent', 'Urgent'),
        ],
        default='normal'
    )
    error_message = models.TextField(blank=True)
    sent_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Email Log"
        verbose_name_plural = "Email Logs"

    def __str__(self):
        return f"Email to {self.recipients} - {self.status}"


class SignatureCertificate(models.Model):
    """Digital signature certificates"""

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='signature_certificates'
    )
    certificate_name = models.CharField(max_length=200)
    organization = models.CharField(max_length=200)
    country = models.CharField(max_length=2, default='SA')
    state = models.CharField(max_length=100)
    city = models.CharField(max_length=100)
    email = models.EmailField()

    certificate_file = models.CharField(max_length=500, help_text="Path to certificate file")
    private_key_file = models.CharField(max_length=500, help_text="Path to private key file")
    p12_file = models.CharField(max_length=500, help_text="Path to PKCS#12 file")
    p12_password = models.CharField(max_length=100, help_text="PKCS#12 password")

    serial_number = models.CharField(max_length=100, unique=True)
    fingerprint = models.CharField(max_length=200, unique=True)

    valid_from = models.DateTimeField()
    valid_until = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)

    is_active = models.BooleanField(default=True)
    revoked_at = models.DateTimeField(null=True, blank=True)
    revocation_reason = models.CharField(max_length=200, blank=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Signature Certificate"
        verbose_name_plural = "Signature Certificates"

    def is_valid(self):
        """Check if certificate is currently valid"""
        from django.utils import timezone
        now = timezone.now()
        return (
            self.is_active and
            self.valid_from <= now <= self.valid_until and
            not self.revoked_at
        )

    def __str__(self):
        return f"{self.certificate_name} - {self.user.get_full_name()}"


class DigitalSignature(models.Model):
    """Digital signatures applied to PDFs"""

    certificate = models.ForeignKey(
        SignatureCertificate,
        on_delete=models.CASCADE,
        related_name='signatures'
    )
    document_path = models.CharField(max_length=500, help_text="Path to original document")
    signed_document_path = models.CharField(max_length=500, help_text="Path to signed document")
    signature_data = models.TextField(help_text="Base64 encoded signature data")
    document_hash = models.CharField(max_length=64, help_text="SHA256 hash of original document")

    reason = models.CharField(max_length=200, default="Document approval")
    location = models.CharField(max_length=200, default="Saudi Arabia")
    contact_info = models.CharField(max_length=200, blank=True)

    signed_at = models.DateTimeField()
    is_valid = models.BooleanField(default=True)

    class Meta:
        ordering = ['-signed_at']
        verbose_name = "Digital Signature"
        verbose_name_plural = "Digital Signatures"

    def __str__(self):
        return f"Signature by {self.certificate.user.get_full_name()} on {self.signed_at}"
