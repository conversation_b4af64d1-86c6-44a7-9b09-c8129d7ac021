"""
PDF Generation Views
Provides API endpoints for PDF generation across the EMS application
"""

from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views import View
from rest_framework import viewsets, status
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
import json
import logging
from datetime import datetime

from .models import PDFTemplate, PDFGenerationHistory, PDFSettings
from .services import pdf_service, PDFGenerationError
from .serializers import PDFTemplateSerializer, PDFGenerationHistorySerializer, PDFSettingsSerializer

logger = logging.getLogger(__name__)


class PDFTemplateViewSet(viewsets.ModelViewSet):
    """ViewSet for managing PDF templates"""
    queryset = PDFTemplate.objects.all()
    serializer_class = PDFTemplateSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def test_generate(self, request, pk=None):
        """Test PDF generation with sample data"""
        template = self.get_object()

        # Sample data for testing
        sample_data = {
            'employees': [
                {'name': 'John <PERSON>e', 'position': 'Manager', 'department': 'HR'},
                {'name': 'Jane Smith', 'position': 'Developer', 'department': 'IT'},
            ],
            'title': f'Test {template.name}',
            'date': datetime.now().strftime('%Y-%m-%d'),
        }

        try:
            history = pdf_service.generate_pdf(
                template_type=template.template_type,
                language=template.language,
                data=sample_data,
                user=request.user,
                filename=f'test_{template.template_type}_{template.language}.pdf'
            )

            return Response({
                'success': True,
                'history_id': str(history.id),
                'filename': history.filename,
                'status': history.status
            })

        except PDFGenerationError as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class PDFGenerationHistoryViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for viewing PDF generation history"""
    queryset = PDFGenerationHistory.objects.all()
    serializer_class = PDFGenerationHistorySerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by user if not admin
        if not self.request.user.is_staff:
            queryset = queryset.filter(generated_by=self.request.user)

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by template type
        template_type = self.request.query_params.get('template_type')
        if template_type:
            queryset = queryset.filter(template__template_type=template_type)

        return queryset.order_by('-started_at')

    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """Download generated PDF"""
        history = self.get_object()

        # Check permissions
        if not request.user.is_staff and history.generated_by != request.user:
            return Response({
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            return pdf_service.get_pdf_response(history)
        except PDFGenerationError as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class PDFSettingsViewSet(viewsets.ModelViewSet):
    """ViewSet for managing PDF settings"""
    queryset = PDFSettings.objects.all()
    serializer_class = PDFSettingsSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Only show latest settings
        return self.queryset.order_by('-updated_at')[:1]

    def perform_update(self, serializer):
        serializer.save(updated_by=self.request.user)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def generate_hr_pdf(request):
    """
    Generate HR report PDF
    Supports both English and Arabic
    """
    language = request.GET.get('language', 'en')

    try:
        # Get HR data from EMS models
        from ems.models import Employee, Department, Attendance, LeaveRequest
        from django.db.models import Count, Q
        from datetime import datetime, timedelta

        # Prepare HR data
        employees = Employee.objects.select_related('user', 'department').all()
        departments = Department.objects.annotate(
            employee_count=Count('employee')
        ).all()

        # Recent attendance data
        recent_date = datetime.now().date() - timedelta(days=30)
        attendance_data = Attendance.objects.filter(
            date__gte=recent_date
        ).select_related('employee__user').order_by('-date')[:100]

        # Leave requests
        leave_requests = LeaveRequest.objects.filter(
            created_at__gte=datetime.now() - timedelta(days=30)
        ).select_related('employee__user', 'leave_type').order_by('-created_at')[:50]

        # Prepare context
        context = {
            'title': 'HR Report' if language == 'en' else 'تقرير الموارد البشرية',
            'employees': employees,
            'departments': departments,
            'attendance_data': attendance_data,
            'leave_requests': leave_requests,
            'total_employees': employees.count(),
            'total_departments': departments.count(),
            'report_date': datetime.now().strftime('%Y-%m-%d'),
        }

        # Generate PDF
        history = pdf_service.generate_pdf(
            template_type='hr_report_ar' if language == 'ar' else 'hr_report',
            language=language,
            data=context,
            user=request.user,
            filename=f'hr_report_{language}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf'
        )

        # Return PDF response
        return pdf_service.get_pdf_response(history)

    except Exception as e:
        logger.error(f"HR PDF generation failed: {str(e)}")
        return JsonResponse({
            'error': 'PDF generation failed',
            'details': str(e)
        }, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def generate_template_pdf(request, template_type):
    """
    Generic endpoint for generating PDFs from any template
    """
    language = request.GET.get('language', 'en')

    try:
        # Get data based on template type
        data = _get_template_data(template_type, request)

        # Generate PDF
        history = pdf_service.generate_pdf(
            template_type=f"{template_type}_ar" if language == 'ar' else template_type,
            language=language,
            data=data,
            user=request.user,
            filename=f'{template_type}_{language}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf'
        )

        # Return PDF response
        return pdf_service.get_pdf_response(history)

    except Exception as e:
        logger.error(f"PDF generation failed for {template_type}: {str(e)}")
        return JsonResponse({
            'error': 'PDF generation failed',
            'details': str(e)
        }, status=500)


def _get_template_data(template_type: str, request) -> dict:
    """Get data for specific template types"""
    from ems.models import (
        Employee, Department, Project, Task, Budget, Expense,
        Asset, Supplier, PurchaseOrder, Customer, SalesOrder,
        Attendance, LeaveRequest, KPI, KPIValue
    )
    from datetime import datetime, timedelta

    data = {}

    if template_type == 'employee_report':
        data = {
            'employees': Employee.objects.select_related('user', 'department').all(),
            'title': 'Employee Report',
        }

    elif template_type == 'attendance_report':
        recent_date = datetime.now().date() - timedelta(days=30)
        data = {
            'attendance': Attendance.objects.filter(date__gte=recent_date).select_related('employee__user'),
            'title': 'Attendance Report',
        }

    elif template_type == 'project_report':
        data = {
            'projects': Project.objects.select_related('project_manager__user', 'department').all(),
            'title': 'Project Report',
        }

    elif template_type == 'financial_report':
        data = {
            'budgets': Budget.objects.select_related('department').all(),
            'expenses': Expense.objects.select_related('employee__user', 'budget').all()[:100],
            'title': 'Financial Report',
        }

    elif template_type == 'sales_report':
        data = {
            'orders': SalesOrder.objects.select_related('customer').all()[:100],
            'customers': Customer.objects.all(),
            'title': 'Sales Report',
        }

    elif template_type == 'kpi_report':
        data = {
            'kpis': KPI.objects.select_related('category', 'owner__user').all(),
            'kpi_values': KPIValue.objects.select_related('kpi').order_by('-date')[:100],
            'title': 'KPI Report',
        }

    # Add common context
    data.update({
        'generated_at': datetime.now(),
        'generated_by': request.user.get_full_name() or request.user.username,
        'report_date': datetime.now().strftime('%Y-%m-%d'),
    })

    return data


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def pdf_status(request):
    """Get PDF generation service status"""
    try:
        from .services import WEASYPRINT_AVAILABLE, REPORTLAB_AVAILABLE

        return JsonResponse({
            'status': 'operational',
            'weasyprint_available': WEASYPRINT_AVAILABLE,
            'reportlab_available': REPORTLAB_AVAILABLE,
            'preferred_engine': 'weasyprint' if WEASYPRINT_AVAILABLE else 'reportlab',
            'arabic_support': WEASYPRINT_AVAILABLE,  # WeasyPrint has better Arabic support
        })
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'error': str(e)
        }, status=500)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def cleanup_old_pdfs(request):
    """Clean up old PDF files"""
    days = request.data.get('days', 30)

    try:
        deleted_count = pdf_service.cleanup_old_files(days)
        return JsonResponse({
            'success': True,
            'deleted_count': deleted_count,
            'message': f'Cleaned up {deleted_count} old PDF files'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


# ============================================================================
# ADVANCED FEATURES: EMAIL INTEGRATION
# ============================================================================

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def send_pdf_email(request):
    """Send PDF via email"""
    try:
        from .email_service import email_service

        # Get request data
        pdf_history_id = request.data.get('pdf_history_id')
        recipients = request.data.get('recipients', [])
        subject = request.data.get('subject')
        message = request.data.get('message')
        language = request.data.get('language', 'en')
        email_template = request.data.get('email_template', 'default')
        cc_recipients = request.data.get('cc_recipients', [])
        bcc_recipients = request.data.get('bcc_recipients', [])
        priority = request.data.get('priority', 'normal')

        # Validate inputs
        if not pdf_history_id:
            return Response({
                'success': False,
                'error': 'PDF history ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not recipients:
            return Response({
                'success': False,
                'error': 'At least one recipient is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get PDF history
        try:
            pdf_history = PDFGenerationHistory.objects.get(id=pdf_history_id)
        except PDFGenerationHistory.DoesNotExist:
            return Response({
                'success': False,
                'error': 'PDF not found'
            }, status=status.HTTP_404_NOT_FOUND)

        # Check permissions
        if not request.user.is_staff and pdf_history.generated_by != request.user:
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        # Send email
        result = email_service.send_pdf_email(
            pdf_history=pdf_history,
            recipients=recipients,
            subject=subject,
            message=message,
            sender=request.user,
            language=language,
            email_template=email_template,
            cc_recipients=cc_recipients,
            bcc_recipients=bcc_recipients,
            priority=priority
        )

        return Response(result)

    except Exception as e:
        logger.error(f"Email sending failed: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def send_bulk_pdf_emails(request):
    """Send PDF to multiple recipients with personalized messages"""
    try:
        from .email_service import email_service

        pdf_history_id = request.data.get('pdf_history_id')
        recipient_list = request.data.get('recipient_list', [])
        language = request.data.get('language', 'en')
        email_template = request.data.get('email_template', 'default')

        # Validate inputs
        if not pdf_history_id or not recipient_list:
            return Response({
                'success': False,
                'error': 'PDF history ID and recipient list are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get PDF history
        pdf_history = get_object_or_404(PDFGenerationHistory, id=pdf_history_id)

        # Check permissions
        if not request.user.is_staff and pdf_history.generated_by != request.user:
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        # Send bulk emails
        result = email_service.send_bulk_pdf_emails(
            pdf_history=pdf_history,
            recipient_list=recipient_list,
            sender=request.user,
            language=language,
            email_template=email_template
        )

        return Response({
            'success': True,
            'results': result
        })

    except Exception as e:
        logger.error(f"Bulk email sending failed: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def email_history(request):
    """Get email sending history"""
    try:
        from .models import EmailLog

        # Get query parameters
        pdf_history_id = request.GET.get('pdf_history_id')
        status_filter = request.GET.get('status')

        # Build queryset
        queryset = EmailLog.objects.all()

        if pdf_history_id:
            queryset = queryset.filter(pdf_history_id=pdf_history_id)

        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by user permissions
        if not request.user.is_staff:
            queryset = queryset.filter(sender=request.user)

        # Serialize data
        emails = []
        for email_log in queryset.order_by('-created_at')[:100]:
            emails.append({
                'id': email_log.id,
                'recipients': email_log.recipients,
                'subject': email_log.subject,
                'status': email_log.status,
                'priority': email_log.priority,
                'sent_at': email_log.sent_at,
                'created_at': email_log.created_at,
                'error_message': email_log.error_message,
            })

        return Response({
            'success': True,
            'emails': emails
        })

    except Exception as e:
        logger.error(f"Email history retrieval failed: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ============================================================================
# ADVANCED FEATURES: DIGITAL SIGNATURES
# ============================================================================

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_certificate(request):
    """Create a digital certificate for a user"""
    try:
        from .signature_service import signature_service

        organization = request.data.get('organization', 'Your Company')
        country = request.data.get('country', 'SA')
        state = request.data.get('state', 'Riyadh')
        city = request.data.get('city', 'Riyadh')
        email = request.data.get('email', request.user.email)
        validity_days = request.data.get('validity_days', 365)

        result = signature_service.create_certificate(
            user=request.user,
            organization=organization,
            country=country,
            state=state,
            city=city,
            email=email,
            validity_days=validity_days
        )

        return Response(result)

    except Exception as e:
        logger.error(f"Certificate creation failed: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def sign_pdf(request):
    """Digitally sign a PDF document"""
    try:
        from .signature_service import signature_service

        pdf_history_id = request.data.get('pdf_history_id')
        certificate_id = request.data.get('certificate_id')
        reason = request.data.get('reason', 'Document approval')
        location = request.data.get('location', 'Saudi Arabia')
        contact_info = request.data.get('contact_info')

        # Validate inputs
        if not pdf_history_id or not certificate_id:
            return Response({
                'success': False,
                'error': 'PDF history ID and certificate ID are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get PDF history
        pdf_history = get_object_or_404(PDFGenerationHistory, id=pdf_history_id)

        # Check permissions
        if not request.user.is_staff and pdf_history.generated_by != request.user:
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        # Sign PDF
        result = signature_service.sign_pdf(
            pdf_path=pdf_history.file_path,
            certificate_id=certificate_id,
            reason=reason,
            location=location,
            contact_info=contact_info
        )

        return Response(result)

    except Exception as e:
        logger.error(f"PDF signing failed: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def verify_signature(request, signature_id):
    """Verify a digital signature"""
    try:
        from .signature_service import signature_service

        result = signature_service.verify_signature(signature_id)
        return Response(result)

    except Exception as e:
        logger.error(f"Signature verification failed: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_certificates(request):
    """Get user's certificates"""
    try:
        from .signature_service import signature_service

        certificates = signature_service.get_user_certificates(request.user)
        return Response({
            'success': True,
            'certificates': certificates
        })

    except Exception as e:
        logger.error(f"Certificate retrieval failed: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)