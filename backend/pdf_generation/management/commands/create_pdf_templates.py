"""
Django management command to create default PDF templates
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from pdf_generation.models import PDFTemplate, PDFSettings
import os


class Command(BaseCommand):
    help = 'Create default PDF templates for the EMS application'

    def add_arguments(self, parser):
        parser.add_argument(
            '--overwrite',
            action='store_true',
            help='Overwrite existing templates',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Creating default PDF templates...'))

        # Get or create admin user
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            admin_user = User.objects.create_user(
                username='pdf_admin',
                email='<EMAIL>',
                is_staff=True,
                is_superuser=True
            )
            self.stdout.write(self.style.WARNING('Created admin user for PDF templates'))

        # Create default settings if not exists
        if not PDFSettings.objects.exists():
            PDFSettings.objects.create(
                updated_by=admin_user,
                company_name="Enterprise Management System",
                company_name_ar="نظام إدارة المؤسسات"
            )
            self.stdout.write(self.style.SUCCESS('Created default PDF settings'))

        # Load HR report template
        hr_template_path = os.path.join(
            os.path.dirname(__file__),
            '../../templates/pdf/hr_report.html'
        )

        if os.path.exists(hr_template_path):
            with open(hr_template_path, 'r', encoding='utf-8') as f:
                hr_template_content = f.read()
        else:
            hr_template_content = self._get_default_hr_template()

        # Create templates
        templates_to_create = [
            {
                'name': 'HR Report (English)',
                'name_ar': 'تقرير الموارد البشرية (إنجليزي)',
                'template_type': 'hr_report',
                'language': 'en',
                'description': 'Comprehensive HR report with employee data, attendance, and leave information',
                'description_ar': 'تقرير شامل للموارد البشرية يتضمن بيانات الموظفين والحضور والإجازات',
                'html_template': hr_template_content,
            },
            {
                'name': 'HR Report (Arabic)',
                'name_ar': 'تقرير الموارد البشرية (عربي)',
                'template_type': 'hr_report_ar',
                'language': 'ar',
                'description': 'Comprehensive HR report with employee data, attendance, and leave information (Arabic)',
                'description_ar': 'تقرير شامل للموارد البشرية يتضمن بيانات الموظفين والحضور والإجازات (عربي)',
                'html_template': hr_template_content,
            },
            {
                'name': 'Employee Report (English)',
                'name_ar': 'تقرير الموظفين (إنجليزي)',
                'template_type': 'employee_report',
                'language': 'en',
                'description': 'Detailed employee information report',
                'description_ar': 'تقرير تفصيلي لمعلومات الموظفين',
                'html_template': self._get_employee_template(),
            },
            {
                'name': 'Employee Report (Arabic)',
                'name_ar': 'تقرير الموظفين (عربي)',
                'template_type': 'employee_report_ar',
                'language': 'ar',
                'description': 'Detailed employee information report (Arabic)',
                'description_ar': 'تقرير تفصيلي لمعلومات الموظفين (عربي)',
                'html_template': self._get_employee_template(),
            },
            {
                'name': 'Attendance Report (English)',
                'name_ar': 'تقرير الحضور (إنجليزي)',
                'template_type': 'attendance_report',
                'language': 'en',
                'description': 'Employee attendance tracking report',
                'description_ar': 'تقرير متابعة حضور الموظفين',
                'html_template': self._get_attendance_template(),
            },
            {
                'name': 'Attendance Report (Arabic)',
                'name_ar': 'تقرير الحضور (عربي)',
                'template_type': 'attendance_report_ar',
                'language': 'ar',
                'description': 'Employee attendance tracking report (Arabic)',
                'description_ar': 'تقرير متابعة حضور الموظفين (عربي)',
                'html_template': self._get_attendance_template(),
            },
            {
                'name': 'Financial Report (English)',
                'name_ar': 'التقرير المالي (إنجليزي)',
                'template_type': 'financial_report',
                'language': 'en',
                'description': 'Financial summary with budgets and expenses',
                'description_ar': 'ملخص مالي يتضمن الميزانيات والمصروفات',
                'html_template': self._get_financial_template(),
            },
            {
                'name': 'Financial Report (Arabic)',
                'name_ar': 'التقرير المالي (عربي)',
                'template_type': 'financial_report_ar',
                'language': 'ar',
                'description': 'Financial summary with budgets and expenses (Arabic)',
                'description_ar': 'ملخص مالي يتضمن الميزانيات والمصروفات (عربي)',
                'html_template': self._get_financial_template(),
            },
            {
                'name': 'Expense Report (English)',
                'name_ar': 'تقرير المصروفات (إنجليزي)',
                'template_type': 'expense_report',
                'language': 'en',
                'description': 'Detailed expense tracking and analysis report',
                'description_ar': 'تقرير تفصيلي لتتبع وتحليل المصروفات',
                'html_template': self._get_expense_template(),
            },
            {
                'name': 'Expense Report (Arabic)',
                'name_ar': 'تقرير المصروفات (عربي)',
                'template_type': 'expense_report_ar',
                'language': 'ar',
                'description': 'Detailed expense tracking and analysis report (Arabic)',
                'description_ar': 'تقرير تفصيلي لتتبع وتحليل المصروفات (عربي)',
                'html_template': self._get_expense_template(),
            },
            {
                'name': 'Sales Report (English)',
                'name_ar': 'تقرير المبيعات (إنجليزي)',
                'template_type': 'sales_report',
                'language': 'en',
                'description': 'Comprehensive sales performance and customer analysis',
                'description_ar': 'تحليل شامل لأداء المبيعات والعملاء',
                'html_template': self._get_sales_template(),
            },
            {
                'name': 'Sales Report (Arabic)',
                'name_ar': 'تقرير المبيعات (عربي)',
                'template_type': 'sales_report_ar',
                'language': 'ar',
                'description': 'Comprehensive sales performance and customer analysis (Arabic)',
                'description_ar': 'تحليل شامل لأداء المبيعات والعملاء (عربي)',
                'html_template': self._get_sales_template(),
            },
            {
                'name': 'Support Ticket Report (English)',
                'name_ar': 'تقرير تذاكر الدعم (إنجليزي)',
                'template_type': 'support_ticket_report',
                'language': 'en',
                'description': 'Customer support tickets analysis and performance metrics',
                'description_ar': 'تحليل تذاكر دعم العملاء ومقاييس الأداء',
                'html_template': self._get_support_template(),
            },
            {
                'name': 'Support Ticket Report (Arabic)',
                'name_ar': 'تقرير تذاكر الدعم (عربي)',
                'template_type': 'support_ticket_report_ar',
                'language': 'ar',
                'description': 'Customer support tickets analysis and performance metrics (Arabic)',
                'description_ar': 'تحليل تذاكر دعم العملاء ومقاييس الأداء (عربي)',
                'html_template': self._get_support_template(),
            },
            {
                'name': 'Asset Report (English)',
                'name_ar': 'تقرير الأصول (إنجليزي)',
                'template_type': 'asset_report',
                'language': 'en',
                'description': 'Comprehensive asset management and valuation report',
                'description_ar': 'تقرير شامل لإدارة وتقييم الأصول',
                'html_template': self._get_asset_template(),
            },
            {
                'name': 'Asset Report (Arabic)',
                'name_ar': 'تقرير الأصول (عربي)',
                'template_type': 'asset_report_ar',
                'language': 'ar',
                'description': 'Comprehensive asset management and valuation report (Arabic)',
                'description_ar': 'تقرير شامل لإدارة وتقييم الأصول (عربي)',
                'html_template': self._get_asset_template(),
            },
            {
                'name': 'Customer Report (English)',
                'name_ar': 'تقرير العملاء (إنجليزي)',
                'template_type': 'customer_report',
                'language': 'en',
                'description': 'Customer analysis and segmentation report',
                'description_ar': 'تقرير تحليل وتقسيم العملاء',
                'html_template': self._get_customer_template(),
            },
            {
                'name': 'Customer Report (Arabic)',
                'name_ar': 'تقرير العملاء (عربي)',
                'template_type': 'customer_report_ar',
                'language': 'ar',
                'description': 'Customer analysis and segmentation report (Arabic)',
                'description_ar': 'تقرير تحليل وتقسيم العملاء (عربي)',
                'html_template': self._get_customer_template(),
            },
            {
                'name': 'KPI Report (English)',
                'name_ar': 'تقرير مؤشرات الأداء (إنجليزي)',
                'template_type': 'kpi_report',
                'language': 'en',
                'description': 'Key Performance Indicators analysis and trends',
                'description_ar': 'تحليل واتجاهات مؤشرات الأداء الرئيسية',
                'html_template': self._get_kpi_template(),
            },
            {
                'name': 'KPI Report (Arabic)',
                'name_ar': 'تقرير مؤشرات الأداء (عربي)',
                'template_type': 'kpi_report_ar',
                'language': 'ar',
                'description': 'Key Performance Indicators analysis and trends (Arabic)',
                'description_ar': 'تحليل واتجاهات مؤشرات الأداء الرئيسية (عربي)',
                'html_template': self._get_kpi_template(),
            },
        ]

        created_count = 0
        updated_count = 0

        for template_data in templates_to_create:
            template, created = PDFTemplate.objects.get_or_create(
                template_type=template_data['template_type'],
                language=template_data['language'],
                defaults={
                    **template_data,
                    'created_by': admin_user
                }
            )

            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created template: {template.name}')
                )
            elif options['overwrite']:
                for key, value in template_data.items():
                    if key not in ['template_type', 'language']:
                        setattr(template, key, value)
                template.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'Updated template: {template.name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Template already exists: {template.name}')
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'Completed! Created {created_count} templates, updated {updated_count} templates.'
            )
        )

    def _get_default_hr_template(self):
        """Get default HR template if file not found"""
        return """
        {% extends "pdf/base.html" %}
        {% block content %}
        <div class="section">
            <h2 class="section-title">{{ title|default:"HR Report" }}</h2>
            <p>This is a basic HR report template. Please update with proper content.</p>

            {% if employees %}
            <table class="table">
                <thead>
                    <tr>
                        <th>Employee ID</th>
                        <th>Name</th>
                        <th>Position</th>
                        <th>Department</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees %}
                    <tr>
                        <td>{{ employee.employee_id }}</td>
                        <td>{{ employee.user.get_full_name }}</td>
                        <td>{{ employee.position }}</td>
                        <td>{{ employee.department.name|default:"-" }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endblock %}
        """

    def _get_employee_template(self):
        """Get employee report template"""
        return """
        {% extends "pdf/base.html" %}
        {% block content %}
        <div class="section">
            <h2 class="section-title">
                {% if is_rtl %}قائمة الموظفين{% else %}Employee List{% endif %}
            </h2>

            {% if employees %}
            <table class="table">
                <thead>
                    <tr>
                        <th>{% if is_rtl %}رقم الموظف{% else %}Employee ID{% endif %}</th>
                        <th>{% if is_rtl %}الاسم{% else %}Name{% endif %}</th>
                        <th>{% if is_rtl %}المنصب{% else %}Position{% endif %}</th>
                        <th>{% if is_rtl %}القسم{% else %}Department{% endif %}</th>
                        <th>{% if is_rtl %}تاريخ التوظيف{% else %}Hire Date{% endif %}</th>
                        <th>{% if is_rtl %}الراتب{% else %}Salary{% endif %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees %}
                    <tr>
                        <td>{{ employee.employee_id }}</td>
                        <td>{{ employee.user.get_full_name|default:employee.user.username }}</td>
                        <td>
                            {% if is_rtl and employee.position_ar %}
                            {{ employee.position_ar }}
                            {% else %}
                            {{ employee.position }}
                            {% endif %}
                        </td>
                        <td>
                            {% if employee.department %}
                                {% if is_rtl and employee.department.name_ar %}
                                {{ employee.department.name_ar }}
                                {% else %}
                                {{ employee.department.name }}
                                {% endif %}
                            {% else %}
                            -
                            {% endif %}
                        </td>
                        <td>{{ employee.hire_date|date:"Y-m-d" }}</td>
                        <td>{{ employee.salary|default:"-" }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endblock %}
        """

    def _get_attendance_template(self):
        """Get attendance report template"""
        return """
        {% extends "pdf/base.html" %}
        {% block content %}
        <div class="section">
            <h2 class="section-title">
                {% if is_rtl %}تقرير الحضور{% else %}Attendance Report{% endif %}
            </h2>

            {% if attendance %}
            <table class="table">
                <thead>
                    <tr>
                        <th>{% if is_rtl %}الموظف{% else %}Employee{% endif %}</th>
                        <th>{% if is_rtl %}التاريخ{% else %}Date{% endif %}</th>
                        <th>{% if is_rtl %}وقت الدخول{% else %}Check In{% endif %}</th>
                        <th>{% if is_rtl %}وقت الخروج{% else %}Check Out{% endif %}</th>
                        <th>{% if is_rtl %}إجمالي الساعات{% else %}Total Hours{% endif %}</th>
                        <th>{% if is_rtl %}الحالة{% else %}Status{% endif %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in attendance %}
                    <tr>
                        <td>{{ record.employee.user.get_full_name|default:record.employee.user.username }}</td>
                        <td>{{ record.date|date:"Y-m-d" }}</td>
                        <td>{{ record.check_in_time|time:"H:i"|default:"-" }}</td>
                        <td>{{ record.check_out_time|time:"H:i"|default:"-" }}</td>
                        <td>{{ record.total_hours|default:"-" }}</td>
                        <td>
                            {% if record.is_present %}
                                <span class="text-green">{% if is_rtl %}حاضر{% else %}Present{% endif %}</span>
                            {% else %}
                                <span class="text-red">{% if is_rtl %}غائب{% else %}Absent{% endif %}</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endblock %}
        """

    def _get_financial_template(self):
        """Get financial report template"""
        return """
        {% extends "pdf/base.html" %}
        {% block content %}
        <div class="section">
            <h2 class="section-title">
                {% if is_rtl %}التقرير المالي{% else %}Financial Report{% endif %}
            </h2>

            {% if budgets %}
            <h3>{% if is_rtl %}الميزانيات{% else %}Budgets{% endif %}</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>{% if is_rtl %}اسم الميزانية{% else %}Budget Name{% endif %}</th>
                        <th>{% if is_rtl %}المبلغ المخصص{% else %}Allocated Amount{% endif %}</th>
                        <th>{% if is_rtl %}المبلغ المستخدم{% else %}Used Amount{% endif %}</th>
                        <th>{% if is_rtl %}المتبقي{% else %}Remaining{% endif %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for budget in budgets %}
                    <tr>
                        <td>{{ budget.name }}</td>
                        <td>{{ budget.allocated_amount }}</td>
                        <td>{{ budget.used_amount|default:0 }}</td>
                        <td>{{ budget.remaining_amount|default:budget.allocated_amount }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}

            {% if expenses %}
            <h3>{% if is_rtl %}المصروفات{% else %}Expenses{% endif %}</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>{% if is_rtl %}الوصف{% else %}Description{% endif %}</th>
                        <th>{% if is_rtl %}المبلغ{% else %}Amount{% endif %}</th>
                        <th>{% if is_rtl %}التاريخ{% else %}Date{% endif %}</th>
                        <th>{% if is_rtl %}الموظف{% else %}Employee{% endif %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for expense in expenses %}
                    <tr>
                        <td>{{ expense.description }}</td>
                        <td>{{ expense.amount }}</td>
                        <td>{{ expense.date|date:"Y-m-d" }}</td>
                        <td>{{ expense.employee.user.get_full_name|default:expense.employee.user.username }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endblock %}
        """

    def _get_expense_template(self):
        """Get expense report template"""
        expense_template_path = os.path.join(
            os.path.dirname(__file__),
            '../../templates/pdf/expense_report.html'
        )

        if os.path.exists(expense_template_path):
            with open(expense_template_path, 'r', encoding='utf-8') as f:
                return f.read()

        return """
        {% extends "pdf/base.html" %}
        {% block content %}
        <div class="section">
            <h2 class="section-title">
                {% if is_rtl %}تقرير المصروفات{% else %}Expense Report{% endif %}
            </h2>
            <p>Expense report template content goes here.</p>
        </div>
        {% endblock %}
        """

    def _get_sales_template(self):
        """Get sales report template"""
        sales_template_path = os.path.join(
            os.path.dirname(__file__),
            '../../templates/pdf/sales_report.html'
        )

        if os.path.exists(sales_template_path):
            with open(sales_template_path, 'r', encoding='utf-8') as f:
                return f.read()

        return """
        {% extends "pdf/base.html" %}
        {% block content %}
        <div class="section">
            <h2 class="section-title">
                {% if is_rtl %}تقرير المبيعات{% else %}Sales Report{% endif %}
            </h2>
            <p>Sales report template content goes here.</p>
        </div>
        {% endblock %}
        """

    def _get_support_template(self):
        """Get support ticket report template"""
        support_template_path = os.path.join(
            os.path.dirname(__file__),
            '../../templates/pdf/support_ticket_report.html'
        )

        if os.path.exists(support_template_path):
            with open(support_template_path, 'r', encoding='utf-8') as f:
                return f.read()

        return """
        {% extends "pdf/base.html" %}
        {% block content %}
        <div class="section">
            <h2 class="section-title">
                {% if is_rtl %}تقرير تذاكر الدعم{% else %}Support Ticket Report{% endif %}
            </h2>
            <p>Support ticket report template content goes here.</p>
        </div>
        {% endblock %}
        """

    def _get_asset_template(self):
        """Get asset report template"""
        asset_template_path = os.path.join(
            os.path.dirname(__file__),
            '../../templates/pdf/asset_report.html'
        )

        if os.path.exists(asset_template_path):
            with open(asset_template_path, 'r', encoding='utf-8') as f:
                return f.read()

        return """
        {% extends "pdf/base.html" %}
        {% block content %}
        <div class="section">
            <h2 class="section-title">
                {% if is_rtl %}تقرير الأصول{% else %}Asset Report{% endif %}
            </h2>
            <p>Asset report template content goes here.</p>
        </div>
        {% endblock %}
        """

    def _get_customer_template(self):
        """Get customer report template"""
        customer_template_path = os.path.join(
            os.path.dirname(__file__),
            '../../templates/pdf/customer_report.html'
        )

        if os.path.exists(customer_template_path):
            with open(customer_template_path, 'r', encoding='utf-8') as f:
                return f.read()

        return """
        {% extends "pdf/base.html" %}
        {% block content %}
        <div class="section">
            <h2 class="section-title">
                {% if is_rtl %}تقرير العملاء{% else %}Customer Report{% endif %}
            </h2>
            <p>Customer report template content goes here.</p>
        </div>
        {% endblock %}
        """

    def _get_kpi_template(self):
        """Get KPI report template"""
        kpi_template_path = os.path.join(
            os.path.dirname(__file__),
            '../../templates/pdf/kpi_report.html'
        )

        if os.path.exists(kpi_template_path):
            with open(kpi_template_path, 'r', encoding='utf-8') as f:
                return f.read()

        return """
        {% extends "pdf/base.html" %}
        {% block content %}
        <div class="section">
            <h2 class="section-title">
                {% if is_rtl %}تقرير مؤشرات الأداء{% else %}KPI Report{% endif %}
            </h2>
            <p>KPI report template content goes here.</p>
        </div>
        {% endblock %}
        """