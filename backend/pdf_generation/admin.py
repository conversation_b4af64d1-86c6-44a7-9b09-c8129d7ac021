"""
PDF Generation Admin Configuration
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import PDFTemplate, PDFGenerationHistory, PDFSettings


@admin.register(PDFTemplate)
class PDFTemplateAdmin(admin.ModelAdmin):
    """Admin interface for PDF templates"""

    list_display = [
        'name', 'template_type', 'language', 'is_active',
        'created_by', 'created_at', 'version'
    ]
    list_filter = ['template_type', 'language', 'is_active', 'created_at']
    search_fields = ['name', 'name_ar', 'description', 'template_type']
    readonly_fields = ['id', 'created_at', 'updated_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'name_ar', 'template_type', 'language', 'description', 'description_ar')
        }),
        ('Template Content', {
            'fields': ('html_template', 'css_styles'),
            'classes': ('wide',)
        }),
        ('Configuration', {
            'fields': ('page_size', 'orientation', 'is_active', 'version')
        }),
        ('Metadata', {
            'fields': ('id', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new template
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(PDFGenerationHistory)
class PDFGenerationHistoryAdmin(admin.ModelAdmin):
    """Admin interface for PDF generation history"""

    list_display = [
        'filename', 'template_type_display', 'status', 'progress',
        'generated_by', 'started_at', 'file_size_display', 'download_count'
    ]
    list_filter = ['status', 'template__template_type', 'template__language', 'started_at']
    search_fields = ['filename', 'generated_by__username', 'template__name']
    readonly_fields = [
        'id', 'template', 'generated_by', 'filename', 'file_path',
        'file_size', 'started_at', 'completed_at', 'processing_time',
        'generation_parameters', 'error_details'
    ]

    fieldsets = (
        ('Generation Info', {
            'fields': ('template', 'generated_by', 'filename', 'status', 'progress')
        }),
        ('File Details', {
            'fields': ('file_path', 'file_size', 'download_count', 'last_downloaded')
        }),
        ('Timing', {
            'fields': ('started_at', 'completed_at', 'processing_time')
        }),
        ('Data', {
            'fields': ('data_source', 'record_count', 'generation_parameters'),
            'classes': ('collapse',)
        }),
        ('Errors', {
            'fields': ('error_message', 'error_details'),
            'classes': ('collapse',)
        })
    )

    def template_type_display(self, obj):
        return f"{obj.template.template_type} ({obj.template.language})"
    template_type_display.short_description = 'Template Type'

    def file_size_display(self, obj):
        if obj.file_size:
            if obj.file_size < 1024:
                return f"{obj.file_size} B"
            elif obj.file_size < 1024 * 1024:
                return f"{obj.file_size / 1024:.1f} KB"
            else:
                return f"{obj.file_size / (1024 * 1024):.1f} MB"
        return "-"
    file_size_display.short_description = 'File Size'

    def has_add_permission(self, request):
        return False  # Don't allow manual creation


@admin.register(PDFSettings)
class PDFSettingsAdmin(admin.ModelAdmin):
    """Admin interface for PDF settings"""

    list_display = ['company_name', 'updated_at', 'updated_by']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Font Settings', {
            'fields': ('default_font_en', 'default_font_ar', 'font_size')
        }),
        ('Page Settings', {
            'fields': ('margin_top', 'margin_bottom', 'margin_left', 'margin_right')
        }),
        ('Header/Footer', {
            'fields': (
                'include_header', 'include_footer',
                'header_text', 'header_text_ar',
                'footer_text', 'footer_text_ar'
            )
        }),
        ('Branding', {
            'fields': (
                'company_logo', 'company_name', 'company_name_ar',
                'company_address', 'company_address_ar'
            )
        }),
        ('Performance', {
            'fields': ('max_records_per_pdf', 'enable_compression', 'quality_level')
        }),
        ('Storage', {
            'fields': ('storage_path', 'auto_cleanup_days')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'updated_by'),
            'classes': ('collapse',)
        })
    )

    def save_model(self, request, obj, form, change):
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)

    def has_delete_permission(self, request, obj=None):
        return False  # Don't allow deletion of settings
