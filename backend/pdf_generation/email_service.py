"""
Email Integration Service for PDF Generation
Handles sending PDFs via email with professional templates
"""

import os
import logging
from typing import List, Optional, Dict, Any
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from django.contrib.auth.models import User
from django.utils import timezone
from .models import PDFGenerationHistory, EmailLog

logger = logging.getLogger(__name__)

class EmailService:
    """Service for sending PDFs via email"""
    
    def __init__(self):
        self.from_email = getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>')
        self.company_name = getattr(settings, 'COMPANY_NAME', 'Your Company')
        
    def send_pdf_email(
        self,
        pdf_history: PDFGenerationHistory,
        recipients: List[str],
        subject: Optional[str] = None,
        message: Optional[str] = None,
        sender: Optional[User] = None,
        language: str = 'en',
        email_template: str = 'default',
        cc_recipients: Optional[List[str]] = None,
        bcc_recipients: Optional[List[str]] = None,
        priority: str = 'normal'
    ) -> Dict[str, Any]:
        """
        Send PDF via email with professional template
        
        Args:
            pdf_history: PDFGenerationHistory instance
            recipients: List of recipient email addresses
            subject: Email subject (optional, will be auto-generated)
            message: Custom message (optional)
            sender: User who is sending the email
            language: Email language ('en' or 'ar')
            email_template: Template type ('default', 'invoice', 'report', 'formal')
            cc_recipients: CC recipients
            bcc_recipients: BCC recipients
            priority: Email priority ('low', 'normal', 'high', 'urgent')
            
        Returns:
            Dict with success status and details
        """
        
        try:
            # Validate inputs
            if not recipients:
                raise ValueError("At least one recipient is required")
            
            if not pdf_history.file_path or not os.path.exists(pdf_history.file_path):
                raise ValueError("PDF file not found")
            
            # Generate subject if not provided
            if not subject:
                subject = self._generate_subject(pdf_history, language)
            
            # Prepare email context
            context = {
                'pdf_history': pdf_history,
                'sender': sender,
                'company_name': self.company_name,
                'message': message,
                'language': language,
                'is_rtl': language == 'ar',
                'generated_date': pdf_history.created_at,
                'pdf_type': pdf_history.template.name,
                'priority': priority,
            }
            
            # Render email templates
            html_content = self._render_email_template(email_template, context, language)
            text_content = self._render_text_template(email_template, context, language)
            
            # Create email message
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=self.from_email,
                to=recipients,
                cc=cc_recipients or [],
                bcc=bcc_recipients or [],
            )
            
            # Add HTML alternative
            email.attach_alternative(html_content, "text/html")
            
            # Attach PDF file
            with open(pdf_history.file_path, 'rb') as pdf_file:
                email.attach(
                    filename=os.path.basename(pdf_history.file_path),
                    content=pdf_file.read(),
                    mimetype='application/pdf'
                )
            
            # Set priority headers
            if priority in ['high', 'urgent']:
                email.extra_headers['X-Priority'] = '1' if priority == 'urgent' else '2'
                email.extra_headers['X-MSMail-Priority'] = 'High'
                email.extra_headers['Importance'] = 'High'
            elif priority == 'low':
                email.extra_headers['X-Priority'] = '5'
                email.extra_headers['X-MSMail-Priority'] = 'Low'
                email.extra_headers['Importance'] = 'Low'
            
            # Send email
            email.send()
            
            # Log email
            email_log = self._log_email(
                pdf_history=pdf_history,
                recipients=recipients,
                subject=subject,
                sender=sender,
                status='sent',
                cc_recipients=cc_recipients,
                bcc_recipients=bcc_recipients,
                priority=priority
            )
            
            logger.info(f"PDF email sent successfully to {len(recipients)} recipients")
            
            return {
                'success': True,
                'message': f'Email sent successfully to {len(recipients)} recipients',
                'email_log_id': email_log.id,
                'recipients': recipients,
                'subject': subject
            }
            
        except Exception as e:
            logger.error(f"Failed to send PDF email: {str(e)}")
            
            # Log failed email
            self._log_email(
                pdf_history=pdf_history,
                recipients=recipients,
                subject=subject or 'Failed to generate subject',
                sender=sender,
                status='failed',
                error_message=str(e),
                cc_recipients=cc_recipients,
                bcc_recipients=bcc_recipients,
                priority=priority
            )
            
            return {
                'success': False,
                'message': f'Failed to send email: {str(e)}',
                'error': str(e)
            }
    
    def send_bulk_pdf_emails(
        self,
        pdf_history: PDFGenerationHistory,
        recipient_list: List[Dict[str, Any]],
        sender: Optional[User] = None,
        language: str = 'en',
        email_template: str = 'default'
    ) -> Dict[str, Any]:
        """
        Send PDF to multiple recipients with personalized messages
        
        Args:
            pdf_history: PDFGenerationHistory instance
            recipient_list: List of dicts with 'email', 'name', 'message' keys
            sender: User sending the emails
            language: Email language
            email_template: Template type
            
        Returns:
            Dict with bulk send results
        """
        
        results = {
            'total': len(recipient_list),
            'sent': 0,
            'failed': 0,
            'details': []
        }
        
        for recipient_info in recipient_list:
            try:
                email = recipient_info.get('email')
                name = recipient_info.get('name', email)
                custom_message = recipient_info.get('message', '')
                
                if not email:
                    continue
                
                # Personalized subject
                subject = f"Report for {name}" if language == 'en' else f"تقرير لـ {name}"
                
                result = self.send_pdf_email(
                    pdf_history=pdf_history,
                    recipients=[email],
                    subject=subject,
                    message=custom_message,
                    sender=sender,
                    language=language,
                    email_template=email_template
                )
                
                if result['success']:
                    results['sent'] += 1
                else:
                    results['failed'] += 1
                
                results['details'].append({
                    'email': email,
                    'name': name,
                    'success': result['success'],
                    'message': result['message']
                })
                
            except Exception as e:
                results['failed'] += 1
                results['details'].append({
                    'email': recipient_info.get('email', 'unknown'),
                    'name': recipient_info.get('name', 'unknown'),
                    'success': False,
                    'message': str(e)
                })
        
        return results
    
    def _generate_subject(self, pdf_history: PDFGenerationHistory, language: str) -> str:
        """Generate email subject based on PDF type and language"""
        
        template_name = pdf_history.template.name
        company_name = self.company_name
        
        if language == 'ar':
            subjects = {
                'hr_report': f'تقرير الموارد البشرية - {company_name}',
                'employee_report': f'تقرير الموظفين - {company_name}',
                'attendance_report': f'تقرير الحضور - {company_name}',
                'financial_report': f'التقرير المالي - {company_name}',
                'expense_report': f'تقرير المصروفات - {company_name}',
                'sales_report': f'تقرير المبيعات - {company_name}',
                'asset_report': f'تقرير الأصول - {company_name}',
                'customer_report': f'تقرير العملاء - {company_name}',
                'kpi_report': f'تقرير مؤشرات الأداء - {company_name}',
                'invoice': f'فاتورة من {company_name}',
            }
        else:
            subjects = {
                'hr_report': f'HR Report - {company_name}',
                'employee_report': f'Employee Report - {company_name}',
                'attendance_report': f'Attendance Report - {company_name}',
                'financial_report': f'Financial Report - {company_name}',
                'expense_report': f'Expense Report - {company_name}',
                'sales_report': f'Sales Report - {company_name}',
                'asset_report': f'Asset Report - {company_name}',
                'customer_report': f'Customer Report - {company_name}',
                'kpi_report': f'KPI Report - {company_name}',
                'invoice': f'Invoice from {company_name}',
            }
        
        # Try to match template type
        for key, subject in subjects.items():
            if key in pdf_history.template.template_type.lower():
                return subject
        
        # Default subject
        return f'{template_name} - {company_name}'
    
    def _render_email_template(self, template_type: str, context: Dict, language: str) -> str:
        """Render HTML email template"""
        
        template_name = f'emails/{template_type}_{language}.html'
        
        try:
            return render_to_string(template_name, context)
        except:
            # Fallback to default template
            return render_to_string(f'emails/default_{language}.html', context)
    
    def _render_text_template(self, template_type: str, context: Dict, language: str) -> str:
        """Render text email template"""
        
        template_name = f'emails/{template_type}_{language}.txt'
        
        try:
            return render_to_string(template_name, context)
        except:
            # Fallback to default template
            return render_to_string(f'emails/default_{language}.txt', context)
    
    def _log_email(
        self,
        pdf_history: PDFGenerationHistory,
        recipients: List[str],
        subject: str,
        sender: Optional[User],
        status: str,
        cc_recipients: Optional[List[str]] = None,
        bcc_recipients: Optional[List[str]] = None,
        priority: str = 'normal',
        error_message: Optional[str] = None
    ) -> 'EmailLog':
        """Log email sending activity"""
        
        return EmailLog.objects.create(
            pdf_history=pdf_history,
            recipients=', '.join(recipients),
            cc_recipients=', '.join(cc_recipients or []),
            bcc_recipients=', '.join(bcc_recipients or []),
            subject=subject,
            sender=sender,
            status=status,
            priority=priority,
            error_message=error_message,
            sent_at=timezone.now() if status == 'sent' else None
        )

# Global email service instance
email_service = EmailService()
