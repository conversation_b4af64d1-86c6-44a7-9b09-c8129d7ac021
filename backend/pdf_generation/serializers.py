"""
PDF Generation Serializers
"""

from rest_framework import serializers
from .models import PDFTemplate, PDFGenerationHistory, PDFSettings


class PDFTemplateSerializer(serializers.ModelSerializer):
    """Serializer for PDF templates"""
    
    created_by_name = serializers.Char<PERSON>ield(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = PDFTemplate
        fields = [
            'id', 'name', 'name_ar', 'template_type', 'language',
            'description', 'description_ar', 'html_template', 'css_styles',
            'page_size', 'orientation', 'is_active', 'created_by', 'created_by_name',
            'created_at', 'updated_at', 'version'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at']


class PDFGenerationHistorySerializer(serializers.ModelSerializer):
    """Serializer for PDF generation history"""
    
    template_name = serializers.CharField(source='template.name', read_only=True)
    template_type = serializers.CharField(source='template.template_type', read_only=True)
    generated_by_name = serializers.CharField(source='generated_by.get_full_name', read_only=True)
    
    class Meta:
        model = PDFGenerationHistory
        fields = [
            'id', 'template', 'template_name', 'template_type',
            'generated_by', 'generated_by_name', 'filename', 'file_path',
            'file_size', 'status', 'progress', 'started_at', 'completed_at',
            'processing_time', 'generation_parameters', 'data_source',
            'record_count', 'error_message', 'error_details',
            'download_count', 'last_downloaded'
        ]
        read_only_fields = [
            'id', 'started_at', 'completed_at', 'processing_time',
            'file_size', 'download_count', 'last_downloaded'
        ]


class PDFSettingsSerializer(serializers.ModelSerializer):
    """Serializer for PDF settings"""
    
    updated_by_name = serializers.CharField(source='updated_by.get_full_name', read_only=True)
    
    class Meta:
        model = PDFSettings
        fields = [
            'id', 'default_font_en', 'default_font_ar', 'font_size',
            'margin_top', 'margin_bottom', 'margin_left', 'margin_right',
            'include_header', 'include_footer', 'header_text', 'header_text_ar',
            'footer_text', 'footer_text_ar', 'company_logo', 'company_name',
            'company_name_ar', 'company_address', 'company_address_ar',
            'max_records_per_pdf', 'enable_compression', 'quality_level',
            'storage_path', 'auto_cleanup_days', 'created_at', 'updated_at',
            'updated_by', 'updated_by_name'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'updated_by']
