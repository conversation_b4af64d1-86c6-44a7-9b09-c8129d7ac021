"""
Digital Signature Service for PDF Documents
Handles digital signing, verification, and certificate management
"""

import os
import hashlib
import base64
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.serialization import pkcs12
from cryptography import x509
from cryptography.x509.oid import NameOID
from django.conf import settings
from django.contrib.auth.models import User
from django.utils import timezone
from .models import DigitalSignature, SignatureCertificate

logger = logging.getLogger(__name__)

class SignatureService:
    """Service for digital signatures and certificates"""
    
    def __init__(self):
        self.signature_dir = os.path.join(settings.MEDIA_ROOT, 'signatures')
        self.certificate_dir = os.path.join(settings.MEDIA_ROOT, 'certificates')
        
        # Create directories if they don't exist
        os.makedirs(self.signature_dir, exist_ok=True)
        os.makedirs(self.certificate_dir, exist_ok=True)
    
    def create_certificate(
        self,
        user: User,
        organization: str,
        country: str = 'SA',
        state: str = 'Riyadh',
        city: str = 'Riyadh',
        email: Optional[str] = None,
        validity_days: int = 365
    ) -> Dict[str, Any]:
        """
        Create a digital certificate for a user
        
        Args:
            user: User for whom to create certificate
            organization: Organization name
            country: Country code (default: SA for Saudi Arabia)
            state: State/Province
            city: City
            email: Email address
            validity_days: Certificate validity in days
            
        Returns:
            Dict with certificate details and file paths
        """
        
        try:
            # Generate private key
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048,
            )
            
            # Create certificate subject
            subject = x509.Name([
                x509.NameAttribute(NameOID.COUNTRY_NAME, country),
                x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, state),
                x509.NameAttribute(NameOID.LOCALITY_NAME, city),
                x509.NameAttribute(NameOID.ORGANIZATION_NAME, organization),
                x509.NameAttribute(NameOID.COMMON_NAME, user.get_full_name() or user.username),
            ])
            
            if email:
                subject = x509.Name(list(subject) + [
                    x509.NameAttribute(NameOID.EMAIL_ADDRESS, email)
                ])
            
            # Create certificate
            cert = x509.CertificateBuilder().subject_name(
                subject
            ).issuer_name(
                subject  # Self-signed
            ).public_key(
                private_key.public_key()
            ).serial_number(
                x509.random_serial_number()
            ).not_valid_before(
                datetime.utcnow()
            ).not_valid_after(
                datetime.utcnow() + timedelta(days=validity_days)
            ).add_extension(
                x509.SubjectAlternativeName([
                    x509.DNSName("localhost"),
                ]),
                critical=False,
            ).add_extension(
                x509.KeyUsage(
                    digital_signature=True,
                    content_commitment=True,
                    key_encipherment=False,
                    data_encipherment=False,
                    key_agreement=False,
                    key_cert_sign=True,
                    crl_sign=False,
                    encipher_only=False,
                    decipher_only=False,
                ),
                critical=True,
            ).sign(private_key, hashes.SHA256())
            
            # Generate file names
            cert_filename = f"cert_{user.id}_{int(datetime.now().timestamp())}.pem"
            key_filename = f"key_{user.id}_{int(datetime.now().timestamp())}.pem"
            p12_filename = f"cert_{user.id}_{int(datetime.now().timestamp())}.p12"
            
            cert_path = os.path.join(self.certificate_dir, cert_filename)
            key_path = os.path.join(self.certificate_dir, key_filename)
            p12_path = os.path.join(self.certificate_dir, p12_filename)
            
            # Save certificate
            with open(cert_path, "wb") as f:
                f.write(cert.public_bytes(serialization.Encoding.PEM))
            
            # Save private key
            with open(key_path, "wb") as f:
                f.write(private_key.private_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PrivateFormat.PKCS8,
                    encryption_algorithm=serialization.NoEncryption()
                ))
            
            # Create PKCS#12 file (for easier distribution)
            p12_password = f"cert_{user.id}_{int(datetime.now().timestamp())}"
            p12_data = pkcs12.serialize_key_and_certificates(
                name=f"{user.get_full_name() or user.username}".encode(),
                key=private_key,
                cert=cert,
                cas=None,
                encryption_algorithm=serialization.BestAvailableEncryption(p12_password.encode())
            )
            
            with open(p12_path, "wb") as f:
                f.write(p12_data)
            
            # Save to database
            cert_record = SignatureCertificate.objects.create(
                user=user,
                certificate_name=f"{user.get_full_name() or user.username} Certificate",
                organization=organization,
                country=country,
                state=state,
                city=city,
                email=email or user.email,
                certificate_file=cert_path,
                private_key_file=key_path,
                p12_file=p12_path,
                p12_password=p12_password,
                valid_from=timezone.now(),
                valid_until=timezone.now() + timedelta(days=validity_days),
                serial_number=str(cert.serial_number),
                fingerprint=cert.fingerprint(hashes.SHA256()).hex(),
                is_active=True
            )
            
            logger.info(f"Certificate created for user {user.username}")
            
            return {
                'success': True,
                'certificate_id': cert_record.id,
                'certificate_path': cert_path,
                'private_key_path': key_path,
                'p12_path': p12_path,
                'p12_password': p12_password,
                'fingerprint': cert_record.fingerprint,
                'valid_until': cert_record.valid_until,
                'message': 'Certificate created successfully'
            }
            
        except Exception as e:
            logger.error(f"Failed to create certificate: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': f'Failed to create certificate: {str(e)}'
            }
    
    def sign_pdf(
        self,
        pdf_path: str,
        certificate_id: int,
        reason: str = "Document approval",
        location: str = "Saudi Arabia",
        contact_info: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Digitally sign a PDF document
        
        Args:
            pdf_path: Path to PDF file to sign
            certificate_id: ID of certificate to use for signing
            reason: Reason for signing
            location: Location of signing
            contact_info: Contact information
            
        Returns:
            Dict with signing results
        """
        
        try:
            # Get certificate
            certificate = SignatureCertificate.objects.get(
                id=certificate_id,
                is_active=True
            )
            
            if not certificate.is_valid():
                raise ValueError("Certificate is expired or invalid")
            
            if not os.path.exists(pdf_path):
                raise ValueError("PDF file not found")
            
            # Read PDF content
            with open(pdf_path, 'rb') as pdf_file:
                pdf_content = pdf_file.read()
            
            # Create signature hash
            pdf_hash = hashlib.sha256(pdf_content).hexdigest()
            
            # Load private key
            with open(certificate.private_key_file, 'rb') as key_file:
                private_key = serialization.load_pem_private_key(
                    key_file.read(),
                    password=None
                )
            
            # Create signature
            signature_data = private_key.sign(
                pdf_content,
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )
            
            # Encode signature
            signature_b64 = base64.b64encode(signature_data).decode('utf-8')
            
            # Generate signed PDF filename
            base_name = os.path.splitext(os.path.basename(pdf_path))[0]
            signed_filename = f"{base_name}_signed_{int(datetime.now().timestamp())}.pdf"
            signed_path = os.path.join(self.signature_dir, signed_filename)
            
            # For now, copy the original PDF (in a real implementation, you'd embed the signature)
            # This is a simplified version - real PDF signing requires specialized libraries
            import shutil
            shutil.copy2(pdf_path, signed_path)
            
            # Save signature record
            signature_record = DigitalSignature.objects.create(
                certificate=certificate,
                document_path=pdf_path,
                signed_document_path=signed_path,
                signature_data=signature_b64,
                document_hash=pdf_hash,
                reason=reason,
                location=location,
                contact_info=contact_info or certificate.email,
                signed_at=timezone.now(),
                is_valid=True
            )
            
            logger.info(f"PDF signed successfully: {signed_path}")
            
            return {
                'success': True,
                'signature_id': signature_record.id,
                'signed_pdf_path': signed_path,
                'signature_data': signature_b64,
                'document_hash': pdf_hash,
                'signed_at': signature_record.signed_at,
                'signer': certificate.user.get_full_name() or certificate.user.username,
                'message': 'PDF signed successfully'
            }
            
        except SignatureCertificate.DoesNotExist:
            return {
                'success': False,
                'error': 'Certificate not found',
                'message': 'Certificate not found or inactive'
            }
        except Exception as e:
            logger.error(f"Failed to sign PDF: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': f'Failed to sign PDF: {str(e)}'
            }
    
    def verify_signature(self, signature_id: int) -> Dict[str, Any]:
        """
        Verify a digital signature
        
        Args:
            signature_id: ID of signature to verify
            
        Returns:
            Dict with verification results
        """
        
        try:
            signature = DigitalSignature.objects.get(id=signature_id)
            
            # Check if certificate is still valid
            if not signature.certificate.is_valid():
                return {
                    'success': False,
                    'valid': False,
                    'message': 'Certificate is expired or revoked',
                    'details': {
                        'certificate_expired': True,
                        'signature_id': signature_id
                    }
                }
            
            # Verify document hasn't been tampered with
            if os.path.exists(signature.document_path):
                with open(signature.document_path, 'rb') as f:
                    current_hash = hashlib.sha256(f.read()).hexdigest()
                
                if current_hash != signature.document_hash:
                    return {
                        'success': True,
                        'valid': False,
                        'message': 'Document has been modified after signing',
                        'details': {
                            'document_tampered': True,
                            'original_hash': signature.document_hash,
                            'current_hash': current_hash
                        }
                    }
            
            # Load certificate and verify signature
            with open(signature.certificate.certificate_file, 'rb') as cert_file:
                cert_data = cert_file.read()
                cert = x509.load_pem_x509_certificate(cert_data)
            
            # Decode signature
            signature_data = base64.b64decode(signature.signature_data)
            
            # Verify signature (simplified - real implementation would verify against PDF content)
            try:
                with open(signature.document_path, 'rb') as f:
                    document_content = f.read()
                
                cert.public_key().verify(
                    signature_data,
                    document_content,
                    padding.PSS(
                        mgf=padding.MGF1(hashes.SHA256()),
                        salt_length=padding.PSS.MAX_LENGTH
                    ),
                    hashes.SHA256()
                )
                
                verification_valid = True
                verification_message = "Signature is valid"
                
            except Exception:
                verification_valid = False
                verification_message = "Signature verification failed"
            
            return {
                'success': True,
                'valid': verification_valid,
                'message': verification_message,
                'details': {
                    'signature_id': signature_id,
                    'signer': signature.certificate.user.get_full_name(),
                    'signed_at': signature.signed_at,
                    'certificate_valid': signature.certificate.is_valid(),
                    'document_hash_valid': True,
                    'signature_valid': verification_valid
                }
            }
            
        except DigitalSignature.DoesNotExist:
            return {
                'success': False,
                'valid': False,
                'message': 'Signature not found',
                'error': 'Signature record not found'
            }
        except Exception as e:
            logger.error(f"Failed to verify signature: {str(e)}")
            return {
                'success': False,
                'valid': False,
                'message': f'Verification failed: {str(e)}',
                'error': str(e)
            }
    
    def get_user_certificates(self, user: User) -> List[Dict[str, Any]]:
        """Get all certificates for a user"""
        
        certificates = SignatureCertificate.objects.filter(
            user=user,
            is_active=True
        ).order_by('-created_at')
        
        return [
            {
                'id': cert.id,
                'name': cert.certificate_name,
                'organization': cert.organization,
                'valid_from': cert.valid_from,
                'valid_until': cert.valid_until,
                'is_valid': cert.is_valid(),
                'fingerprint': cert.fingerprint,
                'email': cert.email
            }
            for cert in certificates
        ]
    
    def revoke_certificate(self, certificate_id: int, reason: str = "User request") -> Dict[str, Any]:
        """Revoke a certificate"""
        
        try:
            certificate = SignatureCertificate.objects.get(id=certificate_id)
            certificate.is_active = False
            certificate.revoked_at = timezone.now()
            certificate.revocation_reason = reason
            certificate.save()
            
            return {
                'success': True,
                'message': 'Certificate revoked successfully'
            }
            
        except SignatureCertificate.DoesNotExist:
            return {
                'success': False,
                'error': 'Certificate not found'
            }

# Global signature service instance
signature_service = SignatureService()
