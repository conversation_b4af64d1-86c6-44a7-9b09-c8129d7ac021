"""
PDF Generation URLs
Centralized PDF generation endpoints for the EMS application
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# Create router for ViewSets
router = DefaultRouter()
router.register(r'templates', views.PDFTemplateViewSet)
router.register(r'history', views.PDFGenerationHistoryViewSet)
router.register(r'settings', views.PDFSettingsViewSet)

urlpatterns = [
    # ViewSet routes
    path('api/pdf/', include(router.urls)),

    # Specific PDF generation endpoints
    path('api/pdf/generate/hr-report/', views.generate_hr_pdf, name='generate-hr-pdf'),
    path('api/pdf/generate/<str:template_type>/', views.generate_template_pdf, name='generate-template-pdf'),

    # Utility endpoints
    path('api/pdf/status/', views.pdf_status, name='pdf-status'),
    path('api/pdf/cleanup/', views.cleanup_old_pdfs, name='cleanup-old-pdfs'),

    # Email Integration
    path('api/pdf/email/send/', views.send_pdf_email, name='send-pdf-email'),
    path('api/pdf/email/bulk/', views.send_bulk_pdf_emails, name='send-bulk-pdf-emails'),
    path('api/pdf/email/history/', views.email_history, name='email-history'),

    # Digital Signatures
    path('api/pdf/signature/create-certificate/', views.create_certificate, name='create-certificate'),
    path('api/pdf/signature/sign/', views.sign_pdf, name='sign-pdf'),
    path('api/pdf/signature/verify/<int:signature_id>/', views.verify_signature, name='verify-signature'),
    path('api/pdf/signature/certificates/', views.user_certificates, name='user-certificates'),
]
