"""
PDF Generation Services
Provides comprehensive PDF generation functionality with Arabic support
"""

import os
import io
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from django.conf import settings
from django.template import Template, Context
from django.http import HttpResponse
from django.core.files.storage import default_storage
from django.utils import timezone
from django.db.models import QuerySet

# PDF generation libraries
WEASYPRINT_AVAILABLE = False
try:
    import warnings
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        from weasyprint import HTML, CSS
        from weasyprint.text.fonts import FontConfiguration
    WEASYPRINT_AVAILABLE = True
except (ImportError, OSError) as e:
    # WeasyPrint may fail on macOS due to missing system libraries
    # Using ReportLab as fallback - this is expected behavior
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"WeasyPrint not available, using ReportLab fallback: {type(e).__name__}")
    WEASYPRINT_AVAILABLE = False

REPORTLAB_AVAILABLE = False
try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter, A4, landscape, portrait
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib import colors
    from reportlab.lib.units import cm
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError as e:
    print(f"ReportLab not available: {e}")
    REPORTLAB_AVAILABLE = False

from .models import PDFTemplate, PDFGenerationHistory, PDFSettings

logger = logging.getLogger(__name__)


class PDFGenerationError(Exception):
    """Custom exception for PDF generation errors"""
    pass


class PDFGenerationService:
    """
    Main service class for PDF generation with Arabic support
    """

    def __init__(self):
        self.settings = None
        self.font_config = None
        if WEASYPRINT_AVAILABLE:
            self.font_config = FontConfiguration()

    def _get_pdf_settings(self) -> PDFSettings:
        """Get or create PDF settings"""
        if self.settings is not None:
            return self.settings

        try:
            self.settings = PDFSettings.objects.latest('updated_at')
            return self.settings
        except PDFSettings.DoesNotExist:
            # Create default settings
            from django.contrib.auth.models import User
            admin_user = User.objects.filter(is_superuser=True).first()
            if not admin_user:
                admin_user = User.objects.create_user(
                    username='pdf_admin',
                    email='<EMAIL>',
                    is_staff=True,
                    is_superuser=True
                )

            self.settings = PDFSettings.objects.create(
                updated_by=admin_user,
                company_name="Enterprise Management System",
                company_name_ar="نظام إدارة المؤسسات"
            )
            return self.settings
        except Exception:
            # Return default settings if database is not ready
            class DefaultSettings:
                company_name = "Enterprise Management System"
                company_name_ar = "نظام إدارة المؤسسات"
                company_address = ""
                company_address_ar = ""
                margin_top = 2.0
                margin_bottom = 2.0
                margin_left = 2.0
                margin_right = 2.0
                font_size = 12
                storage_path = 'generated_pdfs/'
                auto_cleanup_days = 30

            return DefaultSettings()

    def generate_pdf(
        self,
        template_type: str,
        language: str = 'en',
        data: Dict[str, Any] = None,
        context: Dict[str, Any] = None,
        user = None,
        filename: str = None
    ) -> PDFGenerationHistory:
        """
        Generate PDF using specified template and data

        Args:
            template_type: Type of template to use
            language: Language for the PDF ('en' or 'ar')
            data: Data to populate the template
            context: Additional context variables
            user: User generating the PDF
            filename: Custom filename for the PDF

        Returns:
            PDFGenerationHistory: Generation history record
        """
        if not data:
            data = {}
        if not context:
            context = {}
        if not filename:
            filename = f"{template_type}_{language}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

        # Get template
        try:
            template = PDFTemplate.objects.get(
                template_type=template_type,
                language=language,
                is_active=True
            )
        except PDFTemplate.DoesNotExist:
            raise PDFGenerationError(f"Template not found: {template_type} ({language})")

        # Create generation history record
        history = PDFGenerationHistory.objects.create(
            template=template,
            generated_by=user,
            filename=filename,
            data_source=template_type,
            record_count=len(data) if isinstance(data, (list, QuerySet)) else 1,
            generation_parameters={
                'template_type': template_type,
                'language': language,
                'context_keys': list(context.keys()) if context else []
            }
        )

        try:
            # Update status to processing
            history.status = 'processing'
            history.progress = 10
            history.save()

            # Prepare context for template rendering
            template_context = {
                'data': data,
                'settings': self.settings,
                'language': language,
                'is_rtl': language == 'ar',
                'generated_at': timezone.now(),
                'generated_by': user.get_full_name() if user else 'System',
                **context
            }

            history.progress = 30
            history.save()

            # Ensure settings are loaded
            if self.settings is None:
                self.settings = self._get_pdf_settings()

            # Generate PDF content
            if WEASYPRINT_AVAILABLE:
                pdf_content = self._generate_with_weasyprint(template, template_context, language)
            elif REPORTLAB_AVAILABLE:
                pdf_content = self._generate_with_reportlab(template, template_context, language)
            else:
                raise PDFGenerationError("No PDF generation library available")

            history.progress = 80
            history.save()

            # Save PDF file
            file_path = self._save_pdf_file(pdf_content, filename)

            # Mark as completed
            history.mark_completed(file_path, len(pdf_content))

            return history

        except Exception as e:
            logger.error(f"PDF generation failed: {str(e)}")
            history.mark_failed(str(e), {'error_type': type(e).__name__})
            raise PDFGenerationError(f"PDF generation failed: {str(e)}")

    def _generate_with_weasyprint(self, template: PDFTemplate, context: Dict, language: str) -> bytes:
        """Generate PDF using WeasyPrint (better Arabic support)"""
        # Render HTML template
        html_template = Template(template.html_template)
        html_content = html_template.render(Context(context))

        # Prepare CSS with RTL support
        css_content = template.css_styles or ""
        if language == 'ar':
            css_content += """
            body {
                direction: rtl;
                text-align: right;
                font-family: 'Amiri', 'Cairo', 'Tajawal', sans-serif;
            }
            .ltr { direction: ltr; text-align: left; }
            """

        # Generate PDF
        html_doc = HTML(string=html_content)
        css_doc = CSS(string=css_content) if css_content else None

        pdf_bytes = html_doc.write_pdf(
            stylesheets=[css_doc] if css_doc else None,
            font_config=self.font_config
        )

        return pdf_bytes

    def _generate_with_reportlab(self, template: PDFTemplate, context: Dict, language: str) -> bytes:
        """Generate PDF using ReportLab (fallback option)"""
        buffer = io.BytesIO()

        # Create PDF document
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4 if template.page_size == 'A4' else letter,
            rightMargin=self.settings.margin_right * cm,
            leftMargin=self.settings.margin_left * cm,
            topMargin=self.settings.margin_top * cm,
            bottomMargin=self.settings.margin_bottom * cm
        )

        # Build content
        story = []

        # Add title
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=16,
            spaceAfter=30,
            alignment=2 if language == 'ar' else 0  # Right align for Arabic
        )

        title = context.get('title', template.name)
        story.append(Paragraph(title, title_style))
        story.append(Spacer(1, 12))

        # Add content (simplified for now)
        content_style = ParagraphStyle(
            'CustomContent',
            parent=styles['Normal'],
            fontSize=self.settings.font_size,
            alignment=2 if language == 'ar' else 0
        )

        # Render template content as text (simplified)
        html_template = Template(template.html_template)
        content = html_template.render(Context(context))

        # Remove HTML tags for ReportLab (basic implementation)
        import re
        clean_content = re.sub('<[^<]+?>', '', content)

        story.append(Paragraph(clean_content, content_style))

        # Build PDF
        doc.build(story)

        pdf_bytes = buffer.getvalue()
        buffer.close()

        return pdf_bytes

    def _save_pdf_file(self, pdf_content: bytes, filename: str) -> str:
        """Save PDF content to file and return file path"""
        # Ensure storage directory exists
        storage_dir = self.settings.storage_path
        if not os.path.exists(storage_dir):
            os.makedirs(storage_dir, exist_ok=True)

        # Generate unique filename if needed
        file_path = os.path.join(storage_dir, filename)
        counter = 1
        base_name, ext = os.path.splitext(filename)

        while os.path.exists(file_path):
            new_filename = f"{base_name}_{counter}{ext}"
            file_path = os.path.join(storage_dir, new_filename)
            counter += 1

        # Save file
        with open(file_path, 'wb') as f:
            f.write(pdf_content)

        return file_path

    def get_pdf_response(self, history: PDFGenerationHistory) -> HttpResponse:
        """Get HTTP response for PDF download"""
        if history.status != 'completed' or not history.file_path:
            raise PDFGenerationError("PDF not ready for download")

        if not os.path.exists(history.file_path):
            raise PDFGenerationError("PDF file not found")

        # Read PDF content
        with open(history.file_path, 'rb') as f:
            pdf_content = f.read()

        # Update download tracking
        history.download_count += 1
        history.last_downloaded = timezone.now()
        history.save()

        # Create response
        response = HttpResponse(pdf_content, content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="{history.filename}"'
        response['Content-Length'] = len(pdf_content)

        return response

    def cleanup_old_files(self, days: int = None):
        """Clean up old PDF files"""
        if days is None:
            days = self.settings.auto_cleanup_days

        cutoff_date = timezone.now() - timedelta(days=days)
        old_histories = PDFGenerationHistory.objects.filter(
            created_at__lt=cutoff_date,
            status='completed'
        )

        deleted_count = 0
        for history in old_histories:
            if history.file_path and os.path.exists(history.file_path):
                try:
                    os.remove(history.file_path)
                    deleted_count += 1
                except OSError:
                    pass
            history.delete()

        logger.info(f"Cleaned up {deleted_count} old PDF files")
        return deleted_count


# Global service instance
pdf_service = PDFGenerationService()
