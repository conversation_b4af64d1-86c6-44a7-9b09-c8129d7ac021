#!/usr/bin/env python
import os
import sys
import django

# Setup Django
sys.path.append('/Users/<USER>/Desktop/EMS/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from ems.models import Asset, Employee
from decimal import Decimal
from datetime import date, timedelta

def populate_asset_data():
    print('🔧 POPULATING MISSING ASSET DATA...')
    
    # Complete asset data with all missing fields
    assets_data = [
        {
            'asset_id': 'LAPTOP-001',
            'serial_number': 'DL7420-2024-001',
            'model': 'Latitude 7420',
            'manufacturer': 'Dell',
            'current_value': Decimal('3800.00'),
            'warranty_expiry': date.today() + timedelta(days=365),
            'location': 'IT Department - Floor 2',
            'status': 'IN_USE',
            'notes': 'Assigned to development team. High performance laptop for coding and development work.'
        },
        {
            'asset_id': 'LAPTOP-002', 
            'serial_number': 'MBP16-2024-002',
            'model': 'MacBook Pro 16-inch',
            'manufacturer': 'Apple',
            'current_value': Decimal('7200.00'),
            'warranty_expiry': date.today() + timedelta(days=730),
            'location': 'Design Department - Floor 3',
            'status': 'IN_USE',
            'notes': 'Assigned to design team. Used for graphic design, video editing, and creative work.'
        },
        {
            'asset_id': 'MONITOR-001',
            'serial_number': 'DU27-2024-003', 
            'model': 'UltraSharp U2720Q',
            'manufacturer': 'Dell',
            'current_value': Decimal('950.00'),
            'warranty_expiry': date.today() + timedelta(days=1095),
            'location': 'Conference Room A - Floor 2',
            'status': 'AVAILABLE',
            'notes': '4K monitor for presentations and meetings. Excellent color accuracy for design work.'
        },
        {
            'asset_id': 'PRINTER-001',
            'serial_number': 'HP-LJ-2024-004',
            'model': 'LaserJet Pro M404dn', 
            'manufacturer': 'HP',
            'current_value': Decimal('650.00'),
            'warranty_expiry': date.today() + timedelta(days=365),
            'location': 'Office Floor 1 - Print Station',
            'status': 'MAINTENANCE',
            'notes': 'Network printer serving the entire floor. Currently under maintenance for toner replacement and cleaning.'
        },
        {
            'asset_id': 'DESK-001',
            'serial_number': 'ED-EXE-2024-005',
            'model': 'Executive Pro',
            'manufacturer': 'Office Furniture Co',
            'current_value': Decimal('2200.00'),
            'warranty_expiry': date.today() + timedelta(days=1825),
            'location': 'CEO Office - Floor 4',
            'status': 'IN_USE', 
            'notes': 'Premium executive desk with built-in cable management and ergonomic design.'
        }
    ]
    
    # Get some employees for assignment
    employees = list(Employee.objects.all()[:3])
    print(f'Found {len(employees)} employees for assignment')
    
    updated_count = 0
    for i, data in enumerate(assets_data):
        try:
            asset = Asset.objects.get(asset_id=data['asset_id'])
            
            # Update with complete data
            asset.serial_number = data['serial_number']
            asset.model = data['model'] 
            asset.manufacturer = data['manufacturer']
            asset.current_value = data['current_value']
            asset.warranty_expiry = data['warranty_expiry']
            asset.location = data['location']
            asset.status = data['status']
            asset.notes = data['notes']
            
            # Assign to employee if status is IN_USE and we have employees
            if data['status'] == 'IN_USE' and employees and i < len(employees):
                asset.assigned_to = employees[i]
                print(f'  Assigned to: {employees[i].user.get_full_name()}')
            
            asset.save()
            print(f'✅ Updated {asset.asset_id} with complete data')
            updated_count += 1
            
        except Asset.DoesNotExist:
            print(f'❌ Asset {data["asset_id"]} not found')
        except Exception as e:
            print(f'❌ Error updating {data["asset_id"]}: {e}')
    
    print(f'\n🎉 Updated {updated_count} assets with complete data')
    
    # Verification
    print('\n=== VERIFICATION ===')
    for asset in Asset.objects.all():
        assigned = f' → {asset.assigned_to.user.get_full_name()}' if asset.assigned_to else ''
        print(f'{asset.asset_id}: {asset.manufacturer} {asset.model} - {asset.status} - {asset.location}{assigned}')

if __name__ == '__main__':
    populate_asset_data()
