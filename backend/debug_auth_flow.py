#!/usr/bin/env python3
"""
Debug Authentication Flow
Test the exact authentication flow to identify the refresh loop issue
"""

import os
import sys
import django
from pathlib import Path

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.exceptions import TokenError
from django.conf import settings

def test_token_lifecycle():
    """Test the complete token lifecycle"""
    print("🔍 Testing Token Lifecycle")
    print("=" * 50)
    
    # Get test user
    user = User.objects.filter(username='testauth').first()
    if not user:
        print("❌ Test user 'testauth' not found")
        return False
    
    print(f"✅ Test user found: {user.username}")
    
    # Step 1: Create initial tokens
    print("\n📝 Step 1: Creating initial tokens...")
    refresh = RefreshToken.for_user(user)
    access = refresh.access_token
    
    print(f"✅ Refresh token created: {str(refresh)[:50]}...")
    print(f"✅ Access token created: {str(access)[:50]}...")
    
    # Step 2: Test token validation
    print("\n🔍 Step 2: Testing token validation...")
    try:
        # Validate refresh token
        test_refresh = RefreshToken(str(refresh))
        print("✅ Refresh token validation: PASS")
        
        # Validate access token
        from rest_framework_simplejwt.tokens import AccessToken
        test_access = AccessToken(str(access))
        print("✅ Access token validation: PASS")
        
    except TokenError as e:
        print(f"❌ Token validation failed: {e}")
        return False
    
    # Step 3: Test token refresh
    print("\n🔄 Step 3: Testing token refresh...")
    try:
        # Get new access token from refresh token
        new_access = test_refresh.access_token
        print(f"✅ New access token created: {str(new_access)[:50]}...")
        
        # Check if original refresh token is still valid
        try:
            still_valid_refresh = RefreshToken(str(refresh))
            print("✅ Original refresh token still valid after refresh")
        except TokenError:
            print("⚠️  Original refresh token invalidated after refresh (token rotation)")
            
    except TokenError as e:
        print(f"❌ Token refresh failed: {e}")
        return False
    
    # Step 4: Test blacklisting
    print("\n🚫 Step 4: Testing token blacklisting...")
    try:
        test_refresh.blacklist()
        print("✅ Token blacklisting: PASS")
        
        # Try to use blacklisted token
        try:
            blacklisted_refresh = RefreshToken(str(refresh))
            print("❌ Blacklisted token still works (this shouldn't happen)")
        except TokenError:
            print("✅ Blacklisted token properly rejected")
            
    except Exception as e:
        print(f"⚠️  Token blacklisting failed: {e}")
    
    return True

def check_jwt_settings():
    """Check JWT configuration"""
    print("\n⚙️  JWT Configuration")
    print("=" * 50)
    
    jwt_settings = getattr(settings, 'SIMPLE_JWT', {})
    
    print(f"ACCESS_TOKEN_LIFETIME: {jwt_settings.get('ACCESS_TOKEN_LIFETIME')}")
    print(f"REFRESH_TOKEN_LIFETIME: {jwt_settings.get('REFRESH_TOKEN_LIFETIME')}")
    print(f"ROTATE_REFRESH_TOKENS: {jwt_settings.get('ROTATE_REFRESH_TOKENS')}")
    print(f"BLACKLIST_AFTER_ROTATION: {jwt_settings.get('BLACKLIST_AFTER_ROTATION')}")
    
    # Check if rotation is causing issues
    if jwt_settings.get('ROTATE_REFRESH_TOKENS') and jwt_settings.get('BLACKLIST_AFTER_ROTATION'):
        print("\n⚠️  WARNING: Token rotation + auto-blacklisting may cause refresh loops!")
        print("   When a refresh token is used, it gets blacklisted immediately,")
        print("   making subsequent refresh attempts fail.")
        return False
    
    return True

def main():
    """Main debug process"""
    print("🐛 Authentication Flow Debug")
    print("=" * 50)
    
    # Check JWT settings first
    settings_ok = check_jwt_settings()
    
    # Test token lifecycle
    lifecycle_ok = test_token_lifecycle()
    
    print("\n📊 Debug Summary")
    print("=" * 50)
    
    if settings_ok and lifecycle_ok:
        print("✅ Authentication flow appears to be working correctly")
        print("\n💡 If you're still seeing refresh loops, the issue might be:")
        print("   1. Frontend not sending cookies properly")
        print("   2. CORS configuration issues")
        print("   3. Cookie domain/path mismatches")
        print("   4. Race conditions in frontend token refresh logic")
    else:
        print("❌ Issues found in authentication flow")
        if not settings_ok:
            print("   - JWT settings may be causing refresh loops")
        if not lifecycle_ok:
            print("   - Token lifecycle has problems")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
