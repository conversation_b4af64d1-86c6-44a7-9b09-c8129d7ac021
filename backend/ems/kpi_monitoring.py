"""
KPI Monitoring and Alert System
Monitors KPI values and triggers alerts when thresholds are breached.
"""

from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
from django.template.loader import render_to_string
from decimal import Decimal
import logging
from typing import List, Dict, Any

from .models import KPI, KPIValue, Employee

logger = logging.getLogger(__name__)


class KPIMonitor:
    """
    Monitors KPI values and triggers alerts when thresholds are breached.
    """
    
    def __init__(self):
        self.alert_history = []
        
    def check_all_kpis(self) -> Dict[str, Any]:
        """
        Check all active KPIs for threshold breaches and trigger alerts.
        
        Returns:
            Dictionary with monitoring results
        """
        results = {
            'checked_kpis': 0,
            'alerts_triggered': 0,
            'critical_alerts': 0,
            'warning_alerts': 0,
            'alerts': [],
            'timestamp': timezone.now()
        }
        
        active_kpis = KPI.objects.filter(status='ACTIVE')
        
        for kpi in active_kpis:
            alert_info = self.check_kpi_thresholds(kpi)
            if alert_info:
                results['alerts'].append(alert_info)
                results['alerts_triggered'] += 1
                
                if alert_info['severity'] == 'CRITICAL':
                    results['critical_alerts'] += 1
                elif alert_info['severity'] == 'WARNING':
                    results['warning_alerts'] += 1
                    
                # Send alert notification
                self.send_alert_notification(kpi, alert_info)
                
            results['checked_kpis'] += 1
        
        logger.info(f"KPI monitoring completed: {results['alerts_triggered']} alerts triggered out of {results['checked_kpis']} KPIs")
        return results
    
    def check_kpi_thresholds(self, kpi: KPI) -> Dict[str, Any]:
        """
        Check if a KPI has breached its thresholds.
        
        Args:
            kpi: The KPI to check
            
        Returns:
            Alert information if threshold is breached, None otherwise
        """
        if not kpi.current_value:
            return None
            
        current_value = float(kpi.current_value)
        
        # Check critical threshold
        if kpi.critical_threshold:
            critical_threshold = float(kpi.critical_threshold)
            
            if kpi.trend_direction == 'UP':
                # For upward trending KPIs, alert if value is below critical threshold
                if current_value < critical_threshold:
                    return {
                        'kpi_id': kpi.id,
                        'kpi_name': kpi.name,
                        'severity': 'CRITICAL',
                        'current_value': current_value,
                        'threshold_value': critical_threshold,
                        'threshold_type': 'critical',
                        'message': f'{kpi.name} is critically low at {current_value} (threshold: {critical_threshold})',
                        'trend_direction': kpi.trend_direction
                    }
            else:
                # For downward trending KPIs, alert if value is above critical threshold
                if current_value > critical_threshold:
                    return {
                        'kpi_id': kpi.id,
                        'kpi_name': kpi.name,
                        'severity': 'CRITICAL',
                        'current_value': current_value,
                        'threshold_value': critical_threshold,
                        'threshold_type': 'critical',
                        'message': f'{kpi.name} is critically high at {current_value} (threshold: {critical_threshold})',
                        'trend_direction': kpi.trend_direction
                    }
        
        # Check warning threshold
        if kpi.warning_threshold:
            warning_threshold = float(kpi.warning_threshold)
            
            if kpi.trend_direction == 'UP':
                # For upward trending KPIs, alert if value is below warning threshold
                if current_value < warning_threshold:
                    return {
                        'kpi_id': kpi.id,
                        'kpi_name': kpi.name,
                        'severity': 'WARNING',
                        'current_value': current_value,
                        'threshold_value': warning_threshold,
                        'threshold_type': 'warning',
                        'message': f'{kpi.name} is below warning threshold at {current_value} (threshold: {warning_threshold})',
                        'trend_direction': kpi.trend_direction
                    }
            else:
                # For downward trending KPIs, alert if value is above warning threshold
                if current_value > warning_threshold:
                    return {
                        'kpi_id': kpi.id,
                        'kpi_name': kpi.name,
                        'severity': 'WARNING',
                        'current_value': current_value,
                        'threshold_value': warning_threshold,
                        'threshold_type': 'warning',
                        'message': f'{kpi.name} is above warning threshold at {current_value} (threshold: {warning_threshold})',
                        'trend_direction': kpi.trend_direction
                    }
        
        return None
    
    def send_alert_notification(self, kpi: KPI, alert_info: Dict[str, Any]):
        """
        Send alert notification via email and/or other channels.
        
        Args:
            kpi: The KPI that triggered the alert
            alert_info: Alert information dictionary
        """
        try:
            # Get admin users to notify
            admin_employees = Employee.objects.filter(
                user__is_staff=True,
                is_active=True,
                user__email__isnull=False
            ).exclude(user__email='')
            
            if not admin_employees.exists():
                logger.warning("No admin users found to send KPI alerts")
                return
            
            # Prepare email content
            subject = f"🚨 KPI Alert: {kpi.name} - {alert_info['severity']}"
            
            context = {
                'kpi': kpi,
                'alert_info': alert_info,
                'timestamp': timezone.now(),
                'dashboard_url': f"{settings.FRONTEND_URL}/admin/business-intelligence" if hasattr(settings, 'FRONTEND_URL') else None
            }
            
            # Render email template
            html_message = render_to_string('emails/kpi_alert.html', context)
            plain_message = render_to_string('emails/kpi_alert.txt', context)
            
            # Send email to admin users
            recipient_emails = [emp.user.email for emp in admin_employees]
            
            send_mail(
                subject=subject,
                message=plain_message,
                html_message=html_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=recipient_emails,
                fail_silently=False
            )
            
            logger.info(f"KPI alert sent for {kpi.name} to {len(recipient_emails)} recipients")
            
        except Exception as e:
            logger.error(f"Failed to send KPI alert for {kpi.name}: {str(e)}")
    
    def get_kpi_health_summary(self) -> Dict[str, Any]:
        """
        Get overall KPI health summary.
        
        Returns:
            Dictionary with KPI health statistics
        """
        active_kpis = KPI.objects.filter(status='ACTIVE')
        
        summary = {
            'total_kpis': active_kpis.count(),
            'healthy_kpis': 0,
            'warning_kpis': 0,
            'critical_kpis': 0,
            'no_data_kpis': 0,
            'health_percentage': 0.0,
            'last_updated': timezone.now()
        }
        
        for kpi in active_kpis:
            if not kpi.current_value:
                summary['no_data_kpis'] += 1
                continue
                
            alert_info = self.check_kpi_thresholds(kpi)
            if not alert_info:
                summary['healthy_kpis'] += 1
            elif alert_info['severity'] == 'CRITICAL':
                summary['critical_kpis'] += 1
            elif alert_info['severity'] == 'WARNING':
                summary['warning_kpis'] += 1
        
        # Calculate health percentage
        if summary['total_kpis'] > 0:
            summary['health_percentage'] = (summary['healthy_kpis'] / summary['total_kpis']) * 100
        
        return summary


# Global monitor instance
kpi_monitor = KPIMonitor()


def check_kpi_alerts():
    """
    Convenience function to check all KPI alerts.
    Can be called from management commands or scheduled tasks.
    """
    return kpi_monitor.check_all_kpis()


def get_kpi_health():
    """
    Convenience function to get KPI health summary.
    """
    return kpi_monitor.get_kpi_health_summary()
