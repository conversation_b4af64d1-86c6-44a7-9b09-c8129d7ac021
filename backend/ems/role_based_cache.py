"""
Role-Based Caching System for EMS KPI
Implements enterprise-grade caching with role-specific cache keys and intelligent invalidation.

This module provides:
1. Role-specific cache key generation
2. Hierarchical cache invalidation
3. Manager-subordinate cache patterns
4. Department and project scoped caching
5. Real-time cache updates with role context
"""

import json
import hashlib
from typing import Any, Dict, List, Optional, Set, Union
from datetime import datetime, timedelta
import logging

from django.core.cache import cache
from django.conf import settings
from django.contrib.auth.models import User
from django.db.models import QuerySet

from .models import Employee, Department, Project, Role, KPI, KPIValue
from .hierarchical_access import get_hierarchical_access_manager

logger = logging.getLogger(__name__)


class RoleBasedCacheManager:
    """
    Manages role-based caching for KPI data with hierarchical access patterns.
    Implements enterprise caching strategies similar to Tableau and Power BI.
    """
    
    # Cache key prefixes for different data types
    CACHE_PREFIXES = {
        'kpi_list': 'kpi:list',
        'kpi_values': 'kpi:values',
        'kpi_dashboard': 'kpi:dashboard',
        'hierarchical_access': 'access:hierarchical',
        'employee_data': 'employee:data',
        'department_data': 'department:data',
        'project_data': 'project:data',
        'user_permissions': 'permissions:user'
    }
    
    # Default cache timeouts (in seconds)
    CACHE_TIMEOUTS = {
        'kpi_list': 300,  # 5 minutes
        'kpi_values': 180,  # 3 minutes
        'kpi_dashboard': 120,  # 2 minutes
        'hierarchical_access': 600,  # 10 minutes
        'employee_data': 900,  # 15 minutes
        'department_data': 1800,  # 30 minutes
        'project_data': 1800,  # 30 minutes
        'user_permissions': 3600  # 1 hour
    }
    
    def __init__(self, user: User):
        """Initialize with user context"""
        self.user = user
        self.user_profile = getattr(user, 'userprofile', None)
        self.employee = getattr(user, 'employee', None)
        self.role = self.user_profile.role if self.user_profile else None
        self.role_name = self.role.name if self.role else None
        self.access_manager = get_hierarchical_access_manager(user)
    
    def generate_cache_key(self, prefix: str, **kwargs) -> str:
        """
        Generate role-specific cache key with hierarchical context.
        
        Args:
            prefix: Cache key prefix from CACHE_PREFIXES
            **kwargs: Additional parameters for cache key generation
            
        Returns:
            Unique cache key string
        """
        # Base components
        components = [
            self.CACHE_PREFIXES.get(prefix, prefix),
            f"user:{self.user.id}",
            f"role:{self.role_name}" if self.role_name else "role:none"
        ]
        
        # Add hierarchical context
        if self.employee:
            components.append(f"emp:{self.employee.id}")
            
            # Add manager context for hierarchical access
            if self.employee.manager:
                components.append(f"mgr:{self.employee.manager.id}")
            
            # Add department context
            if self.employee.department:
                components.append(f"dept:{self.employee.department.id}")
        
        # Add additional parameters
        for key, value in sorted(kwargs.items()):
            if value is not None:
                components.append(f"{key}:{value}")
        
        # Create hash for long keys
        key_string = ":".join(str(c) for c in components)
        if len(key_string) > 200:  # Redis key length limit consideration
            key_hash = hashlib.md5(key_string.encode()).hexdigest()
            return f"{prefix}:hash:{key_hash}"
        
        return key_string
    
    def get_kpi_list_cache_key(self, filters: Optional[Dict] = None) -> str:
        """Generate cache key for KPI list with role-based filtering"""
        filter_params = {}
        if filters:
            # Sort filters for consistent cache keys
            for key, value in sorted(filters.items()):
                if isinstance(value, (list, tuple)):
                    filter_params[key] = ",".join(str(v) for v in sorted(value))
                else:
                    filter_params[key] = str(value)
        
        return self.generate_cache_key('kpi_list', **filter_params)
    
    def get_kpi_values_cache_key(self, kpi_id: int, period: Optional[str] = None) -> str:
        """Generate cache key for KPI values with role-based data scoping"""
        params = {'kpi_id': kpi_id}
        if period:
            params['period'] = period
        return self.generate_cache_key('kpi_values', **params)
    
    def get_dashboard_cache_key(self, dashboard_type: str = 'default') -> str:
        """Generate cache key for role-specific dashboard data"""
        return self.generate_cache_key('kpi_dashboard', dashboard_type=dashboard_type)
    
    def get_hierarchical_access_cache_key(self) -> str:
        """Generate cache key for hierarchical access information"""
        return self.generate_cache_key('hierarchical_access')
    
    def cache_kpi_list(self, kpi_list: List[Dict], filters: Optional[Dict] = None, timeout: Optional[int] = None) -> bool:
        """
        Cache KPI list with role-based filtering.
        
        Args:
            kpi_list: List of KPI dictionaries to cache
            filters: Filters applied to the KPI list
            timeout: Cache timeout in seconds
            
        Returns:
            Boolean indicating if caching was successful
        """
        try:
            cache_key = self.get_kpi_list_cache_key(filters)
            cache_timeout = timeout or self.CACHE_TIMEOUTS['kpi_list']
            
            cache_data = {
                'data': kpi_list,
                'cached_at': datetime.now().isoformat(),
                'user_id': self.user.id,
                'role': self.role_name,
                'filters': filters or {}
            }
            
            cache.set(cache_key, cache_data, cache_timeout)
            logger.info(f"Cached KPI list for user {self.user.username} ({self.role_name}): {cache_key}")
            return True
            
        except Exception as e:
            logger.error(f"Error caching KPI list: {str(e)}")
            return False
    
    def get_cached_kpi_list(self, filters: Optional[Dict] = None) -> Optional[List[Dict]]:
        """
        Retrieve cached KPI list with role-based filtering.
        
        Args:
            filters: Filters to match cached data
            
        Returns:
            Cached KPI list or None if not found/expired
        """
        try:
            cache_key = self.get_kpi_list_cache_key(filters)
            cached_data = cache.get(cache_key)
            
            if cached_data:
                logger.info(f"Cache hit for KPI list: {cache_key}")
                return cached_data.get('data')
            
            logger.debug(f"Cache miss for KPI list: {cache_key}")
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving cached KPI list: {str(e)}")
            return None
    
    def cache_kpi_values(self, kpi_id: int, values: List[Dict], period: Optional[str] = None, timeout: Optional[int] = None) -> bool:
        """
        Cache KPI values with role-based data scoping.
        
        Args:
            kpi_id: KPI ID
            values: List of KPI value dictionaries
            period: Time period for the values
            timeout: Cache timeout in seconds
            
        Returns:
            Boolean indicating if caching was successful
        """
        try:
            cache_key = self.get_kpi_values_cache_key(kpi_id, period)
            cache_timeout = timeout or self.CACHE_TIMEOUTS['kpi_values']
            
            cache_data = {
                'data': values,
                'cached_at': datetime.now().isoformat(),
                'user_id': self.user.id,
                'role': self.role_name,
                'kpi_id': kpi_id,
                'period': period
            }
            
            cache.set(cache_key, cache_data, cache_timeout)
            logger.info(f"Cached KPI values for KPI {kpi_id}, user {self.user.username}: {cache_key}")
            return True
            
        except Exception as e:
            logger.error(f"Error caching KPI values: {str(e)}")
            return False
    
    def get_cached_kpi_values(self, kpi_id: int, period: Optional[str] = None) -> Optional[List[Dict]]:
        """
        Retrieve cached KPI values with role-based data scoping.
        
        Args:
            kpi_id: KPI ID
            period: Time period for the values
            
        Returns:
            Cached KPI values or None if not found/expired
        """
        try:
            cache_key = self.get_kpi_values_cache_key(kpi_id, period)
            cached_data = cache.get(cache_key)
            
            if cached_data:
                logger.info(f"Cache hit for KPI values: {cache_key}")
                return cached_data.get('data')
            
            logger.debug(f"Cache miss for KPI values: {cache_key}")
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving cached KPI values: {str(e)}")
            return None
    
    def cache_dashboard_data(self, dashboard_data: Dict, dashboard_type: str = 'default', timeout: Optional[int] = None) -> bool:
        """
        Cache role-specific dashboard data.
        
        Args:
            dashboard_data: Dashboard data dictionary
            dashboard_type: Type of dashboard (default, executive, etc.)
            timeout: Cache timeout in seconds
            
        Returns:
            Boolean indicating if caching was successful
        """
        try:
            cache_key = self.get_dashboard_cache_key(dashboard_type)
            cache_timeout = timeout or self.CACHE_TIMEOUTS['kpi_dashboard']
            
            cache_data = {
                'data': dashboard_data,
                'cached_at': datetime.now().isoformat(),
                'user_id': self.user.id,
                'role': self.role_name,
                'dashboard_type': dashboard_type
            }
            
            cache.set(cache_key, cache_data, cache_timeout)
            logger.info(f"Cached dashboard data for user {self.user.username}: {cache_key}")
            return True
            
        except Exception as e:
            logger.error(f"Error caching dashboard data: {str(e)}")
            return False
    
    def get_cached_dashboard_data(self, dashboard_type: str = 'default') -> Optional[Dict]:
        """
        Retrieve cached role-specific dashboard data.
        
        Args:
            dashboard_type: Type of dashboard
            
        Returns:
            Cached dashboard data or None if not found/expired
        """
        try:
            cache_key = self.get_dashboard_cache_key(dashboard_type)
            cached_data = cache.get(cache_key)
            
            if cached_data:
                logger.info(f"Cache hit for dashboard data: {cache_key}")
                return cached_data.get('data')
            
            logger.debug(f"Cache miss for dashboard data: {cache_key}")
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving cached dashboard data: {str(e)}")
            return None
    
    def invalidate_user_cache(self, cache_types: Optional[List[str]] = None) -> bool:
        """
        Invalidate all cache entries for the current user.
        
        Args:
            cache_types: Specific cache types to invalidate, or None for all
            
        Returns:
            Boolean indicating if invalidation was successful
        """
        try:
            types_to_invalidate = cache_types or list(self.CACHE_PREFIXES.keys())
            invalidated_keys = []
            
            for cache_type in types_to_invalidate:
                # Generate pattern for cache keys
                pattern = self.generate_cache_key(cache_type)
                
                # For Redis, we'd use pattern matching, but Django cache doesn't support it
                # So we'll track keys and invalidate them individually
                cache.delete(pattern)
                invalidated_keys.append(pattern)
            
            logger.info(f"Invalidated cache for user {self.user.username}: {invalidated_keys}")
            return True
            
        except Exception as e:
            logger.error(f"Error invalidating user cache: {str(e)}")
            return False
    
    def invalidate_hierarchical_cache(self, employee: Employee) -> bool:
        """
        Invalidate cache for all users in the hierarchical chain.
        Used when data changes affect multiple users in the hierarchy.
        
        Args:
            employee: Employee whose hierarchy should have cache invalidated
            
        Returns:
            Boolean indicating if invalidation was successful
        """
        try:
            # Get all users affected by this employee's data changes
            affected_users = set()
            
            # Add the employee's user
            if employee.user:
                affected_users.add(employee.user)
            
            # Add manager and their managers (up the chain)
            current_manager = employee.manager
            while current_manager:
                if current_manager.user:
                    affected_users.add(current_manager.user)
                current_manager = current_manager.manager
            
            # Add subordinates (down the chain)
            subordinates = Employee.objects.filter(manager=employee)
            for subordinate in subordinates:
                if subordinate.user:
                    affected_users.add(subordinate.user)
                # Recursively add subordinates of subordinates
                self._add_subordinates_to_set(subordinate, affected_users)
            
            # Invalidate cache for all affected users
            invalidated_count = 0
            for user in affected_users:
                user_cache_manager = RoleBasedCacheManager(user)
                if user_cache_manager.invalidate_user_cache():
                    invalidated_count += 1
            
            logger.info(f"Invalidated hierarchical cache for {invalidated_count} users affected by employee {employee.id}")
            return True
            
        except Exception as e:
            logger.error(f"Error invalidating hierarchical cache: {str(e)}")
            return False
    
    def _add_subordinates_to_set(self, employee: Employee, user_set: Set[User]) -> None:
        """Recursively add subordinates to the user set"""
        subordinates = Employee.objects.filter(manager=employee)
        for subordinate in subordinates:
            if subordinate.user:
                user_set.add(subordinate.user)
            self._add_subordinates_to_set(subordinate, user_set)


def get_role_based_cache_manager(user: User) -> RoleBasedCacheManager:
    """
    Factory function to create RoleBasedCacheManager instance.
    
    Args:
        user: Django User object
        
    Returns:
        RoleBasedCacheManager instance
    """
    return RoleBasedCacheManager(user)


def invalidate_kpi_cache_on_data_change(sender, instance, **kwargs):
    """
    Signal handler to invalidate KPI cache when operational data changes.
    Should be connected to post_save signals of relevant models.
    """
    try:
        # Determine which users' cache should be invalidated based on the changed instance
        if hasattr(instance, 'employee') and instance.employee:
            cache_manager = RoleBasedCacheManager(instance.employee.user)
            cache_manager.invalidate_hierarchical_cache(instance.employee)
        elif isinstance(instance, Employee):
            cache_manager = RoleBasedCacheManager(instance.user)
            cache_manager.invalidate_hierarchical_cache(instance)
        else:
            # For other models, invalidate cache for all users (less efficient but safe)
            logger.info(f"Invalidating all KPI cache due to {sender.__name__} change")
            # This would require a more sophisticated cache invalidation strategy
            
    except Exception as e:
        logger.error(f"Error in KPI cache invalidation signal: {str(e)}")
