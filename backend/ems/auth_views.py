"""
Authentication Views for EMS API
Handles JWT authentication, user registration, password reset, etc.
"""

from rest_framework import status, permissions, serializers
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.exceptions import TokenError, InvalidToken
from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from django.core.mail import send_mail
from django.conf import settings
from django.utils.crypto import get_random_string
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.middleware.csrf import get_token
from django.views.decorators.csrf import ensure_csrf_cookie
# TEMPORARILY DISABLED: Rate limiting imports for development
# from django_ratelimit.decorators import ratelimit
# from django_ratelimit.exceptions import Ratelimited
from datetime import timedelta
import logging

from .models import Employee, Role, UserProfile, EmployeeActivation
from .serializers import UserSerializer, EmployeeSerializer, UserProfileSerializer
from .authentication import SecureTokenManager

logger = logging.getLogger(__name__)


class LoginSerializer(serializers.Serializer):
    """Serializer for login credentials with validation"""
    username = serializers.CharField(
        min_length=3,
        max_length=150,
        error_messages={
            'required': 'Username is required',
            'blank': 'Username cannot be empty',
            'min_length': 'Username must be at least 3 characters long',
            'max_length': 'Username cannot exceed 150 characters'
        }
    )
    password = serializers.CharField(
        min_length=4,
        max_length=128,
        error_messages={
            'required': 'Password is required',
            'blank': 'Password cannot be empty',
            'min_length': 'Password must be at least 4 characters long',
            'max_length': 'Password cannot exceed 128 characters'
        }
    )


# TEMPORARILY DISABLED: Rate limiting for development - causing login issues
# @method_decorator(ratelimit(key='ip', rate='5/m', method='POST', block=True), name='post')
class CustomTokenObtainPairView(APIView):
    """
    Custom JWT token obtain view that returns user data along with tokens
    Rate limited to 5 attempts per minute per IP address
    """
    permission_classes = [permissions.AllowAny]  # Allow unauthenticated access

    def post(self, request, *args, **kwargs):
        try:
            # Parse request data manually to handle both DRF and raw requests
            import json

            # Try to get data from different sources
            data = None
            if hasattr(request, 'data') and request.data:
                data = request.data
            elif request.content_type == 'application/json':
                try:
                    data = json.loads(request.body.decode('utf-8'))
                except (json.JSONDecodeError, UnicodeDecodeError):
                    return Response({
                        'message': 'Invalid JSON data'
                    }, status=status.HTTP_400_BAD_REQUEST)
            else:
                data = request.POST

            if not data:
                return Response({
                    'message': 'No data provided'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Use serializer to validate input data
            serializer = LoginSerializer(data=data)
            if not serializer.is_valid():
                # Extract first error message for better UX
                first_error = None
                for field, errors in serializer.errors.items():
                    if errors:
                        first_error = errors[0] if isinstance(errors, list) else str(errors)
                        break

                return Response({
                    'message': first_error or 'Invalid input data',
                    'errors': serializer.errors,
                    'field_errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

            username = serializer.validated_data['username']
            password = serializer.validated_data['password']

            # Authenticate user
            user = authenticate(username=username, password=password)

            if not user:
                # Check if user exists to provide better error message
                from django.contrib.auth import get_user_model
                User = get_user_model()
                try:
                    existing_user = User.objects.get(username=username)
                    if not existing_user.is_active:
                        return Response({
                            'message': 'Your account has been deactivated. Please contact support.',
                            'error_code': 'ACCOUNT_DEACTIVATED'
                        }, status=status.HTTP_401_UNAUTHORIZED)
                    else:
                        return Response({
                            'message': 'Invalid password. Please check your password and try again.',
                            'error_code': 'INVALID_PASSWORD'
                        }, status=status.HTTP_401_UNAUTHORIZED)
                except User.DoesNotExist:
                    return Response({
                        'message': 'Username not found. Please check your username and try again.',
                        'error_code': 'USER_NOT_FOUND'
                    }, status=status.HTTP_401_UNAUTHORIZED)

            if not user.is_active:
                return Response({
                    'message': 'Your account has been deactivated. Please contact support.',
                    'error_code': 'ACCOUNT_DEACTIVATED'
                }, status=status.HTTP_401_UNAUTHORIZED)

            # Generate tokens
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token

            # Get user profile data
            user_data = self._get_user_data(user)

            # Update last login
            user.last_login = timezone.now()
            user.save(update_fields=['last_login'])

            # SECURITY FIX: Create response with httpOnly cookies
            response = Response({
                'user': user_data,
                'message': 'Login successful'
            }, status=status.HTTP_200_OK)

            # SECURITY FIX: Set httpOnly cookies for tokens
            # Get JWT settings safely
            jwt_settings = getattr(settings, 'SIMPLE_JWT', {})
            access_lifetime = jwt_settings.get('ACCESS_TOKEN_LIFETIME', timedelta(minutes=15))
            refresh_lifetime = jwt_settings.get('REFRESH_TOKEN_LIFETIME', timedelta(days=7))

            # Access token (short-lived)
            response.set_cookie(
                'access_token',
                str(access_token),
                max_age=int(access_lifetime.total_seconds()),
                httponly=True,
                secure=False,  # Allow HTTP in development
                samesite='Lax',  # Allow cross-origin in development
                domain=None  # Allow all domains in development
            )

            # Refresh token (longer-lived)
            response.set_cookie(
                'refresh_token',
                str(refresh),
                max_age=int(refresh_lifetime.total_seconds()),
                httponly=True,
                secure=False,  # Allow HTTP in development
                samesite='Lax',  # Allow cross-origin in development
                domain=None  # Allow all domains in development
            )

            return response

        # REMOVED: Ratelimited exception handling since rate limiting is disabled
        # except Ratelimited:
        #     return Response({
        #         'message': 'Too many login attempts. Please try again later.'
        #     }, status=status.HTTP_429_TOO_MANY_REQUESTS)
        except Exception as e:
            logger.error(f"Login error: {str(e)}")
            return Response({
                'message': 'An error occurred during login'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _get_user_data(self, user):
        """Get comprehensive user data including profile and role information"""
        try:
            # Try to get employee profile
            employee = Employee.objects.select_related(
                'user', 'department', 'manager'
            ).get(user=user)

            # Get user profile for role information
            try:
                user_profile = UserProfile.objects.select_related('role').get(user=user)
                if user_profile.role:
                    # Map role name to frontend role ID
                    role_id_map = {
                        'SUPERADMIN': 'super_admin',
                        'ADMIN': 'admin',
                        'HR_MANAGER': 'hr_manager',
                        'FINANCE_MANAGER': 'finance_manager',
                        'DEPARTMENT_MANAGER': 'department_manager',
                        'PROJECT_MANAGER': 'project_manager',
                        'EMPLOYEE': 'employee'
                    }

                    role_data = {
                        'id': role_id_map.get(user_profile.role.name, 'employee'),
                        'name': user_profile.role.name,  # FIXED: Use raw role name instead of get_name_display()
                        'nameAr': user_profile.role.name_ar,
                        'permissions': user_profile.role.permissions or [],
                        'level': user_profile.role.permissions.get('level', 5) if user_profile.role.permissions else 5,
                        'dashboardConfig': {
                            'allowedRoutes': self._get_allowed_routes(user_profile.role.name),
                            'defaultWidgets': self._get_default_widgets(user_profile.role.name),
                            'customizations': {}
                        }
                    }
                else:
                    # Default role if role is None
                    role_data = {
                        'id': 'employee',
                        'name': 'Employee',
                        'nameAr': 'موظف',
                        'permissions': [],
                        'level': 5,
                        'dashboardConfig': {
                            'allowedRoutes': ['/dashboard', '/profile'],
                            'defaultWidgets': ['tasks', 'schedule'],
                            'customizations': {}
                        }
                    }
            except UserProfile.DoesNotExist:
                # Default role if no profile exists
                role_data = {
                    'id': 'employee',
                    'name': 'Employee',
                    'nameAr': 'موظف',
                    'permissions': [],
                    'dashboardConfig': {
                        'allowedRoutes': ['/dashboard', '/profile'],
                        'defaultWidgets': ['tasks', 'schedule'],
                        'customizations': {}
                    }
                }

            return {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': role_data,
                'profile': {
                    'avatar': None,  # Add avatar field to Employee model if needed
                    'phone': employee.phone,
                    'department': employee.department.name if employee.department else None,
                    'position': employee.position,
                    'preferred_language': 'ar',  # Default to Arabic
                    'timezone': 'Asia/Riyadh'
                }
            }

        except Employee.DoesNotExist:
            # User exists but no employee profile - check for UserProfile (for ADMIN, SUPERADMIN, etc.)
            try:
                user_profile = UserProfile.objects.select_related('role').get(user=user)
                if user_profile.role:
                    # Map role name to frontend role ID
                    role_id_map = {
                        'SUPERADMIN': 'super_admin',
                        'ADMIN': 'admin',
                        'HR_MANAGER': 'hr_manager',
                        'FINANCE_MANAGER': 'finance_manager',
                        'DEPARTMENT_MANAGER': 'department_manager',
                        'PROJECT_MANAGER': 'project_manager',
                        'EMPLOYEE': 'employee'
                    }

                    role_data = {
                        'id': role_id_map.get(user_profile.role.name, 'employee'),
                        'name': user_profile.role.name,  # FIXED: Use raw role name instead of get_name_display()
                        'nameAr': user_profile.role.name_ar,
                        'permissions': user_profile.role.permissions or [],
                        'level': user_profile.role.permissions.get('level', 5) if user_profile.role.permissions else 5,
                        'dashboardConfig': {
                            'allowedRoutes': self._get_allowed_routes(user_profile.role.name),
                            'defaultWidgets': self._get_default_widgets(user_profile.role.name),
                            'customizations': {}
                        }
                    }

                    return {
                        'id': user.id,
                        'username': user.username,
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'role': role_data,
                        'profile': {
                            'avatar': None,
                            'phone': user_profile.phone,
                            'department': None,  # UserProfile doesn't have department
                            'position': None,    # UserProfile doesn't have position
                            'preferred_language': user_profile.preferred_language,
                            'timezone': user_profile.timezone
                        }
                    }

            except UserProfile.DoesNotExist:
                pass

            # Fallback for users with no profile at all
            return {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': {
                    'id': 'user',
                    'name': 'User',
                    'nameAr': 'مستخدم',
                    'permissions': [],
                    'dashboardConfig': {
                        'allowedRoutes': ['/profile'],
                        'defaultWidgets': [],
                        'customizations': {}
                    }
                },
                'profile': {
                    'avatar': None,
                    'phone': None,
                    'department': None,
                    'position': None,
                    'preferred_language': 'ar',
                    'timezone': 'Asia/Riyadh'
                }
            }

    def _get_allowed_routes(self, role_name):
        """Get allowed routes based on role"""
        route_map = {
            'SUPERADMIN': ['/admin/*', '/hr/*', '/finance/*', '/department/*', '/projects/*', '/employee/*'],
            'ADMIN': ['/admin/*'],
            'HR_MANAGER': ['/hr/*'],
            'FINANCE_MANAGER': ['/finance/*'],
            'DEPARTMENT_MANAGER': ['/department/*'],
            'PROJECT_MANAGER': ['/projects/*'],
            'EMPLOYEE': ['/employee/*']
        }
        return route_map.get(role_name, ['/employee/*'])

    def _get_default_widgets(self, role_name):
        """Get default widgets based on role"""
        widget_map = {
            'SUPERADMIN': ['system_overview', 'user_analytics', 'performance_metrics', 'security_dashboard', 'compliance_overview', 'ai_insights'],
            'ADMIN': ['system_overview', 'user_analytics', 'performance_metrics'],
            'HR_MANAGER': ['employee_stats', 'leave_requests', 'attendance_summary'],
            'FINANCE_MANAGER': ['budget_overview', 'expense_tracking', 'financial_reports'],
            'DEPARTMENT_MANAGER': ['team_overview', 'project_progress', 'department_metrics'],
            'PROJECT_MANAGER': ['project_status', 'task_overview', 'team_performance'],
            'EMPLOYEE': ['my_tasks', 'my_schedule', 'my_attendance']
        }
        return widget_map.get(role_name, ['my_tasks', 'my_schedule'])


# TEMPORARILY DISABLED: Rate limiting for development - causing refresh issues
# @method_decorator(ratelimit(key='ip', rate='10/m', method='POST', block=True), name='post')
class CustomTokenRefreshView(APIView):
    """
    SECURITY FIX: Custom token refresh view using httpOnly cookies
    Rate limited to 10 attempts per minute per IP address
    """
    permission_classes = [permissions.AllowAny]  # Allow unauthenticated access

    def post(self, request, *args, **kwargs):
        try:
            logger.info(f"Auth attempt from {request.META.get('REMOTE_ADDR', 'unknown')} to /api/auth/refresh/")

            # SECURITY FIX: Get refresh token from httpOnly cookie
            refresh_token = request.COOKIES.get('refresh_token')

            if not refresh_token:
                logger.warning("Unauthorized: /api/auth/refresh/")
                return Response({
                    'message': 'Refresh token not found'
                }, status=status.HTTP_401_UNAUTHORIZED)

            try:
                # Validate and refresh the token
                refresh = RefreshToken(refresh_token)
                access_token = refresh.access_token

                # SECURITY FIX: Create response with new httpOnly cookies
                response = Response({
                    'message': 'Token refreshed successfully'
                }, status=status.HTTP_200_OK)

                # Set new access token cookie
                jwt_settings = getattr(settings, 'SIMPLE_JWT', {})
                access_lifetime = jwt_settings.get('ACCESS_TOKEN_LIFETIME', timedelta(minutes=15))

                response.set_cookie(
                    'access_token',
                    str(access_token),
                    max_age=int(access_lifetime.total_seconds()),
                    httponly=True,
                    secure=False,  # Allow HTTP in development
                    samesite='Lax',  # Allow cross-origin in development
                    domain=None  # Allow all domains in development
                )

                logger.info("Token refreshed successfully")
                return response

            except TokenError as e:
                logger.warning(f"Unauthorized: /api/auth/refresh/ - {str(e)}")
                return Response({
                    'message': 'Invalid or expired refresh token'
                }, status=status.HTTP_401_UNAUTHORIZED)

        except Exception as e:
            logger.error(f"Token refresh error: {str(e)}")
            return Response({
                'message': 'An error occurred during token refresh'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class LogoutView(APIView):
    """
    SECURITY FIX: Logout view that clears httpOnly cookies and blacklists tokens
    """
    permission_classes = [permissions.AllowAny]  # Allow logout even if token is invalid

    def post(self, request):
        try:
            logger.info("Authentication cookies cleared")

            # SECURITY FIX: Get refresh token from httpOnly cookie
            refresh_token = request.COOKIES.get('refresh_token')

            if refresh_token:
                try:
                    token = RefreshToken(refresh_token)
                    # FIXED: Manual blacklisting for logout (since auto-blacklisting is disabled)
                    try:
                        token.blacklist()
                        logger.info("Refresh token blacklisted successfully")
                    except Exception as blacklist_error:
                        # Blacklisting may fail if token is already invalid - that's OK
                        logger.info(f"Token blacklist skipped: {str(blacklist_error)}")
                except Exception as token_error:
                    # Token parsing may fail if token is already invalid - that's OK
                    logger.info(f"Token parsing failed during logout: {str(token_error)}")

            # SECURITY FIX: Create response that clears httpOnly cookies
            response = Response({
                'message': 'Successfully logged out'
            }, status=status.HTTP_200_OK)

            # SECURITY FIX: Use SecureTokenManager for cookie cleanup
            response = SecureTokenManager.clear_auth_cookies(response)

            return response

        except Exception as e:
            logger.error(f"Logout error: {str(e)}")
            logger.info("Authentication cookies cleared")
            # Even on error, clear cookies
            response = Response({
                'message': 'Logged out (with errors)'
            }, status=status.HTTP_200_OK)

            response.delete_cookie('access_token', samesite='Lax')
            response.delete_cookie('refresh_token', samesite='Lax')

            return response


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_profile_view(request):
    """
    Get current authenticated user's profile
    """
    try:
        logger.info(f"User profile request from {request.user.username}")

        # Use the same method as login to get user data
        token_view = CustomTokenObtainPairView()
        user_data = token_view._get_user_data(request.user)

        return Response(user_data, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"User profile error: {str(e)}")
        logger.warning(f"Unauthorized: /api/auth/user/")
        return Response({
            'message': 'An error occurred while fetching user profile'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PATCH'])
@permission_classes([permissions.IsAuthenticated])
def update_profile_view(request):
    """
    Update current user's profile
    """
    try:
        user = request.user
        data = request.data

        # Update User model fields
        if 'first_name' in data:
            user.first_name = data['first_name']
        if 'last_name' in data:
            user.last_name = data['last_name']
        if 'email' in data:
            user.email = data['email']

        user.save()

        # Update Employee model fields if exists
        try:
            employee = Employee.objects.get(user=user)
            if 'phone' in data.get('profile', {}):
                employee.phone = data['profile']['phone']
            employee.save()
        except Employee.DoesNotExist:
            pass

        # Return updated user data
        token_view = CustomTokenObtainPairView()
        user_data = token_view._get_user_data(user)

        return Response(user_data, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Profile update error: {str(e)}")
        return Response({
            'message': 'An error occurred while updating profile'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
# TEMPORARILY DISABLED: Rate limiting for development
# @ratelimit(key='user', rate='3/m', method='POST', block=True)
def change_password_view(request):
    """
    Change user password
    """
    try:
        user = request.user
        current_password = request.data.get('current_password')
        new_password = request.data.get('new_password')

        if not current_password or not new_password:
            return Response({
                'message': 'Current password and new password are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Verify current password
        if not user.check_password(current_password):
            return Response({
                'message': 'Current password is incorrect'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Set new password
        user.set_password(new_password)
        user.save()

        return Response({
            'message': 'Password changed successfully'
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Password change error: {str(e)}")
        return Response({
            'message': 'An error occurred while changing password'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# SECURITY FIX: CSRF Token Endpoint
@api_view(['GET'])
@permission_classes([permissions.AllowAny])
@ensure_csrf_cookie
def csrf_token_view(request):
    """
    SECURITY FIX: Provide CSRF token for frontend SPA
    This endpoint ensures the frontend can get a CSRF token
    """
    try:
        # Get CSRF token for the request
        csrf_token = get_token(request)

        return Response({
            'csrfToken': csrf_token,
            'message': 'CSRF token provided'
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"CSRF token error: {str(e)}")
        return Response({
            'message': 'Failed to get CSRF token'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Employee Activation Views
@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def employee_activation_view(request, token):
    """
    Employee account activation endpoint
    Allows employees to activate their accounts using the token sent via email
    """
    try:
        # Find the activation record
        activation = EmployeeActivation.objects.select_related('employee__user').get(
            activation_token=token
        )

        # Check if already activated
        if activation.is_activated:
            return Response({
                'message': 'Account is already activated',
                'employee': {
                    'name': activation.employee.user.get_full_name(),
                    'email': activation.employee.user.email,
                    'position': activation.employee.position
                }
            }, status=status.HTTP_200_OK)

        # Check if token is expired
        if activation.is_expired():
            return Response({
                'message': 'Activation link has expired. Please contact your administrator.',
                'expired': True
            }, status=status.HTTP_400_BAD_REQUEST)

        # Return employee info for password setup
        return Response({
            'message': 'Activation link is valid',
            'employee': {
                'name': activation.employee.user.get_full_name(),
                'email': activation.employee.user.email,
                'position': activation.employee.position,
                'department': activation.employee.department.name if activation.employee.department else None
            },
            'token': str(activation.activation_token)
        }, status=status.HTTP_200_OK)

    except EmployeeActivation.DoesNotExist:
        return Response({
            'message': 'Invalid activation link',
            'invalid': True
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"Activation view error: {str(e)}")
        return Response({
            'message': 'An error occurred while processing activation'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
# TEMPORARILY DISABLED: Rate limiting for development
# @ratelimit(key='ip', rate='5/m', method='POST', block=True)
def employee_activate_account_view(request, token):
    """
    Complete employee account activation with password setup
    """
    try:
        # Find the activation record
        activation = EmployeeActivation.objects.select_related('employee__user').get(
            activation_token=token
        )

        # Check if already activated
        if activation.is_activated:
            return Response({
                'message': 'Account is already activated'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if token is expired
        if activation.is_expired():
            return Response({
                'message': 'Activation link has expired. Please contact your administrator.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get password from request
        password = request.data.get('password')
        confirm_password = request.data.get('confirm_password')

        if not password or not confirm_password:
            return Response({
                'message': 'Password and confirmation are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if password != confirm_password:
            return Response({
                'message': 'Passwords do not match'
            }, status=status.HTTP_400_BAD_REQUEST)

        if len(password) < 8:
            return Response({
                'message': 'Password must be at least 8 characters long'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Set the password and activate the account
        user = activation.employee.user
        user.set_password(password)
        user.is_active = True
        user.save()

        # Mark activation as complete
        activation.activate()

        return Response({
            'message': 'Account activated successfully! You can now log in.',
            'employee': {
                'name': user.get_full_name(),
                'email': user.email,
                'username': user.username
            }
        }, status=status.HTTP_200_OK)

    except EmployeeActivation.DoesNotExist:
        return Response({
            'message': 'Invalid activation link'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"Account activation error: {str(e)}")
        return Response({
            'message': 'An error occurred while activating account'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


