"""
Email Service for Employee Management System
Handles sending activation emails and other notifications
"""

import logging
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.conf import settings
from django.urls import reverse
from typing import Optional
import os

logger = logging.getLogger(__name__)


class EmailService:
    """Service for sending emails to employees and administrators"""
    
    @staticmethod
    def send_employee_activation_email(employee, activation_token: str) -> bool:
        """
        Send activation email to newly created employee
        
        Args:
            employee: Employee instance
            activation_token: UUID token for activation
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            # Get the frontend URL from settings or use default
            frontend_url = getattr(settings, 'FRONTEND_URL', 'http://localhost:5173')
            # Use standalone activation page to avoid React authentication loops
            activation_url = f"{frontend_url}/activate.html?token={activation_token}"
            
            # Email context
            context = {
                'employee_name': employee.user.get_full_name(),
                'employee_name_ar': f"{employee.first_name_ar} {employee.last_name_ar}",
                'position': employee.position,
                'position_ar': employee.position_ar,
                'department': employee.department.name if employee.department else 'غير محدد',
                'department_ar': employee.department.name_ar if employee.department else 'غير محدد',
                'activation_url': activation_url,
                'company_name': getattr(settings, 'COMPANY_NAME', 'شركة نمو'),
                'support_email': getattr(settings, 'SUPPORT_EMAIL', '<EMAIL>'),
            }
            
            # Render email templates
            html_message = render_to_string('emails/employee_activation.html', context)
            plain_message = render_to_string('emails/employee_activation.txt', context)
            
            # Send email
            success = send_mail(
                subject='تفعيل حساب الموظف - Employee Account Activation',
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[employee.user.email],
                html_message=html_message,
                fail_silently=False
            )
            
            if success:
                logger.info(f"Activation email sent successfully to {employee.user.email}")
                return True
            else:
                logger.error(f"Failed to send activation email to {employee.user.email}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending activation email to {employee.user.email}: {str(e)}")
            return False

    @staticmethod
    def send_employee_rejection_email(employee, reason=""):
        """
        Send rejection notification email to employee

        Args:
            employee: Employee instance
            reason: Reason for rejection

        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            logger.info(f"Sending rejection email to {employee.user.email}")

            # Email context
            context = {
                'employee_name': employee.user.get_full_name(),
                'employee_name_ar': f"{employee.first_name_ar} {employee.last_name_ar}",
                'position': employee.position,
                'position_ar': employee.position_ar,
                'department': employee.department.name if employee.department else 'غير محدد',
                'department_ar': employee.department.name_ar if employee.department else 'غير محدد',
                'rejection_reason': reason,
                'company_name': getattr(settings, 'COMPANY_NAME', 'شركة نمو'),
                'support_email': getattr(settings, 'SUPPORT_EMAIL', '<EMAIL>'),
            }

            # Render email templates
            html_message = render_to_string('emails/employee_rejection.html', context)
            plain_message = render_to_string('emails/employee_rejection.txt', context)

            # Send email
            success = send_mail(
                subject='رفض طلب التوظيف - Employment Application Rejected',
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[employee.user.email],
                html_message=html_message,
                fail_silently=False
            )

            if success:
                logger.info(f"Rejection email sent successfully to {employee.user.email}")
                return True
            else:
                logger.error(f"Failed to send rejection email to {employee.user.email}")
                return False

        except Exception as e:
            logger.error(f"Error sending rejection email to {employee.user.email}: {str(e)}")
            return False
    
    @staticmethod
    def send_admin_notification_email(employee, admin_email: Optional[str] = None) -> bool:
        """
        Send notification to admin when new employee is created
        
        Args:
            employee: Employee instance
            admin_email: Optional admin email, uses settings default if not provided
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            if not admin_email:
                admin_email = getattr(settings, 'ADMIN_EMAIL', None)
                
            if not admin_email:
                logger.warning("No admin email configured for notifications")
                return False
            
            # Email context
            context = {
                'employee_name': employee.user.get_full_name(),
                'employee_name_ar': f"{employee.first_name_ar} {employee.last_name_ar}",
                'employee_id': employee.employee_id,
                'position': employee.position,
                'position_ar': employee.position_ar,
                'department': employee.department.name if employee.department else 'غير محدد',
                'department_ar': employee.department.name_ar if employee.department else 'غير محدد',
                'email': employee.user.email,
                'hire_date': employee.hire_date,
                'company_name': getattr(settings, 'COMPANY_NAME', 'شركة نمو'),
            }
            
            # Render email templates
            html_message = render_to_string('emails/admin_new_employee.html', context)
            plain_message = render_to_string('emails/admin_new_employee.txt', context)
            
            # Send email
            success = send_mail(
                subject='موظف جديد تم إضافته - New Employee Added',
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[admin_email],
                html_message=html_message,
                fail_silently=False
            )
            
            if success:
                logger.info(f"Admin notification email sent successfully to {admin_email}")
                return True
            else:
                logger.error(f"Failed to send admin notification email to {admin_email}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending admin notification email: {str(e)}")
            return False
    
    @staticmethod
    def send_password_reset_email(user, reset_token: str) -> bool:
        """
        Send password reset email to user
        
        Args:
            user: User instance
            reset_token: Password reset token
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            # Get the frontend URL from settings or use default
            frontend_url = getattr(settings, 'FRONTEND_URL', 'http://localhost:5178')
            reset_url = f"{frontend_url}/reset-password/{reset_token}"
            
            # Email context
            context = {
                'user_name': user.get_full_name(),
                'reset_url': reset_url,
                'company_name': getattr(settings, 'COMPANY_NAME', 'شركة نمو'),
                'support_email': getattr(settings, 'SUPPORT_EMAIL', '<EMAIL>'),
            }
            
            # Render email templates
            html_message = render_to_string('emails/password_reset.html', context)
            plain_message = render_to_string('emails/password_reset.txt', context)
            
            # Send email
            success = send_mail(
                subject='إعادة تعيين كلمة المرور - Password Reset',
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
                html_message=html_message,
                fail_silently=False
            )
            
            if success:
                logger.info(f"Password reset email sent successfully to {user.email}")
                return True
            else:
                logger.error(f"Failed to send password reset email to {user.email}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending password reset email to {user.email}: {str(e)}")
            return False


def send_activation_email_async(employee_id: int, activation_token: str):
    """
    Async function to send activation email (can be used with Celery)
    
    Args:
        employee_id: Employee ID
        activation_token: Activation token
    """
    try:
        from .models import Employee
        employee = Employee.objects.select_related('user', 'department').get(id=employee_id)
        EmailService.send_employee_activation_email(employee, activation_token)
    except Employee.DoesNotExist:
        logger.error(f"Employee with ID {employee_id} not found for activation email")
    except Exception as e:
        logger.error(f"Error in async activation email: {str(e)}")


def send_admin_notification_async(employee_id: int):
    """
    Async function to send admin notification (can be used with Celery)
    
    Args:
        employee_id: Employee ID
    """
    try:
        from .models import Employee
        employee = Employee.objects.select_related('user', 'department').get(id=employee_id)
        EmailService.send_admin_notification_email(employee)
    except Employee.DoesNotExist:
        logger.error(f"Employee with ID {employee_id} not found for admin notification")
    except Exception as e:
        logger.error(f"Error in async admin notification: {str(e)}")
