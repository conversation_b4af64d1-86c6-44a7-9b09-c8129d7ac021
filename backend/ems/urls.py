from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from django.views.decorators.csrf import csrf_exempt
from . import views
from . import auth_views
from . import pdf_views
from . import health_checks

router = DefaultRouter()

# Core Management
router.register(r'departments', views.DepartmentViewSet)
router.register(r'employees', views.EmployeeViewSet)
router.register(r'activities', views.ActivityViewSet)

# HR Management
router.register(r'roles', views.RoleViewSet)
router.register(r'user-profiles', views.UserProfileViewSet)
router.register(r'leave-types', views.LeaveTypeViewSet)
router.register(r'leave-requests', views.LeaveRequestViewSet)
router.register(r'employee-leave', views.EmployeeLeaveViewSet, basename='employee-leave')  # Employee leave with data transformation
router.register(r'attendance', views.AttendanceViewSet)
router.register(r'hr/attendance', views.AttendanceViewSet, basename='hr-attendance')  # HR namespace for attendance

# Project Management
router.register(r'projects', views.ProjectViewSet)
router.register(r'tasks', views.TaskViewSet)
router.register(r'employee-tasks', views.EmployeeTaskViewSet, basename='employee-tasks')  # Employee-specific tasks

# Financial Management
router.register(r'budgets', views.BudgetViewSet)
router.register(r'expenses', views.ExpenseViewSet)
router.register(r'finance-budgets', views.BudgetViewSet, basename='finance-budgets')
router.register(r'finance-reports', views.ReportViewSet, basename='finance-reports')
router.register(r'finance-expenses', views.ExpenseViewSet, basename='finance-expenses')

# General Ledger & Chart of Accounts
router.register(r'account-types', views.AccountTypeViewSet)
router.register(r'chart-of-accounts', views.ChartOfAccountsViewSet)
router.register(r'fiscal-years', views.FiscalYearViewSet)
router.register(r'journal-entry-batches', views.JournalEntryBatchViewSet)
router.register(r'journal-entries', views.JournalEntryViewSet)

# Accounts Payable
router.register(r'vendors', views.VendorViewSet)
router.register(r'vendor-invoices', views.VendorInvoiceViewSet)

# Accounts Receivable
router.register(r'customer-invoices', views.CustomerInvoiceViewSet)

# Payments
router.register(r'payments', views.PaymentViewSet)
router.register(r'financial-reports', views.FinancialReportsViewSet, basename='financial-reports')
router.register(r'currencies', views.CurrencyViewSet)
router.register(r'exchange-rates', views.ExchangeRateViewSet)

# Enhanced Asset Management
router.register(r'asset-categories', views.AssetCategoryViewSet)
router.register(r'assets', views.AssetViewSet)
router.register(r'asset-depreciation', views.AssetDepreciationViewSet)
router.register(r'asset-maintenance', views.AssetMaintenanceViewSet)
router.register(r'asset-transfers', views.AssetTransferViewSet)
router.register(r'asset-audits', views.AssetAuditViewSet)

# Advanced Reporting & Analytics
router.register(r'kpi-metrics', views.KPIMetricViewSet)
router.register(r'kpi-metric-values', views.KPIMetricValueViewSet)
router.register(r'report-templates', views.ReportTemplateViewSet)
router.register(r'report-executions', views.ReportExecutionViewSet)
router.register(r'dashboards', views.DashboardViewSet)
router.register(r'analytics-queries', views.AnalyticsQueryViewSet)

# Integration & API Management
router.register(r'api-keys', views.APIKeyViewSet)
router.register(r'external-services', views.ExternalServiceViewSet)
router.register(r'webhook-endpoints', views.WebhookEndpointViewSet)
router.register(r'webhook-events', views.WebhookEventViewSet)
router.register(r'integration-logs', views.IntegrationLogViewSet)

# Security & Compliance
router.register(r'user-security-profiles', views.UserSecurityProfileViewSet)
router.register(r'audit-trail', views.AuditTrailViewSet)
router.register(r'security-incidents', views.SecurityIncidentViewSet)
router.register(r'compliance-frameworks', views.ComplianceFrameworkViewSet)
router.register(r'compliance-controls', views.ComplianceControlViewSet)
router.register(r'data-classifications', views.DataClassificationViewSet)
router.register(r'security-alerts', views.SecurityAlertViewSet)

# Asset Management
router.register(r'asset-categories', views.AssetCategoryViewSet)
router.register(r'assets', views.AssetViewSet)
router.register(r'suppliers', views.SupplierViewSet)
router.register(r'purchase-orders', views.PurchaseOrderViewSet)

# Communication & Collaboration
router.register(r'announcements', views.AnnouncementViewSet)
router.register(r'messages', views.MessageViewSet)
router.register(r'personal-messages', views.PersonalMessageViewSet, basename='personal-messages')  # Personal messages for employees
router.register(r'documents', views.DocumentViewSet)
router.register(r'meetings', views.MeetingViewSet)

# Customer Management
router.register(r'customers', views.CustomerViewSet)

# Product Management
router.register(r'product-categories', views.ProductCategoryViewSet)
router.register(r'products', views.ProductViewSet)

# Report Management
router.register(r'reports', views.ReportViewSet)

# Sales Management
router.register(r'sales-orders', views.SalesOrderViewSet)

# CRITICAL FIX: Consolidated API endpoints with proper versioning
# Remove duplicate registrations and implement clean API structure
router.register(r'job-postings', views.ReportViewSet, basename='job-postings')  # Using Report as base
router.register(r'sales-customers', views.CustomerViewSet, basename='sales-customers')
router.register(r'certifications', views.ReportViewSet, basename='certifications')  # Using Report as base
router.register(r'training-programs', views.ReportViewSet, basename='training-programs')  # Using Report as base
router.register(r'vendors', views.SupplierViewSet, basename='vendors')  # Using Supplier as base

# CRITICAL FIX: Remove all duplicate endpoint registrations
# These will be handled by the main endpoints above with proper permissions
router.register(r'performance-reviews', views.PerformanceReviewViewSet, basename='performance-reviews')  # Performance
router.register(r'payroll', views.PayrollViewSet, basename='payroll')  # Payroll
router.register(r'recruitment', views.RecruitmentViewSet, basename='recruitment')  # Recruitment
router.register(r'training', views.TrainingViewSet, basename='training')  # Training
router.register(r'hr/reports', views.ReportViewSet, basename='hr-reports-alt')  # HR reports
router.register(r'finance/reports', views.ReportViewSet, basename='finance-reports')  # Finance reports
router.register(r'invoices', views.InvoiceViewSet, basename='invoices')  # Invoices
router.register(r'cost-centers', views.CostCenterViewSet, basename='cost-centers')  # Cost centers
router.register(r'finance/analytics', views.ReportViewSet, basename='finance-analytics')  # Finance analytics
router.register(r'calendar/events', views.MeetingViewSet, basename='calendar-events')  # Calendar events
router.register(r'personal-calendar', views.PersonalCalendarViewSet, basename='personal-calendar')  # Personal calendar

urlpatterns = [
    # Export endpoints (must come before router URLs to avoid conflicts)
    path('api/export/employees/', views.employee_export, name='employee-export'),
    path('api/export/departments/', views.department_export, name='department-export'),
    path('api/export/attendance/', views.simple_attendance_export, name='attendance-export'),

    # Additional export endpoints for commonly used data
    path('api/export/projects/', views.simple_attendance_export, name='projects-export'),  # Placeholder
    path('api/export/tasks/', views.simple_attendance_export, name='tasks-export'),  # Placeholder
    path('api/export/quotations/', views.simple_attendance_export, name='quotations-export'),  # Placeholder
    path('api/export/workflows/', views.simple_attendance_export, name='workflows-export'),  # Placeholder
    path('api/export/quality-records/', views.simple_attendance_export, name='quality-records-export'),  # Placeholder
    path('api/export/job-postings/', views.simple_attendance_export, name='job-postings-export'),  # Placeholder
    path('api/export/sales-customers/', views.simple_attendance_export, name='sales-customers-export'),  # Placeholder
    path('api/export/sales-orders/', views.simple_attendance_export, name='sales-orders-export'),  # Placeholder
    path('api/export/certifications/', views.simple_attendance_export, name='certifications-export'),  # Placeholder
    path('api/export/training-programs/', views.simple_attendance_export, name='training-programs-export'),  # Placeholder
    path('api/export/vendors/', views.simple_attendance_export, name='vendors-export'),  # Placeholder
    path('api/export/kpis/', views.simple_attendance_export, name='kpis-export'),  # Placeholder

    # Test endpoint
    path('api/test/', views.dashboard_stats, name='test-endpoint'),
    path('api/test-export/', views.test_export, name='test-export'),
    path('api/attendance-export/', views.simple_attendance_export, name='simple-attendance-export'),

    # CRITICAL FIX: API versioning with clean structure
    path('api/v1/', include('ems.api_v1_urls', namespace='api-v1')),

    # Legacy API support (temporary - will be deprecated)
    path('api/', include(router.urls)),

    # Health check endpoints
    path('api/health/', health_checks.health_check, name='health-check'),
    path('api/health/detailed/', health_checks.detailed_health_check, name='detailed-health-check'),

    # Other endpoints
    path('api/dashboard-stats/', views.dashboard_stats, name='dashboard-stats'),
    path('api/superadmin/system-stats/', views.superadmin_system_stats, name='superadmin-system-stats'),
    path('api/financial-analytics/', views.financial_analytics, name='financial-analytics'),
    path('api/user-profile/', views.user_profile, name='user-profile'),

    # Employee Dashboard specific endpoints
    path('api/meetings/my-meetings/', views.my_meetings, name='my-meetings'),
    path('api/messages/recent/', views.recent_messages, name='recent-messages'),
    path('api/tasks/my-tasks/', views.my_tasks_dashboard, name='my-tasks'),

    # Authentication endpoints (CSRF exempt to prevent 401 errors)
    path('api/auth/login/', csrf_exempt(auth_views.CustomTokenObtainPairView.as_view()), name='token_obtain_pair'),
    path('api/auth/refresh/', csrf_exempt(auth_views.CustomTokenRefreshView.as_view()), name='token_refresh'),
    path('api/auth/logout/', csrf_exempt(auth_views.LogoutView.as_view()), name='logout'),
    path('api/auth/verify/', auth_views.user_profile_view, name='token_verify'),  # FIXED: Add missing verify endpoint
    path('api/auth/csrf/', auth_views.csrf_token_view, name='csrf_token'),  # SECURITY FIX: CSRF token endpoint
    path('api/auth/user/', auth_views.user_profile_view, name='auth_user_profile'),
    path('api/auth/profile/', auth_views.update_profile_view, name='update_profile'),
    path('api/auth/change-password/', auth_views.change_password_view, name='change_password'),

    # Employee activation endpoints
    path('api/auth/activate/<uuid:token>/', auth_views.employee_activation_view, name='employee_activation'),
    path('api/auth/activate/<uuid:token>/complete/', auth_views.employee_activate_account_view, name='employee_activate_account'),

    # KPI endpoints
    path('api/kpi/', include('ems.kpi_urls')),

    # PDF Report endpoints
    path('api/pdf/hr-report/', pdf_views.generate_hr_pdf, name='hr-pdf-report'),
    path('api/pdf/test-arabic/', pdf_views.test_arabic_pdf, name='test-arabic-pdf'),
    path('api/pdf/status/', pdf_views.pdf_status, name='pdf-status'),
]

# Add enterprise features to the router
from . import enterprise_views_simple as enterprise_views

# Register enterprise viewsets
router.register(r'tenants', enterprise_views.TenantViewSet, basename='tenant')
router.register(r'system-settings', enterprise_views.SystemSettingsViewSet, basename='system-settings')
router.register(r'ml/models', enterprise_views.MLModelViewSet, basename='mlmodel')
router.register(r'ml/predictions', enterprise_views.MLPredictionViewSet, basename='mlprediction')
router.register(r'automation/rules', enterprise_views.AutomationRuleViewSet, basename='automationrule')
router.register(r'automation/executions', enterprise_views.AutomationExecutionViewSet, basename='automationexecution')
router.register(r'compliance/frameworks', enterprise_views.ComplianceFrameworkViewSet, basename='complianceframework')
router.register(r'compliance/requirements', enterprise_views.ComplianceRequirementViewSet, basename='compliancerequirement')
router.register(r'compliance/data-protection', enterprise_views.DataProtectionViewSet, basename='dataprotection')
router.register(r'compliance/audit-trail', enterprise_views.AuditTrailViewSet, basename='audittrail')
router.register(r'compliance/data-subject-requests', enterprise_views.DataSubjectRequestViewSet, basename='datasubjectrequest')
