"""
SECURITY FIX: Custom Authentication Classes for EMS
Secure httpOnly cookie-based JWT authentication with enhanced security
"""

import logging
from django.conf import settings
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.exceptions import TokenError, InvalidToken
from rest_framework_simplejwt.tokens import UntypedToken

logger = logging.getLogger(__name__)


class CookieJWTAuthentication(JWTAuthentication):
    """
    Custom JWT authentication that reads tokens from httpOnly cookies
    instead of Authorization header for enhanced security
    """
    
    def get_raw_token(self, header):
        """
        Extract the JWT token from httpOnly cookies instead of Authorization header
        """
        # The parent class passes the Authorization header, but we need the request object
        # We'll override the authenticate method instead to get proper access to request
        return super().get_raw_token(header)

    def authenticate(self, request):
        """
        Override authenticate to handle cookie-based authentication
        """
        # First try to get from cookie (preferred for security)
        raw_token = request.COOKIES.get('access_token')

        if raw_token:
            try:
                # MODERN FIX: Handle token validation properly (no manual encoding needed)
                validated_token = self.get_validated_token(raw_token)
                user = self.get_user(validated_token)
                return (user, validated_token)
            except TokenError as e:
                # Log the specific error for debugging
                logger.debug(f"Cookie JWT authentication failed: {str(e)}")
                pass  # Fall through to header-based auth
            except Exception as e:
                # Log unexpected errors
                logger.error(f"Unexpected error in cookie authentication: {str(e)}")
                pass

        # Fallback to Authorization header for backward compatibility
        return super().authenticate(request)
    
    def get_validated_token(self, raw_token):
        """
        Validate the token and handle cookie-specific errors
        """
        try:
            return super().get_validated_token(raw_token)
        except TokenError as e:
            # Log cookie authentication failure for monitoring
            logger.debug(f"Cookie JWT authentication failed: {str(e)}")
            raise

    def authenticate_header(self, request):
        """
        Return authentication header for 401 responses
        """
        return 'Bearer'


class SecureTokenManager:
    """
    SECURITY FIX: Secure token management utilities
    Handles token creation, validation, and cleanup
    """

    @staticmethod
    def set_auth_cookies(response, access_token, refresh_token=None):
        """
        Set secure httpOnly cookies for authentication tokens
        """
        # Access token cookie (short-lived)
        response.set_cookie(
            'access_token',
            access_token,
            max_age=settings.SIMPLE_JWT['ACCESS_TOKEN_LIFETIME'].total_seconds(),
            httponly=True,  # SECURITY: Prevent XSS access
            secure=False,  # Allow HTTP in development
            samesite='Lax',  # Allow cross-origin in development
            domain=None  # Allow all domains in development
        )

        # Refresh token cookie (longer-lived) if provided
        if refresh_token:
            response.set_cookie(
                'refresh_token',
                refresh_token,
                max_age=settings.SIMPLE_JWT['REFRESH_TOKEN_LIFETIME'].total_seconds(),
                httponly=True,  # SECURITY: Prevent XSS access
                secure=False,  # Allow HTTP in development
                samesite='Lax',  # Allow cross-origin in development
                domain=None  # Allow all domains in development
            )

        logger.info("Secure authentication cookies set")
        return response

    @staticmethod
    def clear_auth_cookies(response):
        """
        Clear authentication cookies on logout
        """
        response.delete_cookie('access_token')
        response.delete_cookie('refresh_token')
        logger.info("Authentication cookies cleared")
        return response
