# Generated by Django 4.2.7 on 2025-07-21 18:48

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('ems', '0004_currency_customerinvoice_discount_amount_base_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AssetAudit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('audit_type', models.CharField(choices=[('PHYSICAL', 'Physical Count'), ('FINANCIAL', 'Financial Verification'), ('CONDITION', 'Condition Assessment'), ('COMPLIANCE', 'Compliance Check'), ('FULL', 'Full Audit')], max_length=20)),
                ('audit_date', models.DateField()),
                ('expected_location', models.CharField(blank=True, max_length=200)),
                ('actual_location', models.Char<PERSON><PERSON>(blank=True, max_length=200)),
                ('expected_condition', models.CharField(blank=True, choices=[('EXCELLENT', 'Excellent'), ('GOOD', 'Good'), ('FAIR', 'Fair'), ('POOR', 'Poor'), ('DAMAGED', 'Damaged')], max_length=20)),
                ('actual_condition', models.CharField(blank=True, choices=[('EXCELLENT', 'Excellent'), ('GOOD', 'Good'), ('FAIR', 'Fair'), ('POOR', 'Poor'), ('DAMAGED', 'Damaged')], max_length=20)),
                ('status', models.CharField(choices=[('PLANNED', 'Planned'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled')], default='PLANNED', max_length=20)),
                ('result', models.CharField(blank=True, choices=[('FOUND', 'Found as Expected'), ('NOT_FOUND', 'Not Found'), ('DAMAGED', 'Found but Damaged'), ('WRONG_LOCATION', 'Found in Wrong Location'), ('WRONG_PERSON', 'Assigned to Wrong Person'), ('DISCREPANCY', 'Other Discrepancy')], max_length=20)),
                ('discrepancy_notes', models.TextField(blank=True)),
                ('corrective_action', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-audit_date'],
            },
        ),
        migrations.CreateModel(
            name='AssetDepreciation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('period_start', models.DateField()),
                ('period_end', models.DateField()),
                ('depreciation_amount', models.DecimalField(decimal_places=2, max_digits=15)),
                ('accumulated_depreciation', models.DecimalField(decimal_places=2, max_digits=15)),
                ('book_value', models.DecimalField(decimal_places=2, max_digits=15)),
                ('method_used', models.CharField(max_length=20)),
                ('calculation_notes', models.TextField(blank=True)),
                ('calculated_at', models.DateTimeField(auto_now_add=True)),
                ('is_adjustment', models.BooleanField(default=False)),
                ('adjustment_reason', models.TextField(blank=True)),
            ],
            options={
                'ordering': ['-period_end'],
            },
        ),
        migrations.CreateModel(
            name='AssetMaintenance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('maintenance_type', models.CharField(choices=[('PREVENTIVE', 'Preventive Maintenance'), ('CORRECTIVE', 'Corrective Maintenance'), ('EMERGENCY', 'Emergency Repair'), ('INSPECTION', 'Inspection'), ('CALIBRATION', 'Calibration'), ('UPGRADE', 'Upgrade'), ('CLEANING', 'Cleaning')], max_length=20)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('scheduled_date', models.DateField()),
                ('scheduled_time', models.TimeField(blank=True, null=True)),
                ('estimated_duration', models.DurationField(blank=True, null=True)),
                ('actual_start_date', models.DateTimeField(blank=True, null=True)),
                ('actual_end_date', models.DateTimeField(blank=True, null=True)),
                ('external_vendor', models.CharField(blank=True, max_length=200)),
                ('status', models.CharField(choices=[('SCHEDULED', 'Scheduled'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled'), ('OVERDUE', 'Overdue')], default='SCHEDULED', max_length=20)),
                ('priority', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('CRITICAL', 'Critical')], default='MEDIUM', max_length=20)),
                ('estimated_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('actual_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('work_performed', models.TextField(blank=True)),
                ('parts_used', models.TextField(blank=True)),
                ('issues_found', models.TextField(blank=True)),
                ('recommendations', models.TextField(blank=True)),
                ('next_maintenance_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-scheduled_date'],
            },
        ),
        migrations.CreateModel(
            name='AssetTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transfer_type', models.CharField(choices=[('ASSIGNMENT', 'Employee Assignment'), ('DEPARTMENT', 'Department Transfer'), ('LOCATION', 'Location Change'), ('RETURN', 'Return to Pool'), ('DISPOSAL', 'Disposal')], max_length=20)),
                ('from_location', models.CharField(blank=True, max_length=200)),
                ('to_location', models.CharField(blank=True, max_length=200)),
                ('transfer_date', models.DateField()),
                ('reason', models.TextField()),
                ('notes', models.TextField(blank=True)),
                ('condition_before', models.CharField(blank=True, choices=[('EXCELLENT', 'Excellent'), ('GOOD', 'Good'), ('FAIR', 'Fair'), ('POOR', 'Poor'), ('DAMAGED', 'Damaged')], max_length=20)),
                ('condition_after', models.CharField(blank=True, choices=[('EXCELLENT', 'Excellent'), ('GOOD', 'Good'), ('FAIR', 'Fair'), ('POOR', 'Poor'), ('DAMAGED', 'Damaged')], max_length=20)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('APPROVED', 'Approved'), ('IN_TRANSIT', 'In Transit'), ('COMPLETED', 'Completed'), ('REJECTED', 'Rejected'), ('CANCELLED', 'Cancelled')], default='PENDING', max_length=20)),
                ('approval_date', models.DateTimeField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-transfer_date'],
            },
        ),
        migrations.AlterModelOptions(
            name='asset',
            options={'ordering': ['-created_at']},
        ),
        migrations.AddField(
            model_name='asset',
            name='barcode',
            field=models.CharField(blank=True, max_length=100, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='asset',
            name='building',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='asset',
            name='condition',
            field=models.CharField(choices=[('EXCELLENT', 'Excellent'), ('GOOD', 'Good'), ('FAIR', 'Fair'), ('POOR', 'Poor'), ('DAMAGED', 'Damaged')], default='GOOD', max_length=20),
        ),
        migrations.AddField(
            model_name='asset',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_assets', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='asset',
            name='currency',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='ems.currency'),
        ),
        migrations.AddField(
            model_name='asset',
            name='department',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.department'),
        ),
        migrations.AddField(
            model_name='asset',
            name='depreciation_method',
            field=models.CharField(choices=[('STRAIGHT_LINE', 'Straight Line'), ('DECLINING_BALANCE', 'Declining Balance'), ('UNITS_OF_PRODUCTION', 'Units of Production'), ('SUM_OF_YEARS', 'Sum of Years Digits'), ('NO_DEPRECIATION', 'No Depreciation')], default='STRAIGHT_LINE', max_length=20),
        ),
        migrations.AddField(
            model_name='asset',
            name='depreciation_rate',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='For declining balance method', max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='asset',
            name='disposal_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='asset',
            name='disposal_method',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='asset',
            name='disposal_reason',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='asset',
            name='disposal_value',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True),
        ),
        migrations.AddField(
            model_name='asset',
            name='floor',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='asset',
            name='insurance_expiry',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='asset',
            name='insurance_policy',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='asset',
            name='invoice_number',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='asset',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='asset',
            name='last_maintenance_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='asset',
            name='maintenance_schedule',
            field=models.CharField(blank=True, help_text='e.g., Monthly, Quarterly, Annually', max_length=50),
        ),
        migrations.AddField(
            model_name='asset',
            name='next_maintenance_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='asset',
            name='purchase_order',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='asset',
            name='purchase_price_base',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True),
        ),
        migrations.AddField(
            model_name='asset',
            name='qr_code',
            field=models.CharField(blank=True, max_length=200),
        ),
        migrations.AddField(
            model_name='asset',
            name='room',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='asset',
            name='salvage_value',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=15),
        ),
        migrations.AddField(
            model_name='asset',
            name='supplier',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.supplier'),
        ),
        migrations.AddField(
            model_name='asset',
            name='tags',
            field=models.CharField(blank=True, help_text='Comma-separated tags', max_length=500),
        ),
        migrations.AddField(
            model_name='asset',
            name='useful_life_units',
            field=models.PositiveIntegerField(blank=True, help_text='For units of production method', null=True),
        ),
        migrations.AddField(
            model_name='asset',
            name='useful_life_years',
            field=models.PositiveIntegerField(default=5, help_text='Expected useful life in years'),
        ),
        migrations.AddField(
            model_name='asset',
            name='warranty_provider',
            field=models.CharField(blank=True, max_length=200),
        ),
        migrations.AlterField(
            model_name='asset',
            name='assigned_to',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_assets', to='ems.employee'),
        ),
        migrations.AlterField(
            model_name='asset',
            name='current_value',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True),
        ),
        migrations.AlterField(
            model_name='asset',
            name='purchase_price',
            field=models.DecimalField(decimal_places=2, max_digits=15),
        ),
        migrations.AlterField(
            model_name='asset',
            name='status',
            field=models.CharField(choices=[('AVAILABLE', 'Available'), ('IN_USE', 'In Use'), ('MAINTENANCE', 'Under Maintenance'), ('RETIRED', 'Retired'), ('LOST', 'Lost'), ('DAMAGED', 'Damaged'), ('DISPOSED', 'Disposed')], default='AVAILABLE', max_length=20),
        ),
        migrations.AddIndex(
            model_name='asset',
            index=models.Index(fields=['asset_id'], name='ems_asset_asset_i_4c1e50_idx'),
        ),
        migrations.AddIndex(
            model_name='asset',
            index=models.Index(fields=['status'], name='ems_asset_status_aee896_idx'),
        ),
        migrations.AddIndex(
            model_name='asset',
            index=models.Index(fields=['category'], name='ems_asset_categor_8a96db_idx'),
        ),
        migrations.AddIndex(
            model_name='asset',
            index=models.Index(fields=['assigned_to'], name='ems_asset_assigne_3b2bc3_idx'),
        ),
        migrations.AddField(
            model_name='assettransfer',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_transfers', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='assettransfer',
            name='asset',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers', to='ems.asset'),
        ),
        migrations.AddField(
            model_name='assettransfer',
            name='from_department',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='asset_transfers_from', to='ems.department'),
        ),
        migrations.AddField(
            model_name='assettransfer',
            name='from_employee',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='asset_transfers_from', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='assettransfer',
            name='requested_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='requested_transfers', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='assettransfer',
            name='to_department',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='asset_transfers_to', to='ems.department'),
        ),
        migrations.AddField(
            model_name='assettransfer',
            name='to_employee',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='asset_transfers_to', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='assetmaintenance',
            name='asset',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='maintenance_records', to='ems.asset'),
        ),
        migrations.AddField(
            model_name='assetmaintenance',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_maintenance', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='assetmaintenance',
            name='currency',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='ems.currency'),
        ),
        migrations.AddField(
            model_name='assetmaintenance',
            name='performed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='performed_maintenance', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='assetdepreciation',
            name='asset',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='depreciation_records', to='ems.asset'),
        ),
        migrations.AddField(
            model_name='assetdepreciation',
            name='calculated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee'),
        ),
        migrations.AddField(
            model_name='assetaudit',
            name='actual_assignee',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='actual_assets', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='assetaudit',
            name='asset',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='audit_records', to='ems.asset'),
        ),
        migrations.AddField(
            model_name='assetaudit',
            name='audited_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='conducted_audits', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='assetaudit',
            name='expected_assignee',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='expected_assets', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='assetaudit',
            name='verified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_audits', to='ems.employee'),
        ),
        migrations.AlterUniqueTogether(
            name='assetdepreciation',
            unique_together={('asset', 'period_start', 'period_end')},
        ),
    ]
