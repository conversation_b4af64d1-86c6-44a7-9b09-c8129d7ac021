# Generated by Django 4.2.7 on 2025-07-21 19:02

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('ems', '0005_assetaudit_assetdepreciation_assetmaintenance_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ReportTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('report_type', models.CharField(choices=[('FINANCIAL', 'Financial Report'), ('OPERATIONAL', 'Operational Report'), ('EXECUTIVE', 'Executive Dashboard'), ('COMPLIANCE', 'Compliance Report'), ('CUSTOM', 'Custom Report')], max_length=20)),
                ('template_config', models.JSONField(default=dict, help_text='Report configuration and layout')),
                ('data_sources', models.JSONField(default=list, help_text='List of data sources')),
                ('default_filters', models.JSONField(default=dict, help_text='Default filter values')),
                ('output_formats', models.JSONField(default=list, help_text='Supported output formats')),
                ('page_orientation', models.CharField(choices=[('PORTRAIT', 'Portrait'), ('LANDSCAPE', 'Landscape')], default='PORTRAIT', max_length=20)),
                ('is_public', models.BooleanField(default=False)),
                ('allowed_roles', models.JSONField(default=list, help_text='List of allowed role IDs')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee')),
            ],
            options={
                'ordering': ['report_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ReportExecution',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('parameters', models.JSONField(default=dict, help_text='Report parameters and filters')),
                ('output_format', models.CharField(max_length=20)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('RUNNING', 'Running'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('CANCELLED', 'Cancelled')], default='PENDING', max_length=20)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('execution_time', models.DurationField(blank=True, null=True)),
                ('output_file', models.FileField(blank=True, null=True, upload_to='reports/')),
                ('file_size', models.PositiveIntegerField(blank=True, help_text='File size in bytes', null=True)),
                ('error_message', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('requested_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='executions', to='ems.reporttemplate')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='KPIMetric',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('metric_type', models.CharField(choices=[('FINANCIAL', 'Financial'), ('OPERATIONAL', 'Operational'), ('CUSTOMER', 'Customer'), ('EMPLOYEE', 'Employee'), ('ASSET', 'Asset'), ('CUSTOM', 'Custom')], max_length=20)),
                ('calculation_method', models.CharField(choices=[('SUM', 'Sum'), ('AVERAGE', 'Average'), ('COUNT', 'Count'), ('PERCENTAGE', 'Percentage'), ('RATIO', 'Ratio'), ('CUSTOM_FORMULA', 'Custom Formula')], max_length=20)),
                ('custom_formula', models.TextField(blank=True, help_text='Custom calculation formula')),
                ('target_value', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('warning_threshold', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('critical_threshold', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('unit', models.CharField(blank=True, help_text='e.g., %, $, days', max_length=50)),
                ('decimal_places', models.PositiveIntegerField(default=2)),
                ('is_higher_better', models.BooleanField(default=True, help_text='Is a higher value better?')),
                ('frequency', models.CharField(choices=[('DAILY', 'Daily'), ('WEEKLY', 'Weekly'), ('MONTHLY', 'Monthly'), ('QUARTERLY', 'Quarterly'), ('YEARLY', 'Yearly')], default='MONTHLY', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee')),
            ],
            options={
                'ordering': ['metric_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Dashboard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('dashboard_type', models.CharField(choices=[('EXECUTIVE', 'Executive Dashboard'), ('FINANCIAL', 'Financial Dashboard'), ('OPERATIONAL', 'Operational Dashboard'), ('DEPARTMENTAL', 'Department Dashboard'), ('PERSONAL', 'Personal Dashboard')], max_length=20)),
                ('layout_config', models.JSONField(default=dict, help_text='Dashboard layout and widget configuration')),
                ('widgets', models.JSONField(default=list, help_text='List of dashboard widgets')),
                ('refresh_interval', models.PositiveIntegerField(default=300, help_text='Auto-refresh interval in seconds')),
                ('is_default', models.BooleanField(default=False)),
                ('is_public', models.BooleanField(default=False)),
                ('allowed_roles', models.JSONField(default=list, help_text='List of allowed role IDs')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.department')),
            ],
            options={
                'ordering': ['dashboard_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='AnalyticsQuery',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('query_type', models.CharField(choices=[('SQL', 'SQL Query'), ('AGGREGATION', 'Aggregation Query'), ('FILTER', 'Filter Query'), ('CUSTOM', 'Custom Query')], max_length=20)),
                ('query_definition', models.JSONField(help_text='Query structure and parameters')),
                ('sql_query', models.TextField(blank=True, help_text='Raw SQL query if applicable')),
                ('cache_duration', models.PositiveIntegerField(default=3600, help_text='Cache duration in seconds')),
                ('last_executed', models.DateTimeField(blank=True, null=True)),
                ('execution_count', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee')),
            ],
            options={
                'ordering': ['name'],
            },
        ),
    ]
