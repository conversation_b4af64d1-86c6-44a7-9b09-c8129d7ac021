# Generated by Django 4.2.7 on 2025-07-21 19:24

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('ems', '0007_kpimetricvalue'),
    ]

    operations = [
        migrations.CreateModel(
            name='APIKey',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('key_type', models.CharField(choices=[('INTERNAL', 'Internal API Key'), ('EXTERNAL', 'External Service Key'), ('WEBHOOK', 'Webhook Key'), ('INTEGRATION', 'Integration Key')], max_length=20)),
                ('key_id', models.CharField(max_length=100, unique=True)),
                ('key_secret', models.Char<PERSON><PERSON>(max_length=255)),
                ('key_prefix', models.Char<PERSON><PERSON>(default='ems', max_length=20)),
                ('scopes', models.J<PERSON><PERSON>ield(default=list, help_text='List of allowed scopes/permissions')),
                ('allowed_ips', models.JSONField(default=list, help_text='List of allowed IP addresses')),
                ('rate_limit', models.PositiveIntegerField(default=1000, help_text='Requests per hour')),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('INACTIVE', 'Inactive'), ('EXPIRED', 'Expired'), ('REVOKED', 'Revoked')], default='ACTIVE', max_length=20)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('last_used_at', models.DateTimeField(blank=True, null=True)),
                ('usage_count', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ExternalService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('service_type', models.CharField(choices=[('PAYMENT', 'Payment Gateway'), ('BANK', 'Banking Service'), ('ACCOUNTING', 'Accounting Software'), ('CRM', 'Customer Relationship Management'), ('INVENTORY', 'Inventory Management'), ('SHIPPING', 'Shipping Service'), ('EMAIL', 'Email Service'), ('SMS', 'SMS Service'), ('STORAGE', 'Cloud Storage'), ('ANALYTICS', 'Analytics Service'), ('CUSTOM', 'Custom Integration')], max_length=20)),
                ('description', models.TextField(blank=True)),
                ('base_url', models.URLField()),
                ('api_version', models.CharField(blank=True, max_length=20)),
                ('authentication_type', models.CharField(default='API_KEY', max_length=50)),
                ('config', models.JSONField(default=dict, help_text='Service-specific configuration')),
                ('headers', models.JSONField(default=dict, help_text='Default headers for requests')),
                ('timeout', models.PositiveIntegerField(default=30, help_text='Request timeout in seconds')),
                ('credentials', models.JSONField(default=dict, help_text='Encrypted service credentials')),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('INACTIVE', 'Inactive'), ('TESTING', 'Testing'), ('ERROR', 'Error'), ('MAINTENANCE', 'Maintenance')], default='INACTIVE', max_length=20)),
                ('last_health_check', models.DateTimeField(blank=True, null=True)),
                ('health_check_url', models.URLField(blank=True)),
                ('is_healthy', models.BooleanField(default=False)),
                ('total_requests', models.PositiveIntegerField(default=0)),
                ('successful_requests', models.PositiveIntegerField(default=0)),
                ('failed_requests', models.PositiveIntegerField(default=0)),
                ('last_request_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee')),
            ],
            options={
                'ordering': ['service_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='WebhookEndpoint',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('event_types', models.JSONField(default=list, help_text='List of event types to handle')),
                ('url', models.URLField()),
                ('method', models.CharField(default='POST', max_length=10)),
                ('headers', models.JSONField(default=dict, help_text='Headers to include in webhook calls')),
                ('secret', models.CharField(blank=True, help_text='Webhook secret for verification', max_length=255)),
                ('max_retries', models.PositiveIntegerField(default=3)),
                ('retry_delay', models.PositiveIntegerField(default=60, help_text='Retry delay in seconds')),
                ('timeout', models.PositiveIntegerField(default=30, help_text='Request timeout in seconds')),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('INACTIVE', 'Inactive'), ('PAUSED', 'Paused'), ('ERROR', 'Error')], default='ACTIVE', max_length=20)),
                ('is_verified', models.BooleanField(default=False)),
                ('last_triggered_at', models.DateTimeField(blank=True, null=True)),
                ('total_calls', models.PositiveIntegerField(default=0)),
                ('successful_calls', models.PositiveIntegerField(default=0)),
                ('failed_calls', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WebhookEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(max_length=50)),
                ('event_id', models.CharField(max_length=100, unique=True)),
                ('payload', models.JSONField(help_text='Event payload data')),
                ('headers', models.JSONField(default=dict, help_text='Headers sent with the webhook')),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('DELIVERED', 'Delivered'), ('FAILED', 'Failed'), ('RETRYING', 'Retrying'), ('CANCELLED', 'Cancelled')], default='PENDING', max_length=20)),
                ('attempts', models.PositiveIntegerField(default=0)),
                ('max_attempts', models.PositiveIntegerField(default=3)),
                ('response_status_code', models.PositiveIntegerField(blank=True, null=True)),
                ('response_body', models.TextField(blank=True)),
                ('response_headers', models.JSONField(default=dict)),
                ('scheduled_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('first_attempted_at', models.DateTimeField(blank=True, null=True)),
                ('last_attempted_at', models.DateTimeField(blank=True, null=True)),
                ('delivered_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('webhook_endpoint', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='events', to='ems.webhookendpoint')),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['event_type', 'status'], name='ems_webhook_event_t_3ea6cf_idx'), models.Index(fields=['webhook_endpoint', 'status'], name='ems_webhook_webhook_d55a59_idx')],
            },
        ),
        migrations.CreateModel(
            name='IntegrationLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('log_type', models.CharField(choices=[('API_REQUEST', 'API Request'), ('API_RESPONSE', 'API Response'), ('WEBHOOK_SENT', 'Webhook Sent'), ('WEBHOOK_RECEIVED', 'Webhook Received'), ('SYNC_OPERATION', 'Sync Operation'), ('ERROR', 'Error'), ('INFO', 'Information')], max_length=20)),
                ('severity', models.CharField(choices=[('DEBUG', 'Debug'), ('INFO', 'Information'), ('WARNING', 'Warning'), ('ERROR', 'Error'), ('CRITICAL', 'Critical')], default='INFO', max_length=20)),
                ('message', models.TextField()),
                ('details', models.JSONField(default=dict, help_text='Additional log details')),
                ('request_data', models.JSONField(blank=True, default=dict)),
                ('response_data', models.JSONField(blank=True, default=dict)),
                ('duration_ms', models.PositiveIntegerField(blank=True, help_text='Operation duration in milliseconds', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('api_key', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.apikey')),
                ('external_service', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.externalservice')),
                ('webhook_endpoint', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.webhookendpoint')),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['log_type', 'severity'], name='ems_integra_log_typ_d744b3_idx'), models.Index(fields=['external_service', 'created_at'], name='ems_integra_externa_8149e4_idx'), models.Index(fields=['created_at'], name='ems_integra_created_a80391_idx')],
            },
        ),
    ]
