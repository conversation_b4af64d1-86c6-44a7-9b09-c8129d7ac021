# Generated by Django 4.2.7 on 2025-07-21 16:34

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('ems', '0002_employeeactivation'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('ASSET', 'Asset'), ('LIABILITY', 'Liability'), ('EQUITY', 'Equity'), ('REVENUE', 'Revenue'), ('EXPENSE', 'Expense'), ('COST_OF_GOODS_SOLD', 'Cost of Goods Sold')], max_length=50, unique=True)),
                ('name_ar', models.CharField(max_length=50)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('is_active', models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ChartOfAccounts',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account_code', models.CharField(help_text='Account code (e.g., 1000, 1100)', max_length=20, unique=True)),
                ('account_name', models.CharField(max_length=200)),
                ('account_name_ar', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_system_account', models.BooleanField(default=False, help_text='System-generated account')),
                ('level', models.IntegerField(default=0, help_text='Account hierarchy level')),
                ('allow_manual_entries', models.BooleanField(default=True)),
                ('require_department', models.BooleanField(default=False)),
                ('require_project', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('account_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.accounttype')),
                ('parent_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sub_accounts', to='ems.chartofaccounts')),
            ],
            options={
                'verbose_name_plural': 'Chart of Accounts',
                'ordering': ['account_code'],
            },
        ),
        migrations.CreateModel(
            name='CustomerInvoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(max_length=50, unique=True)),
                ('invoice_date', models.DateField()),
                ('due_date', models.DateField()),
                ('description', models.TextField()),
                ('description_ar', models.TextField(blank=True)),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=12)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('paid_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('SENT', 'Sent'), ('PAID', 'Paid'), ('PARTIAL', 'Partially Paid'), ('OVERDUE', 'Overdue'), ('CANCELLED', 'Cancelled')], default='DRAFT', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customer_invoices', to='ems.customer')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.department')),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.project')),
                ('sales_order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.salesorder')),
            ],
            options={
                'ordering': ['-invoice_date'],
            },
        ),
        migrations.CreateModel(
            name='FiscalYear',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('is_current', models.BooleanField(default=False)),
                ('is_closed', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='Vendor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('vendor_code', models.CharField(max_length=20, unique=True)),
                ('company_name', models.CharField(max_length=200)),
                ('company_name_ar', models.CharField(blank=True, max_length=200)),
                ('contact_person', models.CharField(blank=True, max_length=100)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('address', models.TextField(blank=True)),
                ('address_ar', models.TextField(blank=True)),
                ('tax_id', models.CharField(blank=True, max_length=50)),
                ('payment_terms', models.CharField(default='Net 30', max_length=50)),
                ('credit_limit', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('currency', models.CharField(default='SAR', max_length=3)),
                ('is_active', models.BooleanField(default=True)),
                ('is_approved', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='VendorInvoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(max_length=50, unique=True)),
                ('vendor_invoice_number', models.CharField(help_text="Vendor's invoice number", max_length=50)),
                ('invoice_date', models.DateField()),
                ('due_date', models.DateField()),
                ('description', models.TextField()),
                ('description_ar', models.TextField(blank=True)),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=12)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('paid_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('PENDING', 'Pending Approval'), ('APPROVED', 'Approved'), ('PAID', 'Paid'), ('CANCELLED', 'Cancelled'), ('OVERDUE', 'Overdue')], default='DRAFT', max_length=20)),
                ('approval_date', models.DateTimeField(blank=True, null=True)),
                ('invoice_file', models.FileField(blank=True, null=True, upload_to='vendor_invoices/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_vendor_invoices', to='ems.employee')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.department')),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.project')),
                ('purchase_order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.purchaseorder')),
                ('vendor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vendor_invoices', to='ems.vendor')),
            ],
            options={
                'ordering': ['-invoice_date'],
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_number', models.CharField(max_length=50, unique=True)),
                ('payment_type', models.CharField(choices=[('VENDOR_PAYMENT', 'Vendor Payment'), ('CUSTOMER_PAYMENT', 'Customer Payment')], max_length=20)),
                ('payment_method', models.CharField(choices=[('CASH', 'Cash'), ('CHECK', 'Check'), ('BANK_TRANSFER', 'Bank Transfer'), ('CREDIT_CARD', 'Credit Card'), ('ONLINE', 'Online Payment')], max_length=20)),
                ('payment_date', models.DateField()),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('currency', models.CharField(default='SAR', max_length=3)),
                ('exchange_rate', models.DecimalField(decimal_places=4, default=1.0, max_digits=10)),
                ('bank_account', models.CharField(blank=True, max_length=100)),
                ('check_number', models.CharField(blank=True, max_length=50)),
                ('reference_number', models.CharField(blank=True, max_length=100)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
                ('customer_invoice', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='ems.customerinvoice')),
                ('vendor_invoice', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='ems.vendorinvoice')),
            ],
            options={
                'ordering': ['-payment_date'],
            },
        ),
        migrations.CreateModel(
            name='JournalEntryBatch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('batch_number', models.CharField(max_length=50, unique=True)),
                ('description', models.CharField(max_length=200)),
                ('description_ar', models.CharField(blank=True, max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_posted', models.BooleanField(default=False)),
                ('posted_at', models.DateTimeField(blank=True, null=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
                ('posted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='posted_batches', to='ems.employee')),
            ],
        ),
        migrations.CreateModel(
            name='JournalEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('entry_number', models.CharField(max_length=50)),
                ('transaction_date', models.DateField()),
                ('description', models.CharField(max_length=200)),
                ('description_ar', models.CharField(blank=True, max_length=200)),
                ('debit_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('credit_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('source_document_type', models.CharField(blank=True, help_text='e.g., Invoice, Payment, Expense', max_length=50)),
                ('source_document_id', models.CharField(blank=True, max_length=50)),
                ('is_posted', models.BooleanField(default=False)),
                ('posted_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.chartofaccounts')),
                ('batch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='journal_entries', to='ems.journalentrybatch')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.department')),
                ('employee', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee')),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.project')),
            ],
            options={
                'verbose_name_plural': 'Journal Entries',
                'ordering': ['-transaction_date', 'entry_number'],
            },
        ),
    ]
