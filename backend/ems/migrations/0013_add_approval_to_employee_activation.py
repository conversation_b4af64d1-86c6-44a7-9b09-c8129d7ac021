# Generated by Django 4.2.7 on 2025-07-23 06:24

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('ems', '0012_add_created_by_to_employee'),
    ]

    operations = [
        migrations.AddField(
            model_name='employeeactivation',
            name='approval_status',
            field=models.CharField(choices=[('PENDING', 'Pending Admin Approval'), ('APPROVED', 'Approved by Admin'), ('REJECTED', 'Rejected by Admin')], default='PENDING', max_length=20),
        ),
        migrations.AddField(
            model_name='employeeactivation',
            name='approved_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='employeeactivation',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_activations', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='employeeactivation',
            name='rejection_reason',
            field=models.TextField(blank=True, help_text='Reason for rejection if applicable'),
        ),
    ]
