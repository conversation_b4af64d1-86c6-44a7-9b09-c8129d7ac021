# Generated by Django 4.2.7 on 2025-07-21 18:23

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('ems', '0003_accounttype_chartofaccounts_customerinvoice_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Currency',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(help_text='ISO 4217 currency code (e.g., USD, EUR, SAR)', max_length=3, unique=True)),
                ('name', models.CharField(help_text='Full currency name', max_length=100)),
                ('symbol', models.CharField(help_text='Currency symbol (e.g., $, €, ﷼)', max_length=10)),
                ('decimal_places', models.PositiveIntegerField(default=2, help_text='Number of decimal places')),
                ('is_base_currency', models.BooleanField(default=False, help_text='Is this the base currency for the system?')),
                ('is_active', models.BooleanField(default=True, help_text='Is this currency active?')),
                ('symbol_position', models.CharField(choices=[('before', 'Before amount (e.g., $100)'), ('after', 'After amount (e.g., 100$)')], default='before', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Currencies',
                'ordering': ['code'],
            },
        ),
        migrations.AddField(
            model_name='customerinvoice',
            name='discount_amount_base',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AddField(
            model_name='customerinvoice',
            name='exchange_rate',
            field=models.DecimalField(decimal_places=6, default=1, help_text='Exchange rate to base currency', max_digits=15),
        ),
        migrations.AddField(
            model_name='customerinvoice',
            name='paid_amount_base',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=12),
        ),
        migrations.AddField(
            model_name='customerinvoice',
            name='subtotal_base',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AddField(
            model_name='customerinvoice',
            name='tax_amount_base',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AddField(
            model_name='customerinvoice',
            name='total_amount_base',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AddField(
            model_name='vendorinvoice',
            name='discount_amount_base',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AddField(
            model_name='vendorinvoice',
            name='exchange_rate',
            field=models.DecimalField(decimal_places=6, default=1, help_text='Exchange rate to base currency', max_digits=15),
        ),
        migrations.AddField(
            model_name='vendorinvoice',
            name='paid_amount_base',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=12),
        ),
        migrations.AddField(
            model_name='vendorinvoice',
            name='subtotal_base',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AddField(
            model_name='vendorinvoice',
            name='tax_amount_base',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AddField(
            model_name='vendorinvoice',
            name='total_amount_base',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AddField(
            model_name='customerinvoice',
            name='currency',
            field=models.ForeignKey(blank=True, help_text='Invoice currency', null=True, on_delete=django.db.models.deletion.PROTECT, to='ems.currency'),
        ),
        migrations.AddField(
            model_name='vendorinvoice',
            name='currency',
            field=models.ForeignKey(blank=True, help_text='Invoice currency', null=True, on_delete=django.db.models.deletion.PROTECT, to='ems.currency'),
        ),
        migrations.CreateModel(
            name='ExchangeRate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rate', models.DecimalField(decimal_places=6, help_text='Exchange rate from source to target currency', max_digits=15)),
                ('effective_date', models.DateField(help_text='Date when this rate becomes effective')),
                ('source', models.CharField(choices=[('manual', 'Manual Entry'), ('api', 'API Feed'), ('bank', 'Bank Rate'), ('central_bank', 'Central Bank Rate')], default='manual', max_length=50)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee')),
                ('from_currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rates_from', to='ems.currency')),
                ('to_currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rates_to', to='ems.currency')),
            ],
            options={
                'ordering': ['-effective_date', 'from_currency__code', 'to_currency__code'],
                'unique_together': {('from_currency', 'to_currency', 'effective_date')},
            },
        ),
    ]
