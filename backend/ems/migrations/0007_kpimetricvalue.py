# Generated by Django 4.2.7 on 2025-07-21 19:07

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('ems', '0006_reporttemplate_reportexecution_kpimetric_dashboard_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='KPIMetricValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('period_start', models.DateField()),
                ('period_end', models.DateField()),
                ('value', models.DecimalField(decimal_places=6, max_digits=15)),
                ('notes', models.TextField(blank=True)),
                ('data_source', models.CharField(blank=True, max_length=200)),
                ('calculated_at', models.DateTimeField(auto_now_add=True)),
                ('is_manual', models.<PERSON><PERSON>an<PERSON>ield(default=False)),
                ('calculated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee')),
                ('kpi_metric', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='metric_values', to='ems.kpimetric')),
            ],
            options={
                'ordering': ['-period_end'],
                'unique_together': {('kpi_metric', 'period_start', 'period_end')},
            },
        ),
    ]
