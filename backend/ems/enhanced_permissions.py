"""
Enhanced Role-Based Permissions for EMS KPI System
Implements enterprise-grade hierarchical access control with data scoping.

This module provides:
1. Hierarchical role-based permissions
2. Manager-subordinate data access
3. Department and project scoped permissions
4. KPI-specific access control
5. Data filtering based on organizational hierarchy
"""

from rest_framework.permissions import BasePermission
from django.db.models import Q
from typing import Optional, List, Dict, Any
import logging

from .models import Employee, Department, Project, KPI, KPIValue, Role
from .hierarchical_access import get_hierarchical_access_manager

logger = logging.getLogger(__name__)


class HierarchicalKPIPermission(BasePermission):
    """
    Enhanced KPI permission class with hierarchical access control.
    Implements enterprise patterns similar to Tableau and Power BI.
    """
    
    def has_permission(self, request, view):
        """Check if user has permission to access KPI endpoints"""
        if not request.user or not request.user.is_authenticated:
            return False
            
        try:
            user_profile = request.user.userprofile
            if not user_profile or not user_profile.role:
                return False
                
            # All authenticated users with roles can access KPI endpoints
            # Specific data filtering happens in has_object_permission and QuerySet filtering
            return True
            
        except AttributeError:
            return False
    
    def has_object_permission(self, request, view, obj):
        """Check if user has permission to access specific KPI object"""
        try:
            access_manager = get_hierarchical_access_manager(request.user)
            
            if isinstance(obj, KPI):
                return self._check_kpi_access(access_manager, obj, request.method)
            elif isinstance(obj, KPIValue):
                return self._check_kpi_value_access(access_manager, obj, request.method)
                
            return False
            
        except Exception as e:
            logger.error(f"Error checking KPI object permission: {str(e)}")
            return False
    
    def _check_kpi_access(self, access_manager, kpi: KPI, method: str) -> bool:
        """Check access to specific KPI based on hierarchical rules"""
        role_name = access_manager.role_name
        
        # Super Admin and Admin: Full access
        if role_name in ['SUPERADMIN', 'ADMIN']:
            return True
            
        # Check if KPI is visible to user's role
        if access_manager.role and kpi.visible_to_roles.exists():
            if not kpi.visible_to_roles.filter(id=access_manager.role.id).exists():
                return False
        
        # Role-specific KPI access rules
        role_categories = access_manager.get_role_specific_kpi_categories()
        
        if 'All' in role_categories:
            return True
            
        # Check if KPI category matches role-specific categories
        if kpi.category and kpi.category.name:
            for category in role_categories:
                if category.lower() in kpi.category.name.lower():
                    return True
        
        # For write operations, additional restrictions
        if method in ['POST', 'PUT', 'PATCH', 'DELETE']:
            # Only allow recalculation triggers, not manual entry
            if role_name in ['SUPERADMIN', 'ADMIN', 'HR_MANAGER', 'FINANCE_MANAGER']:
                return True
            return False
            
        return False
    
    def _check_kpi_value_access(self, access_manager, kpi_value: KPIValue, method: str) -> bool:
        """Check access to specific KPI value based on hierarchical rules and data scoping"""
        # First check if user can access the KPI itself
        if not self._check_kpi_access(access_manager, kpi_value.kpi, method):
            return False
            
        # For KPI values, apply additional data scoping
        role_name = access_manager.role_name
        
        # Super Admin and Admin: Full access
        if role_name in ['SUPERADMIN', 'ADMIN']:
            return True
            
        # HR Manager: Access to all employee-related KPI values
        if role_name == 'HR_MANAGER':
            return True
            
        # Finance Manager: Access to all financial KPI values
        if role_name == 'FINANCE_MANAGER':
            return True
            
        # Department Manager: Access to KPI values for accessible employees/departments
        if role_name == 'DEPARTMENT_MANAGER':
            accessible_employees = access_manager.get_accessible_employees()
            accessible_departments = access_manager.get_accessible_departments()
            
            # Check if KPI value relates to accessible data
            # This would need customization based on how KPI values relate to employees/departments
            return True  # Simplified for now
            
        # Project Manager: Access to KPI values for accessible projects/team members
        if role_name == 'PROJECT_MANAGER':
            accessible_employees = access_manager.get_accessible_employees()
            accessible_projects = access_manager.get_accessible_projects()
            
            # Check if KPI value relates to accessible data
            return True  # Simplified for now
            
        # Employee/Intern: Access to own KPI values only
        if role_name in ['EMPLOYEE', 'INTERN']:
            # Check if KPI value relates to the user's own data
            if hasattr(kpi_value, 'employee') and kpi_value.employee:
                return kpi_value.employee == access_manager.employee
            return True  # Allow access to general KPI values
            
        return False


class DepartmentScopedPermission(BasePermission):
    """
    Permission class for department-scoped data access.
    Ensures users can only access data within their department hierarchy.
    """
    
    def has_permission(self, request, view):
        """Check basic permission to access department-scoped endpoints"""
        if not request.user or not request.user.is_authenticated:
            return False
            
        try:
            user_profile = request.user.userprofile
            return user_profile and user_profile.role
        except AttributeError:
            return False
    
    def has_object_permission(self, request, view, obj):
        """Check if user can access specific department-scoped object"""
        try:
            access_manager = get_hierarchical_access_manager(request.user)
            accessible_departments = access_manager.get_accessible_departments()
            
            # Check if object belongs to accessible department
            if hasattr(obj, 'department') and obj.department:
                return accessible_departments.filter(id=obj.department.id).exists()
            elif hasattr(obj, 'employee') and obj.employee and obj.employee.department:
                return accessible_departments.filter(id=obj.employee.department.id).exists()
                
            return True  # Allow access if no department relationship
            
        except Exception as e:
            logger.error(f"Error checking department-scoped permission: {str(e)}")
            return False


class ProjectScopedPermission(BasePermission):
    """
    Permission class for project-scoped data access.
    Ensures users can only access data within their project scope.
    """
    
    def has_permission(self, request, view):
        """Check basic permission to access project-scoped endpoints"""
        if not request.user or not request.user.is_authenticated:
            return False
            
        try:
            user_profile = request.user.userprofile
            return user_profile and user_profile.role
        except AttributeError:
            return False
    
    def has_object_permission(self, request, view, obj):
        """Check if user can access specific project-scoped object"""
        try:
            access_manager = get_hierarchical_access_manager(request.user)
            accessible_projects = access_manager.get_accessible_projects()
            
            # Check if object belongs to accessible project
            if hasattr(obj, 'project') and obj.project:
                return accessible_projects.filter(id=obj.project.id).exists()
                
            return True  # Allow access if no project relationship
            
        except Exception as e:
            logger.error(f"Error checking project-scoped permission: {str(e)}")
            return False


class HierarchicalEmployeePermission(BasePermission):
    """
    Permission class for hierarchical employee data access.
    Implements manager-subordinate access patterns.
    """
    
    def has_permission(self, request, view):
        """Check basic permission to access employee endpoints"""
        if not request.user or not request.user.is_authenticated:
            return False
            
        try:
            user_profile = request.user.userprofile
            return user_profile and user_profile.role
        except AttributeError:
            return False
    
    def has_object_permission(self, request, view, obj):
        """Check if user can access specific employee object"""
        try:
            access_manager = get_hierarchical_access_manager(request.user)
            
            if isinstance(obj, Employee):
                return access_manager.can_access_employee_data(obj)
            elif hasattr(obj, 'employee') and obj.employee:
                return access_manager.can_access_employee_data(obj.employee)
            elif hasattr(obj, 'user') and obj.user:
                try:
                    employee = obj.user.employee
                    return access_manager.can_access_employee_data(employee)
                except Employee.DoesNotExist:
                    pass
                    
            return True  # Allow access if no employee relationship
            
        except Exception as e:
            logger.error(f"Error checking hierarchical employee permission: {str(e)}")
            return False


class EnhancedRoleBasedPermission(BasePermission):
    """
    Enhanced role-based permission with hierarchical access control.
    Combines traditional role-based permissions with hierarchical data access.
    """
    
    def __init__(self):
        self.role_permissions = {
            'SUPERADMIN': {
                'can_access_all_data': True,
                'can_manage_system': True,
                'can_manage_users': True,
                'can_view_all_kpis': True,
                'can_modify_kpis': True
            },
            'ADMIN': {
                'can_access_all_data': True,
                'can_manage_system': False,
                'can_manage_users': True,
                'can_view_all_kpis': True,
                'can_modify_kpis': True
            },
            'HR_MANAGER': {
                'can_access_all_employees': True,
                'can_view_hr_kpis': True,
                'can_manage_hr_data': True,
                'can_view_organizational_kpis': True
            },
            'FINANCE_MANAGER': {
                'can_access_financial_data': True,
                'can_view_financial_kpis': True,
                'can_manage_budgets': True,
                'can_view_cost_kpis': True
            },
            'DEPARTMENT_MANAGER': {
                'can_access_department_data': True,
                'can_view_team_kpis': True,
                'can_manage_department': True,
                'can_view_subordinate_data': True
            },
            'PROJECT_MANAGER': {
                'can_access_project_data': True,
                'can_view_project_kpis': True,
                'can_manage_projects': True,
                'can_view_team_member_data': True
            },
            'EMPLOYEE': {
                'can_access_own_data': True,
                'can_view_personal_kpis': True,
                'can_view_team_kpis': False
            },
            'INTERN': {
                'can_access_own_data': True,
                'can_view_personal_kpis': True,
                'can_view_learning_kpis': True
            }
        }
    
    def has_permission(self, request, view):
        """Check if user has basic permission based on role"""
        if not request.user or not request.user.is_authenticated:
            return False
            
        try:
            user_profile = request.user.userprofile
            if not user_profile or not user_profile.role:
                return False
                
            role_name = user_profile.role.name
            return role_name in self.role_permissions
            
        except AttributeError:
            return False
    
    def has_object_permission(self, request, view, obj):
        """Check object-level permission with hierarchical access"""
        try:
            access_manager = get_hierarchical_access_manager(request.user)
            role_name = access_manager.role_name
            
            if not role_name or role_name not in self.role_permissions:
                return False
                
            permissions = self.role_permissions[role_name]
            
            # Apply hierarchical access control based on object type
            if isinstance(obj, (KPI, KPIValue)):
                hierarchical_permission = HierarchicalKPIPermission()
                return hierarchical_permission.has_object_permission(request, view, obj)
            elif isinstance(obj, Employee):
                hierarchical_permission = HierarchicalEmployeePermission()
                return hierarchical_permission.has_object_permission(request, view, obj)
            elif hasattr(obj, 'department'):
                department_permission = DepartmentScopedPermission()
                return department_permission.has_object_permission(request, view, obj)
            elif hasattr(obj, 'project'):
                project_permission = ProjectScopedPermission()
                return project_permission.has_object_permission(request, view, obj)
                
            return True
            
        except Exception as e:
            logger.error(f"Error checking enhanced role-based permission: {str(e)}")
            return False
    
    def check_permission(self, role_name: str, permission_key: str) -> bool:
        """Check if role has specific permission"""
        if role_name not in self.role_permissions:
            return False
        return self.role_permissions[role_name].get(permission_key, False)
