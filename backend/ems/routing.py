"""
WebSocket routing configuration for EMS real-time features.

This module defines WebSocket URL patterns for:
- Real-time KPI updates
- Live notifications
- Dashboard streaming
- Enterprise monitoring

All WebSocket connections require authentication and follow enterprise security standards.
"""

from django.urls import re_path
from . import consumers

websocket_urlpatterns = [
    # Real-time KPI updates
    re_path(r'ws/kpi/$', consumers.KPIConsumer.as_asgi()),
    
    # Live notifications
    re_path(r'ws/notifications/$', consumers.NotificationConsumer.as_asgi()),
    
    # Dashboard streaming
    re_path(r'ws/dashboard/$', consumers.DashboardConsumer.as_asgi()),
    
    # System monitoring
    re_path(r'ws/monitoring/$', consumers.MonitoringConsumer.as_asgi()),
]
