from django.core.management.base import BaseCommand
from django.utils import timezone
from ems.models import (
    AssetCategory, Asset, Currency, Department, Employee,
    AssetMaintenance, AssetDepreciation
)
from decimal import Decimal
from datetime import date, timedelta
import random


class Command(BaseCommand):
    help = 'Set up sample asset data for asset management demonstration'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-categories',
            action='store_true',
            help='Create sample asset categories'
        )
        parser.add_argument(
            '--create-assets',
            action='store_true',
            help='Create sample assets'
        )
        parser.add_argument(
            '--create-maintenance',
            action='store_true',
            help='Create sample maintenance records'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up asset management data...'))
        
        if options['create_categories'] or not any([
            options['create_categories'], options['create_assets'], options['create_maintenance']
        ]):
            self.create_asset_categories()
        
        if options['create_assets'] or not any([
            options['create_categories'], options['create_assets'], options['create_maintenance']
        ]):
            self.create_sample_assets()
        
        if options['create_maintenance'] or not any([
            options['create_categories'], options['create_assets'], options['create_maintenance']
        ]):
            self.create_sample_maintenance()
        
        self.stdout.write(self.style.SUCCESS('\n=== ASSET MANAGEMENT SETUP COMPLETE ==='))

    def create_asset_categories(self):
        """Create sample asset categories"""
        categories_data = [
            {
                'name': 'Computer Equipment',
                'name_ar': 'معدات الحاسوب',
                'description': 'Laptops, desktops, servers, and computer peripherals',
                'description_ar': 'أجهزة الكمبيوتر المحمولة وأجهزة سطح المكتب والخوادم وملحقات الكمبيوتر'
            },
            {
                'name': 'Office Furniture',
                'name_ar': 'أثاث المكتب',
                'description': 'Desks, chairs, cabinets, and office furniture',
                'description_ar': 'المكاتب والكراسي والخزائن وأثاث المكتب'
            },
            {
                'name': 'Vehicles',
                'name_ar': 'المركبات',
                'description': 'Company cars, trucks, and other vehicles',
                'description_ar': 'سيارات الشركة والشاحنات والمركبات الأخرى'
            },
            {
                'name': 'Manufacturing Equipment',
                'name_ar': 'معدات التصنيع',
                'description': 'Production machinery and manufacturing tools',
                'description_ar': 'آلات الإنتاج وأدوات التصنيع'
            },
            {
                'name': 'Office Equipment',
                'name_ar': 'معدات المكتب',
                'description': 'Printers, scanners, phones, and office devices',
                'description_ar': 'الطابعات والماسحات الضوئية والهواتف وأجهزة المكتب'
            },
            {
                'name': 'Security Equipment',
                'name_ar': 'معدات الأمان',
                'description': 'Cameras, access control systems, and security devices',
                'description_ar': 'الكاميرات وأنظمة التحكم في الوصول وأجهزة الأمان'
            }
        ]
        
        created_categories = []
        for category_data in categories_data:
            category, created = AssetCategory.objects.get_or_create(
                name=category_data['name'],
                defaults=category_data
            )
            if created:
                created_categories.append(category.name)
                self.stdout.write(f'Created category: {category.name}')
            else:
                self.stdout.write(f'Category already exists: {category.name}')
        
        self.stdout.write(f'Asset categories setup complete. Created: {len(created_categories)}')

    def create_sample_assets(self):
        """Create sample assets"""
        try:
            # Get base currency
            base_currency = Currency.objects.get(is_base_currency=True)
        except Currency.DoesNotExist:
            self.stdout.write(self.style.WARNING('No base currency found. Please run setup_currencies first.'))
            return
        
        # Get categories
        categories = AssetCategory.objects.all()
        if not categories.exists():
            self.stdout.write(self.style.WARNING('No asset categories found. Creating categories first.'))
            self.create_asset_categories()
            categories = AssetCategory.objects.all()
        
        # Get departments and employees
        departments = list(Department.objects.all())
        employees = list(Employee.objects.all())
        
        if not departments or not employees:
            self.stdout.write(self.style.WARNING('No departments or employees found. Some assets will not be assigned.'))
        
        # Sample asset data
        assets_data = [
            # Computer Equipment
            {
                'name': 'Dell Laptop OptiPlex 7090',
                'name_ar': 'جهاز كمبيوتر محمول ديل OptiPlex 7090',
                'category': 'Computer Equipment',
                'serial_number': '*********',
                'model': 'OptiPlex 7090',
                'manufacturer': 'Dell',
                'purchase_price': Decimal('3500.00'),
                'useful_life_years': 4,
                'depreciation_method': 'STRAIGHT_LINE'
            },
            {
                'name': 'HP Desktop ProDesk 600',
                'name_ar': 'جهاز كمبيوتر مكتبي HP ProDesk 600',
                'category': 'Computer Equipment',
                'serial_number': 'HP600001',
                'model': 'ProDesk 600',
                'manufacturer': 'HP',
                'purchase_price': Decimal('2800.00'),
                'useful_life_years': 5,
                'depreciation_method': 'STRAIGHT_LINE'
            },
            # Office Furniture
            {
                'name': 'Executive Office Desk',
                'name_ar': 'مكتب تنفيذي',
                'category': 'Office Furniture',
                'serial_number': 'DESK001',
                'model': 'Executive Pro',
                'manufacturer': 'Office Solutions',
                'purchase_price': Decimal('1200.00'),
                'useful_life_years': 10,
                'depreciation_method': 'STRAIGHT_LINE'
            },
            {
                'name': 'Ergonomic Office Chair',
                'name_ar': 'كرسي مكتب مريح',
                'category': 'Office Furniture',
                'serial_number': 'CHAIR001',
                'model': 'ErgoMax Pro',
                'manufacturer': 'Comfort Seating',
                'purchase_price': Decimal('800.00'),
                'useful_life_years': 7,
                'depreciation_method': 'STRAIGHT_LINE'
            },
            # Vehicles
            {
                'name': 'Toyota Camry 2023',
                'name_ar': 'تويوتا كامري 2023',
                'category': 'Vehicles',
                'serial_number': 'TC2023001',
                'model': 'Camry LE',
                'manufacturer': 'Toyota',
                'purchase_price': Decimal('95000.00'),
                'useful_life_years': 8,
                'depreciation_method': 'DECLINING_BALANCE',
                'depreciation_rate': Decimal('25.00')
            },
            # Office Equipment
            {
                'name': 'Canon Printer ImageRunner',
                'name_ar': 'طابعة كانون ImageRunner',
                'category': 'Office Equipment',
                'serial_number': 'CN001',
                'model': 'ImageRunner 2625i',
                'manufacturer': 'Canon',
                'purchase_price': Decimal('4500.00'),
                'useful_life_years': 6,
                'depreciation_method': 'STRAIGHT_LINE'
            }
        ]
        
        created_assets = []
        for i, asset_data in enumerate(assets_data):
            try:
                category = AssetCategory.objects.get(name=asset_data['category'])
                
                # Generate unique asset ID
                asset_id = f"AST{str(i+1).zfill(4)}"
                
                # Random assignment
                assigned_to = random.choice(employees) if employees and random.choice([True, False]) else None
                department = random.choice(departments) if departments else None
                
                # Purchase date (random within last 2 years)
                days_ago = random.randint(30, 730)
                purchase_date = timezone.now().date() - timedelta(days=days_ago)
                
                # Warranty (1-3 years from purchase)
                warranty_years = random.randint(1, 3)
                warranty_expiry = purchase_date + timedelta(days=warranty_years * 365)
                
                asset = Asset.objects.create(
                    asset_id=asset_id,
                    name=asset_data['name'],
                    name_ar=asset_data['name_ar'],
                    category=category,
                    serial_number=asset_data['serial_number'],
                    model=asset_data['model'],
                    manufacturer=asset_data['manufacturer'],
                    currency=base_currency,
                    purchase_date=purchase_date,
                    purchase_price=asset_data['purchase_price'],
                    salvage_value=asset_data['purchase_price'] * Decimal('0.1'),  # 10% salvage value
                    useful_life_years=asset_data['useful_life_years'],
                    depreciation_method=asset_data['depreciation_method'],
                    depreciation_rate=asset_data.get('depreciation_rate'),
                    warranty_expiry=warranty_expiry,
                    assigned_to=assigned_to,
                    department=department,
                    location=f"Building A, Floor {random.randint(1, 5)}",
                    status=random.choice(['AVAILABLE', 'IN_USE', 'IN_USE', 'IN_USE']),  # Bias toward IN_USE
                    condition=random.choice(['EXCELLENT', 'GOOD', 'GOOD', 'FAIR']),  # Bias toward GOOD
                    maintenance_schedule='Quarterly',
                    next_maintenance_date=timezone.now().date() + timedelta(days=random.randint(30, 120))
                )
                
                created_assets.append(asset)
                self.stdout.write(f'Created asset: {asset.asset_id} - {asset.name}')
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'Error creating asset {asset_data["name"]}: {e}'))
        
        self.stdout.write(f'Sample assets created: {len(created_assets)}')

    def create_sample_maintenance(self):
        """Create sample maintenance records"""
        assets = Asset.objects.all()[:3]  # Get first 3 assets
        
        if not assets:
            self.stdout.write(self.style.WARNING('No assets found. Please create assets first.'))
            return
        
        employees = list(Employee.objects.all())
        if not employees:
            self.stdout.write(self.style.WARNING('No employees found. Maintenance records will not have performers.'))
            return
        
        maintenance_types = ['PREVENTIVE', 'CORRECTIVE', 'INSPECTION', 'CLEANING']
        
        created_maintenance = []
        for asset in assets:
            # Create 2-3 maintenance records per asset
            for i in range(random.randint(2, 3)):
                days_ago = random.randint(10, 365)
                scheduled_date = timezone.now().date() - timedelta(days=days_ago)
                
                maintenance = AssetMaintenance.objects.create(
                    asset=asset,
                    maintenance_type=random.choice(maintenance_types),
                    title=f'{random.choice(maintenance_types).title()} Maintenance - {asset.name}',
                    description=f'Scheduled {random.choice(maintenance_types).lower()} maintenance for {asset.name}',
                    scheduled_date=scheduled_date,
                    status=random.choice(['COMPLETED', 'COMPLETED', 'SCHEDULED']),  # Bias toward completed
                    priority=random.choice(['LOW', 'MEDIUM', 'HIGH']),
                    estimated_cost=Decimal(str(random.randint(100, 1000))),
                    performed_by=random.choice(employees),
                    created_by=random.choice(employees)
                )
                
                # If completed, add completion details
                if maintenance.status == 'COMPLETED':
                    maintenance.actual_start_date = timezone.now() - timedelta(days=days_ago, hours=2)
                    maintenance.actual_end_date = timezone.now() - timedelta(days=days_ago, hours=1)
                    maintenance.actual_cost = maintenance.estimated_cost + Decimal(str(random.randint(-100, 200)))
                    maintenance.work_performed = f'Completed {maintenance.maintenance_type.lower()} maintenance tasks'
                    maintenance.save()
                
                created_maintenance.append(maintenance)
                self.stdout.write(f'Created maintenance: {maintenance.title}')
        
        self.stdout.write(f'Sample maintenance records created: {len(created_maintenance)}')
