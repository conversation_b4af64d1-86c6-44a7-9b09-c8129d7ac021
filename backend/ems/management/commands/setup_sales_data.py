"""
Django management command to create sample sales data
Creates customers, products, quotations, and sales orders for testing
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.contrib.auth.models import User
from datetime import datetime, timedelta, date
from decimal import Decimal
import random
from ems.models import (
    Customer, Product, ProductCategory, SalesOrder,
    Employee, Department
)


class Command(BaseCommand):
    help = 'Create sample sales data for testing the sales management system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--customers',
            type=int,
            default=15,
            help='Number of customers to create',
        )
        parser.add_argument(
            '--products',
            type=int,
            default=20,
            help='Number of products to create',
        )
        parser.add_argument(
            '--orders',
            type=int,
            default=25,
            help='Number of sales orders to create',
        )


    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up sales data...'))
        
        # Get or create a sales employee
        employee = self.get_or_create_sales_employee()
        
        # Create sample data
        categories_created = self.create_product_categories()
        self.stdout.write(f'Created {categories_created} product categories')
        
        products_created = self.create_sample_products(options['products'])
        self.stdout.write(f'Created {products_created} products')
        
        customers_created = self.create_sample_customers(options['customers'])
        self.stdout.write(f'Created {customers_created} customers')

        orders_created = self.create_sample_sales_orders(options['orders'], employee)
        self.stdout.write(f'Created {orders_created} sales orders')
        
        self.stdout.write(
            self.style.SUCCESS('Successfully created sample sales data!')
        )

    def get_or_create_sales_employee(self):
        """Get or create a sales employee for creating orders"""
        try:
            # Try to get an existing employee
            employee = Employee.objects.filter(
                user__is_staff=True
            ).first()
            
            if employee:
                return employee
            
            # Create a sales user if none exists
            user, created = User.objects.get_or_create(
                username='sales_manager',
                defaults={
                    'first_name': 'Sales',
                    'last_name': 'Manager',
                    'email': '<EMAIL>',
                    'is_staff': True
                }
            )
            
            if created:
                user.set_password('password123')
                user.save()
            
            # Get or create sales department
            sales_dept, _ = Department.objects.get_or_create(
                name='Sales',
                defaults={
                    'name_ar': 'المبيعات',
                    'description': 'Sales Department',
                    'description_ar': 'قسم المبيعات'
                }
            )
            
            # Create employee
            employee, created = Employee.objects.get_or_create(
                user=user,
                defaults={
                    'employee_id': 'EMP-SALES-001',
                    'department': sales_dept,
                    'position': 'Sales Manager',
                    'position_ar': 'مدير المبيعات',
                    'phone': '+966501234567',
                    'hire_date': date.today() - timedelta(days=365),
                    'salary': Decimal('8000.00')
                }
            )
            
            return employee
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating sales employee: {e}')
            )
            return None

    def create_product_categories(self):
        """Create sample product categories"""
        categories = [
            ('Electronics', 'الإلكترونيات', 'Electronic devices and components'),
            ('Software', 'البرمجيات', 'Software products and licenses'),
            ('Services', 'الخدمات', 'Professional services and consulting'),
            ('Hardware', 'الأجهزة', 'Computer hardware and peripherals'),
            ('Office Supplies', 'مستلزمات المكتب', 'Office equipment and supplies'),
        ]
        
        created_count = 0
        for name, name_ar, description in categories:
            category, created = ProductCategory.objects.get_or_create(
                name=name,
                defaults={
                    'name_ar': name_ar,
                    'description': description,
                    'description_ar': description,
                    'is_active': True
                }
            )
            if created:
                created_count += 1
        
        return created_count

    def create_sample_products(self, count):
        """Create sample products"""
        categories = list(ProductCategory.objects.all())
        if not categories:
            self.stdout.write(self.style.ERROR('No product categories found'))
            return 0
        
        products_data = [
            ('Laptop Pro 15"', 'لابتوب برو 15 بوصة', 'High-performance laptop', Decimal('4500.00')),
            ('Desktop Workstation', 'محطة عمل مكتبية', 'Professional desktop computer', Decimal('3200.00')),
            ('Wireless Mouse', 'فأرة لاسلكية', 'Ergonomic wireless mouse', Decimal('85.00')),
            ('Mechanical Keyboard', 'لوحة مفاتيح ميكانيكية', 'RGB mechanical keyboard', Decimal('250.00')),
            ('4K Monitor 27"', 'شاشة 4K 27 بوصة', 'Ultra HD 4K monitor', Decimal('1200.00')),
            ('Office Chair Pro', 'كرسي مكتب احترافي', 'Ergonomic office chair', Decimal('650.00')),
            ('Printer Multifunction', 'طابعة متعددة الوظائف', 'All-in-one printer', Decimal('450.00')),
            ('External Hard Drive 2TB', 'قرص صلب خارجي 2 تيرا', '2TB external storage', Decimal('180.00')),
            ('Webcam HD', 'كاميرا ويب عالية الدقة', 'Full HD webcam', Decimal('120.00')),
            ('Headset Wireless', 'سماعة رأس لاسلكية', 'Noise-canceling headset', Decimal('200.00')),
        ]
        
        created_count = 0
        for i, (name, name_ar, description, price) in enumerate(products_data):
            if i >= count:
                break
                
            category = random.choice(categories)
            
            product, created = Product.objects.get_or_create(
                name=name,
                defaults={
                    'name_ar': name_ar,
                    'description': description,
                    'description_ar': description,
                    'category': category,
                    'unit_price': price,
                    'cost_price': price * Decimal('0.7'),  # 30% markup
                    'sku': f'SKU-{i+1:04d}',
                    'quantity_in_stock': random.randint(10, 100),
                    'minimum_stock_level': 5,
                    'status': 'active'
                }
            )
            if created:
                created_count += 1
        
        return created_count

    def create_sample_customers(self, count):
        """Create sample customers"""
        customers_data = [
            ('Ahmed', 'Al-Rashid', '<EMAIL>', '+966501111111', 'individual'),
            ('Fatima', 'Al-Zahra', '<EMAIL>', '+966502222222', 'individual'),
            ('Tech Solutions', 'Co.', '<EMAIL>', '+966503333333', 'business'),
            ('Modern Office', 'Ltd.', '<EMAIL>', '+966504444444', 'business'),
            ('Omar', 'Al-Mansouri', '<EMAIL>', '+966505555555', 'individual'),
            ('Digital', 'Innovations', '<EMAIL>', '+966506666666', 'enterprise'),
            ('Sara', 'Al-Qasimi', '<EMAIL>', '+966507777777', 'individual'),
            ('Global Systems', 'Corp', '<EMAIL>', '+966508888888', 'enterprise'),
            ('Khalid', 'Al-Otaibi', '<EMAIL>', '+966509999999', 'individual'),
            ('Smart Business', 'Hub', '<EMAIL>', '+966501010101', 'business'),
        ]

        created_count = 0
        for i, (first_name, last_name, email, phone, customer_type) in enumerate(customers_data):
            if i >= count:
                break

            # For business customers, use company name in first_name field
            if customer_type in ['business', 'enterprise']:
                company_name = f'{first_name} {last_name}'
                first_name = company_name
                last_name = ''

            customer, created = Customer.objects.get_or_create(
                email=email,
                defaults={
                    'first_name': first_name,
                    'last_name': last_name,
                    'phone': phone,
                    'customer_type': customer_type,
                    'status': 'active',
                    'company_name': f'{first_name} {last_name}' if customer_type in ['business', 'enterprise'] else '',
                    'address_line1': f'الرياض، المملكة العربية السعودية - {i+1}',
                    'city': 'الرياض',
                    'country': 'المملكة العربية السعودية'
                }
            )
            if created:
                created_count += 1

        return created_count



    def create_sample_sales_orders(self, count, employee):
        """Create sample sales orders"""
        customers = list(Customer.objects.all())

        if not customers:
            self.stdout.write(self.style.ERROR('No customers found'))
            return 0

        created_count = 0
        for i in range(count):
            customer = random.choice(customers)

            # Random date within last 90 days
            days_ago = random.randint(1, 90)
            order_date = timezone.now().date() - timedelta(days=days_ago)
            delivery_date = order_date + timedelta(days=random.randint(7, 30))

            # Calculate amounts
            total_amount = Decimal(str(random.uniform(1000, 50000))).quantize(Decimal('0.01'))
            discount = (total_amount * Decimal(str(random.uniform(0, 0.1)))).quantize(Decimal('0.01'))
            tax = ((total_amount - discount) * Decimal('0.15')).quantize(Decimal('0.01'))

            # Random status and priority
            status = random.choice(['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'])
            priority = random.choice(['low', 'medium', 'high', 'urgent'])

            # Set delivery date to None for pending/cancelled orders
            if status in ['pending', 'cancelled']:
                delivery_date = None

            sales_order = SalesOrder.objects.create(
                order_number=f'SO-{timezone.now().year}-{i+1:04d}',
                customer=customer,
                total_amount=total_amount,
                status=status,
                priority=priority,
                order_date=order_date,
                delivery_date=delivery_date,
                items_count=random.randint(1, 10),
                discount=discount,
                tax=tax,
                notes=f'Sample sales order for {customer.first_name} {customer.last_name}',
                created_by=employee
            )

            created_count += 1

        return created_count
