from django.core.management.base import BaseCommand
from django.utils import timezone
from ems.models import Currency, ExchangeRate, Employee
from decimal import Decimal


class Command(BaseCommand):
    help = 'Set up initial currencies and exchange rates for multi-currency support'

    def add_arguments(self, parser):
        parser.add_argument(
            '--base-currency',
            type=str,
            default='SAR',
            help='Base currency code (default: SAR)'
        )

    def handle(self, *args, **options):
        base_currency_code = options['base_currency']
        
        self.stdout.write(self.style.SUCCESS('Setting up currencies and exchange rates...'))
        
        # Create common currencies
        currencies_data = [
            {
                'code': 'SAR',
                'name': 'Saudi Riyal',
                'symbol': '﷼',
                'decimal_places': 2,
                'symbol_position': 'after',
                'is_base_currency': base_currency_code == 'SAR'
            },
            {
                'code': 'USD',
                'name': 'US Dollar',
                'symbol': '$',
                'decimal_places': 2,
                'symbol_position': 'before',
                'is_base_currency': base_currency_code == 'USD'
            },
            {
                'code': 'EUR',
                'name': 'Euro',
                'symbol': '€',
                'decimal_places': 2,
                'symbol_position': 'before',
                'is_base_currency': base_currency_code == 'EUR'
            },
            {
                'code': 'GBP',
                'name': 'British Pound',
                'symbol': '£',
                'decimal_places': 2,
                'symbol_position': 'before',
                'is_base_currency': base_currency_code == 'GBP'
            },
            {
                'code': 'AED',
                'name': 'UAE Dirham',
                'symbol': 'د.إ',
                'decimal_places': 2,
                'symbol_position': 'after',
                'is_base_currency': base_currency_code == 'AED'
            },
            {
                'code': 'KWD',
                'name': 'Kuwaiti Dinar',
                'symbol': 'د.ك',
                'decimal_places': 3,
                'symbol_position': 'after',
                'is_base_currency': base_currency_code == 'KWD'
            },
            {
                'code': 'QAR',
                'name': 'Qatari Riyal',
                'symbol': 'ر.ق',
                'decimal_places': 2,
                'symbol_position': 'after',
                'is_base_currency': base_currency_code == 'QAR'
            },
            {
                'code': 'BHD',
                'name': 'Bahraini Dinar',
                'symbol': 'د.ب',
                'decimal_places': 3,
                'symbol_position': 'after',
                'is_base_currency': base_currency_code == 'BHD'
            },
            {
                'code': 'OMR',
                'name': 'Omani Rial',
                'symbol': 'ر.ع.',
                'decimal_places': 3,
                'symbol_position': 'after',
                'is_base_currency': base_currency_code == 'OMR'
            },
            {
                'code': 'JPY',
                'name': 'Japanese Yen',
                'symbol': '¥',
                'decimal_places': 0,
                'symbol_position': 'before',
                'is_base_currency': base_currency_code == 'JPY'
            }
        ]
        
        created_currencies = []
        for currency_data in currencies_data:
            currency, created = Currency.objects.get_or_create(
                code=currency_data['code'],
                defaults=currency_data
            )
            if created:
                created_currencies.append(currency.code)
                self.stdout.write(f'Created currency: {currency.code} - {currency.name}')
            else:
                # Update existing currency if it's set as base
                if currency_data['is_base_currency']:
                    Currency.objects.filter(is_base_currency=True).update(is_base_currency=False)
                    currency.is_base_currency = True
                    currency.save()
                    self.stdout.write(f'Updated base currency: {currency.code}')
                else:
                    self.stdout.write(f'Currency already exists: {currency.code}')
        
        # Set up sample exchange rates (as of typical rates - should be updated with real data)
        if base_currency_code == 'SAR':
            exchange_rates = [
                ('USD', 'SAR', Decimal('3.75')),  # 1 USD = 3.75 SAR
                ('EUR', 'SAR', Decimal('4.10')),  # 1 EUR = 4.10 SAR
                ('GBP', 'SAR', Decimal('4.75')),  # 1 GBP = 4.75 SAR
                ('AED', 'SAR', Decimal('1.02')),  # 1 AED = 1.02 SAR
                ('KWD', 'SAR', Decimal('12.25')), # 1 KWD = 12.25 SAR
                ('QAR', 'SAR', Decimal('1.03')),  # 1 QAR = 1.03 SAR
                ('BHD', 'SAR', Decimal('9.95')),  # 1 BHD = 9.95 SAR
                ('OMR', 'SAR', Decimal('9.75')),  # 1 OMR = 9.75 SAR
                ('JPY', 'SAR', Decimal('0.025')), # 1 JPY = 0.025 SAR
            ]
        else:
            # If base currency is not SAR, set up basic USD rates
            exchange_rates = [
                ('SAR', 'USD', Decimal('0.2667')),  # 1 SAR = 0.2667 USD
                ('EUR', 'USD', Decimal('1.09')),    # 1 EUR = 1.09 USD
                ('GBP', 'USD', Decimal('1.27')),    # 1 GBP = 1.27 USD
            ]
        
        # Get admin user for created_by field
        try:
            admin_employee = Employee.objects.filter(user__is_superuser=True).first()
        except Employee.DoesNotExist:
            admin_employee = None
        
        today = timezone.now().date()
        created_rates = []
        
        for from_code, to_code, rate in exchange_rates:
            try:
                from_currency = Currency.objects.get(code=from_code)
                to_currency = Currency.objects.get(code=to_code)
                
                exchange_rate, created = ExchangeRate.objects.get_or_create(
                    from_currency=from_currency,
                    to_currency=to_currency,
                    effective_date=today,
                    defaults={
                        'rate': rate,
                        'source': 'manual',
                        'is_active': True,
                        'created_by': admin_employee
                    }
                )
                
                if created:
                    created_rates.append(f'{from_code} → {to_code}: {rate}')
                    self.stdout.write(f'Created exchange rate: {from_code} → {to_code} = {rate}')
                else:
                    self.stdout.write(f'Exchange rate already exists: {from_code} → {to_code}')
                    
            except Currency.DoesNotExist as e:
                self.stdout.write(
                    self.style.WARNING(f'Currency not found for rate {from_code} → {to_code}: {e}')
                )
        
        # Summary
        self.stdout.write(self.style.SUCCESS('\n=== MULTI-CURRENCY SETUP COMPLETE ==='))
        self.stdout.write(f'Base currency: {base_currency_code}')
        self.stdout.write(f'Created currencies: {len(created_currencies)}')
        self.stdout.write(f'Created exchange rates: {len(created_rates)}')
        
        if created_currencies:
            self.stdout.write(f'New currencies: {", ".join(created_currencies)}')
        
        if created_rates:
            self.stdout.write('New exchange rates:')
            for rate in created_rates:
                self.stdout.write(f'  • {rate}')
        
        self.stdout.write(self.style.SUCCESS('\nMulti-currency support is now ready!'))
        self.stdout.write('You can now:')
        self.stdout.write('• Create invoices in different currencies')
        self.stdout.write('• Manage exchange rates via API')
        self.stdout.write('• View financial reports in base currency')
        self.stdout.write('• Update exchange rates regularly for accuracy')
