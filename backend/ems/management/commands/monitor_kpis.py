"""
Management command to monitor KPIs and trigger alerts.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from ems.kpi_monitoring import check_kpi_alerts, get_kpi_health
import json


class Command(BaseCommand):
    help = 'Monitor KPIs and trigger alerts for threshold breaches'

    def add_arguments(self, parser):
        parser.add_argument(
            '--health-only',
            action='store_true',
            help='Only show KPI health summary without checking alerts',
        )
        parser.add_argument(
            '--json',
            action='store_true',
            help='Output results in JSON format',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed output',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS(f'Starting KPI monitoring at {timezone.now()}')
        )

        try:
            if options['health_only']:
                # Only get health summary
                health_summary = get_kpi_health()
                
                if options['json']:
                    self.stdout.write(json.dumps(health_summary, indent=2, default=str))
                else:
                    self.display_health_summary(health_summary, options['verbose'])
            else:
                # Full monitoring with alerts
                results = check_kpi_alerts()
                
                if options['json']:
                    self.stdout.write(json.dumps(results, indent=2, default=str))
                else:
                    self.display_monitoring_results(results, options['verbose'])
                    
                    # Also show health summary
                    health_summary = get_kpi_health()
                    self.display_health_summary(health_summary, options['verbose'])

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error during KPI monitoring: {str(e)}')
            )
            raise

        self.stdout.write(
            self.style.SUCCESS(f'KPI monitoring completed at {timezone.now()}')
        )

    def display_monitoring_results(self, results, verbose=False):
        """Display monitoring results in a formatted way"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(self.style.HTTP_INFO('KPI MONITORING RESULTS'))
        self.stdout.write('='*60)
        
        self.stdout.write(f"📊 Total KPIs Checked: {results['checked_kpis']}")
        self.stdout.write(f"🚨 Alerts Triggered: {results['alerts_triggered']}")
        self.stdout.write(f"🔴 Critical Alerts: {results['critical_alerts']}")
        self.stdout.write(f"🟡 Warning Alerts: {results['warning_alerts']}")
        
        if results['alerts']:
            self.stdout.write('\n' + self.style.WARNING('ACTIVE ALERTS:'))
            self.stdout.write('-'*40)
            
            for alert in results['alerts']:
                severity_style = self.style.ERROR if alert['severity'] == 'CRITICAL' else self.style.WARNING
                
                self.stdout.write(f"\n{severity_style('●')} {alert['kpi_name']}")
                self.stdout.write(f"  Severity: {severity_style(alert['severity'])}")
                self.stdout.write(f"  Current: {alert['current_value']}")
                self.stdout.write(f"  Threshold: {alert['threshold_value']} ({alert['threshold_type']})")
                
                if verbose:
                    self.stdout.write(f"  Message: {alert['message']}")
                    self.stdout.write(f"  Trend: {alert['trend_direction']}")
        else:
            self.stdout.write(f"\n{self.style.SUCCESS('✅ No alerts triggered - all KPIs are within thresholds!')}")

    def display_health_summary(self, health_summary, verbose=False):
        """Display KPI health summary"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(self.style.HTTP_INFO('KPI HEALTH SUMMARY'))
        self.stdout.write('='*60)
        
        total = health_summary['total_kpis']
        healthy = health_summary['healthy_kpis']
        warning = health_summary['warning_kpis']
        critical = health_summary['critical_kpis']
        no_data = health_summary['no_data_kpis']
        health_pct = health_summary['health_percentage']
        
        self.stdout.write(f"📈 Total KPIs: {total}")
        self.stdout.write(f"✅ Healthy: {healthy}")
        self.stdout.write(f"🟡 Warning: {warning}")
        self.stdout.write(f"🔴 Critical: {critical}")
        self.stdout.write(f"❓ No Data: {no_data}")
        self.stdout.write(f"💚 Health Score: {health_pct:.1f}%")
        
        # Health status indicator
        if health_pct >= 90:
            status = self.style.SUCCESS('EXCELLENT')
        elif health_pct >= 75:
            status = self.style.HTTP_INFO('GOOD')
        elif health_pct >= 50:
            status = self.style.WARNING('FAIR')
        else:
            status = self.style.ERROR('POOR')
            
        self.stdout.write(f"🏥 Overall Health: {status}")
        
        if verbose:
            self.stdout.write(f"\nLast Updated: {health_summary['last_updated']}")
