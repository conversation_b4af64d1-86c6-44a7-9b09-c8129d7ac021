"""
Enhanced KPI Calculation Management Command
Scheduled command for automatic KPI calculation and monitoring.

This command should be run periodically (e.g., via cron) to ensure
all KPIs are up-to-date with the latest operational data.

Usage:
    python manage.py calculate_enhanced_kpis
    python manage.py calculate_enhanced_kpis --period monthly
    python manage.py calculate_enhanced_kpis --start-date 2024-01-01 --end-date 2024-01-31
    python manage.py calculate_enhanced_kpis --force-recalculate
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from datetime import datetime, timedelta
from ems.enhanced_kpi_engine import enhanced_kpi_engine
from ems.kpi_monitoring import kpi_monitor
from ems.models import KPI


class Command(BaseCommand):
    help = 'Calculate enhanced KPIs automatically from operational data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--period',
            type=str,
            choices=['daily', 'weekly', 'monthly', 'quarterly'],
            default='monthly',
            help='Calculation period (default: monthly)',
        )
        parser.add_argument(
            '--start-date',
            type=str,
            help='Start date for calculation (YYYY-MM-DD format)',
        )
        parser.add_argument(
            '--end-date',
            type=str,
            help='End date for calculation (YYYY-MM-DD format)',
        )
        parser.add_argument(
            '--force-recalculate',
            action='store_true',
            help='Force recalculation even if values exist',
        )
        parser.add_argument(
            '--send-report',
            action='store_true',
            help='Send calculation report via email',
        )
        parser.add_argument(
            '--check-alerts',
            action='store_true',
            help='Check for KPI threshold alerts after calculation',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting Enhanced KPI Calculation...'))
        
        # Determine calculation period
        start_date, end_date = self.get_calculation_period(options)
        
        self.stdout.write(f'Calculation period: {start_date.date()} to {end_date.date()}')
        
        # Run KPI calculations
        calculation_results = self.run_kpi_calculations(start_date, end_date, options)
        
        # Check for alerts if requested
        alert_results = None
        if options['check_alerts']:
            alert_results = self.check_kpi_alerts()
        
        # Generate and display report
        self.display_calculation_report(calculation_results, alert_results)
        
        # Send email report if requested
        if options['send_report']:
            self.send_email_report(calculation_results, alert_results, start_date, end_date)
        
        self.stdout.write(self.style.SUCCESS('Enhanced KPI calculation completed successfully!'))

    def get_calculation_period(self, options):
        """Determine the calculation period based on options"""
        if options['start_date'] and options['end_date']:
            # Use provided dates
            start_date = datetime.strptime(options['start_date'], '%Y-%m-%d')
            end_date = datetime.strptime(options['end_date'], '%Y-%m-%d')
            start_date = timezone.make_aware(start_date)
            end_date = timezone.make_aware(end_date.replace(hour=23, minute=59, second=59))
        else:
            # Use period-based calculation
            now = timezone.now()
            
            if options['period'] == 'daily':
                start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
                end_date = now
            elif options['period'] == 'weekly':
                # Start of current week (Monday)
                days_since_monday = now.weekday()
                start_date = (now - timedelta(days=days_since_monday)).replace(hour=0, minute=0, second=0, microsecond=0)
                end_date = now
            elif options['period'] == 'monthly':
                # Start of current month
                start_date = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                end_date = now
            elif options['period'] == 'quarterly':
                # Start of current quarter
                quarter_start_month = ((now.month - 1) // 3) * 3 + 1
                start_date = now.replace(month=quarter_start_month, day=1, hour=0, minute=0, second=0, microsecond=0)
                end_date = now
        
        return start_date, end_date

    def run_kpi_calculations(self, start_date, end_date, options):
        """Run the enhanced KPI calculations"""
        self.stdout.write('Running enhanced KPI calculations...')
        
        try:
            # Use enhanced calculation engine
            results = enhanced_kpi_engine.calculate_all_kpis_enhanced(start_date, end_date)
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Calculation completed: {results["calculated"]} KPIs calculated, '
                    f'{results["failed"]} failed in {results["execution_time"]:.2f}s'
                )
            )
            
            # Log any failures
            if results['failed_kpis']:
                self.stdout.write(self.style.WARNING('Failed KPIs:'))
                for failed_kpi in results['failed_kpis']:
                    self.stdout.write(f'  - {failed_kpi["name"]}: {failed_kpi["error"]}')
            
            return results
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Calculation failed: {str(e)}'))
            raise

    def check_kpi_alerts(self):
        """Check for KPI threshold alerts"""
        self.stdout.write('Checking KPI alerts...')
        
        try:
            alert_results = kpi_monitor.check_all_kpis()
            
            if alert_results['alerts_triggered'] > 0:
                self.stdout.write(
                    self.style.WARNING(
                        f'Found {alert_results["alerts_triggered"]} KPI alerts '
                        f'({alert_results["critical_alerts"]} critical, {alert_results["warning_alerts"]} warnings)'
                    )
                )
                
                for alert in alert_results['alerts']:
                    severity_style = self.style.ERROR if alert['severity'] == 'CRITICAL' else self.style.WARNING
                    self.stdout.write(severity_style(f'  - {alert["message"]}'))
            else:
                self.stdout.write(self.style.SUCCESS('No KPI alerts found - all KPIs within thresholds'))
            
            return alert_results
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Alert checking failed: {str(e)}'))
            return None

    def display_calculation_report(self, calculation_results, alert_results):
        """Display a comprehensive calculation report"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(self.style.SUCCESS('ENHANCED KPI CALCULATION REPORT'))
        self.stdout.write('='*60)
        
        # Calculation summary
        self.stdout.write(f'Total KPIs processed: {calculation_results["calculated"] + calculation_results["failed"]}')
        self.stdout.write(f'Successfully calculated: {calculation_results["calculated"]}')
        self.stdout.write(f'Failed calculations: {calculation_results["failed"]}')
        self.stdout.write(f'Execution time: {calculation_results["execution_time"]:.2f} seconds')
        
        # Data quality summary
        if calculation_results['updated_kpis']:
            avg_quality = sum(kpi['data_quality_score'] for kpi in calculation_results['updated_kpis']) / len(calculation_results['updated_kpis'])
            self.stdout.write(f'Average data quality score: {avg_quality:.1f}%')
        
        # Alert summary
        if alert_results:
            self.stdout.write(f'\nAlert Summary:')
            self.stdout.write(f'Total alerts: {alert_results["alerts_triggered"]}')
            self.stdout.write(f'Critical alerts: {alert_results["critical_alerts"]}')
            self.stdout.write(f'Warning alerts: {alert_results["warning_alerts"]}')
        
        # Top performing KPIs
        if calculation_results['updated_kpis']:
            self.stdout.write(f'\nTop 5 Updated KPIs:')
            for kpi in calculation_results['updated_kpis'][:5]:
                self.stdout.write(f'  - {kpi["name"]}: {kpi["value"]:.2f} (Quality: {kpi["data_quality_score"]:.1f}%)')
        
        self.stdout.write('='*60)

    def send_email_report(self, calculation_results, alert_results, start_date, end_date):
        """Send calculation report via email"""
        self.stdout.write('Sending email report...')
        
        try:
            # Get admin email addresses
            from ems.models import Employee
            admin_emails = list(Employee.objects.filter(
                user__is_staff=True,
                is_active=True,
                user__email__isnull=False
            ).exclude(user__email='').values_list('user__email', flat=True))
            
            if not admin_emails:
                self.stdout.write(self.style.WARNING('No admin email addresses found'))
                return
            
            # Prepare email content
            subject = f'Enhanced KPI Calculation Report - {start_date.date()} to {end_date.date()}'
            
            message = f"""
Enhanced KPI Calculation Report
===============================

Calculation Period: {start_date.date()} to {end_date.date()}
Generated: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}

SUMMARY:
--------
Total KPIs processed: {calculation_results["calculated"] + calculation_results["failed"]}
Successfully calculated: {calculation_results["calculated"]}
Failed calculations: {calculation_results["failed"]}
Execution time: {calculation_results["execution_time"]:.2f} seconds

"""
            
            # Add alert information
            if alert_results:
                message += f"""
ALERTS:
-------
Total alerts: {alert_results["alerts_triggered"]}
Critical alerts: {alert_results["critical_alerts"]}
Warning alerts: {alert_results["warning_alerts"]}

"""
                
                if alert_results['alerts']:
                    message += "Alert Details:\n"
                    for alert in alert_results['alerts'][:10]:  # Limit to first 10 alerts
                        message += f"- {alert['severity']}: {alert['message']}\n"
            
            # Add successful KPIs
            if calculation_results['updated_kpis']:
                message += "\nUPDATED KPIs:\n"
                message += "-------------\n"
                for kpi in calculation_results['updated_kpis'][:10]:  # Limit to first 10
                    message += f"- {kpi['name']}: {kpi['value']:.2f} (Quality: {kpi['data_quality_score']:.1f}%)\n"
            
            # Add failed KPIs
            if calculation_results['failed_kpis']:
                message += "\nFAILED KPIs:\n"
                message += "------------\n"
                for failed_kpi in calculation_results['failed_kpis']:
                    message += f"- {failed_kpi['name']}: {failed_kpi['error']}\n"
            
            message += f"""

This report was generated automatically by the Enhanced KPI Calculation Engine.
For more details, please check the EMS admin dashboard.
"""
            
            # Send email
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=admin_emails,
                fail_silently=False
            )
            
            self.stdout.write(self.style.SUCCESS(f'Email report sent to {len(admin_emails)} recipients'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Failed to send email report: {str(e)}'))
