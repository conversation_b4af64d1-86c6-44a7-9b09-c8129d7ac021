from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from ems.models import Employee, UserProfile, Role

class Command(BaseCommand):
    help = 'List all users and their roles for debugging'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('=== USER ROLES DEBUG ==='))
        
        # List all roles
        self.stdout.write('\n--- Available Roles ---')
        roles = Role.objects.all()
        for role in roles:
            self.stdout.write(f'Role ID: {role.id}, Name: {role.name}, Name AR: {role.name_ar}')
        
        # List all users and their roles
        self.stdout.write('\n--- Users and Their Roles ---')
        users = User.objects.all()
        
        for user in users:
            self.stdout.write(f'\nUser: {user.username} ({user.first_name} {user.last_name})')
            self.stdout.write(f'  Email: {user.email}')
            self.stdout.write(f'  Is Staff: {user.is_staff}')
            self.stdout.write(f'  Is Superuser: {user.is_superuser}')
            
            # Check Employee record
            try:
                employee = Employee.objects.get(user=user)
                self.stdout.write(f'  Employee ID: {employee.id}')
                self.stdout.write(f'  Department: {employee.department.name if employee.department else "None"}')
            except Employee.DoesNotExist:
                self.stdout.write(f'  Employee: No Employee record found')
            
            # Check UserProfile and Role
            try:
                user_profile = UserProfile.objects.get(user=user)
                if user_profile.role:
                    self.stdout.write(f'  Role: {user_profile.role.name} (ID: {user_profile.role.id})')
                    self.stdout.write(f'  Role AR: {user_profile.role.name_ar}')
                    self.stdout.write(f'  Permissions: {user_profile.role.permissions}')
                else:
                    self.stdout.write(f'  Role: No role assigned in UserProfile')
            except UserProfile.DoesNotExist:
                self.stdout.write(f'  UserProfile: No UserProfile found')
        
        # Check for users without profiles
        self.stdout.write('\n--- Users Without UserProfile ---')
        users_without_profile = User.objects.filter(userprofile__isnull=True)
        for user in users_without_profile:
            self.stdout.write(f'User: {user.username} - No UserProfile')
        
        # Check for users without Employee records
        self.stdout.write('\n--- Users Without Employee Records ---')
        users_without_employee = User.objects.filter(employee__isnull=True)
        for user in users_without_employee:
            self.stdout.write(f'User: {user.username} - No Employee record')
        
        self.stdout.write(self.style.SUCCESS('\n=== END DEBUG ==='))
