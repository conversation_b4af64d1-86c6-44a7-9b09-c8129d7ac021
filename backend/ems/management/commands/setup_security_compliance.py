from django.core.management.base import BaseCommand
from django.utils import timezone
from django.contrib.auth.models import User
from ems.models import (
    Employee, Department, Role, UserProfile, UserSecurityProfile,
    ComplianceFramework, ComplianceControl, DataClassification,
    SecurityIncident, SecurityAlert, AuditTrail
)
from datetime import timedelta, date
import uuid


class Command(BaseCommand):
    help = 'Set up sample security and compliance data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-roles',
            action='store_true',
            help='Create sample security roles'
        )
        parser.add_argument(
            '--create-frameworks',
            action='store_true',
            help='Create sample compliance frameworks'
        )
        parser.add_argument(
            '--create-classifications',
            action='store_true',
            help='Create sample data classifications'
        )
        parser.add_argument(
            '--create-incidents',
            action='store_true',
            help='Create sample security incidents'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up security and compliance data...'))
        
        if options['create_roles'] or not any([
            options['create_roles'], options['create_frameworks'], 
            options['create_classifications'], options['create_incidents']
        ]):
            self.create_security_roles()
        
        if options['create_frameworks'] or not any([
            options['create_roles'], options['create_frameworks'], 
            options['create_classifications'], options['create_incidents']
        ]):
            self.create_compliance_frameworks()
        
        if options['create_classifications'] or not any([
            options['create_roles'], options['create_frameworks'], 
            options['create_classifications'], options['create_incidents']
        ]):
            self.create_data_classifications()
        
        if options['create_incidents'] or not any([
            options['create_roles'], options['create_frameworks'], 
            options['create_classifications'], options['create_incidents']
        ]):
            self.create_security_incidents()
        
        self.stdout.write(self.style.SUCCESS('\n=== SECURITY & COMPLIANCE SETUP COMPLETE ==='))

    def create_security_roles(self):
        """Create security profiles for existing roles"""
        self.stdout.write('Using existing role system - creating security profiles for employees...')

        # Create security profiles for employees who don't have them
        employees_without_profiles = Employee.objects.filter(security_profile__isnull=True)
        created_profiles = 0

        for employee in employees_without_profiles:
            # Determine security level based on role from UserProfile
            security_level = 'MEDIUM'  # Default

            try:
                user_profile = UserProfile.objects.get(user=employee.user)
                if user_profile.role:
                    role_name = user_profile.role.name.lower()
                    if any(admin_role in role_name for admin_role in ['admin', 'manager', 'supervisor']):
                        security_level = 'HIGH'
                    elif 'employee' in role_name:
                        security_level = 'LOW'
            except UserProfile.DoesNotExist:
                # Create UserProfile if it doesn't exist
                UserProfile.objects.create(
                    user=employee.user,
                    role=None,  # Will be set later
                    preferred_language='ar'
                )

            UserSecurityProfile.objects.create(
                employee=employee,
                security_level=security_level,
                mfa_enabled=False,
                mfa_method='NONE',
                max_concurrent_sessions=3 if security_level == 'HIGH' else 2,
                login_notifications=True,
                suspicious_activity_alerts=True
            )
            created_profiles += 1

        self.stdout.write(f'Created {created_profiles} security profiles for existing employees')

        # Show existing roles
        existing_roles = Role.objects.all()
        self.stdout.write(f'Existing roles in system: {[role.name for role in existing_roles]}')

    def create_compliance_frameworks(self):
        """Create sample compliance frameworks"""
        try:
            admin_employee = Employee.objects.filter(user__is_superuser=True).first()
        except Employee.DoesNotExist:
            admin_employee = None
        
        frameworks_data = [
            {
                'name': 'GDPR Compliance',
                'name_ar': 'امتثال اللائحة العامة لحماية البيانات',
                'framework_type': 'GDPR',
                'description': 'General Data Protection Regulation compliance framework',
                'description_ar': 'إطار امتثال اللائحة العامة لحماية البيانات',
                'version': '2018',
                'effective_date': date(2018, 5, 25),
                'requirements': [
                    'Data Protection Impact Assessments',
                    'Privacy by Design',
                    'Data Subject Rights',
                    'Breach Notification',
                    'Data Protection Officer'
                ],
                'implementation_status': 'IMPLEMENTED',
                'compliance_score': 85.5,
                'assessment_frequency_months': 12
            },
            {
                'name': 'Saudi Data Protection Act',
                'name_ar': 'قانون حماية البيانات السعودي',
                'framework_type': 'SAUDI_DPA',
                'description': 'Saudi Arabia Data Protection Act compliance',
                'description_ar': 'امتثال قانون حماية البيانات في المملكة العربية السعودية',
                'version': '2023',
                'effective_date': date(2023, 9, 14),
                'requirements': [
                    'Data Localization',
                    'Consent Management',
                    'Data Transfer Restrictions',
                    'Incident Reporting',
                    'Data Protection Officer'
                ],
                'implementation_status': 'IN_PROGRESS',
                'compliance_score': 72.0,
                'assessment_frequency_months': 6
            },
            {
                'name': 'ISO 27001:2022',
                'name_ar': 'آيزو 27001:2022',
                'framework_type': 'ISO_27001',
                'description': 'Information Security Management System',
                'description_ar': 'نظام إدارة أمن المعلومات',
                'version': '2022',
                'effective_date': date(2022, 10, 25),
                'requirements': [
                    'Information Security Policy',
                    'Risk Management',
                    'Asset Management',
                    'Access Control',
                    'Incident Management'
                ],
                'implementation_status': 'IMPLEMENTED',
                'compliance_score': 91.2,
                'assessment_frequency_months': 12
            },
            {
                'name': 'SOX Compliance',
                'name_ar': 'امتثال ساربانيس أوكسلي',
                'framework_type': 'SOX',
                'description': 'Sarbanes-Oxley Act financial compliance',
                'description_ar': 'امتثال قانون ساربانيس أوكسلي المالي',
                'version': '2002',
                'effective_date': date(2002, 7, 30),
                'requirements': [
                    'Internal Controls',
                    'Financial Reporting',
                    'Audit Requirements',
                    'Management Certification',
                    'Whistleblower Protection'
                ],
                'implementation_status': 'PARTIALLY_IMPLEMENTED',
                'compliance_score': 68.5,
                'assessment_frequency_months': 6
            }
        ]
        
        created_frameworks = []
        for framework_data in frameworks_data:
            framework, created = ComplianceFramework.objects.get_or_create(
                name=framework_data['name'],
                defaults={**framework_data, 'created_by': admin_employee}
            )
            
            if created:
                created_frameworks.append(framework)
                self.stdout.write(f'Created compliance framework: {framework.name}')
                
                # Create sample controls for each framework
                self.create_sample_controls(framework)
            else:
                self.stdout.write(f'Compliance framework already exists: {framework.name}')
        
        self.stdout.write(f'Compliance frameworks setup complete. Created: {len(created_frameworks)}')

    def create_sample_controls(self, framework):
        """Create sample controls for a framework"""
        if framework.framework_type == 'GDPR':
            controls_data = [
                {
                    'control_id': 'GDPR-001',
                    'name': 'Data Protection Impact Assessment',
                    'name_ar': 'تقييم أثر حماية البيانات',
                    'description': 'Conduct DPIA for high-risk processing activities',
                    'control_type': 'PREVENTIVE',
                    'status': 'IMPLEMENTED',
                    'risk_level': 'HIGH',
                    'priority': 1
                },
                {
                    'control_id': 'GDPR-002',
                    'name': 'Privacy by Design',
                    'name_ar': 'الخصوصية بالتصميم',
                    'description': 'Implement privacy by design principles',
                    'control_type': 'PREVENTIVE',
                    'status': 'IMPLEMENTED',
                    'risk_level': 'MEDIUM',
                    'priority': 2
                },
                {
                    'control_id': 'GDPR-003',
                    'name': 'Breach Notification',
                    'name_ar': 'إشعار الخرق',
                    'description': 'Notify authorities within 72 hours of breach',
                    'control_type': 'DETECTIVE',
                    'status': 'PARTIALLY_IMPLEMENTED',
                    'risk_level': 'CRITICAL',
                    'priority': 1
                }
            ]
        elif framework.framework_type == 'ISO_27001':
            controls_data = [
                {
                    'control_id': 'ISO-A.5.1',
                    'name': 'Information Security Policy',
                    'name_ar': 'سياسة أمن المعلومات',
                    'description': 'Establish and maintain information security policy',
                    'control_type': 'PREVENTIVE',
                    'status': 'IMPLEMENTED',
                    'risk_level': 'HIGH',
                    'priority': 1
                },
                {
                    'control_id': 'ISO-A.8.1',
                    'name': 'Asset Management',
                    'name_ar': 'إدارة الأصول',
                    'description': 'Maintain inventory of information assets',
                    'control_type': 'PREVENTIVE',
                    'status': 'IMPLEMENTED',
                    'risk_level': 'MEDIUM',
                    'priority': 3
                }
            ]
        else:
            # Generic controls for other frameworks
            controls_data = [
                {
                    'control_id': f'{framework.framework_type}-001',
                    'name': 'Policy Management',
                    'name_ar': 'إدارة السياسات',
                    'description': 'Establish and maintain compliance policies',
                    'control_type': 'PREVENTIVE',
                    'status': 'IMPLEMENTED',
                    'risk_level': 'MEDIUM',
                    'priority': 2
                }
            ]
        
        for control_data in controls_data:
            ComplianceControl.objects.get_or_create(
                framework=framework,
                control_id=control_data['control_id'],
                defaults=control_data
            )

    def create_data_classifications(self):
        """Create sample data classifications"""
        try:
            admin_employee = Employee.objects.filter(user__is_superuser=True).first()
        except Employee.DoesNotExist:
            admin_employee = None
        
        classifications_data = [
            {
                'name': 'Public Information',
                'name_ar': 'معلومات عامة',
                'classification_level': 'PUBLIC',
                'description': 'Information that can be freely shared with the public',
                'description_ar': 'معلومات يمكن مشاركتها بحرية مع الجمهور',
                'encryption_required': False,
                'access_logging_required': False,
                'approval_required_for_access': False,
                'retention_period': 7,
                'retention_unit': 'YEARS'
            },
            {
                'name': 'Internal Information',
                'name_ar': 'معلومات داخلية',
                'classification_level': 'INTERNAL',
                'description': 'Information for internal use within the organization',
                'description_ar': 'معلومات للاستخدام الداخلي داخل المنظمة',
                'encryption_required': False,
                'access_logging_required': True,
                'approval_required_for_access': False,
                'retention_period': 5,
                'retention_unit': 'YEARS'
            },
            {
                'name': 'Confidential Information',
                'name_ar': 'معلومات سرية',
                'classification_level': 'CONFIDENTIAL',
                'description': 'Sensitive information requiring protection',
                'description_ar': 'معلومات حساسة تتطلب الحماية',
                'encryption_required': True,
                'access_logging_required': True,
                'approval_required_for_access': True,
                'retention_period': 10,
                'retention_unit': 'YEARS'
            },
            {
                'name': 'Personal Data',
                'name_ar': 'البيانات الشخصية',
                'classification_level': 'RESTRICTED',
                'description': 'Personal data subject to privacy regulations',
                'description_ar': 'البيانات الشخصية الخاضعة لأنظمة الخصوصية',
                'encryption_required': True,
                'access_logging_required': True,
                'approval_required_for_access': True,
                'retention_period': 3,
                'retention_unit': 'YEARS',
                'disposal_method': 'Secure deletion with certificate'
            }
        ]
        
        created_classifications = []
        for classification_data in classifications_data:
            classification, created = DataClassification.objects.get_or_create(
                name=classification_data['name'],
                defaults={**classification_data, 'created_by': admin_employee}
            )
            
            if created:
                created_classifications.append(classification)
                self.stdout.write(f'Created data classification: {classification.name}')
            else:
                self.stdout.write(f'Data classification already exists: {classification.name}')
        
        self.stdout.write(f'Data classifications setup complete. Created: {len(created_classifications)}')

    def create_security_incidents(self):
        """Create sample security incidents and alerts"""
        try:
            admin_employee = Employee.objects.filter(user__is_superuser=True).first()
        except Employee.DoesNotExist:
            admin_employee = None
        
        # Create sample security incidents
        incidents_data = [
            {
                'title': 'Suspicious Login Activity',
                'description': 'Multiple failed login attempts detected from unusual location',
                'incident_type': 'UNAUTHORIZED_ACCESS',
                'severity': 'MEDIUM',
                'status': 'RESOLVED',
                'detected_at': timezone.now() - timedelta(days=5),
                'contained_at': timezone.now() - timedelta(days=4),
                'resolved_at': timezone.now() - timedelta(days=3),
                'affected_systems': ['Authentication System', 'User Portal'],
                'data_categories_affected': ['User Credentials'],
                'root_cause': 'Brute force attack attempt from compromised IP address',
                'impact_assessment': 'No successful unauthorized access. Security controls worked as expected.',
                'remediation_actions': ['Blocked suspicious IP addresses', 'Enhanced monitoring', 'User notification']
            },
            {
                'title': 'Data Export Anomaly',
                'description': 'Unusual large data export detected outside business hours',
                'incident_type': 'DATA_LOSS',
                'severity': 'HIGH',
                'status': 'INVESTIGATING',
                'detected_at': timezone.now() - timedelta(days=2),
                'affected_systems': ['Employee Database', 'Reporting System'],
                'data_categories_affected': ['Employee Personal Data', 'Payroll Information'],
                'impact_assessment': 'Potential unauthorized access to sensitive employee data'
            },
            {
                'title': 'Phishing Email Campaign',
                'description': 'Targeted phishing emails sent to finance department',
                'incident_type': 'PHISHING',
                'severity': 'MEDIUM',
                'status': 'CONTAINED',
                'detected_at': timezone.now() - timedelta(days=1),
                'contained_at': timezone.now() - timedelta(hours=6),
                'affected_systems': ['Email System'],
                'data_categories_affected': ['Email Communications'],
                'remediation_actions': ['Email quarantine', 'User awareness training', 'Email security enhancement']
            }
        ]
        
        created_incidents = []
        for incident_data in incidents_data:
            incident_id = f"SEC-{timezone.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
            
            incident, created = SecurityIncident.objects.get_or_create(
                title=incident_data['title'],
                defaults={
                    **incident_data,
                    'incident_id': incident_id,
                    'reported_by': admin_employee,
                    'assigned_to': admin_employee
                }
            )
            
            if created:
                created_incidents.append(incident)
                self.stdout.write(f'Created security incident: {incident.incident_id}')
            else:
                self.stdout.write(f'Security incident already exists: {incident.title}')
        
        # Create sample security alerts
        alerts_data = [
            {
                'title': 'Multiple Failed Login Attempts',
                'description': 'User account locked due to multiple failed login attempts',
                'alert_type': 'MULTIPLE_FAILED_LOGINS',
                'severity': 'MEDIUM',
                'status': 'RESOLVED',
                'detected_at': timezone.now() - timedelta(hours=2),
                'resolved_at': timezone.now() - timedelta(hours=1),
                'risk_score': 65,
                'detection_rules': ['Failed login threshold exceeded'],
                'evidence': {'failed_attempts': 5, 'source_ip': '*************'}
            },
            {
                'title': 'Unusual Data Access Pattern',
                'description': 'Employee accessing data outside normal working hours',
                'alert_type': 'DATA_ACCESS_VIOLATION',
                'severity': 'LOW',
                'status': 'ACKNOWLEDGED',
                'detected_at': timezone.now() - timedelta(hours=8),
                'acknowledged_at': timezone.now() - timedelta(hours=6),
                'risk_score': 35,
                'detection_rules': ['After-hours access detected'],
                'evidence': {'access_time': '02:30 AM', 'data_type': 'Employee Records'}
            }
        ]
        
        created_alerts = []
        for alert_data in alerts_data:
            alert_id = f"ALERT-{timezone.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
            
            alert, created = SecurityAlert.objects.get_or_create(
                title=alert_data['title'],
                defaults={
                    **alert_data,
                    'alert_id': alert_id,
                    'assigned_to': admin_employee
                }
            )
            
            if created:
                created_alerts.append(alert)
                self.stdout.write(f'Created security alert: {alert.alert_id}')
            else:
                self.stdout.write(f'Security alert already exists: {alert.title}')
        
        self.stdout.write(f'Security incidents and alerts setup complete. Created: {len(created_incidents)} incidents, {len(created_alerts)} alerts')
