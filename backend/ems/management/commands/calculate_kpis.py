"""
Management command for scheduled KPI calculation.
This command should be run periodically (e.g., via cron) to ensure
all KPIs are up-to-date with the latest operational data.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from ems.kpi_engine import KPICalculationEngine
from ems.models import KPI


class Command(BaseCommand):
    help = 'Calculate all automated KPIs from operational data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--period',
            type=str,
            choices=['current_month', 'last_month', 'current_quarter', 'custom'],
            default='current_month',
            help='Period to calculate KPIs for'
        )
        parser.add_argument(
            '--start-date',
            type=str,
            help='Start date for custom period (YYYY-MM-DD)'
        )
        parser.add_argument(
            '--end-date',
            type=str,
            help='End date for custom period (YYYY-MM-DD)'
        )
        parser.add_argument(
            '--kpi-ids',
            type=str,
            help='Comma-separated list of KPI IDs to calculate (calculate all if not specified)'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recalculation even if values already exist'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting KPI calculation...'))
        
        # Determine calculation period
        period_start, period_end = self.get_calculation_period(options)
        
        self.stdout.write(f'Calculation period: {period_start.date()} to {period_end.date()}')
        
        # Initialize calculation engine
        engine = KPICalculationEngine()
        
        # Get KPIs to calculate
        if options['kpi_ids']:
            kpi_ids = [int(id.strip()) for id in options['kpi_ids'].split(',')]
            kpis = KPI.objects.filter(id__in=kpi_ids, status='ACTIVE')
            self.stdout.write(f'Calculating specific KPIs: {list(kpis.values_list("name", flat=True))}')
        else:
            kpis = KPI.objects.filter(status='ACTIVE', calculation_method__isnull=False)
            self.stdout.write(f'Calculating all automated KPIs ({kpis.count()} total)')
        
        # Calculate KPIs
        results = {
            'calculated': 0,
            'failed': 0,
            'skipped': 0,
            'updated_kpis': [],
            'failed_kpis': [],
            'skipped_kpis': []
        }
        
        for kpi in kpis:
            try:
                # Check if value already exists (unless force is specified)
                if not options['force']:
                    from ems.models import KPIValue
                    existing_value = KPIValue.objects.filter(
                        kpi=kpi,
                        period_start=period_start,
                        period_end=period_end
                    ).first()
                    
                    if existing_value:
                        results['skipped'] += 1
                        results['skipped_kpis'].append({
                            'kpi_id': kpi.id,
                            'name': kpi.name,
                            'existing_value': float(existing_value.value)
                        })
                        continue
                
                # Calculate KPI value
                calculated_value = engine.calculate_kpi(kpi, period_start, period_end)
                
                if calculated_value is not None:
                    # Create or update KPI value
                    from ems.models import KPIValue
                    kpi_value, created = KPIValue.objects.update_or_create(
                        kpi=kpi,
                        period_start=period_start,
                        period_end=period_end,
                        defaults={
                            'value': calculated_value,
                            'recorded_by': kpi.created_by,
                            'is_estimated': False,
                            'confidence_level': 100.0,
                            'data_quality_score': 100.0,
                            'notes': f'Calculated via scheduled job',
                            'source_data': {
                                'calculation_method': kpi.calculation_method,
                                'engine': 'KPICalculationEngine',
                                'timestamp': timezone.now().isoformat(),
                                'job_type': 'scheduled'
                            }
                        }
                    )
                    
                    # Update KPI current value
                    kpi.current_value = calculated_value
                    kpi.last_updated = timezone.now()
                    kpi.save(update_fields=['current_value', 'last_updated'])
                    
                    results['calculated'] += 1
                    results['updated_kpis'].append({
                        'kpi_id': kpi.id,
                        'name': kpi.name,
                        'value': float(calculated_value),
                        'created': created
                    })
                    
                    action = "Created" if created else "Updated"
                    self.stdout.write(f"{action} {kpi.name}: {calculated_value}")
                    
                else:
                    results['failed'] += 1
                    results['failed_kpis'].append({
                        'kpi_id': kpi.id,
                        'name': kpi.name,
                        'calculation_method': kpi.calculation_method
                    })
                    self.stdout.write(
                        self.style.WARNING(f"Failed to calculate {kpi.name}")
                    )
                    
            except Exception as e:
                results['failed'] += 1
                results['failed_kpis'].append({
                    'kpi_id': kpi.id,
                    'name': kpi.name,
                    'error': str(e)
                })
                self.stdout.write(
                    self.style.ERROR(f"Error calculating {kpi.name}: {str(e)}")
                )
        
        # Print summary
        self.stdout.write(self.style.SUCCESS('\nKPI Calculation Summary:'))
        self.stdout.write(f'  Calculated: {results["calculated"]}')
        self.stdout.write(f'  Failed: {results["failed"]}')
        self.stdout.write(f'  Skipped: {results["skipped"]}')
        
        if results['failed_kpis']:
            self.stdout.write(self.style.WARNING('\nFailed KPIs:'))
            for failed_kpi in results['failed_kpis']:
                error_msg = failed_kpi.get('error', failed_kpi.get('calculation_method', 'Unknown error'))
                self.stdout.write(f"  - {failed_kpi['name']}: {error_msg}")
        
        if results['skipped_kpis']:
            self.stdout.write(self.style.WARNING('\nSkipped KPIs (use --force to recalculate):'))
            for skipped_kpi in results['skipped_kpis']:
                self.stdout.write(f"  - {skipped_kpi['name']}: {skipped_kpi['existing_value']}")
        
        self.stdout.write(self.style.SUCCESS('\nKPI calculation completed!'))

    def get_calculation_period(self, options):
        """Determine the calculation period based on options"""
        now = timezone.now()
        
        if options['period'] == 'current_month':
            period_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            period_end = now
            
        elif options['period'] == 'last_month':
            # First day of current month
            first_day_current = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            # Last day of previous month
            period_end = first_day_current - timedelta(days=1)
            period_end = period_end.replace(hour=23, minute=59, second=59, microsecond=999999)
            # First day of previous month
            period_start = period_end.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            
        elif options['period'] == 'current_quarter':
            # Determine current quarter
            current_quarter = (now.month - 1) // 3 + 1
            quarter_start_month = (current_quarter - 1) * 3 + 1
            period_start = now.replace(month=quarter_start_month, day=1, hour=0, minute=0, second=0, microsecond=0)
            period_end = now
            
        elif options['period'] == 'custom':
            if not options['start_date'] or not options['end_date']:
                raise ValueError("Custom period requires both --start-date and --end-date")
            
            period_start = datetime.strptime(options['start_date'], '%Y-%m-%d')
            period_end = datetime.strptime(options['end_date'], '%Y-%m-%d')
            
            # Make timezone aware
            period_start = timezone.make_aware(period_start)
            period_end = timezone.make_aware(period_end.replace(hour=23, minute=59, second=59))
            
        else:
            raise ValueError(f"Invalid period: {options['period']}")
        
        return period_start, period_end
