"""
Management command to set up enhanced enterprise KPI system.
This command creates comprehensive KPIs that eliminate manual entry and provide
enterprise-grade automatic calculation from operational data.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from ems.models import KPI, KPICategory, Employee
from ems.enhanced_kpi_engine import enhanced_kpi_engine


class Command(BaseCommand):
    help = 'Set up enhanced enterprise KPI system with comprehensive automation'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreation of existing KPIs',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up Enhanced Enterprise KPI System...'))
        
        # Get or create admin employee
        try:
            admin_employee = Employee.objects.filter(user__is_staff=True).first()
            if not admin_employee:
                self.stdout.write(self.style.ERROR('No admin employee found. Please create an admin user first.'))
                return
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error finding admin employee: {str(e)}'))
            return

        # Create or get KPI categories
        categories = self.create_kpi_categories()
        
        # Create enhanced KPIs
        kpis_created = self.create_enhanced_kpis(categories, admin_employee, options['force'])
        
        # Run initial calculation
        self.run_initial_calculation()
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Enhanced KPI system setup complete! Created {kpis_created} KPIs with automatic calculation.'
            )
        )

    def create_kpi_categories(self):
        """Create or get KPI categories"""
        categories = {}
        
        category_data = [
            ('HR', 'الموارد البشرية', 'HR and employee-related metrics'),
            ('FINANCIAL', 'المالية', 'Financial performance and profitability metrics'),
            ('OPERATIONS', 'العمليات', 'Operational efficiency and project metrics'),
            ('CUSTOMER', 'العملاء', 'Customer satisfaction and retention metrics'),
            ('COMPLIANCE', 'الامتثال والمخاطر', 'Compliance, security, and risk metrics'),
        ]

        for name, name_ar, description in category_data:
            category, created = KPICategory.objects.get_or_create(
                name=name,
                defaults={
                    'name_ar': name_ar,
                    'description': description,
                    'description_ar': description,
                    'is_active': True,
                    'sort_order': len(categories) + 1
                }
            )
            categories[name] = category
            if created:
                self.stdout.write(f"Created category: {category.name}")

        return categories

    def create_enhanced_kpis(self, categories, admin_employee, force=False):
        """Create comprehensive enhanced KPIs"""
        kpis_created = 0
        
        enhanced_kpis_data = [
            # HR KPIs
            {
                'name': 'Employee Turnover Rate',
                'name_ar': 'معدل دوران الموظفين',
                'description': 'Percentage of employees who left the organization (automatically calculated)',
                'description_ar': 'نسبة الموظفين الذين تركوا المنظمة (محسوبة تلقائياً)',
                'category': categories['HR'],
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'DOWN',
                'target_value': 5.0,
                'warning_threshold': 8.0,
                'critical_threshold': 12.0,
                'calculation_method': 'EMPLOYEE_TURNOVER_RATE',
                'is_automated': True,
            },
            {
                'name': 'Employee Retention Rate',
                'name_ar': 'معدل الاحتفاظ بالموظفين',
                'description': 'Percentage of employees retained (inverse of turnover)',
                'description_ar': 'نسبة الموظفين المحتفظ بهم (عكس معدل الدوران)',
                'category': categories['HR'],
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 95.0,
                'warning_threshold': 92.0,
                'critical_threshold': 88.0,
                'calculation_method': 'EMPLOYEE_RETENTION_RATE',
                'is_automated': True,
            },
            {
                'name': 'Average Attendance Rate',
                'name_ar': 'متوسط معدل الحضور',
                'description': 'Average employee attendance rate (automatically calculated)',
                'description_ar': 'متوسط معدل حضور الموظفين (محسوب تلقائياً)',
                'category': categories['HR'],
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 95.0,
                'warning_threshold': 90.0,
                'critical_threshold': 85.0,
                'calculation_method': 'AVERAGE_ATTENDANCE_RATE',
                'is_automated': True,
            },
            {
                'name': 'Employee Productivity Score',
                'name_ar': 'نقاط إنتاجية الموظفين',
                'description': 'Employee productivity based on task completion (automatically calculated)',
                'description_ar': 'إنتاجية الموظفين بناءً على إنجاز المهام (محسوبة تلقائياً)',
                'category': categories['HR'],
                'measurement_type': 'SCORE',
                'unit': 'Points',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 80.0,
                'warning_threshold': 70.0,
                'critical_threshold': 60.0,
                'calculation_method': 'EMPLOYEE_PRODUCTIVITY_SCORE',
                'is_automated': True,
            },
            {
                'name': 'Employee Satisfaction Score',
                'name_ar': 'نقاط رضا الموظفين',
                'description': 'Employee satisfaction based on multiple workplace indicators',
                'description_ar': 'رضا الموظفين بناءً على مؤشرات مكان العمل المتعددة',
                'category': categories['HR'],
                'measurement_type': 'SCORE',
                'unit': 'Points',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 85.0,
                'warning_threshold': 75.0,
                'critical_threshold': 65.0,
                'calculation_method': 'EMPLOYEE_SATISFACTION_SCORE',
                'is_automated': True,
            },
            
            # Financial KPIs
            {
                'name': 'Monthly Revenue',
                'name_ar': 'الإيرادات الشهرية',
                'description': 'Total revenue from paid customer invoices (automatically calculated)',
                'description_ar': 'إجمالي الإيرادات من فواتير العملاء المدفوعة (محسوبة تلقائياً)',
                'category': categories['FINANCIAL'],
                'measurement_type': 'CURRENCY',
                'unit': '$',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 100000.0,
                'warning_threshold': 80000.0,
                'critical_threshold': 60000.0,
                'calculation_method': 'MONTHLY_REVENUE',
                'is_automated': True,
            },
            {
                'name': 'Profit Margin',
                'name_ar': 'هامش الربح',
                'description': 'Profit margin percentage (automatically calculated)',
                'description_ar': 'نسبة هامش الربح (محسوبة تلقائياً)',
                'category': categories['FINANCIAL'],
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 25.0,
                'warning_threshold': 20.0,
                'critical_threshold': 15.0,
                'calculation_method': 'PROFIT_MARGIN',
                'is_automated': True,
            },
            {
                'name': 'Cash Flow',
                'name_ar': 'التدفق النقدي',
                'description': 'Net cash flow from operations (automatically calculated)',
                'description_ar': 'صافي التدفق النقدي من العمليات (محسوب تلقائياً)',
                'category': categories['FINANCIAL'],
                'measurement_type': 'CURRENCY',
                'unit': '$',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 50000.0,
                'warning_threshold': 25000.0,
                'critical_threshold': 10000.0,
                'calculation_method': 'CASH_FLOW',
                'is_automated': True,
            },
            {
                'name': 'Revenue Growth Rate',
                'name_ar': 'معدل نمو الإيرادات',
                'description': 'Revenue growth compared to previous period (automatically calculated)',
                'description_ar': 'نمو الإيرادات مقارنة بالفترة السابقة (محسوب تلقائياً)',
                'category': categories['FINANCIAL'],
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 10.0,
                'warning_threshold': 5.0,
                'critical_threshold': 0.0,
                'calculation_method': 'REVENUE_GROWTH_RATE',
                'is_automated': True,
            },
            
            # Operations KPIs
            {
                'name': 'Project Completion Rate',
                'name_ar': 'معدل إنجاز المشاريع',
                'description': 'Percentage of projects completed on time (automatically calculated)',
                'description_ar': 'نسبة المشاريع المنجزة في الوقت المحدد (محسوبة تلقائياً)',
                'category': categories['OPERATIONS'],
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 90.0,
                'warning_threshold': 80.0,
                'critical_threshold': 70.0,
                'calculation_method': 'PROJECT_COMPLETION_RATE',
                'is_automated': True,
            },
            {
                'name': 'Task Completion Rate',
                'name_ar': 'معدل إنجاز المهام',
                'description': 'Percentage of tasks completed (automatically calculated)',
                'description_ar': 'نسبة المهام المنجزة (محسوبة تلقائياً)',
                'category': categories['OPERATIONS'],
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 85.0,
                'warning_threshold': 75.0,
                'critical_threshold': 65.0,
                'calculation_method': 'TASK_COMPLETION_RATE',
                'is_automated': True,
            },
            {
                'name': 'Average Project Duration',
                'name_ar': 'متوسط مدة المشروع',
                'description': 'Average time to complete projects (automatically calculated)',
                'description_ar': 'متوسط الوقت لإكمال المشاريع (محسوب تلقائياً)',
                'category': categories['OPERATIONS'],
                'measurement_type': 'TIME',
                'unit': 'Days',
                'frequency': 'MONTHLY',
                'trend_direction': 'DOWN',
                'target_value': 60.0,
                'warning_threshold': 75.0,
                'critical_threshold': 90.0,
                'calculation_method': 'AVERAGE_PROJECT_DURATION',
                'is_automated': True,
            },
            {
                'name': 'Resource Utilization Rate',
                'name_ar': 'معدل استخدام الموارد',
                'description': 'Percentage of resources actively utilized (automatically calculated)',
                'description_ar': 'نسبة الموارد المستخدمة بنشاط (محسوبة تلقائياً)',
                'category': categories['OPERATIONS'],
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 80.0,
                'warning_threshold': 70.0,
                'critical_threshold': 60.0,
                'calculation_method': 'RESOURCE_UTILIZATION_RATE',
                'is_automated': True,
            },
            
            # Customer KPIs
            {
                'name': 'Customer Retention Rate',
                'name_ar': 'معدل الاحتفاظ بالعملاء',
                'description': 'Percentage of customers retained (automatically calculated)',
                'description_ar': 'نسبة العملاء المحتفظ بهم (محسوبة تلقائياً)',
                'category': categories['CUSTOMER'],
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 90.0,
                'warning_threshold': 85.0,
                'critical_threshold': 80.0,
                'calculation_method': 'CUSTOMER_RETENTION_RATE',
                'is_automated': True,
            },
            {
                'name': 'Average Invoice Value',
                'name_ar': 'متوسط قيمة الفاتورة',
                'description': 'Average value per customer invoice (automatically calculated)',
                'description_ar': 'متوسط القيمة لكل فاتورة عميل (محسوبة تلقائياً)',
                'category': categories['CUSTOMER'],
                'measurement_type': 'CURRENCY',
                'unit': '$',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 5000.0,
                'warning_threshold': 4000.0,
                'critical_threshold': 3000.0,
                'calculation_method': 'AVERAGE_INVOICE_VALUE',
                'is_automated': True,
            },
        ]
        
        for kpi_data in enhanced_kpis_data:
            kpi_data['created_by'] = admin_employee
            kpi_data['status'] = 'ACTIVE'
            
            if force:
                # Delete existing KPI if force is True
                KPI.objects.filter(name=kpi_data['name']).delete()
            
            kpi, created = KPI.objects.get_or_create(
                name=kpi_data['name'],
                defaults=kpi_data
            )
            
            if created:
                kpis_created += 1
                self.stdout.write(f"Created KPI: {kpi.name}")
            elif force:
                # Update existing KPI with new data
                for key, value in kpi_data.items():
                    if key != 'name':  # Don't update the name
                        setattr(kpi, key, value)
                kpi.save()
                self.stdout.write(f"Updated KPI: {kpi.name}")
        
        return kpis_created

    def run_initial_calculation(self):
        """Run initial KPI calculation"""
        self.stdout.write('Running initial KPI calculations...')
        
        try:
            results = enhanced_kpi_engine.calculate_all_kpis_enhanced()
            self.stdout.write(
                self.style.SUCCESS(
                    f'Initial calculation complete: {results["calculated"]} KPIs calculated, '
                    f'{results["failed"]} failed in {results["execution_time"]:.2f}s'
                )
            )
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error during initial calculation: {str(e)}'))
