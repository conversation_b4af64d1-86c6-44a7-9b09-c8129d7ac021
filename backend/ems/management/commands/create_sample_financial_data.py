from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
import random
from ems.models import (
    Vendor, VendorInvoice, CustomerInvoice, Payment, Employee, 
    Department, Customer, ChartOfAccounts
)


class Command(BaseCommand):
    help = 'Create sample financial data for testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--vendors',
            type=int,
            default=10,
            help='Number of vendors to create',
        )
        parser.add_argument(
            '--invoices',
            type=int,
            default=25,
            help='Number of invoices to create',
        )

    def handle(self, *args, **options):
        self.stdout.write('Creating sample financial data...')
        
        # Get or create a default employee for created_by fields
        try:
            employee = Employee.objects.first()
            if not employee:
                self.stdout.write(self.style.ERROR('No employees found. Please create an employee first.'))
                return
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error getting employee: {e}'))
            return

        # Create sample vendors
        vendors_created = self.create_sample_vendors(options['vendors'])
        self.stdout.write(f'Created {vendors_created} vendors')

        # Create sample vendor invoices
        invoices_created = self.create_sample_vendor_invoices(options['invoices'], employee)
        self.stdout.write(f'Created {invoices_created} vendor invoices')

        # Create sample payments
        payments_created = self.create_sample_payments(employee)
        self.stdout.write(f'Created {payments_created} payments')

        # Create sample customer invoices
        customer_invoices_created = self.create_sample_customer_invoices(employee)
        self.stdout.write(f'Created {customer_invoices_created} customer invoices')

        self.stdout.write(
            self.style.SUCCESS('Successfully created sample financial data!')
        )

    def create_sample_vendors(self, count):
        vendors_data = [
            {
                'vendor_code': 'VEN001',
                'company_name': 'Al-Rajhi Technology Solutions',
                'company_name_ar': 'شركة الراجحي للحلول التقنية',
                'contact_person': 'Ahmed Al-Rajhi',
                'email': '<EMAIL>',
                'phone': '+966501234567',
                'address': 'Riyadh, Saudi Arabia',
                'tax_id': '300123456789003',
                'payment_terms': 'Net 30',
                'credit_limit': Decimal('100000.00'),
                'is_active': True,
                'is_approved': True,
            },
            {
                'vendor_code': 'VEN002',
                'company_name': 'Saudi Business Systems',
                'company_name_ar': 'أنظمة الأعمال السعودية',
                'contact_person': 'Fatima Al-Zahra',
                'email': '<EMAIL>',
                'phone': '+966502345678',
                'address': 'Jeddah, Saudi Arabia',
                'tax_id': '300234567890003',
                'payment_terms': 'Net 15',
                'credit_limit': Decimal('75000.00'),
                'is_active': True,
                'is_approved': True,
            },
            {
                'vendor_code': 'VEN003',
                'company_name': 'Gulf Office Supplies',
                'company_name_ar': 'لوازم المكاتب الخليجية',
                'contact_person': 'Mohammed Al-Ghamdi',
                'email': '<EMAIL>',
                'phone': '+966503456789',
                'address': 'Dammam, Saudi Arabia',
                'tax_id': '300345678901003',
                'payment_terms': 'Net 30',
                'credit_limit': Decimal('50000.00'),
                'is_active': True,
                'is_approved': True,
            },
            {
                'vendor_code': 'VEN004',
                'company_name': 'Arabian Consulting Group',
                'company_name_ar': 'مجموعة الاستشارات العربية',
                'contact_person': 'Sara Al-Mansouri',
                'email': '<EMAIL>',
                'phone': '+966504567890',
                'address': 'Riyadh, Saudi Arabia',
                'tax_id': '300456789012003',
                'payment_terms': 'Net 45',
                'credit_limit': Decimal('200000.00'),
                'is_active': True,
                'is_approved': True,
            },
            {
                'vendor_code': 'VEN005',
                'company_name': 'Digital Solutions KSA',
                'company_name_ar': 'الحلول الرقمية السعودية',
                'contact_person': 'Omar Al-Harbi',
                'email': '<EMAIL>',
                'phone': '+966505678901',
                'address': 'Riyadh, Saudi Arabia',
                'tax_id': '300567890123003',
                'payment_terms': 'Net 30',
                'credit_limit': Decimal('150000.00'),
                'is_active': True,
                'is_approved': False,  # Pending approval
            },
        ]

        created_count = 0
        for vendor_data in vendors_data[:count]:
            vendor, created = Vendor.objects.get_or_create(
                vendor_code=vendor_data['vendor_code'],
                defaults=vendor_data
            )
            if created:
                created_count += 1

        return created_count

    def create_sample_vendor_invoices(self, count, employee):
        vendors = list(Vendor.objects.all())
        if not vendors:
            return 0

        departments = list(Department.objects.all())
        
        created_count = 0
        for i in range(count):
            vendor = random.choice(vendors)
            department = random.choice(departments) if departments else None
            
            # Random date within last 90 days
            days_ago = random.randint(1, 90)
            invoice_date = timezone.now().date() - timedelta(days=days_ago)
            due_date = invoice_date + timedelta(days=30)  # 30 days payment terms
            
            subtotal = Decimal(str(random.uniform(1000, 50000))).quantize(Decimal('0.01'))
            tax_rate = Decimal('0.15')  # 15% VAT
            tax_amount = (subtotal * tax_rate).quantize(Decimal('0.01'))
            total_amount = subtotal + tax_amount
            
            # Random payment status
            status_choices = ['DRAFT', 'PENDING', 'APPROVED', 'PAID']
            status_weights = [0.1, 0.3, 0.4, 0.2]  # More approved invoices
            status = random.choices(status_choices, weights=status_weights)[0]
            
            # If paid, set paid amount
            paid_amount = total_amount if status == 'PAID' else Decimal('0.00')
            
            invoice_data = {
                'invoice_number': f'VI-2024-{str(i+1).zfill(4)}',
                'vendor_invoice_number': f'{vendor.vendor_code}-INV-{random.randint(1000, 9999)}',
                'vendor': vendor,
                'invoice_date': invoice_date,
                'due_date': due_date,
                'description': f'Professional services and supplies from {vendor.company_name}',
                'subtotal': subtotal,
                'tax_amount': tax_amount,
                'total_amount': total_amount,
                'paid_amount': paid_amount,
                'department': department,
                'status': status,
                'created_by': employee,
            }
            
            # Check if invoice already exists
            if not VendorInvoice.objects.filter(invoice_number=invoice_data['invoice_number']).exists():
                VendorInvoice.objects.create(**invoice_data)
                created_count += 1

        return created_count

    def create_sample_customer_invoices(self, employee):
        customers = list(Customer.objects.all())
        if not customers:
            # Create a sample customer
            customer = Customer.objects.create(
                first_name='ABC',
                last_name='Corporation',
                email='<EMAIL>',
                phone='+966501111111',
                customer_type='business',
                status='active',
                company_name='ABC Corporation',
                address_line1='123 Business District',
                city='Riyadh',
                country='Saudi Arabia'
            )
            customers = [customer]

        created_count = 0
        for i in range(10):  # Create 10 customer invoices
            customer = random.choice(customers)
            
            # Random date within last 60 days
            days_ago = random.randint(1, 60)
            invoice_date = timezone.now().date() - timedelta(days=days_ago)
            due_date = invoice_date + timedelta(days=30)
            
            subtotal = Decimal(str(random.uniform(5000, 100000))).quantize(Decimal('0.01'))
            tax_amount = (subtotal * Decimal('0.15')).quantize(Decimal('0.01'))
            total_amount = subtotal + tax_amount
            
            status_choices = ['DRAFT', 'SENT', 'PAID', 'PARTIAL']
            status = random.choice(status_choices)
            
            paid_amount = Decimal('0.00')
            if status == 'PAID':
                paid_amount = total_amount
            elif status == 'PARTIAL':
                paid_amount = (total_amount * Decimal(str(random.uniform(0.3, 0.8)))).quantize(Decimal('0.01'))
            
            invoice_data = {
                'invoice_number': f'CI-2024-{str(i+1).zfill(4)}',
                'customer': customer,
                'invoice_date': invoice_date,
                'due_date': due_date,
                'description': f'Professional services provided to {customer.company_name or customer.first_name}',
                'subtotal': subtotal,
                'tax_amount': tax_amount,
                'total_amount': total_amount,
                'paid_amount': paid_amount,
                'status': status,
                'created_by': employee,
            }
            
            if not CustomerInvoice.objects.filter(invoice_number=invoice_data['invoice_number']).exists():
                CustomerInvoice.objects.create(**invoice_data)
                created_count += 1

        return created_count

    def create_sample_payments(self, employee):
        # Get some paid invoices to create payments for
        vendor_invoices = VendorInvoice.objects.filter(status='PAID')[:10]
        customer_invoices = CustomerInvoice.objects.filter(status__in=['PAID', 'PARTIAL'])[:10]
        
        created_count = 0
        
        # Create vendor payments
        for i, invoice in enumerate(vendor_invoices):
            payment_methods = ['BANK_TRANSFER', 'CHECK', 'ONLINE']
            payment_method = random.choice(payment_methods)
            
            payment_data = {
                'payment_number': f'VP-2024-{str(i+1).zfill(4)}',
                'payment_type': 'VENDOR_PAYMENT',
                'payment_method': payment_method,
                'payment_date': invoice.due_date + timedelta(days=random.randint(-5, 5)),
                'amount': invoice.total_amount,
                'vendor_invoice': invoice,
                'reference_number': f'REF-{random.randint(100000, 999999)}',
                'description': f'Payment for invoice {invoice.invoice_number}',
                'created_by': employee,
            }
            
            if not Payment.objects.filter(payment_number=payment_data['payment_number']).exists():
                Payment.objects.create(**payment_data)
                created_count += 1

        # Create customer payments
        for i, invoice in enumerate(customer_invoices):
            payment_methods = ['BANK_TRANSFER', 'ONLINE', 'CHECK']
            payment_method = random.choice(payment_methods)
            
            payment_data = {
                'payment_number': f'CP-2024-{str(i+1).zfill(4)}',
                'payment_type': 'CUSTOMER_PAYMENT',
                'payment_method': payment_method,
                'payment_date': invoice.invoice_date + timedelta(days=random.randint(15, 45)),
                'amount': invoice.paid_amount,
                'customer_invoice': invoice,
                'reference_number': f'REF-{random.randint(100000, 999999)}',
                'description': f'Payment received for invoice {invoice.invoice_number}',
                'created_by': employee,
            }
            
            if not Payment.objects.filter(payment_number=payment_data['payment_number']).exists():
                Payment.objects.create(**payment_data)
                created_count += 1

        return created_count