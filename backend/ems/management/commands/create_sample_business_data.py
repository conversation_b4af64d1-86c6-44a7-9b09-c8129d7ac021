from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from decimal import Decimal
import random
from datetime import timedelta, date
from ems.models import (
    Employee, Department, Attendance, Project, Task, CustomerInvoice, 
    Payment, Expense, JobPosting, Customer, PayrollEntry
)


class Command(BaseCommand):
    help = 'Create sample business data for realistic KPI calculations'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Creating sample business data...'))
        
        # Create sample data
        self.create_sample_employees()
        self.create_sample_attendance()
        self.create_sample_projects()
        self.create_sample_customers()
        self.create_sample_invoices()
        self.create_sample_expenses()
        self.create_sample_job_postings()
        
        self.stdout.write(self.style.SUCCESS('Sample business data created successfully!'))

    def create_sample_employees(self):
        """Create sample employees if they don't exist"""
        departments = Department.objects.all()
        if not departments.exists():
            # Create sample departments
            dept_data = [
                {'name': 'Engineering', 'name_ar': 'الهندسة'},
                {'name': 'Sales', 'name_ar': 'المبيعات'},
                {'name': 'Marketing', 'name_ar': 'التسويق'},
                {'name': 'Finance', 'name_ar': 'المالية'},
                {'name': 'HR', 'name_ar': 'الموارد البشرية'}
            ]
            for dept in dept_data:
                Department.objects.get_or_create(
                    name=dept['name'],
                    defaults=dept
                )
            departments = Department.objects.all()

        # Create sample employees
        for i in range(1, 21):  # Create 20 employees
            username = f'employee{i:02d}'
            if not User.objects.filter(username=username).exists():
                user = User.objects.create_user(
                    username=username,
                    email=f'{username}@company.com',
                    first_name=f'Employee',
                    last_name=f'{i:02d}',
                    password='password123'
                )
                
                Employee.objects.get_or_create(
                    employee_id=f'EMP{i:03d}',
                    defaults={
                        'user': user,
                        'department': random.choice(departments),
                        'position': random.choice(['Developer', 'Manager', 'Analyst', 'Coordinator']),
                        'position_ar': random.choice(['مطور', 'مدير', 'محلل', 'منسق']),
                        'phone': f'+*********{i:03d}',
                        'gender': random.choice(['M', 'F']),
                        'hire_date': timezone.now().date() - timedelta(days=random.randint(30, 1000)),
                        'salary': Decimal(random.randint(5000, 15000)),
                        'employment_status': 'FULL_TIME'
                    }
                )
                
        self.stdout.write(f"Created sample employees")

    def create_sample_attendance(self):
        """Create sample attendance records"""
        employees = Employee.objects.all()
        if not employees.exists():
            return
            
        # Create attendance for last 30 days
        for days_ago in range(30):
            attendance_date = timezone.now().date() - timedelta(days=days_ago)
            
            # Skip weekends
            if attendance_date.weekday() >= 5:
                continue
                
            for employee in employees:
                # 95% attendance rate
                if random.random() < 0.95:
                    is_present = True
                    check_in_time = timezone.datetime.min.time().replace(hour=8, minute=random.randint(0, 30))
                    check_out_time = timezone.datetime.min.time().replace(hour=17, minute=random.randint(0, 59))
                    total_hours = Decimal('8.5')
                else:
                    is_present = False
                    check_in_time = None
                    check_out_time = None
                    total_hours = Decimal('0')

                Attendance.objects.get_or_create(
                    employee=employee,
                    date=attendance_date,
                    defaults={
                        'is_present': is_present,
                        'check_in': check_in_time,
                        'check_out': check_out_time,
                        'total_hours': total_hours,
                        'notes': 'Sample attendance data'
                    }
                )
                
        self.stdout.write("Created sample attendance records")

    def create_sample_projects(self):
        """Create sample projects and tasks"""
        employees = Employee.objects.all()
        if not employees.exists():
            return
            
        # Create 10 projects
        for i in range(1, 11):
            start_date = timezone.now() - timedelta(days=random.randint(30, 180))
            
            # 70% of projects are completed
            if random.random() < 0.7:
                status = 'COMPLETED'
                actual_end_date = start_date.date() + timedelta(days=random.randint(30, 120))
            else:
                status = random.choice(['IN_PROGRESS', 'PLANNING'])
                actual_end_date = None

            project = Project.objects.create(
                name=f'Project {i}',
                name_ar=f'مشروع {i}',
                description=f'Sample project {i} description',
                description_ar=f'وصف المشروع النموذجي {i}',
                project_manager=random.choice(employees),
                start_date=start_date.date(),
                end_date=start_date.date() + timedelta(days=random.randint(60, 180)),
                actual_start_date=start_date.date(),
                actual_end_date=actual_end_date,
                status=status,
                priority='MEDIUM',
                budget_amount=Decimal(random.randint(50000, 200000))
            )
            
            # Create 5-10 tasks per project
            for j in range(random.randint(5, 10)):
                task_start = start_date + timedelta(days=random.randint(0, 30))
                task_due = task_start + timedelta(days=random.randint(5, 30))
                
                # 80% of tasks are completed
                if random.random() < 0.8:
                    task_status = 'COMPLETED'
                    task_completion = task_due - timedelta(days=random.randint(0, 5))
                else:
                    task_status = random.choice(['IN_PROGRESS', 'PENDING'])
                    task_completion = None
                    
                Task.objects.create(
                    project=project,
                    title=f'Task {j+1} for Project {i}',
                    title_ar=f'المهمة {j+1} للمشروع {i}',
                    description=f'Sample task description',
                    description_ar=f'وصف المهمة النموذجية',
                    assigned_to=random.choice(employees),
                    created_by=project.project_manager,
                    due_date=timezone.make_aware(timezone.datetime.combine(task_due, timezone.datetime.min.time())),
                    start_date=timezone.make_aware(timezone.datetime.combine(task_start, timezone.datetime.min.time())),
                    completion_date=timezone.make_aware(timezone.datetime.combine(task_completion, timezone.datetime.min.time())) if task_completion else None,
                    status=task_status,
                    priority='MEDIUM',
                    estimated_hours=Decimal(random.randint(8, 40))
                )
                
        self.stdout.write("Created sample projects and tasks")

    def create_sample_customers(self):
        """Create sample customers"""
        for i in range(1, 11):  # Create 10 customers
            Customer.objects.get_or_create(
                company_name=f'Customer Company {i}',
                defaults={
                    'first_name': f'Contact',
                    'last_name': f'Person {i}',
                    'email': f'customer{i}@company.com',
                    'phone': f'+*********{i:03d}',
                    'address_line1': f'Address {i}',
                    'city': 'Riyadh',
                    'country': 'Saudi Arabia',
                    'customer_type': 'BUSINESS',
                    'status': 'ACTIVE'
                }
            )
        self.stdout.write("Created sample customers")

    def create_sample_invoices(self):
        """Create sample customer invoices"""
        customers = Customer.objects.all()
        employees = Employee.objects.all()
        
        if not customers.exists() or not employees.exists():
            return
            
        # Create invoices for last 3 months
        for i in range(1, 31):  # Create 30 invoices
            invoice_date = timezone.now().date() - timedelta(days=random.randint(1, 90))
            
            # 80% of invoices are paid
            if random.random() < 0.8:
                status = 'PAID'
                payment_date = invoice_date + timedelta(days=random.randint(1, 30))
            else:
                status = random.choice(['SENT', 'PARTIAL', 'OVERDUE'])
                payment_date = None
                
            total_amount = Decimal(random.randint(5000, 50000))
            
            CustomerInvoice.objects.create(
                invoice_number=f'INV-{i:04d}',
                customer=random.choice(customers),
                invoice_date=invoice_date,
                due_date=invoice_date + timedelta(days=30),
                description=f'Sample invoice {i} for services',
                description_ar=f'فاتورة نموذجية {i} للخدمات',
                subtotal=total_amount * Decimal('0.85'),  # Before tax
                tax_amount=total_amount * Decimal('0.15'),  # 15% tax
                total_amount=total_amount,
                paid_amount=total_amount if status == 'PAID' else Decimal('0'),
                status=status,
                created_by=random.choice(employees)
            )
            
        self.stdout.write("Created sample customer invoices")

    def create_sample_expenses(self):
        """Create sample expenses"""
        employees = Employee.objects.all()
        if not employees.exists():
            return
            
        # Create expenses for last 3 months
        for i in range(1, 51):  # Create 50 expenses
            expense_date = timezone.now().date() - timedelta(days=random.randint(1, 90))
            
            Expense.objects.create(
                employee=random.choice(employees),
                title=f'Business Expense {i}',
                title_ar=f'مصروف العمل {i}',
                description=f'Sample business expense {i}',
                description_ar=f'مصروف العمل النموذجي {i}',
                category=random.choice(['TRAVEL', 'OFFICE_SUPPLIES', 'TRAINING', 'MEALS', 'OTHER']),
                amount=Decimal(random.randint(100, 5000)),
                currency='SAR',
                expense_date=expense_date,
                status='APPROVED'
            )
            
        self.stdout.write("Created sample expenses")

    def create_sample_job_postings(self):
        """Create sample job postings"""
        departments = Department.objects.all()
        employees = Employee.objects.all()
        
        if not departments.exists() or not employees.exists():
            return
            
        # Create job postings
        for i in range(1, 6):  # Create 5 job postings
            created_date = timezone.now() - timedelta(days=random.randint(10, 60))
            
            # 60% are closed (filled)
            if random.random() < 0.6:
                status = 'closed'
                updated_date = created_date + timedelta(days=random.randint(15, 45))
            else:
                status = 'active'
                updated_date = created_date
                
            JobPosting.objects.create(
                title=f'Job Position {i}',
                title_ar=f'المنصب الوظيفي {i}',
                department=random.choice(departments),
                description=f'Job description for position {i}',
                description_ar=f'وصف الوظيفة للمنصب {i}',
                requirements=f'Requirements for position {i}',
                requirements_ar=f'متطلبات المنصب {i}',
                salary_min=Decimal(random.randint(5000, 8000)),
                salary_max=Decimal(random.randint(8000, 15000)),
                employment_type='full_time',
                status=status,
                created_at=timezone.make_aware(timezone.datetime.combine(created_date, timezone.datetime.min.time())),
                updated_at=timezone.make_aware(timezone.datetime.combine(updated_date, timezone.datetime.min.time())),
                posted_by=random.choice(employees)
            )
            
        self.stdout.write("Created sample job postings")
