from django.core.management.base import BaseCommand
from django.utils import timezone
from ems.models import AccountType, ChartOfAccounts, FiscalYear


class Command(BaseCommand):
    help = 'Setup initial Chart of Accounts and Fiscal Year'

    def handle(self, *args, **options):
        self.stdout.write('Setting up Chart of Accounts...')
        
        # Create Account Types
        account_types = [
            ('ASSET', 'الأصول', 'Assets', 'الأصول'),
            ('LIABILITY', 'الخصوم', 'Liabilities', 'الخصوم'),
            ('EQUITY', 'حقوق الملكية', 'Equity', 'حقوق الملكية'),
            ('REVENUE', 'الإيرادات', 'Revenue', 'الإيرادات'),
            ('EXPENSE', 'المصروفات', 'Expenses', 'المصروفات'),
            ('COST_OF_GOODS_SOLD', 'تكلفة البضاعة المباعة', 'Cost of Goods Sold', 'تكلفة البضاعة المباعة'),
        ]
        
        for type_code, name_ar, desc_en, desc_ar in account_types:
            account_type, created = AccountType.objects.get_or_create(
                name=type_code,
                defaults={
                    'name_ar': name_ar,
                    'description': desc_en,
                    'description_ar': desc_ar
                }
            )
            if created:
                self.stdout.write(f'Created account type: {account_type.get_name_display()}')
        
        # Create Chart of Accounts
        accounts = [
            # Assets (1000-1999)
            ('1000', 'Current Assets', 'الأصول المتداولة', 'ASSET', None, 0),
            ('1100', 'Cash and Cash Equivalents', 'النقد وما في حكمه', 'ASSET', '1000', 1),
            ('1110', 'Cash in Hand', 'النقد في الصندوق', 'ASSET', '1100', 2),
            ('1120', 'Bank Account - Main', 'الحساب البنكي الرئيسي', 'ASSET', '1100', 2),
            ('1130', 'Bank Account - Payroll', 'الحساب البنكي للرواتب', 'ASSET', '1100', 2),
            
            ('1200', 'Accounts Receivable', 'الذمم المدينة', 'ASSET', '1000', 1),
            ('1210', 'Trade Receivables', 'الذمم التجارية المدينة', 'ASSET', '1200', 2),
            ('1220', 'Employee Advances', 'سلف الموظفين', 'ASSET', '1200', 2),
            ('1230', 'Other Receivables', 'ذمم مدينة أخرى', 'ASSET', '1200', 2),
            
            ('1300', 'Inventory', 'المخزون', 'ASSET', '1000', 1),
            ('1310', 'Raw Materials', 'المواد الخام', 'ASSET', '1300', 2),
            ('1320', 'Work in Progress', 'الإنتاج تحت التشغيل', 'ASSET', '1300', 2),
            ('1330', 'Finished Goods', 'البضاعة التامة', 'ASSET', '1300', 2),
            
            ('1400', 'Prepaid Expenses', 'المصروفات المدفوعة مقدماً', 'ASSET', '1000', 1),
            ('1410', 'Prepaid Insurance', 'التأمين المدفوع مقدماً', 'ASSET', '1400', 2),
            ('1420', 'Prepaid Rent', 'الإيجار المدفوع مقدماً', 'ASSET', '1400', 2),
            
            ('1500', 'Fixed Assets', 'الأصول الثابتة', 'ASSET', None, 0),
            ('1510', 'Property, Plant & Equipment', 'الممتلكات والمصانع والمعدات', 'ASSET', '1500', 1),
            ('1511', 'Land', 'الأراضي', 'ASSET', '1510', 2),
            ('1512', 'Buildings', 'المباني', 'ASSET', '1510', 2),
            ('1513', 'Machinery & Equipment', 'الآلات والمعدات', 'ASSET', '1510', 2),
            ('1514', 'Furniture & Fixtures', 'الأثاث والتجهيزات', 'ASSET', '1510', 2),
            ('1515', 'Vehicles', 'المركبات', 'ASSET', '1510', 2),
            ('1516', 'Computer Equipment', 'معدات الحاسوب', 'ASSET', '1510', 2),
            
            ('1520', 'Accumulated Depreciation', 'مجمع الإهلاك', 'ASSET', '1500', 1),
            ('1521', 'Accumulated Depreciation - Buildings', 'مجمع إهلاك المباني', 'ASSET', '1520', 2),
            ('1522', 'Accumulated Depreciation - Equipment', 'مجمع إهلاك المعدات', 'ASSET', '1520', 2),
            
            # Liabilities (2000-2999)
            ('2000', 'Current Liabilities', 'الخصوم المتداولة', 'LIABILITY', None, 0),
            ('2100', 'Accounts Payable', 'الذمم الدائنة', 'LIABILITY', '2000', 1),
            ('2110', 'Trade Payables', 'الذمم التجارية الدائنة', 'LIABILITY', '2100', 2),
            ('2120', 'Accrued Expenses', 'المصروفات المستحقة', 'LIABILITY', '2100', 2),
            
            ('2200', 'Employee Liabilities', 'التزامات الموظفين', 'LIABILITY', '2000', 1),
            ('2210', 'Salaries Payable', 'الرواتب المستحقة', 'LIABILITY', '2200', 2),
            ('2220', 'Employee Benefits Payable', 'مزايا الموظفين المستحقة', 'LIABILITY', '2200', 2),
            ('2230', 'GOSI Payable', 'التأمينات الاجتماعية المستحقة', 'LIABILITY', '2200', 2),
            
            ('2300', 'Tax Liabilities', 'الالتزامات الضريبية', 'LIABILITY', '2000', 1),
            ('2310', 'VAT Payable', 'ضريبة القيمة المضافة المستحقة', 'LIABILITY', '2300', 2),
            ('2320', 'Withholding Tax Payable', 'ضريبة الاستقطاع المستحقة', 'LIABILITY', '2300', 2),
            
            ('2400', 'Long-term Liabilities', 'الخصوم طويلة الأجل', 'LIABILITY', None, 0),
            ('2410', 'Bank Loans', 'القروض البنكية', 'LIABILITY', '2400', 1),
            ('2420', 'Long-term Debt', 'الديون طويلة الأجل', 'LIABILITY', '2400', 1),
            
            # Equity (3000-3999)
            ('3000', 'Owner\'s Equity', 'حقوق الملكية', 'EQUITY', None, 0),
            ('3100', 'Capital', 'رأس المال', 'EQUITY', '3000', 1),
            ('3200', 'Retained Earnings', 'الأرباح المحتجزة', 'EQUITY', '3000', 1),
            ('3300', 'Current Year Earnings', 'أرباح السنة الحالية', 'EQUITY', '3000', 1),
            
            # Revenue (4000-4999)
            ('4000', 'Revenue', 'الإيرادات', 'REVENUE', None, 0),
            ('4100', 'Sales Revenue', 'إيرادات المبيعات', 'REVENUE', '4000', 1),
            ('4110', 'Product Sales', 'مبيعات المنتجات', 'REVENUE', '4100', 2),
            ('4120', 'Service Revenue', 'إيرادات الخدمات', 'REVENUE', '4100', 2),
            ('4200', 'Other Revenue', 'إيرادات أخرى', 'REVENUE', '4000', 1),
            ('4210', 'Interest Income', 'إيرادات الفوائد', 'REVENUE', '4200', 2),
            ('4220', 'Rental Income', 'إيرادات الإيجار', 'REVENUE', '4200', 2),
            
            # Cost of Goods Sold (5000-5999)
            ('5000', 'Cost of Goods Sold', 'تكلفة البضاعة المباعة', 'COST_OF_GOODS_SOLD', None, 0),
            ('5100', 'Direct Materials', 'المواد المباشرة', 'COST_OF_GOODS_SOLD', '5000', 1),
            ('5200', 'Direct Labor', 'العمالة المباشرة', 'COST_OF_GOODS_SOLD', '5000', 1),
            ('5300', 'Manufacturing Overhead', 'التكاليف الصناعية غير المباشرة', 'COST_OF_GOODS_SOLD', '5000', 1),
            
            # Expenses (6000-6999)
            ('6000', 'Operating Expenses', 'المصروفات التشغيلية', 'EXPENSE', None, 0),
            ('6100', 'Salaries and Benefits', 'الرواتب والمزايا', 'EXPENSE', '6000', 1),
            ('6110', 'Basic Salaries', 'الرواتب الأساسية', 'EXPENSE', '6100', 2),
            ('6120', 'Overtime', 'العمل الإضافي', 'EXPENSE', '6100', 2),
            ('6130', 'Employee Benefits', 'مزايا الموظفين', 'EXPENSE', '6100', 2),
            ('6140', 'GOSI Contribution', 'مساهمة التأمينات الاجتماعية', 'EXPENSE', '6100', 2),
            
            ('6200', 'Office Expenses', 'مصروفات المكتب', 'EXPENSE', '6000', 1),
            ('6210', 'Office Supplies', 'لوازم المكتب', 'EXPENSE', '6200', 2),
            ('6220', 'Utilities', 'المرافق', 'EXPENSE', '6200', 2),
            ('6230', 'Telephone & Internet', 'الهاتف والإنترنت', 'EXPENSE', '6200', 2),
            
            ('6300', 'Travel & Transportation', 'السفر والمواصلات', 'EXPENSE', '6000', 1),
            ('6310', 'Travel Expenses', 'مصروفات السفر', 'EXPENSE', '6300', 2),
            ('6320', 'Vehicle Expenses', 'مصروفات المركبات', 'EXPENSE', '6300', 2),
            
            ('6400', 'Professional Services', 'الخدمات المهنية', 'EXPENSE', '6000', 1),
            ('6410', 'Legal & Professional Fees', 'الرسوم القانونية والمهنية', 'EXPENSE', '6400', 2),
            ('6420', 'Consulting Fees', 'رسوم الاستشارات', 'EXPENSE', '6400', 2),
            
            ('6500', 'Marketing & Advertising', 'التسويق والإعلان', 'EXPENSE', '6000', 1),
            ('6510', 'Advertising Expenses', 'مصروفات الإعلان', 'EXPENSE', '6500', 2),
            ('6520', 'Marketing Materials', 'مواد التسويق', 'EXPENSE', '6500', 2),
            
            ('6600', 'Depreciation & Amortization', 'الإهلاك والاستنفاد', 'EXPENSE', '6000', 1),
            ('6610', 'Depreciation Expense', 'مصروف الإهلاك', 'EXPENSE', '6600', 2),
            
            ('6700', 'Other Expenses', 'مصروفات أخرى', 'EXPENSE', '6000', 1),
            ('6710', 'Bank Charges', 'رسوم البنك', 'EXPENSE', '6700', 2),
            ('6720', 'Insurance Expense', 'مصروف التأمين', 'EXPENSE', '6700', 2),
        ]
        
        for code, name_en, name_ar, acc_type, parent_code, level in accounts:
            account_type = AccountType.objects.get(name=acc_type)
            parent_account = None
            if parent_code:
                parent_account = ChartOfAccounts.objects.get(account_code=parent_code)
            
            account, created = ChartOfAccounts.objects.get_or_create(
                account_code=code,
                defaults={
                    'account_name': name_en,
                    'account_name_ar': name_ar,
                    'account_type': account_type,
                    'parent_account': parent_account,
                    'level': level,
                    'is_system_account': True,
                    'allow_manual_entries': level > 0,  # Only allow entries on sub-accounts
                }
            )
            if created:
                self.stdout.write(f'Created account: {code} - {name_en}')
        
        # Create current fiscal year
        current_year = timezone.now().year
        fiscal_year, created = FiscalYear.objects.get_or_create(
            name=f'FY {current_year}',
            defaults={
                'start_date': f'{current_year}-01-01',
                'end_date': f'{current_year}-12-31',
                'is_current': True
            }
        )
        if created:
            self.stdout.write(f'Created fiscal year: {fiscal_year.name}')
        
        self.stdout.write(
            self.style.SUCCESS('Successfully set up Chart of Accounts and Fiscal Year!')
        )