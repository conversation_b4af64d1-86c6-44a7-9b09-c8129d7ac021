from django.core.management.base import BaseCommand
from django.utils import timezone
from ems.models import (
    KPIMetric, KPIMetricValue, ReportTemplate, Dashboard, AnalyticsQuery, Employee,
    KPI, KPICategory, KPIValue, KPITarget, KPIAlert
)
from decimal import Decimal
from datetime import date, timedelta
import random
import json


class Command(BaseCommand):
    help = 'Set up sample analytics and reporting data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-kpis',
            action='store_true',
            help='Create sample KPI metrics'
        )
        parser.add_argument(
            '--create-reports',
            action='store_true',
            help='Create sample report templates'
        )
        parser.add_argument(
            '--create-dashboards',
            action='store_true',
            help='Create sample dashboards'
        )
        parser.add_argument(
            '--create-new-kpis',
            action='store_true',
            help='Create sample data for new KPI system'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up analytics and reporting data...'))
        
        if options['create_kpis'] or not any([
            options['create_kpis'], options['create_reports'], options['create_dashboards'], options['create_new_kpis']
        ]):
            self.create_kpi_metrics()

        if options['create_reports'] or not any([
            options['create_kpis'], options['create_reports'], options['create_dashboards'], options['create_new_kpis']
        ]):
            self.create_report_templates()

        if options['create_dashboards'] or not any([
            options['create_kpis'], options['create_reports'], options['create_dashboards'], options['create_new_kpis']
        ]):
            self.create_dashboards()

        if options['create_new_kpis'] or not any([
            options['create_kpis'], options['create_reports'], options['create_dashboards'], options['create_new_kpis']
        ]):
            self.create_new_kpi_system()
        
        self.stdout.write(self.style.SUCCESS('\n=== ANALYTICS & REPORTING SETUP COMPLETE ==='))

    def create_kpi_metrics(self):
        """Create sample KPI metrics"""
        kpi_data = [
            {
                'name': 'Monthly Revenue',
                'name_ar': 'الإيرادات الشهرية',
                'description': 'Total monthly revenue from customer invoices',
                'description_ar': 'إجمالي الإيرادات الشهرية من فواتير العملاء',
                'metric_type': 'FINANCIAL',
                'calculation_method': 'SUM',
                'target_value': Decimal('500000.00'),
                'warning_threshold': Decimal('400000.00'),
                'critical_threshold': Decimal('300000.00'),
                'unit': 'SAR',
                'frequency': 'MONTHLY',
                'is_higher_better': True
            },
            {
                'name': 'Asset Utilization Rate',
                'name_ar': 'معدل استخدام الأصول',
                'description': 'Percentage of assets currently in use',
                'description_ar': 'نسبة الأصول المستخدمة حالياً',
                'metric_type': 'ASSET',
                'calculation_method': 'PERCENTAGE',
                'target_value': Decimal('85.00'),
                'warning_threshold': Decimal('70.00'),
                'critical_threshold': Decimal('60.00'),
                'unit': '%',
                'frequency': 'MONTHLY',
                'is_higher_better': True
            },
            {
                'name': 'Average Invoice Processing Time',
                'name_ar': 'متوسط وقت معالجة الفواتير',
                'description': 'Average time to process customer invoices',
                'description_ar': 'متوسط الوقت لمعالجة فواتير العملاء',
                'metric_type': 'OPERATIONAL',
                'calculation_method': 'AVERAGE',
                'target_value': Decimal('2.00'),
                'warning_threshold': Decimal('3.00'),
                'critical_threshold': Decimal('5.00'),
                'unit': 'days',
                'frequency': 'MONTHLY',
                'is_higher_better': False
            },
            {
                'name': 'Employee Satisfaction Score',
                'name_ar': 'درجة رضا الموظفين',
                'description': 'Employee satisfaction survey score',
                'description_ar': 'درجة استطلاع رضا الموظفين',
                'metric_type': 'EMPLOYEE',
                'calculation_method': 'AVERAGE',
                'target_value': Decimal('4.50'),
                'warning_threshold': Decimal('4.00'),
                'critical_threshold': Decimal('3.50'),
                'unit': '/5',
                'frequency': 'QUARTERLY',
                'is_higher_better': True
            },
            {
                'name': 'Maintenance Cost Ratio',
                'name_ar': 'نسبة تكلفة الصيانة',
                'description': 'Maintenance costs as percentage of asset value',
                'description_ar': 'تكاليف الصيانة كنسبة من قيمة الأصول',
                'metric_type': 'FINANCIAL',
                'calculation_method': 'RATIO',
                'target_value': Decimal('5.00'),
                'warning_threshold': Decimal('8.00'),
                'critical_threshold': Decimal('12.00'),
                'unit': '%',
                'frequency': 'QUARTERLY',
                'is_higher_better': False
            },
            {
                'name': 'Cash Flow',
                'name_ar': 'التدفق النقدي',
                'description': 'Monthly net cash flow',
                'description_ar': 'صافي التدفق النقدي الشهري',
                'metric_type': 'FINANCIAL',
                'calculation_method': 'SUM',
                'target_value': Decimal('100000.00'),
                'warning_threshold': Decimal('50000.00'),
                'critical_threshold': Decimal('0.00'),
                'unit': 'SAR',
                'frequency': 'MONTHLY',
                'is_higher_better': True
            }
        ]
        
        # Get admin user for created_by field
        try:
            admin_employee = Employee.objects.filter(user__is_superuser=True).first()
        except Employee.DoesNotExist:
            admin_employee = None
        
        created_kpis = []
        for kpi_data_item in kpi_data:
            kpi, created = KPIMetric.objects.get_or_create(
                name=kpi_data_item['name'],
                defaults={**kpi_data_item, 'created_by': admin_employee}
            )
            if created:
                created_kpis.append(kpi)
                self.stdout.write(f'Created KPI: {kpi.name}')
                
                # Create sample historical values
                self.create_kpi_values(kpi)
            else:
                self.stdout.write(f'KPI already exists: {kpi.name}')
        
        self.stdout.write(f'KPI metrics setup complete. Created: {len(created_kpis)}')

    def create_kpi_values(self, kpi):
        """Create sample historical values for a KPI"""
        # Create values for last 12 months
        end_date = timezone.now().date()
        
        for i in range(12):
            if kpi.frequency == 'MONTHLY':
                period_end = end_date.replace(day=1) - timedelta(days=i*30)
                period_start = period_end.replace(day=1)
            else:  # QUARTERLY
                period_end = end_date - timedelta(days=i*90)
                period_start = period_end - timedelta(days=90)
            
            # Generate realistic sample values based on KPI type
            if kpi.metric_type == 'FINANCIAL':
                if 'Revenue' in kpi.name:
                    base_value = 450000
                    variation = random.uniform(0.8, 1.2)
                    value = Decimal(str(base_value * variation))
                elif 'Cash Flow' in kpi.name:
                    base_value = 80000
                    variation = random.uniform(0.5, 1.5)
                    value = Decimal(str(base_value * variation))
                else:
                    value = Decimal(str(random.uniform(5, 15)))
            elif kpi.metric_type == 'ASSET':
                value = Decimal(str(random.uniform(70, 90)))
            elif kpi.metric_type == 'OPERATIONAL':
                value = Decimal(str(random.uniform(1.5, 4.0)))
            elif kpi.metric_type == 'EMPLOYEE':
                value = Decimal(str(random.uniform(3.5, 4.8)))
            else:
                value = Decimal(str(random.uniform(50, 100)))
            
            KPIMetricValue.objects.get_or_create(
                kpi_metric=kpi,
                period_start=period_start,
                period_end=period_end,
                defaults={
                    'value': value,
                    'data_source': 'sample_data',
                    'calculated_by': kpi.created_by
                }
            )

    def create_report_templates(self):
        """Create sample report templates"""
        templates_data = [
            {
                'name': 'Executive Dashboard Report',
                'name_ar': 'تقرير لوحة القيادة التنفيذية',
                'description': 'Comprehensive executive summary with key metrics',
                'description_ar': 'ملخص تنفيذي شامل مع المؤشرات الرئيسية',
                'report_type': 'EXECUTIVE',
                'template_config': {
                    'sections': ['kpi_summary', 'financial_overview', 'operational_metrics'],
                    'charts': ['revenue_trend', 'asset_utilization', 'cash_flow'],
                    'time_period': 'last_12_months'
                },
                'data_sources': ['kpi_metrics', 'financial_data', 'asset_data'],
                'output_formats': ['PDF', 'EXCEL'],
                'is_public': True
            },
            {
                'name': 'Financial Performance Report',
                'name_ar': 'تقرير الأداء المالي',
                'description': 'Detailed financial analysis and trends',
                'description_ar': 'تحليل مالي مفصل والاتجاهات',
                'report_type': 'FINANCIAL',
                'template_config': {
                    'sections': ['revenue_analysis', 'expense_breakdown', 'profitability'],
                    'charts': ['monthly_revenue', 'expense_categories', 'profit_margins'],
                    'time_period': 'configurable'
                },
                'data_sources': ['customer_invoices', 'vendor_invoices', 'payments'],
                'output_formats': ['PDF', 'EXCEL', 'CSV'],
                'is_public': False
            },
            {
                'name': 'Asset Management Report',
                'name_ar': 'تقرير إدارة الأصول',
                'description': 'Asset utilization, depreciation, and maintenance analysis',
                'description_ar': 'تحليل استخدام الأصول والاستهلاك والصيانة',
                'report_type': 'OPERATIONAL',
                'template_config': {
                    'sections': ['asset_summary', 'depreciation_analysis', 'maintenance_costs'],
                    'charts': ['asset_status', 'depreciation_trend', 'maintenance_schedule'],
                    'time_period': 'last_6_months'
                },
                'data_sources': ['assets', 'asset_depreciation', 'asset_maintenance'],
                'output_formats': ['PDF', 'EXCEL'],
                'is_public': False
            }
        ]
        
        try:
            admin_employee = Employee.objects.filter(user__is_superuser=True).first()
        except Employee.DoesNotExist:
            admin_employee = None
        
        created_templates = []
        for template_data in templates_data:
            template, created = ReportTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults={**template_data, 'created_by': admin_employee}
            )
            if created:
                created_templates.append(template)
                self.stdout.write(f'Created report template: {template.name}')
            else:
                self.stdout.write(f'Report template already exists: {template.name}')
        
        self.stdout.write(f'Report templates setup complete. Created: {len(created_templates)}')

    def create_dashboards(self):
        """Create sample dashboards"""
        dashboards_data = [
            {
                'name': 'Executive Dashboard',
                'name_ar': 'لوحة القيادة التنفيذية',
                'description': 'High-level overview for executives',
                'description_ar': 'نظرة عامة عالية المستوى للمديرين التنفيذيين',
                'dashboard_type': 'EXECUTIVE',
                'layout_config': {
                    'grid_size': [12, 8],
                    'theme': 'professional'
                },
                'widgets': [
                    {
                        'id': 'revenue_kpi',
                        'type': 'kpi_metric',
                        'title': 'Monthly Revenue',
                        'title_ar': 'الإيرادات الشهرية',
                        'position': {'x': 0, 'y': 0, 'w': 3, 'h': 2},
                        'metric_id': 1  # Will be updated after KPI creation
                    },
                    {
                        'id': 'asset_utilization',
                        'type': 'kpi_metric',
                        'title': 'Asset Utilization',
                        'title_ar': 'استخدام الأصول',
                        'position': {'x': 3, 'y': 0, 'w': 3, 'h': 2},
                        'metric_id': 2
                    },
                    {
                        'id': 'revenue_chart',
                        'type': 'chart',
                        'title': 'Revenue Trend',
                        'title_ar': 'اتجاه الإيرادات',
                        'position': {'x': 0, 'y': 2, 'w': 6, 'h': 4},
                        'chart_type': 'line',
                        'data_source': 'financial_summary'
                    },
                    {
                        'id': 'asset_status_chart',
                        'type': 'chart',
                        'title': 'Asset Status Distribution',
                        'title_ar': 'توزيع حالة الأصول',
                        'position': {'x': 6, 'y': 0, 'w': 6, 'h': 4},
                        'chart_type': 'pie',
                        'data_source': 'asset_status'
                    }
                ],
                'refresh_interval': 300,
                'is_public': True
            },
            {
                'name': 'Financial Dashboard',
                'name_ar': 'لوحة القيادة المالية',
                'description': 'Financial metrics and analysis',
                'description_ar': 'المؤشرات والتحليل المالي',
                'dashboard_type': 'FINANCIAL',
                'layout_config': {
                    'grid_size': [12, 10],
                    'theme': 'financial'
                },
                'widgets': [
                    {
                        'id': 'cash_flow_kpi',
                        'type': 'kpi_metric',
                        'title': 'Cash Flow',
                        'title_ar': 'التدفق النقدي',
                        'position': {'x': 0, 'y': 0, 'w': 4, 'h': 2},
                        'metric_id': 6
                    },
                    {
                        'id': 'recent_invoices',
                        'type': 'table',
                        'title': 'Recent Invoices',
                        'title_ar': 'الفواتير الحديثة',
                        'position': {'x': 0, 'y': 2, 'w': 12, 'h': 4},
                        'data_source': 'recent_invoices'
                    }
                ],
                'refresh_interval': 600,
                'is_public': False
            }
        ]
        
        try:
            admin_employee = Employee.objects.filter(user__is_superuser=True).first()
        except Employee.DoesNotExist:
            admin_employee = None
        
        created_dashboards = []
        for dashboard_data in dashboards_data:
            dashboard, created = Dashboard.objects.get_or_create(
                name=dashboard_data['name'],
                defaults={**dashboard_data, 'created_by': admin_employee}
            )
            if created:
                created_dashboards.append(dashboard)
                self.stdout.write(f'Created dashboard: {dashboard.name}')
            else:
                self.stdout.write(f'Dashboard already exists: {dashboard.name}')
        
        self.stdout.write(f'Dashboards setup complete. Created: {len(created_dashboards)}')

    def create_new_kpi_system(self):
        """Create sample data for the new KPI system (KPI, KPICategory, etc.)"""
        self.stdout.write('Creating new KPI system data...')

        # Get admin employee
        admin_employee = Employee.objects.filter(user__username='admin').first()
        if not admin_employee:
            admin_employee = Employee.objects.first()

        if not admin_employee:
            self.stdout.write(self.style.ERROR('No employees found. Please create employees first.'))
            return

        # Create KPI Categories
        categories_data = [
            {
                'name': 'FINANCIAL',
                'name_ar': 'الأداء المالي',
                'description': 'Financial metrics and revenue tracking',
                'description_ar': 'المقاييس المالية وتتبع الإيرادات',
                'color': 'from-green-500 to-green-600',
                'icon': 'DollarSign',
                'sort_order': 1
            },
            {
                'name': 'HR',
                'name_ar': 'الموارد البشرية',
                'description': 'Employee performance and satisfaction metrics',
                'description_ar': 'مقاييس أداء الموظفين والرضا الوظيفي',
                'color': 'from-blue-500 to-blue-600',
                'icon': 'Users',
                'sort_order': 2
            },
            {
                'name': 'OPERATIONS',
                'name_ar': 'العمليات',
                'description': 'Operational efficiency and productivity metrics',
                'description_ar': 'مقاييس الكفاءة التشغيلية والإنتاجية',
                'color': 'from-yellow-500 to-yellow-600',
                'icon': 'Settings',
                'sort_order': 3
            },
            {
                'name': 'SALES',
                'name_ar': 'المبيعات والتسويق',
                'description': 'Sales performance and marketing effectiveness',
                'description_ar': 'أداء المبيعات وفعالية التسويق',
                'color': 'from-red-500 to-red-600',
                'icon': 'TrendingUp',
                'sort_order': 4
            },
            {
                'name': 'CUSTOMER',
                'name_ar': 'خدمة العملاء',
                'description': 'Customer satisfaction and service quality',
                'description_ar': 'رضا العملاء وجودة الخدمة',
                'color': 'from-purple-500 to-purple-600',
                'icon': 'Heart',
                'sort_order': 5
            }
        ]

        created_categories = []
        for cat_data in categories_data:
            category, created = KPICategory.objects.get_or_create(
                name=cat_data['name'],
                defaults=cat_data
            )
            if created:
                created_categories.append(category)
                self.stdout.write(f'Created KPI category: {category.name}')
            else:
                self.stdout.write(f'KPI category already exists: {category.name}')

        self.stdout.write(f'KPI categories setup complete. Created: {len(created_categories)}')

        # Create KPIs
        financial_category = KPICategory.objects.get(name='FINANCIAL')
        hr_category = KPICategory.objects.get(name='HR')
        operations_category = KPICategory.objects.get(name='OPERATIONS')
        sales_category = KPICategory.objects.get(name='SALES')
        customer_category = KPICategory.objects.get(name='CUSTOMER')

        kpis_data = [
            # Financial KPIs
            {
                'name': 'Monthly Revenue',
                'name_ar': 'الإيرادات الشهرية',
                'description': 'Total monthly revenue from all sources',
                'description_ar': 'إجمالي الإيرادات الشهرية من جميع المصادر',
                'category': financial_category,
                'measurement_type': 'CURRENCY',
                'unit': 'SAR',
                'unit_ar': 'ريال سعودي',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 500000.00,
                'warning_threshold': 400000.00,
                'critical_threshold': 300000.00,
                'status': 'ACTIVE',
                'created_by': admin_employee
            },
            {
                'name': 'Profit Margin',
                'name_ar': 'هامش الربح',
                'description': 'Net profit margin percentage',
                'description_ar': 'نسبة هامش الربح الصافي',
                'category': financial_category,
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'unit_ar': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 25.0,
                'warning_threshold': 20.0,
                'critical_threshold': 15.0,
                'status': 'ACTIVE',
                'created_by': admin_employee
            },
            {
                'name': 'Cash Flow',
                'name_ar': 'التدفق النقدي',
                'description': 'Monthly cash flow from operations',
                'description_ar': 'التدفق النقدي الشهري من العمليات',
                'category': financial_category,
                'measurement_type': 'CURRENCY',
                'unit': 'SAR',
                'unit_ar': 'ريال سعودي',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 200000.00,
                'warning_threshold': 150000.00,
                'critical_threshold': 100000.00,
                'status': 'ACTIVE',
                'created_by': admin_employee
            },
            # HR KPIs
            {
                'name': 'Employee Satisfaction',
                'name_ar': 'رضا الموظفين',
                'description': 'Average employee satisfaction score',
                'description_ar': 'متوسط درجة رضا الموظفين',
                'category': hr_category,
                'measurement_type': 'SCORE',
                'unit': 'Score',
                'unit_ar': 'نقطة',
                'frequency': 'QUARTERLY',
                'trend_direction': 'UP',
                'target_value': 4.5,
                'warning_threshold': 4.0,
                'critical_threshold': 3.5,
                'status': 'ACTIVE',
                'created_by': admin_employee
            },
            {
                'name': 'Employee Turnover Rate',
                'name_ar': 'معدل دوران الموظفين',
                'description': 'Annual employee turnover percentage',
                'description_ar': 'نسبة دوران الموظفين السنوية',
                'category': hr_category,
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'unit_ar': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'DOWN',
                'target_value': 5.0,
                'warning_threshold': 8.0,
                'critical_threshold': 12.0,
                'status': 'ACTIVE',
                'created_by': admin_employee
            },
            {
                'name': 'Training Hours per Employee',
                'name_ar': 'ساعات التدريب لكل موظف',
                'description': 'Average training hours per employee per month',
                'description_ar': 'متوسط ساعات التدريب لكل موظف شهرياً',
                'category': hr_category,
                'measurement_type': 'NUMBER',
                'unit': 'Hours',
                'unit_ar': 'ساعة',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 8.0,
                'warning_threshold': 6.0,
                'critical_threshold': 4.0,
                'status': 'ACTIVE',
                'created_by': admin_employee
            },
            # Operations KPIs
            {
                'name': 'Project Completion Rate',
                'name_ar': 'معدل إنجاز المشاريع',
                'description': 'Percentage of projects completed on time',
                'description_ar': 'نسبة المشاريع المكتملة في الوقت المحدد',
                'category': operations_category,
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'unit_ar': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 90.0,
                'warning_threshold': 80.0,
                'critical_threshold': 70.0,
                'status': 'ACTIVE',
                'created_by': admin_employee
            },
            {
                'name': 'System Uptime',
                'name_ar': 'وقت تشغيل النظام',
                'description': 'System availability percentage',
                'description_ar': 'نسبة توفر النظام',
                'category': operations_category,
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'unit_ar': '%',
                'frequency': 'DAILY',
                'trend_direction': 'UP',
                'target_value': 99.9,
                'warning_threshold': 99.5,
                'critical_threshold': 99.0,
                'status': 'ACTIVE',
                'created_by': admin_employee
            },
            {
                'name': 'Process Efficiency',
                'name_ar': 'كفاءة العمليات',
                'description': 'Overall operational efficiency score',
                'description_ar': 'درجة الكفاءة التشغيلية الإجمالية',
                'category': operations_category,
                'measurement_type': 'SCORE',
                'unit': 'Score',
                'unit_ar': 'نقطة',
                'frequency': 'WEEKLY',
                'trend_direction': 'UP',
                'target_value': 4.2,
                'warning_threshold': 3.8,
                'critical_threshold': 3.5,
                'status': 'ACTIVE',
                'created_by': admin_employee
            },
            # Sales KPIs
            {
                'name': 'Sales Conversion Rate',
                'name_ar': 'معدل تحويل المبيعات',
                'description': 'Percentage of leads converted to sales',
                'description_ar': 'نسبة العملاء المحتملين المحولين إلى مبيعات',
                'category': sales_category,
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'unit_ar': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 25.0,
                'warning_threshold': 20.0,
                'critical_threshold': 15.0,
                'status': 'ACTIVE',
                'created_by': admin_employee
            },
            {
                'name': 'Average Deal Size',
                'name_ar': 'متوسط حجم الصفقة',
                'description': 'Average value of closed deals',
                'description_ar': 'متوسط قيمة الصفقات المغلقة',
                'category': sales_category,
                'measurement_type': 'CURRENCY',
                'unit': 'SAR',
                'unit_ar': 'ريال سعودي',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 15000.00,
                'warning_threshold': 12000.00,
                'critical_threshold': 10000.00,
                'status': 'ACTIVE',
                'created_by': admin_employee
            },
            {
                'name': 'Sales Pipeline Value',
                'name_ar': 'قيمة خط أنابيب المبيعات',
                'description': 'Total value of opportunities in sales pipeline',
                'description_ar': 'القيمة الإجمالية للفرص في خط أنابيب المبيعات',
                'category': sales_category,
                'measurement_type': 'CURRENCY',
                'unit': 'SAR',
                'unit_ar': 'ريال سعودي',
                'frequency': 'WEEKLY',
                'trend_direction': 'UP',
                'target_value': 800000.00,
                'warning_threshold': 600000.00,
                'critical_threshold': 400000.00,
                'status': 'ACTIVE',
                'created_by': admin_employee
            },
            # Customer Service KPIs
            {
                'name': 'Customer Response Time',
                'name_ar': 'وقت استجابة العملاء',
                'description': 'Average time to respond to customer inquiries',
                'description_ar': 'متوسط الوقت للرد على استفسارات العملاء',
                'category': customer_category,
                'measurement_type': 'TIME',
                'unit': 'Hours',
                'unit_ar': 'ساعة',
                'frequency': 'WEEKLY',
                'trend_direction': 'DOWN',
                'target_value': 2.0,
                'warning_threshold': 4.0,
                'critical_threshold': 6.0,
                'status': 'ACTIVE',
                'created_by': admin_employee
            },
            {
                'name': 'Customer Satisfaction Score',
                'name_ar': 'درجة رضا العملاء',
                'description': 'Average customer satisfaction rating',
                'description_ar': 'متوسط تقييم رضا العملاء',
                'category': customer_category,
                'measurement_type': 'SCORE',
                'unit': 'Score',
                'unit_ar': 'نقطة',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': 4.6,
                'warning_threshold': 4.2,
                'critical_threshold': 3.8,
                'status': 'ACTIVE',
                'created_by': admin_employee
            },
            {
                'name': 'First Call Resolution Rate',
                'name_ar': 'معدل الحل من الاتصال الأول',
                'description': 'Percentage of issues resolved on first contact',
                'description_ar': 'نسبة المشاكل المحلولة من الاتصال الأول',
                'category': customer_category,
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'unit_ar': '%',
                'frequency': 'WEEKLY',
                'trend_direction': 'UP',
                'target_value': 85.0,
                'warning_threshold': 75.0,
                'critical_threshold': 65.0,
                'status': 'ACTIVE',
                'created_by': admin_employee
            }
        ]

        created_kpis = []
        for kpi_data in kpis_data:
            kpi, created = KPI.objects.get_or_create(
                name=kpi_data['name'],
                defaults=kpi_data
            )
            if created:
                created_kpis.append(kpi)
                self.stdout.write(f'Created KPI: {kpi.name}')

                # Create sample values for the last 12 months
                self.create_sample_kpi_values(kpi)

                # Create KPI targets
                self.create_kpi_targets(kpi)

                # Create some sample alerts for demonstration
                self.create_sample_alerts(kpi)
            else:
                self.stdout.write(f'KPI already exists: {kpi.name}')

        self.stdout.write(f'KPIs setup complete. Created: {len(created_kpis)}')

    def create_sample_kpi_values(self, kpi):
        """Create sample historical values for a KPI with realistic trends"""
        end_date = timezone.now().date()

        # Create values for last 12 months for more comprehensive data
        for i in range(12):
            period_start = end_date - timedelta(days=30 * (i + 1))
            period_end = end_date - timedelta(days=30 * i)

            # Generate realistic values based on KPI type and target with trends
            if kpi.target_value:
                base_value = float(kpi.target_value)

                # Create realistic trends based on KPI type
                if kpi.trend_direction == 'UP':
                    # Gradual improvement over time
                    trend_factor = 0.8 + (0.4 * (12 - i) / 12)  # Improve from 80% to 120% of target
                    variance = random.uniform(-0.15, 0.15)
                elif kpi.trend_direction == 'DOWN':
                    # Gradual improvement (lower is better)
                    trend_factor = 1.4 - (0.6 * (12 - i) / 12)  # Improve from 140% to 80% of target
                    variance = random.uniform(-0.15, 0.15)
                else:  # TARGET
                    # Fluctuate around target
                    trend_factor = 1.0
                    variance = random.uniform(-0.25, 0.25)

                value = base_value * trend_factor * (1 + variance)

                # Ensure positive values and reasonable bounds
                if value < 0:
                    value = base_value * 0.1
                elif kpi.measurement_type == 'PERCENTAGE' and value > 100:
                    value = min(value, 100)
                elif kpi.measurement_type == 'SCORE' and value > 5:
                    value = min(value, 5)

                # Add some seasonal variation for certain KPIs
                if 'Revenue' in kpi.name or 'Sales' in kpi.name:
                    # Higher in Q4, lower in Q1
                    month = period_start.month
                    if month in [11, 12]:  # Q4
                        value *= 1.2
                    elif month in [1, 2]:  # Q1
                        value *= 0.9

            else:
                value = random.uniform(50, 150)

            # Convert dates to timezone-aware datetime objects
            period_start_dt = timezone.make_aware(timezone.datetime.combine(period_start, timezone.datetime.min.time()))
            period_end_dt = timezone.make_aware(timezone.datetime.combine(period_end, timezone.datetime.min.time()))

            # Add some data quality variation
            confidence = random.uniform(85, 100)
            quality_score = random.uniform(90, 100)

            KPIValue.objects.get_or_create(
                kpi=kpi,
                period_start=period_start_dt,
                period_end=period_end_dt,
                defaults={
                    'value': Decimal(str(round(value, 2))),
                    'notes': f'Sample data for {period_start.strftime("%B %Y")} - Trend: {kpi.trend_direction}',
                    'recorded_by': kpi.created_by,
                    'is_estimated': True,
                    'confidence_level': Decimal(str(round(confidence, 2))),
                    'data_quality_score': Decimal(str(round(quality_score, 2)))
                }
            )

    def create_kpi_targets(self, kpi):
        """Create quarterly and annual targets for a KPI"""
        current_year = timezone.now().year

        # Create quarterly targets for current year
        quarters = [
            (f'{current_year}-01-01', f'{current_year}-03-31', 'Q1 {}'.format(current_year)),
            (f'{current_year}-04-01', f'{current_year}-06-30', 'Q2 {}'.format(current_year)),
            (f'{current_year}-07-01', f'{current_year}-09-30', 'Q3 {}'.format(current_year)),
            (f'{current_year}-10-01', f'{current_year}-12-31', 'Q4 {}'.format(current_year)),
        ]

        for start_date, end_date, description in quarters:
            if kpi.target_value:
                # Vary targets slightly by quarter
                base_target = float(kpi.target_value)
                quarter_variance = random.uniform(0.95, 1.05)
                target_value = base_target * quarter_variance

                KPITarget.objects.get_or_create(
                    kpi=kpi,
                    start_date=start_date,
                    end_date=end_date,
                    defaults={
                        'target_value': Decimal(str(round(target_value, 2))),
                        'target_type': 'ABSOLUTE',
                        'description': f'{description} target for {kpi.name}',
                        'is_stretch_goal': False,
                        'weight': 1.0,
                        'created_by': kpi.created_by
                    }
                )

    def create_sample_alerts(self, kpi):
        """Create sample alerts for KPIs that are underperforming"""
        # Only create alerts for some KPIs to make it realistic
        if random.random() > 0.3:  # 30% chance of having an alert
            return

        # Get the latest KPI value
        latest_value = kpi.values.first()
        if not latest_value or not kpi.target_value:
            return

        current_value = float(latest_value.value)
        target_value = float(kpi.target_value)

        # Determine alert severity based on thresholds
        if kpi.critical_threshold:
            critical_threshold = float(kpi.critical_threshold)
            warning_threshold = float(kpi.warning_threshold) if kpi.warning_threshold else critical_threshold * 1.2

            if kpi.trend_direction == 'UP':
                if current_value <= critical_threshold:
                    severity = 'CRITICAL'
                    message = f'{kpi.name} is critically below target ({current_value} vs {target_value})'
                elif current_value <= warning_threshold:
                    severity = 'WARNING'
                    message = f'{kpi.name} is below warning threshold ({current_value} vs {target_value})'
                else:
                    return  # No alert needed
            else:  # DOWN trend (lower is better)
                if current_value >= critical_threshold:
                    severity = 'CRITICAL'
                    message = f'{kpi.name} is critically above target ({current_value} vs {target_value})'
                elif current_value >= warning_threshold:
                    severity = 'WARNING'
                    message = f'{kpi.name} is above warning threshold ({current_value} vs {target_value})'
                else:
                    return  # No alert needed

            # Create the alert
            KPIAlert.objects.get_or_create(
                kpi=kpi,
                alert_type='THRESHOLD',
                severity=severity,
                defaults={
                    'message': message,
                    'status': 'ACTIVE',
                    'triggered_by': kpi.created_by,
                    'threshold_value': Decimal(str(critical_threshold if severity == 'CRITICAL' else warning_threshold)),
                    'actual_value': latest_value.value,
                    'alert_config': {
                        'threshold_type': 'absolute',
                        'comparison': 'less_than' if kpi.trend_direction == 'UP' else 'greater_than'
                    }
                }
            )
