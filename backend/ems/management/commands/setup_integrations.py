from django.core.management.base import BaseCommand
from django.utils import timezone
from ems.models import (
    APIKey, ExternalService, WebhookEndpoint, Employee
)
import secrets
import hashlib
from datetime import timedelta


class Command(BaseCommand):
    help = 'Set up sample integration and API management data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-api-keys',
            action='store_true',
            help='Create sample API keys'
        )
        parser.add_argument(
            '--create-services',
            action='store_true',
            help='Create sample external services'
        )
        parser.add_argument(
            '--create-webhooks',
            action='store_true',
            help='Create sample webhook endpoints'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up integration and API management data...'))
        
        if options['create_api_keys'] or not any([
            options['create_api_keys'], options['create_services'], options['create_webhooks']
        ]):
            self.create_api_keys()
        
        if options['create_services'] or not any([
            options['create_api_keys'], options['create_services'], options['create_webhooks']
        ]):
            self.create_external_services()
        
        if options['create_webhooks'] or not any([
            options['create_api_keys'], options['create_services'], options['create_webhooks']
        ]):
            self.create_webhook_endpoints()
        
        self.stdout.write(self.style.SUCCESS('\n=== INTEGRATION & API MANAGEMENT SETUP COMPLETE ==='))

    def create_api_keys(self):
        """Create sample API keys"""
        try:
            admin_employee = Employee.objects.filter(user__is_superuser=True).first()
        except Employee.DoesNotExist:
            admin_employee = None
        
        api_keys_data = [
            {
                'name': 'Mobile App API Key',
                'description': 'API key for the mobile application to access EMS data',
                'key_type': 'INTERNAL',
                'scopes': ['read:employees', 'read:attendance', 'write:attendance', 'read:payroll'],
                'rate_limit': 5000,
                'expires_at': timezone.now() + timedelta(days=365)
            },
            {
                'name': 'Third-party Integration Key',
                'description': 'API key for external HR system integration',
                'key_type': 'EXTERNAL',
                'scopes': ['read:employees', 'write:employees', 'read:departments'],
                'allowed_ips': ['*************', '*********'],
                'rate_limit': 2000,
                'expires_at': timezone.now() + timedelta(days=180)
            },
            {
                'name': 'Webhook Service Key',
                'description': 'API key for webhook service authentication',
                'key_type': 'WEBHOOK',
                'scopes': ['webhook:send', 'webhook:receive'],
                'rate_limit': 10000
            },
            {
                'name': 'Analytics Integration',
                'description': 'API key for business intelligence and analytics platform',
                'key_type': 'INTEGRATION',
                'scopes': ['read:analytics', 'read:reports', 'read:kpi'],
                'rate_limit': 1000,
                'expires_at': timezone.now() + timedelta(days=90)
            }
        ]
        
        created_keys = []
        for key_data in api_keys_data:
            # Generate unique key ID and secret
            key_id = f"ems_{secrets.token_urlsafe(16)}"
            key_secret = secrets.token_urlsafe(32)
            key_secret_hash = hashlib.sha256(key_secret.encode()).hexdigest()
            
            api_key, created = APIKey.objects.get_or_create(
                name=key_data['name'],
                defaults={
                    **key_data,
                    'key_id': key_id,
                    'key_secret': key_secret_hash,
                    'created_by': admin_employee
                }
            )
            
            if created:
                created_keys.append(api_key)
                self.stdout.write(f'Created API key: {api_key.name} (ID: {key_id})')
                # In a real scenario, you'd securely store or display the plain secret
                self.stdout.write(f'  Secret: {key_secret[:8]}...')
            else:
                self.stdout.write(f'API key already exists: {api_key.name}')
        
        self.stdout.write(f'API keys setup complete. Created: {len(created_keys)}')

    def create_external_services(self):
        """Create sample external services"""
        try:
            admin_employee = Employee.objects.filter(user__is_superuser=True).first()
        except Employee.DoesNotExist:
            admin_employee = None
        
        services_data = [
            {
                'name': 'PayPal Payment Gateway',
                'service_type': 'PAYMENT',
                'description': 'PayPal integration for processing employee expense reimbursements',
                'base_url': 'https://api.paypal.com',
                'api_version': 'v2',
                'authentication_type': 'OAUTH2',
                'config': {
                    'environment': 'sandbox',
                    'client_id': 'demo_client_id',
                    'webhook_id': 'demo_webhook_id'
                },
                'headers': {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                'health_check_url': 'https://api.paypal.com/v1/oauth2/token',
                'timeout': 30
            },
            {
                'name': 'Saudi Post Shipping',
                'service_type': 'SHIPPING',
                'description': 'Saudi Post API for shipping employee documents and packages',
                'base_url': 'https://api.saudipost.com.sa',
                'api_version': 'v1',
                'authentication_type': 'API_KEY',
                'config': {
                    'api_key_header': 'X-API-Key',
                    'rate_limit': 1000,
                    'supported_services': ['domestic', 'international']
                },
                'headers': {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'Accept-Language': 'ar,en'
                },
                'timeout': 45
            },
            {
                'name': 'Microsoft Graph API',
                'service_type': 'EMAIL',
                'description': 'Microsoft Graph API for email notifications and calendar integration',
                'base_url': 'https://graph.microsoft.com',
                'api_version': 'v1.0',
                'authentication_type': 'OAUTH2',
                'config': {
                    'tenant_id': 'demo_tenant_id',
                    'client_id': 'demo_client_id',
                    'scopes': ['Mail.Send', 'Calendars.ReadWrite']
                },
                'headers': {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                'health_check_url': 'https://graph.microsoft.com/v1.0/me',
                'timeout': 30
            },
            {
                'name': 'Twilio SMS Service',
                'service_type': 'SMS',
                'description': 'Twilio API for SMS notifications to employees',
                'base_url': 'https://api.twilio.com',
                'api_version': '2010-04-01',
                'authentication_type': 'BASIC_AUTH',
                'config': {
                    'account_sid': 'demo_account_sid',
                    'from_number': '+**********'
                },
                'headers': {
                    'Accept': 'application/json',
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                'timeout': 30
            },
            {
                'name': 'AWS S3 Storage',
                'service_type': 'STORAGE',
                'description': 'Amazon S3 for storing employee documents and backups',
                'base_url': 'https://s3.amazonaws.com',
                'api_version': '2006-03-01',
                'authentication_type': 'AWS_SIGNATURE',
                'config': {
                    'region': 'us-east-1',
                    'bucket': 'ems-documents',
                    'access_key_id': 'demo_access_key'
                },
                'headers': {
                    'Accept': 'application/json'
                },
                'timeout': 60
            }
        ]
        
        created_services = []
        for service_data in services_data:
            service, created = ExternalService.objects.get_or_create(
                name=service_data['name'],
                defaults={**service_data, 'created_by': admin_employee}
            )
            
            if created:
                created_services.append(service)
                self.stdout.write(f'Created external service: {service.name} ({service.service_type})')
            else:
                self.stdout.write(f'External service already exists: {service.name}')
        
        self.stdout.write(f'External services setup complete. Created: {len(created_services)}')

    def create_webhook_endpoints(self):
        """Create sample webhook endpoints"""
        try:
            admin_employee = Employee.objects.filter(user__is_superuser=True).first()
        except Employee.DoesNotExist:
            admin_employee = None
        
        webhooks_data = [
            {
                'name': 'Payment Notification Webhook',
                'description': 'Webhook for receiving payment notifications from PayPal',
                'event_types': ['PAYMENT_RECEIVED', 'PAYMENT_FAILED'],
                'url': 'https://your-domain.com/webhooks/payments',
                'method': 'POST',
                'headers': {
                    'Content-Type': 'application/json',
                    'User-Agent': 'EMS-Webhook/1.0'
                },
                'max_retries': 3,
                'retry_delay': 300,
                'timeout': 30
            },
            {
                'name': 'Employee Update Webhook',
                'description': 'Webhook for notifying external HR system of employee changes',
                'event_types': ['CUSTOMER_CREATED', 'ASSET_UPDATED'],
                'url': 'https://hr-system.company.com/api/webhooks/employees',
                'method': 'POST',
                'headers': {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer demo_token'
                },
                'max_retries': 5,
                'retry_delay': 600,
                'timeout': 45
            },
            {
                'name': 'Maintenance Alert Webhook',
                'description': 'Webhook for sending maintenance due notifications',
                'event_types': ['MAINTENANCE_DUE', 'ASSET_UPDATED'],
                'url': 'https://maintenance.company.com/api/alerts',
                'method': 'POST',
                'headers': {
                    'Content-Type': 'application/json',
                    'X-API-Key': 'demo_maintenance_key'
                },
                'max_retries': 3,
                'retry_delay': 180,
                'timeout': 30
            },
            {
                'name': 'Custom Integration Webhook',
                'description': 'Generic webhook for custom integrations',
                'event_types': ['CUSTOM'],
                'url': 'https://integration.company.com/webhook',
                'method': 'POST',
                'headers': {
                    'Content-Type': 'application/json'
                },
                'max_retries': 2,
                'retry_delay': 120,
                'timeout': 20
            }
        ]
        
        created_webhooks = []
        for webhook_data in webhooks_data:
            # Generate webhook secret
            webhook_secret = secrets.token_urlsafe(32)
            
            webhook, created = WebhookEndpoint.objects.get_or_create(
                name=webhook_data['name'],
                defaults={
                    **webhook_data,
                    'secret': webhook_secret,
                    'created_by': admin_employee
                }
            )
            
            if created:
                created_webhooks.append(webhook)
                self.stdout.write(f'Created webhook: {webhook.name}')
                self.stdout.write(f'  URL: {webhook.url}')
                self.stdout.write(f'  Secret: {webhook_secret[:8]}...')
            else:
                self.stdout.write(f'Webhook already exists: {webhook.name}')
        
        self.stdout.write(f'Webhook endpoints setup complete. Created: {len(created_webhooks)}')
