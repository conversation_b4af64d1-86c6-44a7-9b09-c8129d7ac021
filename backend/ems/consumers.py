"""
WebSocket consumers for real-time EMS features.

This module provides WebSocket consumers for:
- Real-time KPI updates
- Live notifications
- Dashboard streaming
- Enterprise monitoring

All consumers implement enterprise-grade security and performance standards.
"""

import json
import logging
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth.models import AnonymousUser
from datetime import datetime

logger = logging.getLogger(__name__)


class KPIConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for real-time KPI updates.
    
    Features:
    - Real-time KPI value updates
    - Automatic calculation notifications
    - Enterprise security validation
    - Performance monitoring
    """
    
    async def connect(self):
        """Handle WebSocket connection"""
        # Check authentication
        if self.scope["user"] == AnonymousUser():
            await self.close()
            return
            
        self.room_group_name = 'kpi_updates'
        
        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        logger.info(f"KPI WebSocket connected for user: {self.scope['user'].username}")
        
        # Send initial connection confirmation
        await self.send(text_data=json.dumps({
            'type': 'connection_established',
            'message': 'Connected to KPI real-time updates',
            'timestamp': datetime.now().isoformat()
        }))

    async def disconnect(self, close_code):
        """Handle WebSocket disconnection"""
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
        logger.info(f"KPI WebSocket disconnected: {close_code}")

    async def receive(self, text_data):
        """Handle messages from WebSocket"""
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')
            
            if message_type == 'ping':
                # Respond to ping with pong
                await self.send(text_data=json.dumps({
                    'type': 'pong',
                    'timestamp': datetime.now().isoformat()
                }))
            elif message_type == 'subscribe_kpi':
                # Handle KPI subscription
                kpi_id = text_data_json.get('kpi_id')
                await self.handle_kpi_subscription(kpi_id)
                
        except json.JSONDecodeError:
            logger.error("Invalid JSON received in KPI WebSocket")
        except Exception as e:
            logger.error(f"Error in KPI WebSocket receive: {str(e)}")

    async def handle_kpi_subscription(self, kpi_id):
        """Handle KPI subscription request"""
        # Validate KPI access permissions
        has_access = await self.check_kpi_access(kpi_id)
        if has_access:
            await self.send(text_data=json.dumps({
                'type': 'subscription_confirmed',
                'kpi_id': kpi_id,
                'timestamp': datetime.now().isoformat()
            }))
        else:
            await self.send(text_data=json.dumps({
                'type': 'subscription_denied',
                'kpi_id': kpi_id,
                'reason': 'Access denied',
                'timestamp': datetime.now().isoformat()
            }))

    @database_sync_to_async
    def check_kpi_access(self, kpi_id):
        """Check if user has access to specific KPI"""
        try:
            from .models import KPI
            kpi = KPI.objects.get(id=kpi_id)
            # Check role-based access
            user_profile = getattr(self.scope['user'], 'userprofile', None)
            if user_profile and user_profile.role:
                return kpi.visible_to_roles.filter(id=user_profile.role.id).exists()
            return True  # Default access if no role restrictions
        except:
            return False

    # Handlers for different message types
    async def kpi_update(self, event):
        """Send KPI update to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'kpi_update',
            'kpi_id': event['kpi_id'],
            'value': event['value'],
            'timestamp': event['timestamp'],
            'calculation_method': event.get('calculation_method', 'automatic')
        }))

    async def kpi_alert(self, event):
        """Send KPI alert to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'kpi_alert',
            'kpi_id': event['kpi_id'],
            'alert_type': event['alert_type'],
            'message': event['message'],
            'severity': event['severity'],
            'timestamp': event['timestamp']
        }))


class NotificationConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for real-time notifications"""
    
    async def connect(self):
        if self.scope["user"] == AnonymousUser():
            await self.close()
            return
            
        self.room_group_name = f'notifications_{self.scope["user"].id}'
        
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        logger.info(f"Notification WebSocket connected for user: {self.scope['user'].username}")

    async def disconnect(self, close_code):
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )

    async def notification_message(self, event):
        """Send notification to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'notification',
            'id': event['id'],
            'title': event['title'],
            'message': event['message'],
            'notification_type': event['notification_type'],
            'timestamp': event['timestamp']
        }))


class DashboardConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for dashboard real-time updates"""
    
    async def connect(self):
        if self.scope["user"] == AnonymousUser():
            await self.close()
            return
            
        self.room_group_name = 'dashboard_updates'
        
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        logger.info(f"Dashboard WebSocket connected for user: {self.scope['user'].username}")

    async def disconnect(self, close_code):
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )

    async def dashboard_update(self, event):
        """Send dashboard update to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'dashboard_update',
            'section': event['section'],
            'data': event['data'],
            'timestamp': event['timestamp']
        }))


class MonitoringConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for system monitoring"""
    
    async def connect(self):
        if self.scope["user"] == AnonymousUser():
            await self.close()
            return
            
        # Only allow admin users for monitoring
        if not self.scope["user"].is_staff:
            await self.close()
            return
            
        self.room_group_name = 'system_monitoring'
        
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        logger.info(f"Monitoring WebSocket connected for admin: {self.scope['user'].username}")

    async def disconnect(self, close_code):
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )

    async def system_alert(self, event):
        """Send system alert to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'system_alert',
            'alert_type': event['alert_type'],
            'message': event['message'],
            'severity': event['severity'],
            'timestamp': event['timestamp']
        }))
