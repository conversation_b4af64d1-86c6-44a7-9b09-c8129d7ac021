"""
Real-time KPI Update Signals with WebSocket Broadcasting
Automatically updates KPIs when underlying business data changes and
broadcasts updates to connected clients in real-time.

This follows enterprise BI best practices where KPIs are updated
in real-time as business events occur and immediately reflected
in dashboards and monitoring systems.
"""

from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.utils import timezone
from datetime import timed<PERSON>ta
import logging
import json

from .models import (
    Employee, Project, Task, CustomerInvoice, Expense,
    Attendance, LeaveRequest, Payment, KPI, KPIValue
)
from .kpi_engine import KPICalculationEngine

logger = logging.getLogger(__name__)

# WebSocket broadcasting functions
def broadcast_kpi_update(kpi_id, value, timestamp, calculation_method='automatic'):
    """Broadcast KPI update via WebSocket to all connected clients"""
    try:
        from channels.layers import get_channel_layer
        from asgiref.sync import async_to_sync

        channel_layer = get_channel_layer()
        if channel_layer:
            async_to_sync(channel_layer.group_send)(
                'kpi_updates',
                {
                    'type': 'kpi_update',
                    'kpi_id': str(kpi_id),
                    'value': float(value),
                    'timestamp': timestamp.isoformat(),
                    'calculation_method': calculation_method
                }
            )
            logger.info(f"Broadcasted KPI update for {kpi_id}: {value}")
    except Exception as e:
        logger.error(f"Failed to broadcast KPI update: {str(e)}")

def broadcast_kpi_alert(kpi_id, alert_type, message, severity='medium'):
    """Broadcast KPI alert via WebSocket"""
    try:
        from channels.layers import get_channel_layer
        from asgiref.sync import async_to_sync

        channel_layer = get_channel_layer()
        if channel_layer:
            async_to_sync(channel_layer.group_send)(
                'kpi_updates',
                {
                    'type': 'kpi_alert',
                    'kpi_id': str(kpi_id),
                    'alert_type': alert_type,
                    'message': message,
                    'severity': severity,
                    'timestamp': timezone.now().isoformat()
                }
            )
            logger.info(f"Broadcasted KPI alert for {kpi_id}: {alert_type}")
    except Exception as e:
        logger.error(f"Failed to broadcast KPI alert: {str(e)}")


class KPIUpdateService:
    """
    Service to handle real-time KPI updates when business data changes.
    """
    
    def __init__(self):
        self.engine = KPICalculationEngine()
        self.update_batch_size = 10  # Batch updates to avoid performance issues
        
    def update_related_kpis(self, model_name: str, instance_date: timezone.datetime = None):
        """
        Update KPIs that are affected by changes to a specific model.
        
        Args:
            model_name: Name of the model that changed
            instance_date: Date of the changed instance (for period calculation)
        """
        if not instance_date:
            instance_date = timezone.now()
            
        # Define which KPIs are affected by each model
        model_kpi_mapping = {
            'Employee': [
                'EMPLOYEE_TURNOVER_RATE',
                'AVERAGE_ATTENDANCE_RATE', 
                'EMPLOYEE_PRODUCTIVITY_SCORE',
                'RESOURCE_UTILIZATION_RATE'
            ],
            'CustomerInvoice': [
                'MONTHLY_REVENUE',
                'PROFIT_MARGIN',
                'AVERAGE_INVOICE_VALUE',
                'REVENUE_GROWTH_RATE',
                'CUSTOMER_ACQUISITION_RATE',
                'AVERAGE_DEAL_SIZE'
            ],
            'Expense': [
                'PROFIT_MARGIN',
                'CASH_FLOW'
            ],
            'Payment': [
                'CASH_FLOW'
            ],
            'Project': [
                'PROJECT_COMPLETION_RATE',
                'AVERAGE_PROJECT_DURATION',
                'PROJECT_BUDGET_VARIANCE'
            ],
            'Task': [
                'TASK_COMPLETION_RATE',
                'EMPLOYEE_PRODUCTIVITY_SCORE',
                'RESOURCE_UTILIZATION_RATE'
            ],
            'Attendance': [
                'AVERAGE_ATTENDANCE_RATE',
                'EMPLOYEE_SATISFACTION_SCORE'
            ],
            'LeaveRequest': [
                'EMPLOYEE_SATISFACTION_SCORE'
            ]
        }
        
        affected_methods = model_kpi_mapping.get(model_name, [])
        if not affected_methods:
            return
            
        # Get KPIs that need updating
        kpis_to_update = KPI.objects.filter(
            status='ACTIVE',
            calculation_method__in=affected_methods
        )
        
        if not kpis_to_update.exists():
            return
            
        # Calculate period for the update (current month)
        period_start = instance_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        period_end = timezone.now()
        
        updated_count = 0
        for kpi in kpis_to_update:
            try:
                calculated_value = self.engine.calculate_kpi(kpi, period_start, period_end)
                
                if calculated_value is not None:
                    # Update KPI current value
                    kpi.current_value = calculated_value
                    kpi.last_updated = timezone.now()
                    kpi.save(update_fields=['current_value', 'last_updated'])
                    
                    # Create/update KPI value record
                    from .models import KPIValue
                    KPIValue.objects.update_or_create(
                        kpi=kpi,
                        period_start=period_start,
                        period_end=period_end,
                        defaults={
                            'value': calculated_value,
                            'recorded_by': kpi.created_by,
                            'is_estimated': False,
                            'confidence_level': 100.0,
                            'data_quality_score': 100.0,
                            'notes': f'Auto-updated due to {model_name} change',
                            'source_data': {
                                'trigger': f'{model_name}_change',
                                'engine': 'KPICalculationEngine',
                                'timestamp': timezone.now().isoformat()
                            }
                        }
                    )
                    
                    updated_count += 1
                    logger.info(f"Updated KPI {kpi.name} due to {model_name} change: {calculated_value}")
                    
            except Exception as e:
                logger.error(f"Error updating KPI {kpi.name} due to {model_name} change: {str(e)}")
                
        if updated_count > 0:
            logger.info(f"Updated {updated_count} KPIs due to {model_name} change")


# Initialize the update service
kpi_update_service = KPIUpdateService()


# Signal handlers for real-time KPI updates
@receiver(post_save, sender=Employee)
def update_kpis_on_employee_change(sender, instance, created, **kwargs):
    """Update KPIs when employee data changes"""
    kpi_update_service.update_related_kpis('Employee', instance.updated_at)


@receiver(post_delete, sender=Employee)
def update_kpis_on_employee_delete(sender, instance, **kwargs):
    """Update KPIs when employee is deleted"""
    kpi_update_service.update_related_kpis('Employee')


@receiver(post_save, sender=CustomerInvoice)
def update_kpis_on_invoice_change(sender, instance, created, **kwargs):
    """Update KPIs when invoice data changes"""
    kpi_update_service.update_related_kpis('CustomerInvoice', instance.invoice_date)


@receiver(post_save, sender=Expense)
def update_kpis_on_expense_change(sender, instance, created, **kwargs):
    """Update KPIs when expense data changes"""
    kpi_update_service.update_related_kpis('Expense', instance.expense_date)


@receiver(post_save, sender=Payment)
def update_kpis_on_payment_change(sender, instance, created, **kwargs):
    """Update KPIs when payment data changes"""
    kpi_update_service.update_related_kpis('Payment', instance.payment_date)


@receiver(post_save, sender=Project)
def update_kpis_on_project_change(sender, instance, created, **kwargs):
    """Update KPIs when project data changes"""
    # Use actual_end_date if available, otherwise use updated_at
    update_date = instance.actual_end_date or instance.updated_at.date()
    kpi_update_service.update_related_kpis('Project', timezone.datetime.combine(update_date, timezone.datetime.min.time()))


@receiver(post_save, sender=Task)
def update_kpis_on_task_change(sender, instance, created, **kwargs):
    """Update KPIs when task data changes"""
    # Use completion_date if available, otherwise use updated_at
    update_date = instance.completion_date or instance.updated_at
    kpi_update_service.update_related_kpis('Task', update_date)


@receiver(post_save, sender=Attendance)
def update_kpis_on_attendance_change(sender, instance, created, **kwargs):
    """Update KPIs when attendance data changes"""
    kpi_update_service.update_related_kpis('Attendance', timezone.datetime.combine(instance.date, timezone.datetime.min.time()))


@receiver(post_save, sender=LeaveRequest)
def update_kpis_on_leave_change(sender, instance, created, **kwargs):
    """Update KPIs when leave request data changes"""
    kpi_update_service.update_related_kpis('LeaveRequest', timezone.datetime.combine(instance.start_date, timezone.datetime.min.time()))


# Batch update functionality for performance
class BatchKPIUpdater:
    """
    Handles batch KPI updates to improve performance when processing
    large amounts of data changes.
    """
    
    def __init__(self):
        self.pending_updates = set()
        self.last_batch_time = timezone.now()
        self.batch_interval = timedelta(minutes=5)  # Batch updates every 5 minutes
        
    def queue_update(self, model_name: str):
        """Queue a model for KPI update"""
        self.pending_updates.add(model_name)
        
    def process_batch_updates(self):
        """Process all queued updates"""
        if not self.pending_updates:
            return
            
        current_time = timezone.now()
        if current_time - self.last_batch_time < self.batch_interval:
            return  # Not time for batch update yet
            
        logger.info(f"Processing batch KPI updates for models: {self.pending_updates}")
        
        for model_name in self.pending_updates:
            kpi_update_service.update_related_kpis(model_name)
            
        self.pending_updates.clear()
        self.last_batch_time = current_time
        
        logger.info("Batch KPI updates completed")


# Global batch updater instance
batch_updater = BatchKPIUpdater()


# WebSocket broadcasting signal handlers
@receiver(post_save, sender=KPIValue)
def broadcast_kpi_value_update(sender, instance, created, **kwargs):
    """Broadcast KPI value updates via WebSocket when new values are calculated"""
    if created:
        # Broadcast the new KPI value to all connected clients
        broadcast_kpi_update(
            kpi_id=instance.kpi.id,
            value=instance.value,
            timestamp=instance.recorded_at,
            calculation_method='automatic'
        )

        # Check if this triggers any alerts
        kpi = instance.kpi
        if kpi.warning_threshold or kpi.critical_threshold:
            current_value = float(instance.value)

            # Check critical threshold
            if kpi.critical_threshold and current_value <= float(kpi.critical_threshold):
                broadcast_kpi_alert(
                    kpi_id=kpi.id,
                    alert_type='critical_threshold',
                    message=f'KPI {kpi.name} has reached critical threshold: {current_value}',
                    severity='critical'
                )
            # Check warning threshold
            elif kpi.warning_threshold and current_value <= float(kpi.warning_threshold):
                broadcast_kpi_alert(
                    kpi_id=kpi.id,
                    alert_type='warning_threshold',
                    message=f'KPI {kpi.name} has reached warning threshold: {current_value}',
                    severity='warning'
                )


@receiver(post_save, sender=KPI)
def broadcast_kpi_config_update(sender, instance, created, **kwargs):
    """Broadcast KPI configuration changes"""
    if not created:  # Only for updates, not new KPIs
        try:
            from channels.layers import get_channel_layer
            from asgiref.sync import async_to_sync

            channel_layer = get_channel_layer()
            if channel_layer:
                async_to_sync(channel_layer.group_send)(
                    'kpi_updates',
                    {
                        'type': 'kpi_config_update',
                        'kpi_id': str(instance.id),
                        'name': instance.name,
                        'status': instance.status,
                        'timestamp': timezone.now().isoformat()
                    }
                )
        except Exception as e:
            logger.error(f"Failed to broadcast KPI config update: {str(e)}")
