from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import uuid
from django.utils import timezone
from datetime import timedelta
import json


class Currency(models.Model):
    """
    Currency model for multi-currency support
    """
    code = models.CharField(max_length=3, unique=True, help_text="ISO 4217 currency code (e.g., USD, EUR, SAR)")
    name = models.CharField(max_length=100, help_text="Full currency name")
    symbol = models.CharField(max_length=10, help_text="Currency symbol (e.g., $, €, ﷼)")
    decimal_places = models.PositiveIntegerField(default=2, help_text="Number of decimal places")
    is_base_currency = models.BooleanField(default=False, help_text="Is this the base currency for the system?")
    is_active = models.BooleanField(default=True, help_text="Is this currency active?")

    # Display settings
    symbol_position = models.Char<PERSON>ield(
        max_length=10,
        choices=[
            ('before', 'Before amount (e.g., $100)'),
            ('after', 'After amount (e.g., 100$)'),
        ],
        default='before'
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Currencies"
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"

    def save(self, *args, **kwargs):
        # Ensure only one base currency exists
        if self.is_base_currency:
            Currency.objects.filter(is_base_currency=True).exclude(pk=self.pk).update(is_base_currency=False)
        super().save(*args, **kwargs)


class ExchangeRate(models.Model):
    """
    Exchange rate model for currency conversions
    """
    from_currency = models.ForeignKey(Currency, on_delete=models.CASCADE, related_name='rates_from')
    to_currency = models.ForeignKey(Currency, on_delete=models.CASCADE, related_name='rates_to')
    rate = models.DecimalField(max_digits=15, decimal_places=6, help_text="Exchange rate from source to target currency")
    effective_date = models.DateField(help_text="Date when this rate becomes effective")
    source = models.CharField(
        max_length=50,
        choices=[
            ('manual', 'Manual Entry'),
            ('api', 'API Feed'),
            ('bank', 'Bank Rate'),
            ('central_bank', 'Central Bank Rate'),
        ],
        default='manual'
    )
    is_active = models.BooleanField(default=True)

    # Metadata
    created_by = models.ForeignKey('Employee', on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['from_currency', 'to_currency', 'effective_date']
        ordering = ['-effective_date', 'from_currency__code', 'to_currency__code']

    def __str__(self):
        return f"{self.from_currency.code} to {self.to_currency.code}: {self.rate} ({self.effective_date})"

    @classmethod
    def get_rate(cls, from_currency, to_currency, date=None):
        """Get the most recent exchange rate for the given currencies and date"""
        from django.utils import timezone

        if date is None:
            date = timezone.now().date()

        # If same currency, return 1
        if from_currency == to_currency:
            return 1

        # Try direct rate
        try:
            rate = cls.objects.filter(
                from_currency=from_currency,
                to_currency=to_currency,
                effective_date__lte=date,
                is_active=True
            ).order_by('-effective_date').first()

            if rate:
                return rate.rate
        except cls.DoesNotExist:
            pass

        # Try inverse rate
        try:
            rate = cls.objects.filter(
                from_currency=to_currency,
                to_currency=from_currency,
                effective_date__lte=date,
                is_active=True
            ).order_by('-effective_date').first()

            if rate and rate.rate != 0:
                return 1 / rate.rate
        except cls.DoesNotExist:
            pass

        # If no rate found, raise exception
        raise ValueError(f"No exchange rate found for {from_currency.code} to {to_currency.code} on {date}")


class Department(models.Model):
    name = models.CharField(max_length=100)
    name_ar = models.CharField(max_length=100, help_text="Arabic name")
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True, help_text="Arabic description")
    manager = models.ForeignKey('Employee', on_delete=models.SET_NULL, null=True, blank=True, related_name='managed_departments')
    budget_amount = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    location = models.CharField(max_length=200, blank=True)
    phone = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']

# User Roles and Permissions
class Role(models.Model):
    ROLE_TYPES = [
        ('SUPERADMIN', 'Super Administrator'),
        ('ADMIN', 'Administrator'),
        ('HR_MANAGER', 'HR Manager'),
        ('DEPARTMENT_MANAGER', 'Department Manager'),
        ('PROJECT_MANAGER', 'Project Manager'),
        ('FINANCE_MANAGER', 'Finance Manager'),
        ('EMPLOYEE', 'Employee'),
        ('INTERN', 'Intern'),
    ]

    name = models.CharField(max_length=50, choices=ROLE_TYPES, unique=True)
    name_ar = models.CharField(max_length=50)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)
    permissions = models.JSONField(default=dict)  # Store permissions as JSON
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.get_name_display()

class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    role = models.ForeignKey(Role, on_delete=models.SET_NULL, null=True, blank=True)
    avatar = models.ImageField(upload_to='avatars/', null=True, blank=True)
    phone = models.CharField(max_length=20, blank=True)
    address = models.TextField(blank=True)
    date_of_birth = models.DateField(null=True, blank=True)
    emergency_contact_name = models.CharField(max_length=100, blank=True)
    emergency_contact_phone = models.CharField(max_length=20, blank=True)
    preferred_language = models.CharField(max_length=2, choices=[('ar', 'Arabic'), ('en', 'English')], default='ar')
    timezone = models.CharField(max_length=50, default='Asia/Riyadh')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.role}"

class Employee(models.Model):
    GENDER_CHOICES = [
        ('M', 'Male'),
        ('F', 'Female'),
    ]

    EMPLOYMENT_STATUS = [
        ('FULL_TIME', 'Full Time'),
        ('PART_TIME', 'Part Time'),
        ('CONTRACT', 'Contract'),
        ('INTERN', 'Intern'),
        ('CONSULTANT', 'Consultant'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE)
    employee_id = models.CharField(max_length=20, unique=True)
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)
    position = models.CharField(max_length=100)
    position_ar = models.CharField(max_length=100, help_text="Arabic position")
    # Arabic name fields
    first_name_ar = models.CharField(max_length=150, blank=True, help_text="Arabic first name")
    last_name_ar = models.CharField(max_length=150, blank=True, help_text="Arabic last name")
    phone = models.CharField(max_length=20, blank=True)
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES)
    hire_date = models.DateField()
    salary = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    employment_status = models.CharField(max_length=20, choices=EMPLOYMENT_STATUS, default='FULL_TIME')
    manager = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='subordinates')
    work_location = models.CharField(max_length=200, blank=True)
    contract_end_date = models.DateField(null=True, blank=True)
    probation_end_date = models.DateField(null=True, blank=True)
    national_id = models.CharField(max_length=20, blank=True)
    passport_number = models.CharField(max_length=20, blank=True)
    bank_account = models.CharField(max_length=50, blank=True)
    emergency_contact = models.CharField(max_length=100, blank=True)
    emergency_phone = models.CharField(max_length=20, blank=True)
    skills = models.TextField(blank=True)
    education = models.TextField(blank=True)
    certifications = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='created_employees', help_text="Employee who created this record")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.employee_id} - {self.user.get_full_name()}"

    class Meta:
        ordering = ['employee_id']


class EmployeeActivation(models.Model):
    """
    Model to handle employee account activation process with admin approval
    """
    APPROVAL_STATUS_CHOICES = [
        ('PENDING', 'Pending Admin Approval'),
        ('APPROVED', 'Approved by Admin'),
        ('REJECTED', 'Rejected by Admin'),
    ]

    employee = models.OneToOneField(Employee, on_delete=models.CASCADE, related_name='activation')
    activation_token = models.UUIDField(default=uuid.uuid4, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    is_activated = models.BooleanField(default=False)
    activated_at = models.DateTimeField(null=True, blank=True)
    email_sent = models.BooleanField(default=False)
    email_sent_at = models.DateTimeField(null=True, blank=True)

    # New approval fields
    approval_status = models.CharField(max_length=20, choices=APPROVAL_STATUS_CHOICES, default='PENDING')
    approved_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_activations')
    approved_at = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True, help_text="Reason for rejection if applicable")

    def save(self, *args, **kwargs):
        if not self.expires_at:
            # Token expires in 7 days
            self.expires_at = timezone.now() + timedelta(days=7)
        super().save(*args, **kwargs)

    def is_expired(self):
        return timezone.now() > self.expires_at

    def approve(self, approved_by_employee):
        """Approve the employee activation and send activation email"""
        self.approval_status = 'APPROVED'
        self.approved_by = approved_by_employee
        self.approved_at = timezone.now()
        self.save()

        # Send activation email after approval
        try:
            from .email_service import EmailService
            email_sent = EmailService.send_employee_activation_email(self.employee, str(self.activation_token))
            if email_sent:
                self.email_sent = True
                self.email_sent_at = timezone.now()
                self.save()
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to send activation email after approval for employee {self.employee.employee_id}: {str(e)}")

    def reject(self, approved_by_employee, reason=""):
        """Reject the employee activation"""
        self.approval_status = 'REJECTED'
        self.approved_by = approved_by_employee
        self.approved_at = timezone.now()
        self.rejection_reason = reason
        self.save()

        # Optionally send rejection email
        try:
            from .email_service import EmailService
            EmailService.send_employee_rejection_email(self.employee, reason)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to send rejection email for employee {self.employee.employee_id}: {str(e)}")

    def activate(self):
        """Mark the employee as activated (called when user completes activation)"""
        if self.approval_status != 'APPROVED':
            raise ValueError("Employee must be approved before activation")

        self.is_activated = True
        self.activated_at = timezone.now()
        # Set the user as active
        self.employee.user.is_active = True
        self.employee.user.save()
        self.save()

    def __str__(self):
        return f"Activation for {self.employee.user.get_full_name()}"

    class Meta:
        ordering = ['-created_at']


# Leave Management Models
class LeaveType(models.Model):
    name = models.CharField(max_length=50)
    name_ar = models.CharField(max_length=50)
    days_allowed = models.IntegerField()
    is_paid = models.BooleanField(default=True)
    requires_approval = models.BooleanField(default=True)
    carry_forward = models.BooleanField(default=False)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

class LeaveRequest(models.Model):
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
        ('CANCELLED', 'Cancelled'),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE)
    leave_type = models.ForeignKey(LeaveType, on_delete=models.CASCADE)
    start_date = models.DateField()
    end_date = models.DateField()
    days_requested = models.IntegerField()
    reason = models.TextField()
    reason_ar = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    approved_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_leaves')
    approval_date = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True)
    rejection_reason_ar = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.employee} - {self.leave_type} ({self.start_date} to {self.end_date})"

class Attendance(models.Model):
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE)
    date = models.DateField()
    check_in = models.TimeField(null=True, blank=True)
    check_out = models.TimeField(null=True, blank=True)
    break_start = models.TimeField(null=True, blank=True)
    break_end = models.TimeField(null=True, blank=True)
    total_hours = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)
    overtime_hours = models.DecimalField(max_digits=4, decimal_places=2, default=0)
    is_present = models.BooleanField(default=True)
    is_late = models.BooleanField(default=False)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['employee', 'date']
        ordering = ['-date']

    def __str__(self):
        return f"{self.employee} - {self.date}"

# Project Management Models
class Project(models.Model):
    STATUS_CHOICES = [
        ('PLANNING', 'Planning'),
        ('IN_PROGRESS', 'In Progress'),
        ('ON_HOLD', 'On Hold'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
    ]

    PRIORITY_CHOICES = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    ]

    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200)
    description = models.TextField()
    description_ar = models.TextField(blank=True)
    project_manager = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, related_name='managed_projects')
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)
    team_members = models.ManyToManyField(Employee, related_name='projects', blank=True)
    client = models.CharField(max_length=200, blank=True)
    budget_amount = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    start_date = models.DateField()
    end_date = models.DateField()
    actual_start_date = models.DateField(null=True, blank=True)
    actual_end_date = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PLANNING')
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='MEDIUM')
    progress_percentage = models.IntegerField(default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

class Task(models.Model):
    STATUS_CHOICES = [
        ('TODO', 'To Do'),
        ('IN_PROGRESS', 'In Progress'),
        ('REVIEW', 'Under Review'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
    ]

    PRIORITY_CHOICES = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('URGENT', 'Urgent'),
    ]

    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='tasks')
    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField()
    description_ar = models.TextField(blank=True)
    assigned_to = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, related_name='assigned_tasks')
    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='created_tasks')
    due_date = models.DateTimeField()
    start_date = models.DateTimeField(null=True, blank=True)
    completion_date = models.DateTimeField(null=True, blank=True)
    estimated_hours = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True)
    actual_hours = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='TODO')
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='MEDIUM')
    progress_percentage = models.IntegerField(default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])
    dependencies = models.ManyToManyField('self', blank=True, symmetrical=False)
    tags = models.CharField(max_length=500, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.project.name} - {self.title}"

# Financial Management Models

# Chart of Accounts and General Ledger
class AccountType(models.Model):
    """Account types for Chart of Accounts"""
    ACCOUNT_TYPES = [
        ('ASSET', 'Asset'),
        ('LIABILITY', 'Liability'),
        ('EQUITY', 'Equity'),
        ('REVENUE', 'Revenue'),
        ('EXPENSE', 'Expense'),
        ('COST_OF_GOODS_SOLD', 'Cost of Goods Sold'),
    ]

    name = models.CharField(max_length=50, choices=ACCOUNT_TYPES, unique=True)
    name_ar = models.CharField(max_length=50)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.get_name_display()

    class Meta:
        ordering = ['name']

class ChartOfAccounts(models.Model):
    """Chart of Accounts - Account structure"""
    account_code = models.CharField(max_length=20, unique=True, help_text="Account code (e.g., 1000, 1100)")
    account_name = models.CharField(max_length=200)
    account_name_ar = models.CharField(max_length=200)
    account_type = models.ForeignKey(AccountType, on_delete=models.CASCADE)
    parent_account = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='sub_accounts')
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    is_system_account = models.BooleanField(default=False, help_text="System-generated account")
    level = models.IntegerField(default=0, help_text="Account hierarchy level")

    # Financial settings
    allow_manual_entries = models.BooleanField(default=True)
    require_department = models.BooleanField(default=False)
    require_project = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.account_code} - {self.account_name}"

    def get_full_code(self):
        """Get full hierarchical account code"""
        if self.parent_account:
            return f"{self.parent_account.get_full_code()}.{self.account_code}"
        return self.account_code

    def get_balance(self, as_of_date=None):
        """Calculate account balance as of specific date"""
        from django.db.models import Sum
        if as_of_date is None:
            as_of_date = timezone.now().date()

        entries = JournalEntry.objects.filter(
            account=self,
            transaction_date__lte=as_of_date,
            is_posted=True
        )

        debit_total = entries.aggregate(Sum('debit_amount'))['debit_amount__sum'] or 0
        credit_total = entries.aggregate(Sum('credit_amount'))['credit_amount__sum'] or 0

        # Asset, Expense accounts: Debit increases balance
        if self.account_type.name in ['ASSET', 'EXPENSE', 'COST_OF_GOODS_SOLD']:
            return debit_total - credit_total
        # Liability, Equity, Revenue accounts: Credit increases balance
        else:
            return credit_total - debit_total

    class Meta:
        ordering = ['account_code']
        verbose_name_plural = "Chart of Accounts"

class FiscalYear(models.Model):
    """Fiscal Year management"""
    name = models.CharField(max_length=50)
    start_date = models.DateField()
    end_date = models.DateField()
    is_current = models.BooleanField(default=False)
    is_closed = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if self.is_current:
            # Ensure only one fiscal year is current
            FiscalYear.objects.filter(is_current=True).update(is_current=False)
        super().save(*args, **kwargs)

    class Meta:
        ordering = ['-start_date']

class JournalEntryBatch(models.Model):
    """Batch for grouping journal entries"""
    batch_number = models.CharField(max_length=50, unique=True)
    description = models.CharField(max_length=200)
    description_ar = models.CharField(max_length=200, blank=True)
    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    is_posted = models.BooleanField(default=False)
    posted_at = models.DateTimeField(null=True, blank=True)
    posted_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='posted_batches')

    def __str__(self):
        return f"{self.batch_number} - {self.description}"

    def get_total_debits(self):
        return self.journal_entries.aggregate(models.Sum('debit_amount'))['debit_amount__sum'] or 0

    def get_total_credits(self):
        return self.journal_entries.aggregate(models.Sum('credit_amount'))['credit_amount__sum'] or 0

    def is_balanced(self):
        return self.get_total_debits() == self.get_total_credits()

class JournalEntry(models.Model):
    """General Ledger Journal Entries"""
    batch = models.ForeignKey(JournalEntryBatch, on_delete=models.CASCADE, related_name='journal_entries')
    entry_number = models.CharField(max_length=50)
    account = models.ForeignKey(ChartOfAccounts, on_delete=models.CASCADE)
    transaction_date = models.DateField()
    description = models.CharField(max_length=200)
    description_ar = models.CharField(max_length=200, blank=True)

    # Amounts
    debit_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    credit_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    # Optional references
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)
    project = models.ForeignKey('Project', on_delete=models.SET_NULL, null=True, blank=True)
    employee = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)

    # Reference to source document
    source_document_type = models.CharField(max_length=50, blank=True, help_text="e.g., Invoice, Payment, Expense")
    source_document_id = models.CharField(max_length=50, blank=True)

    # Status
    is_posted = models.BooleanField(default=False)
    posted_at = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.entry_number} - {self.account.account_code} - {self.debit_amount or self.credit_amount}"

    def clean(self):
        from django.core.exceptions import ValidationError
        if self.debit_amount and self.credit_amount:
            raise ValidationError("Entry cannot have both debit and credit amounts")
        if not self.debit_amount and not self.credit_amount:
            raise ValidationError("Entry must have either debit or credit amount")

    class Meta:
        ordering = ['-transaction_date', 'entry_number']
        verbose_name_plural = "Journal Entries"

class Budget(models.Model):
    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, null=True, blank=True, related_name='budgets')
    project = models.ForeignKey(Project, on_delete=models.CASCADE, null=True, blank=True, related_name='budgets')
    fiscal_year = models.IntegerField()
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    allocated_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    spent_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    remaining_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    start_date = models.DateField()
    end_date = models.DateField()
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} - {self.fiscal_year}"

class Expense(models.Model):
    CATEGORY_CHOICES = [
        ('TRAVEL', 'Travel'),
        ('OFFICE_SUPPLIES', 'Office Supplies'),
        ('EQUIPMENT', 'Equipment'),
        ('SOFTWARE', 'Software'),
        ('TRAINING', 'Training'),
        ('MARKETING', 'Marketing'),
        ('UTILITIES', 'Utilities'),
        ('RENT', 'Rent'),
        ('OTHER', 'Other'),
    ]

    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
        ('PAID', 'Paid'),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE)
    budget = models.ForeignKey(Budget, on_delete=models.SET_NULL, null=True, blank=True)
    project = models.ForeignKey(Project, on_delete=models.SET_NULL, null=True, blank=True)
    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField()
    description_ar = models.TextField(blank=True)
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='SAR')
    expense_date = models.DateField()
    receipt_file = models.FileField(upload_to='receipts/', null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    approved_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_expenses')
    approval_date = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.title} - {self.amount} {self.currency}"

# Accounts Payable and Receivable Models
class Vendor(models.Model):
    """Vendor/Supplier master for AP"""
    vendor_code = models.CharField(max_length=20, unique=True)
    company_name = models.CharField(max_length=200)
    company_name_ar = models.CharField(max_length=200, blank=True)
    contact_person = models.CharField(max_length=100, blank=True)
    email = models.EmailField(blank=True)
    phone = models.CharField(max_length=20, blank=True)
    address = models.TextField(blank=True)
    address_ar = models.TextField(blank=True)

    # Financial details
    tax_id = models.CharField(max_length=50, blank=True)
    payment_terms = models.CharField(max_length=50, default='Net 30')
    credit_limit = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    currency = models.CharField(max_length=3, default='SAR')

    # Status
    is_active = models.BooleanField(default=True)
    is_approved = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.vendor_code} - {self.company_name}"

    def get_outstanding_balance(self):
        """Get total outstanding payable balance"""
        return self.vendor_invoices.filter(status__in=['PENDING', 'APPROVED']).aggregate(
            models.Sum('total_amount'))['total_amount__sum'] or 0

class VendorInvoice(models.Model):
    """Accounts Payable - Vendor Invoices"""
    STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('PENDING', 'Pending Approval'),
        ('APPROVED', 'Approved'),
        ('PAID', 'Paid'),
        ('CANCELLED', 'Cancelled'),
        ('OVERDUE', 'Overdue'),
    ]

    invoice_number = models.CharField(max_length=50, unique=True)
    vendor_invoice_number = models.CharField(max_length=50, help_text="Vendor's invoice number")
    vendor = models.ForeignKey(Vendor, on_delete=models.CASCADE, related_name='vendor_invoices')

    # Invoice details
    invoice_date = models.DateField()
    due_date = models.DateField()
    description = models.TextField()
    description_ar = models.TextField(blank=True)

    # Currency support
    currency = models.ForeignKey(Currency, on_delete=models.PROTECT, null=True, blank=True, help_text="Invoice currency")
    exchange_rate = models.DecimalField(max_digits=15, decimal_places=6, default=1, help_text="Exchange rate to base currency")

    # Amounts in invoice currency
    subtotal = models.DecimalField(max_digits=12, decimal_places=2)
    tax_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    discount_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    paid_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)

    # Amounts in base currency (calculated)
    subtotal_base = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    tax_amount_base = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    discount_amount_base = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    total_amount_base = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    paid_amount_base = models.DecimalField(max_digits=12, decimal_places=2, default=0)

    # References
    purchase_order = models.ForeignKey('PurchaseOrder', on_delete=models.SET_NULL, null=True, blank=True)
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)
    project = models.ForeignKey('Project', on_delete=models.SET_NULL, null=True, blank=True)

    # Approval workflow
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='DRAFT')
    approved_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_vendor_invoices')
    approval_date = models.DateTimeField(null=True, blank=True)

    # Attachments
    invoice_file = models.FileField(upload_to='vendor_invoices/', null=True, blank=True)

    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.invoice_number} - {self.vendor.company_name}"

    def get_remaining_balance(self):
        return self.total_amount - self.paid_amount

    def get_remaining_balance_base(self):
        """Get remaining balance in base currency"""
        return (self.total_amount_base or 0) - (self.paid_amount_base or 0)

    def is_overdue(self):
        return self.due_date < timezone.now().date() and self.status not in ['PAID', 'CANCELLED']

    def update_base_amounts(self):
        """Update base currency amounts using current exchange rate"""
        if self.currency and self.exchange_rate:
            self.subtotal_base = self.subtotal * self.exchange_rate
            self.tax_amount_base = self.tax_amount * self.exchange_rate
            self.discount_amount_base = self.discount_amount * self.exchange_rate
            self.total_amount_base = self.total_amount * self.exchange_rate
            self.paid_amount_base = self.paid_amount * self.exchange_rate

    def save(self, *args, **kwargs):
        # Set default currency to base currency if not specified
        if not self.currency:
            try:
                self.currency = Currency.objects.get(is_base_currency=True)
            except Currency.DoesNotExist:
                pass

        # Update exchange rate if currency is set
        if self.currency:
            try:
                base_currency = Currency.objects.get(is_base_currency=True)
                if self.currency != base_currency:
                    self.exchange_rate = ExchangeRate.get_rate(self.currency, base_currency, self.invoice_date)
                else:
                    self.exchange_rate = 1
            except (Currency.DoesNotExist, ValueError):
                # Keep existing exchange rate or default to 1
                if not self.exchange_rate:
                    self.exchange_rate = 1

        # Update base amounts
        self.update_base_amounts()

        super().save(*args, **kwargs)

    class Meta:
        ordering = ['-invoice_date']

class CustomerInvoice(models.Model):
    """Accounts Receivable - Customer Invoices"""
    STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('SENT', 'Sent'),
        ('PAID', 'Paid'),
        ('PARTIAL', 'Partially Paid'),
        ('OVERDUE', 'Overdue'),
        ('CANCELLED', 'Cancelled'),
    ]

    invoice_number = models.CharField(max_length=50, unique=True)
    customer = models.ForeignKey('Customer', on_delete=models.CASCADE, related_name='customer_invoices')

    # Invoice details
    invoice_date = models.DateField()
    due_date = models.DateField()
    description = models.TextField()
    description_ar = models.TextField(blank=True)

    # Currency support
    currency = models.ForeignKey(Currency, on_delete=models.PROTECT, null=True, blank=True, help_text="Invoice currency")
    exchange_rate = models.DecimalField(max_digits=15, decimal_places=6, default=1, help_text="Exchange rate to base currency")

    # Amounts in invoice currency
    subtotal = models.DecimalField(max_digits=12, decimal_places=2)
    tax_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    discount_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    paid_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)

    # Amounts in base currency (calculated)
    subtotal_base = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    tax_amount_base = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    discount_amount_base = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    total_amount_base = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    paid_amount_base = models.DecimalField(max_digits=12, decimal_places=2, default=0)

    # References
    sales_order = models.ForeignKey('SalesOrder', on_delete=models.SET_NULL, null=True, blank=True)
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)
    project = models.ForeignKey('Project', on_delete=models.SET_NULL, null=True, blank=True)

    # Status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='DRAFT')

    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.invoice_number} - {self.customer.name}"

    def get_remaining_balance(self):
        return self.total_amount - self.paid_amount

    def get_remaining_balance_base(self):
        """Get remaining balance in base currency"""
        return (self.total_amount_base or 0) - (self.paid_amount_base or 0)

    def is_overdue(self):
        return self.due_date < timezone.now().date() and self.status not in ['PAID', 'CANCELLED']

    def update_base_amounts(self):
        """Update base currency amounts using current exchange rate"""
        if self.currency and self.exchange_rate:
            self.subtotal_base = self.subtotal * self.exchange_rate
            self.tax_amount_base = self.tax_amount * self.exchange_rate
            self.discount_amount_base = self.discount_amount * self.exchange_rate
            self.total_amount_base = self.total_amount * self.exchange_rate
            self.paid_amount_base = self.paid_amount * self.exchange_rate

    def save(self, *args, **kwargs):
        # Set default currency to base currency if not specified
        if not self.currency:
            try:
                self.currency = Currency.objects.get(is_base_currency=True)
            except Currency.DoesNotExist:
                pass

        # Update exchange rate if currency is set
        if self.currency:
            try:
                base_currency = Currency.objects.get(is_base_currency=True)
                if self.currency != base_currency:
                    self.exchange_rate = ExchangeRate.get_rate(self.currency, base_currency, self.invoice_date)
                else:
                    self.exchange_rate = 1
            except (Currency.DoesNotExist, ValueError):
                # Keep existing exchange rate or default to 1
                if not self.exchange_rate:
                    self.exchange_rate = 1

        # Update base amounts
        self.update_base_amounts()

        super().save(*args, **kwargs)

    class Meta:
        ordering = ['-invoice_date']


class Payment(models.Model):
    """Payment records for both AP and AR"""
    PAYMENT_TYPES = [
        ('VENDOR_PAYMENT', 'Vendor Payment'),
        ('CUSTOMER_PAYMENT', 'Customer Payment'),
    ]

    PAYMENT_METHODS = [
        ('CASH', 'Cash'),
        ('CHECK', 'Check'),
        ('BANK_TRANSFER', 'Bank Transfer'),
        ('CREDIT_CARD', 'Credit Card'),
        ('ONLINE', 'Online Payment'),
    ]

    payment_number = models.CharField(max_length=50, unique=True)
    payment_type = models.CharField(max_length=20, choices=PAYMENT_TYPES)
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHODS)

    # Payment details
    payment_date = models.DateField()
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    currency = models.CharField(max_length=3, default='SAR')
    exchange_rate = models.DecimalField(max_digits=10, decimal_places=4, default=1.0000)

    # References
    vendor_invoice = models.ForeignKey(VendorInvoice, on_delete=models.CASCADE, null=True, blank=True, related_name='payments')
    customer_invoice = models.ForeignKey(CustomerInvoice, on_delete=models.CASCADE, null=True, blank=True, related_name='payments')

    # Bank details
    bank_account = models.CharField(max_length=100, blank=True)
    check_number = models.CharField(max_length=50, blank=True)
    reference_number = models.CharField(max_length=100, blank=True)

    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)

    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.payment_number} - {self.amount} {self.currency}"

    class Meta:
        ordering = ['-payment_date']

# Inventory & Asset Management Models
class AssetCategory(models.Model):
    name = models.CharField(max_length=100)
    name_ar = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Asset Categories"

class Asset(models.Model):
    STATUS_CHOICES = [
        ('AVAILABLE', 'Available'),
        ('IN_USE', 'In Use'),
        ('MAINTENANCE', 'Under Maintenance'),
        ('RETIRED', 'Retired'),
        ('LOST', 'Lost'),
        ('DAMAGED', 'Damaged'),
        ('DISPOSED', 'Disposed'),
    ]

    DEPRECIATION_METHODS = [
        ('STRAIGHT_LINE', 'Straight Line'),
        ('DECLINING_BALANCE', 'Declining Balance'),
        ('UNITS_OF_PRODUCTION', 'Units of Production'),
        ('SUM_OF_YEARS', 'Sum of Years Digits'),
        ('NO_DEPRECIATION', 'No Depreciation'),
    ]

    CONDITION_CHOICES = [
        ('EXCELLENT', 'Excellent'),
        ('GOOD', 'Good'),
        ('FAIR', 'Fair'),
        ('POOR', 'Poor'),
        ('DAMAGED', 'Damaged'),
    ]

    # Basic Information
    asset_id = models.CharField(max_length=50, unique=True)
    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    category = models.ForeignKey(AssetCategory, on_delete=models.CASCADE)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)

    # Technical Details
    serial_number = models.CharField(max_length=100, blank=True)
    model = models.CharField(max_length=100, blank=True)
    manufacturer = models.CharField(max_length=100, blank=True)
    barcode = models.CharField(max_length=100, blank=True, unique=True, null=True)
    qr_code = models.CharField(max_length=200, blank=True)

    # Financial Information
    currency = models.ForeignKey(Currency, on_delete=models.PROTECT, null=True, blank=True)
    purchase_date = models.DateField()
    purchase_price = models.DecimalField(max_digits=15, decimal_places=2)
    purchase_price_base = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    current_value = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    salvage_value = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    # Depreciation Settings
    depreciation_method = models.CharField(max_length=20, choices=DEPRECIATION_METHODS, default='STRAIGHT_LINE')
    useful_life_years = models.PositiveIntegerField(default=5, help_text="Expected useful life in years")
    useful_life_units = models.PositiveIntegerField(null=True, blank=True, help_text="For units of production method")
    depreciation_rate = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True, help_text="For declining balance method")

    # Warranty and Maintenance
    warranty_expiry = models.DateField(null=True, blank=True)
    warranty_provider = models.CharField(max_length=200, blank=True)
    maintenance_schedule = models.CharField(max_length=50, blank=True, help_text="e.g., Monthly, Quarterly, Annually")
    last_maintenance_date = models.DateField(null=True, blank=True)
    next_maintenance_date = models.DateField(null=True, blank=True)

    # Assignment and Location
    assigned_to = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_assets')
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)
    location = models.CharField(max_length=200, blank=True)
    building = models.CharField(max_length=100, blank=True)
    floor = models.CharField(max_length=50, blank=True)
    room = models.CharField(max_length=50, blank=True)

    # Status and Condition
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='AVAILABLE')
    condition = models.CharField(max_length=20, choices=CONDITION_CHOICES, default='GOOD')

    # Additional Information
    supplier = models.ForeignKey('Supplier', on_delete=models.SET_NULL, null=True, blank=True)
    purchase_order = models.CharField(max_length=100, blank=True)
    invoice_number = models.CharField(max_length=100, blank=True)
    insurance_policy = models.CharField(max_length=100, blank=True)
    insurance_expiry = models.DateField(null=True, blank=True)

    # Disposal Information
    disposal_date = models.DateField(null=True, blank=True)
    disposal_method = models.CharField(max_length=100, blank=True)
    disposal_value = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    disposal_reason = models.TextField(blank=True)

    # System Fields
    notes = models.TextField(blank=True)
    tags = models.CharField(max_length=500, blank=True, help_text="Comma-separated tags")
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_assets')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['asset_id']),
            models.Index(fields=['status']),
            models.Index(fields=['category']),
            models.Index(fields=['assigned_to']),
        ]

    def __str__(self):
        return f"{self.asset_id} - {self.name}"

    def get_current_book_value(self):
        """Calculate current book value based on depreciation"""
        if self.depreciation_method == 'NO_DEPRECIATION':
            return self.purchase_price

        # Get total accumulated depreciation
        total_depreciation = self.get_accumulated_depreciation()
        return max(self.purchase_price - total_depreciation, self.salvage_value)

    def get_accumulated_depreciation(self):
        """Calculate total accumulated depreciation to date"""
        from django.utils import timezone
        from datetime import date

        if self.depreciation_method == 'NO_DEPRECIATION':
            return 0

        # Calculate years since purchase
        today = timezone.now().date()
        if self.disposal_date:
            end_date = self.disposal_date
        else:
            end_date = today

        if end_date <= self.purchase_date:
            return 0

        years_elapsed = (end_date - self.purchase_date).days / 365.25

        depreciable_amount = self.purchase_price - self.salvage_value

        if self.depreciation_method == 'STRAIGHT_LINE':
            annual_depreciation = depreciable_amount / self.useful_life_years
            total_depreciation = annual_depreciation * min(years_elapsed, self.useful_life_years)

        elif self.depreciation_method == 'DECLINING_BALANCE':
            rate = self.depreciation_rate / 100 if self.depreciation_rate else (2 / self.useful_life_years)
            remaining_value = self.purchase_price
            total_depreciation = 0

            for year in range(int(min(years_elapsed, self.useful_life_years)) + 1):
                if year < years_elapsed:
                    year_depreciation = remaining_value * rate
                    # Don't depreciate below salvage value
                    year_depreciation = min(year_depreciation, remaining_value - self.salvage_value)
                    total_depreciation += year_depreciation
                    remaining_value -= year_depreciation

                    if remaining_value <= self.salvage_value:
                        break

        else:
            # Default to straight line for other methods
            annual_depreciation = depreciable_amount / self.useful_life_years
            total_depreciation = annual_depreciation * min(years_elapsed, self.useful_life_years)

        return min(total_depreciation, depreciable_amount)

    def get_annual_depreciation(self, year=None):
        """Calculate depreciation for a specific year"""
        if year is None:
            from django.utils import timezone
            year = timezone.now().year

        if self.depreciation_method == 'NO_DEPRECIATION':
            return 0

        # Implementation for annual depreciation calculation
        depreciable_amount = self.purchase_price - self.salvage_value

        if self.depreciation_method == 'STRAIGHT_LINE':
            return depreciable_amount / self.useful_life_years

        # Add other methods as needed
        return depreciable_amount / self.useful_life_years

    def is_under_warranty(self):
        """Check if asset is still under warranty"""
        if not self.warranty_expiry:
            return False
        from django.utils import timezone
        return self.warranty_expiry > timezone.now().date()

    def days_until_warranty_expiry(self):
        """Get days until warranty expires"""
        if not self.warranty_expiry:
            return None
        from django.utils import timezone
        delta = self.warranty_expiry - timezone.now().date()
        return delta.days if delta.days > 0 else 0

    def is_maintenance_due(self):
        """Check if maintenance is due"""
        if not self.next_maintenance_date:
            return False
        from django.utils import timezone
        return self.next_maintenance_date <= timezone.now().date()

    def save(self, *args, **kwargs):
        # Set currency to base currency if not specified
        if not self.currency:
            try:
                self.currency = Currency.objects.get(is_base_currency=True)
            except Currency.DoesNotExist:
                pass

        # Calculate base currency amount
        if self.currency:
            try:
                base_currency = Currency.objects.get(is_base_currency=True)
                if self.currency != base_currency:
                    rate = ExchangeRate.get_rate(self.currency, base_currency, self.purchase_date)
                    self.purchase_price_base = self.purchase_price * rate
                else:
                    self.purchase_price_base = self.purchase_price
            except (Currency.DoesNotExist, ValueError):
                self.purchase_price_base = self.purchase_price

        super().save(*args, **kwargs)


class AssetDepreciation(models.Model):
    """
    Track depreciation calculations for assets by period
    """
    asset = models.ForeignKey(Asset, on_delete=models.CASCADE, related_name='depreciation_records')
    period_start = models.DateField()
    period_end = models.DateField()
    depreciation_amount = models.DecimalField(max_digits=15, decimal_places=2)
    accumulated_depreciation = models.DecimalField(max_digits=15, decimal_places=2)
    book_value = models.DecimalField(max_digits=15, decimal_places=2)

    # Calculation details
    method_used = models.CharField(max_length=20)
    calculation_notes = models.TextField(blank=True)

    # System fields
    calculated_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)
    calculated_at = models.DateTimeField(auto_now_add=True)
    is_adjustment = models.BooleanField(default=False)
    adjustment_reason = models.TextField(blank=True)

    class Meta:
        unique_together = ['asset', 'period_start', 'period_end']
        ordering = ['-period_end']

    def __str__(self):
        return f"{self.asset.asset_id} - {self.period_start} to {self.period_end}"


class AssetMaintenance(models.Model):
    """
    Track maintenance activities for assets
    """
    MAINTENANCE_TYPES = [
        ('PREVENTIVE', 'Preventive Maintenance'),
        ('CORRECTIVE', 'Corrective Maintenance'),
        ('EMERGENCY', 'Emergency Repair'),
        ('INSPECTION', 'Inspection'),
        ('CALIBRATION', 'Calibration'),
        ('UPGRADE', 'Upgrade'),
        ('CLEANING', 'Cleaning'),
    ]

    STATUS_CHOICES = [
        ('SCHEDULED', 'Scheduled'),
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
        ('OVERDUE', 'Overdue'),
    ]

    PRIORITY_CHOICES = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    ]

    asset = models.ForeignKey(Asset, on_delete=models.CASCADE, related_name='maintenance_records')
    maintenance_type = models.CharField(max_length=20, choices=MAINTENANCE_TYPES)
    title = models.CharField(max_length=200)
    description = models.TextField()

    # Scheduling
    scheduled_date = models.DateField()
    scheduled_time = models.TimeField(null=True, blank=True)
    estimated_duration = models.DurationField(null=True, blank=True)

    # Execution
    actual_start_date = models.DateTimeField(null=True, blank=True)
    actual_end_date = models.DateTimeField(null=True, blank=True)
    performed_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='performed_maintenance')
    external_vendor = models.CharField(max_length=200, blank=True)

    # Status and Priority
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='SCHEDULED')
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='MEDIUM')

    # Costs
    estimated_cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    currency = models.ForeignKey(Currency, on_delete=models.PROTECT, null=True, blank=True)

    # Results
    work_performed = models.TextField(blank=True)
    parts_used = models.TextField(blank=True)
    issues_found = models.TextField(blank=True)
    recommendations = models.TextField(blank=True)

    # Next maintenance
    next_maintenance_date = models.DateField(null=True, blank=True)

    # System fields
    created_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_maintenance')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-scheduled_date']

    def __str__(self):
        return f"{self.asset.asset_id} - {self.title} ({self.scheduled_date})"

    def is_overdue(self):
        """Check if maintenance is overdue"""
        if self.status in ['COMPLETED', 'CANCELLED']:
            return False
        from django.utils import timezone
        return self.scheduled_date < timezone.now().date()


class AssetTransfer(models.Model):
    """
    Track asset transfers between employees, departments, or locations
    """
    TRANSFER_TYPES = [
        ('ASSIGNMENT', 'Employee Assignment'),
        ('DEPARTMENT', 'Department Transfer'),
        ('LOCATION', 'Location Change'),
        ('RETURN', 'Return to Pool'),
        ('DISPOSAL', 'Disposal'),
    ]

    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('APPROVED', 'Approved'),
        ('IN_TRANSIT', 'In Transit'),
        ('COMPLETED', 'Completed'),
        ('REJECTED', 'Rejected'),
        ('CANCELLED', 'Cancelled'),
    ]

    asset = models.ForeignKey(Asset, on_delete=models.CASCADE, related_name='transfers')
    transfer_type = models.CharField(max_length=20, choices=TRANSFER_TYPES)

    # From details
    from_employee = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='asset_transfers_from')
    from_department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True, related_name='asset_transfers_from')
    from_location = models.CharField(max_length=200, blank=True)

    # To details
    to_employee = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='asset_transfers_to')
    to_department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True, related_name='asset_transfers_to')
    to_location = models.CharField(max_length=200, blank=True)

    # Transfer details
    transfer_date = models.DateField()
    reason = models.TextField()
    notes = models.TextField(blank=True)
    condition_before = models.CharField(max_length=20, choices=Asset.CONDITION_CHOICES, blank=True)
    condition_after = models.CharField(max_length=20, choices=Asset.CONDITION_CHOICES, blank=True)

    # Approval workflow
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    requested_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='requested_transfers')
    approved_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_transfers')
    approval_date = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True)

    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-transfer_date']

    def __str__(self):
        return f"{self.asset.asset_id} - {self.get_transfer_type_display()} ({self.transfer_date})"


class AssetAudit(models.Model):
    """
    Track asset audit activities and results
    """
    AUDIT_TYPES = [
        ('PHYSICAL', 'Physical Count'),
        ('FINANCIAL', 'Financial Verification'),
        ('CONDITION', 'Condition Assessment'),
        ('COMPLIANCE', 'Compliance Check'),
        ('FULL', 'Full Audit'),
    ]

    STATUS_CHOICES = [
        ('PLANNED', 'Planned'),
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
    ]

    RESULT_CHOICES = [
        ('FOUND', 'Found as Expected'),
        ('NOT_FOUND', 'Not Found'),
        ('DAMAGED', 'Found but Damaged'),
        ('WRONG_LOCATION', 'Found in Wrong Location'),
        ('WRONG_PERSON', 'Assigned to Wrong Person'),
        ('DISCREPANCY', 'Other Discrepancy'),
    ]

    asset = models.ForeignKey(Asset, on_delete=models.CASCADE, related_name='audit_records')
    audit_type = models.CharField(max_length=20, choices=AUDIT_TYPES)
    audit_date = models.DateField()

    # Expected vs Actual
    expected_location = models.CharField(max_length=200, blank=True)
    actual_location = models.CharField(max_length=200, blank=True)
    expected_assignee = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='expected_assets')
    actual_assignee = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='actual_assets')
    expected_condition = models.CharField(max_length=20, choices=Asset.CONDITION_CHOICES, blank=True)
    actual_condition = models.CharField(max_length=20, choices=Asset.CONDITION_CHOICES, blank=True)

    # Results
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PLANNED')
    result = models.CharField(max_length=20, choices=RESULT_CHOICES, blank=True)
    discrepancy_notes = models.TextField(blank=True)
    corrective_action = models.TextField(blank=True)

    # Auditor information
    audited_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='conducted_audits')
    verified_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='verified_audits')

    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-audit_date']

    def __str__(self):
        return f"{self.asset.asset_id} - {self.get_audit_type_display()} ({self.audit_date})"


class KPIMetric(models.Model):
    """
    Key Performance Indicator tracking model
    """
    METRIC_TYPES = [
        ('FINANCIAL', 'Financial'),
        ('OPERATIONAL', 'Operational'),
        ('CUSTOMER', 'Customer'),
        ('EMPLOYEE', 'Employee'),
        ('ASSET', 'Asset'),
        ('CUSTOM', 'Custom'),
    ]

    CALCULATION_METHODS = [
        ('SUM', 'Sum'),
        ('AVERAGE', 'Average'),
        ('COUNT', 'Count'),
        ('PERCENTAGE', 'Percentage'),
        ('RATIO', 'Ratio'),
        ('CUSTOM_FORMULA', 'Custom Formula'),
    ]

    FREQUENCY_CHOICES = [
        ('DAILY', 'Daily'),
        ('WEEKLY', 'Weekly'),
        ('MONTHLY', 'Monthly'),
        ('QUARTERLY', 'Quarterly'),
        ('YEARLY', 'Yearly'),
    ]

    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)

    metric_type = models.CharField(max_length=20, choices=METRIC_TYPES)
    calculation_method = models.CharField(max_length=20, choices=CALCULATION_METHODS)
    custom_formula = models.TextField(blank=True, help_text="Custom calculation formula")

    # Target and thresholds
    target_value = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    warning_threshold = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    critical_threshold = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)

    # Display settings
    unit = models.CharField(max_length=50, blank=True, help_text="e.g., %, $, days")
    decimal_places = models.PositiveIntegerField(default=2)
    is_higher_better = models.BooleanField(default=True, help_text="Is a higher value better?")

    # Tracking settings
    frequency = models.CharField(max_length=20, choices=FREQUENCY_CHOICES, default='MONTHLY')
    is_active = models.BooleanField(default=True)

    # System fields
    created_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['metric_type', 'name']

    def __str__(self):
        return self.name


class KPIMetricValue(models.Model):
    """
    KPI metric values over time for analytics reporting
    """
    kpi_metric = models.ForeignKey(KPIMetric, on_delete=models.CASCADE, related_name='metric_values')
    period_start = models.DateField()
    period_end = models.DateField()
    value = models.DecimalField(max_digits=15, decimal_places=6)

    # Additional context
    notes = models.TextField(blank=True)
    data_source = models.CharField(max_length=200, blank=True)

    # System fields
    calculated_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)
    calculated_at = models.DateTimeField(auto_now_add=True)
    is_manual = models.BooleanField(default=False)

    class Meta:
        unique_together = ['kpi_metric', 'period_start', 'period_end']
        ordering = ['-period_end']

    def __str__(self):
        return f"{self.kpi_metric.name} - {self.period_start} to {self.period_end}: {self.value}"

    def get_status(self):
        """Get status based on thresholds"""
        if not self.kpi_metric.warning_threshold and not self.kpi_metric.critical_threshold:
            return 'NORMAL'

        value = float(self.value)
        warning = float(self.kpi_metric.warning_threshold) if self.kpi_metric.warning_threshold else None
        critical = float(self.kpi_metric.critical_threshold) if self.kpi_metric.critical_threshold else None

        if self.kpi_metric.is_higher_better:
            if critical and value <= critical:
                return 'CRITICAL'
            elif warning and value <= warning:
                return 'WARNING'
            else:
                return 'GOOD'
        else:
            if critical and value >= critical:
                return 'CRITICAL'
            elif warning and value >= warning:
                return 'WARNING'
            else:
                return 'GOOD'


class ReportTemplate(models.Model):
    """
    Customizable report templates
    """
    REPORT_TYPES = [
        ('FINANCIAL', 'Financial Report'),
        ('OPERATIONAL', 'Operational Report'),
        ('EXECUTIVE', 'Executive Dashboard'),
        ('COMPLIANCE', 'Compliance Report'),
        ('CUSTOM', 'Custom Report'),
    ]

    OUTPUT_FORMATS = [
        ('PDF', 'PDF Document'),
        ('EXCEL', 'Excel Spreadsheet'),
        ('CSV', 'CSV File'),
        ('JSON', 'JSON Data'),
        ('HTML', 'HTML Report'),
    ]

    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)

    report_type = models.CharField(max_length=20, choices=REPORT_TYPES)
    template_config = models.JSONField(default=dict, help_text="Report configuration and layout")

    # Data sources and filters
    data_sources = models.JSONField(default=list, help_text="List of data sources")
    default_filters = models.JSONField(default=dict, help_text="Default filter values")

    # Output settings
    output_formats = models.JSONField(default=list, help_text="Supported output formats")
    page_orientation = models.CharField(max_length=20, choices=[('PORTRAIT', 'Portrait'), ('LANDSCAPE', 'Landscape')], default='PORTRAIT')

    # Access control
    is_public = models.BooleanField(default=False)
    allowed_roles = models.JSONField(default=list, help_text="List of allowed role IDs")

    # System fields
    created_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['report_type', 'name']

    def __str__(self):
        return self.name


class ReportExecution(models.Model):
    """
    Track report generation and execution
    """
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('RUNNING', 'Running'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('CANCELLED', 'Cancelled'),
    ]

    template = models.ForeignKey(ReportTemplate, on_delete=models.CASCADE, related_name='executions')

    # Execution parameters
    parameters = models.JSONField(default=dict, help_text="Report parameters and filters")
    output_format = models.CharField(max_length=20)

    # Execution tracking
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    execution_time = models.DurationField(null=True, blank=True)

    # Results
    output_file = models.FileField(upload_to='reports/', null=True, blank=True)
    file_size = models.PositiveIntegerField(null=True, blank=True, help_text="File size in bytes")
    error_message = models.TextField(blank=True)

    # System fields
    requested_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.template.name} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"


class Dashboard(models.Model):
    """
    Customizable dashboards for different user roles
    """
    DASHBOARD_TYPES = [
        ('EXECUTIVE', 'Executive Dashboard'),
        ('FINANCIAL', 'Financial Dashboard'),
        ('OPERATIONAL', 'Operational Dashboard'),
        ('DEPARTMENTAL', 'Department Dashboard'),
        ('PERSONAL', 'Personal Dashboard'),
    ]

    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)

    dashboard_type = models.CharField(max_length=20, choices=DASHBOARD_TYPES)
    layout_config = models.JSONField(default=dict, help_text="Dashboard layout and widget configuration")

    # Widget configuration
    widgets = models.JSONField(default=list, help_text="List of dashboard widgets")
    refresh_interval = models.PositiveIntegerField(default=300, help_text="Auto-refresh interval in seconds")

    # Access control
    is_default = models.BooleanField(default=False)
    is_public = models.BooleanField(default=False)
    allowed_roles = models.JSONField(default=list, help_text="List of allowed role IDs")
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)

    # System fields
    created_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['dashboard_type', 'name']

    def __str__(self):
        return self.name


class AnalyticsQuery(models.Model):
    """
    Store and manage analytics queries for reuse
    """
    QUERY_TYPES = [
        ('SQL', 'SQL Query'),
        ('AGGREGATION', 'Aggregation Query'),
        ('FILTER', 'Filter Query'),
        ('CUSTOM', 'Custom Query'),
    ]

    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    query_type = models.CharField(max_length=20, choices=QUERY_TYPES)

    # Query definition
    query_definition = models.JSONField(help_text="Query structure and parameters")
    sql_query = models.TextField(blank=True, help_text="Raw SQL query if applicable")

    # Caching settings
    cache_duration = models.PositiveIntegerField(default=3600, help_text="Cache duration in seconds")
    last_executed = models.DateTimeField(null=True, blank=True)
    execution_count = models.PositiveIntegerField(default=0)

    # System fields
    created_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return self.name


class Supplier(models.Model):
    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    contact_person = models.CharField(max_length=100, blank=True)
    email = models.EmailField(blank=True)
    phone = models.CharField(max_length=20, blank=True)
    address = models.TextField(blank=True)
    website = models.URLField(blank=True)
    tax_number = models.CharField(max_length=50, blank=True)
    payment_terms = models.CharField(max_length=100, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

class PurchaseOrder(models.Model):
    STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('PENDING', 'Pending Approval'),
        ('APPROVED', 'Approved'),
        ('ORDERED', 'Ordered'),
        ('RECEIVED', 'Received'),
        ('CANCELLED', 'Cancelled'),
    ]

    po_number = models.CharField(max_length=50, unique=True)
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE)
    requested_by = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='requested_pos')
    approved_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_pos')
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)
    project = models.ForeignKey(Project, on_delete=models.SET_NULL, null=True, blank=True)
    order_date = models.DateField()
    expected_delivery = models.DateField(null=True, blank=True)
    actual_delivery = models.DateField(null=True, blank=True)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    currency = models.CharField(max_length=3, default='SAR')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='DRAFT')
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"PO-{self.po_number} - {self.supplier.name}"

# Product Management Models
class ProductCategory(models.Model):
    name = models.CharField(max_length=100)
    name_ar = models.CharField(max_length=100, blank=True)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Product Categories"

class Product(models.Model):
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('discontinued', 'Discontinued'),
    ]

    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    sku = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)
    category = models.ForeignKey(ProductCategory, on_delete=models.CASCADE, related_name='products')
    brand = models.CharField(max_length=100, blank=True)
    brand_ar = models.CharField(max_length=100, blank=True)
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    cost_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    quantity_in_stock = models.IntegerField(default=0)
    minimum_stock_level = models.IntegerField(default=0)
    maximum_stock_level = models.IntegerField(null=True, blank=True)
    reorder_point = models.IntegerField(default=0)
    unit_of_measure = models.CharField(max_length=50, default='piece')
    unit_of_measure_ar = models.CharField(max_length=50, blank=True)
    barcode = models.CharField(max_length=100, blank=True)
    weight = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
    dimensions = models.CharField(max_length=100, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    supplier = models.ForeignKey(Supplier, on_delete=models.SET_NULL, null=True, blank=True)
    location = models.CharField(max_length=100, blank=True)
    location_ar = models.CharField(max_length=100, blank=True)
    expiry_date = models.DateField(null=True, blank=True)
    batch_number = models.CharField(max_length=50, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.sku})"

    class Meta:
        ordering = ['-created_at']

# Report Management Models
class Report(models.Model):
    REPORT_TYPES = [
        ('employee', 'Employee Report'),
        ('department', 'Department Report'),
        ('financial', 'Financial Report'),
        ('performance', 'Performance Report'),
        ('attendance', 'Attendance Report'),
        ('payroll', 'Payroll Report'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]

    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    type = models.CharField(max_length=20, choices=REPORT_TYPES)
    description = models.TextField()
    description_ar = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    created_date = models.DateTimeField(auto_now_add=True)
    completed_date = models.DateTimeField(null=True, blank=True)
    file_size = models.CharField(max_length=50, blank=True)
    file_url = models.URLField(blank=True)
    parameters = models.JSONField(default=dict, blank=True)
    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='created_reports', null=True, blank=True)

    def __str__(self):
        return f"{self.name} ({self.get_type_display()})"

    class Meta:
        ordering = ['-created_date']

# Workflow Management Models
class Workflow(models.Model):
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('draft', 'Draft'),
        ('archived', 'Archived'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    CATEGORY_CHOICES = [
        ('hr', 'Human Resources'),
        ('finance', 'Finance'),
        ('sales', 'Sales'),
        ('operations', 'Operations'),
        ('it', 'Information Technology'),
        ('general', 'General'),
    ]

    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField()
    description_ar = models.TextField(blank=True)
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, default='general')
    category_ar = models.CharField(max_length=100, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='medium')

    # Workflow configuration
    trigger_event = models.CharField(max_length=100, blank=True)
    conditions = models.TextField(blank=True)
    conditions_ar = models.TextField(blank=True)
    actions = models.TextField(blank=True)
    actions_ar = models.TextField(blank=True)

    # Automation settings
    is_automated = models.BooleanField(default=False)
    next_run = models.DateTimeField(null=True, blank=True)
    last_run = models.DateTimeField(null=True, blank=True)
    run_count = models.IntegerField(default=0)
    success_count = models.IntegerField(default=0)

    # Metadata
    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='created_workflows', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.get_category_display()})"

    @property
    def success_rate(self):
        if self.run_count == 0:
            return 0
        return (self.success_count / self.run_count) * 100

    class Meta:
        ordering = ['-created_at']

# Sales Management Models
class SalesOrder(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('processing', 'Processing'),
        ('shipped', 'Shipped'),
        ('delivered', 'Delivered'),
        ('cancelled', 'Cancelled'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    order_number = models.CharField(max_length=50, unique=True)
    customer = models.ForeignKey('Customer', on_delete=models.CASCADE, related_name='sales_orders')
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='medium')
    order_date = models.DateField()
    delivery_date = models.DateField(null=True, blank=True)
    items_count = models.IntegerField(default=0)
    discount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    tax = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_sales_orders')

    def __str__(self):
        return f"{self.order_number} - {self.customer.get_full_name()}"

    class Meta:
        ordering = ['-created_at']

# Customer Management Models
class Customer(models.Model):
    CUSTOMER_TYPES = [
        ('individual', 'Individual'),
        ('business', 'Business'),
        ('enterprise', 'Enterprise'),
    ]

    CUSTOMER_STATUS = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('suspended', 'Suspended'),
        ('prospect', 'Prospect'),
    ]

    # Basic Information
    customer_id = models.CharField(max_length=20, unique=True, editable=False)
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100, blank=True)
    email = models.EmailField(unique=True)
    phone = models.CharField(max_length=20, blank=True)

    # Customer Details
    customer_type = models.CharField(max_length=20, choices=CUSTOMER_TYPES, default='individual')
    status = models.CharField(max_length=20, choices=CUSTOMER_STATUS, default='prospect')

    # Company Information (for business customers)
    company_name = models.CharField(max_length=200, blank=True)
    company_size = models.CharField(max_length=50, blank=True)
    industry = models.CharField(max_length=100, blank=True)

    # Address Information
    address_line1 = models.CharField(max_length=255, blank=True)
    address_line2 = models.CharField(max_length=255, blank=True)
    city = models.CharField(max_length=100, blank=True)
    state = models.CharField(max_length=100, blank=True)
    postal_code = models.CharField(max_length=20, blank=True)
    country = models.CharField(max_length=100, blank=True)

    # Tracking
    source = models.CharField(max_length=50, blank=True, help_text='How customer found us')
    internal_notes = models.TextField(blank=True)
    tags = models.CharField(max_length=500, blank=True, help_text='Comma-separated tags')

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_contact_date = models.DateTimeField(null=True, blank=True)

    # Statistics
    total_orders = models.IntegerField(default=0)
    total_spent = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    satisfaction_score = models.FloatField(default=0.0, help_text='Average satisfaction score')

    class Meta:
        ordering = ['-created_at']

    def save(self, *args, **kwargs):
        if not self.customer_id:
            self.customer_id = self.generate_customer_id()
        super().save(*args, **kwargs)

    def generate_customer_id(self):
        """Generate unique customer ID"""
        import random
        import string
        from django.utils import timezone

        while True:
            # Format: CUST-YYYYMMDD-XXXX
            date_part = timezone.now().strftime('%Y%m%d')
            random_part = ''.join(random.choices(string.digits, k=4))
            customer_id = f"CUST-{date_part}-{random_part}"

            if not Customer.objects.filter(customer_id=customer_id).exists():
                return customer_id

    def get_full_name(self):
        """Get customer's full name"""
        if self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.first_name

    def __str__(self):
        return f"{self.customer_id} - {self.get_full_name()} ({self.email})"

# Communication & Collaboration Models
class Announcement(models.Model):
    PRIORITY_CHOICES = [
        ('LOW', 'Low'),
        ('NORMAL', 'Normal'),
        ('HIGH', 'High'),
        ('URGENT', 'Urgent'),
    ]

    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    content = models.TextField()
    content_ar = models.TextField(blank=True)
    author = models.ForeignKey(Employee, on_delete=models.CASCADE)
    target_departments = models.ManyToManyField(Department, blank=True)
    target_employees = models.ManyToManyField(Employee, blank=True, related_name='received_announcements')
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='NORMAL')
    is_published = models.BooleanField(default=False)
    publish_date = models.DateTimeField(null=True, blank=True)
    expiry_date = models.DateTimeField(null=True, blank=True)
    attachment = models.FileField(upload_to='announcements/', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

class Message(models.Model):
    sender = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='sent_messages')
    recipient = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='received_messages')
    subject = models.CharField(max_length=200)
    content = models.TextField()
    is_read = models.BooleanField(default=False)
    is_important = models.BooleanField(default=False)
    parent_message = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True)
    attachment = models.FileField(upload_to='messages/', null=True, blank=True)
    sent_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.sender} to {self.recipient}: {self.subject}"

    class Meta:
        ordering = ['-sent_at']

class Document(models.Model):
    CATEGORY_CHOICES = [
        ('POLICY', 'Policy'),
        ('PROCEDURE', 'Procedure'),
        ('FORM', 'Form'),
        ('MANUAL', 'Manual'),
        ('REPORT', 'Report'),
        ('CONTRACT', 'Contract'),
        ('OTHER', 'Other'),
    ]

    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
    file = models.FileField(upload_to='documents/')
    version = models.CharField(max_length=20, default='1.0')
    uploaded_by = models.ForeignKey(Employee, on_delete=models.CASCADE)
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)
    is_public = models.BooleanField(default=False)
    access_permissions = models.ManyToManyField(Employee, blank=True, related_name='accessible_documents')
    tags = models.CharField(max_length=500, blank=True)
    download_count = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

class Meeting(models.Model):
    STATUS_CHOICES = [
        ('SCHEDULED', 'Scheduled'),
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
        ('POSTPONED', 'Postponed'),
    ]

    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)
    organizer = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='organized_meetings')
    attendees = models.ManyToManyField(Employee, related_name='meetings')
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    location = models.CharField(max_length=200, blank=True)
    meeting_link = models.URLField(blank=True)
    agenda = models.TextField(blank=True)
    agenda_ar = models.TextField(blank=True)
    minutes = models.TextField(blank=True)
    minutes_ar = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='SCHEDULED')
    is_recurring = models.BooleanField(default=False)
    recurrence_pattern = models.CharField(max_length=50, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.title} - {self.start_time.strftime('%Y-%m-%d %H:%M')}"

# Enhanced Activity Model
class Activity(models.Model):
    ACTIVITY_TYPES = [
        ('LOGIN', 'User Login'),
        ('LOGOUT', 'User Logout'),
        ('CREATE', 'Record Created'),
        ('UPDATE', 'Record Updated'),
        ('DELETE', 'Record Deleted'),
        ('REPORT', 'Report Generated'),
        ('APPROVAL', 'Approval Action'),
        ('MESSAGE', 'Message Sent'),
        ('MEETING', 'Meeting Action'),
        ('PROJECT', 'Project Action'),
        ('TASK', 'Task Action'),
        ('EXPENSE', 'Expense Action'),
        ('LEAVE', 'Leave Action'),
        ('ASSET', 'Asset Action'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    activity_type = models.CharField(max_length=20, choices=ACTIVITY_TYPES)
    description = models.TextField()
    description_ar = models.TextField(help_text="Arabic description")
    related_object_type = models.CharField(max_length=50, blank=True)  # e.g., 'project', 'task', 'employee'
    related_object_id = models.PositiveIntegerField(null=True, blank=True)
    metadata = models.JSONField(default=dict, blank=True)  # Additional data
    timestamp = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)

    def __str__(self):
        return f"{self.user.username} - {self.activity_type} - {self.timestamp}"

    class Meta:
        ordering = ['-timestamp']
        verbose_name_plural = "Activities"


# ============================================================================
# ENTERPRISE FEATURES - PHASE 3
# ============================================================================

# Multi-Tenant Architecture Models
class Tenant(models.Model):
    PLAN_CHOICES = [
        ('basic', 'Basic'),
        ('professional', 'Professional'),
        ('enterprise', 'Enterprise'),
        ('custom', 'Custom'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('trial', 'Trial'),
        ('expired', 'Expired'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    domain = models.CharField(max_length=100, unique=True)
    subdomain = models.CharField(max_length=50, unique=True)
    plan = models.CharField(max_length=20, choices=PLAN_CHOICES, default='basic')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='trial')

    # Settings stored as JSON
    settings = models.JSONField(default=dict)
    limits = models.JSONField(default=dict)
    features = models.JSONField(default=dict)
    billing_info = models.JSONField(default=dict)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    metadata = models.JSONField(default=dict)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']


class TenantUser(models.Model):
    tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, related_name='tenant_users')
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    role = models.CharField(max_length=50)
    permissions = models.JSONField(default=list)
    is_active = models.BooleanField(default=True)
    last_login = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['tenant', 'user']


# Machine Learning Models
class MLModel(models.Model):
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('training', 'Training'),
        ('deprecated', 'Deprecated'),
    ]

    TYPE_CHOICES = [
        ('regression', 'Regression'),
        ('classification', 'Classification'),
        ('clustering', 'Clustering'),
        ('forecasting', 'Forecasting'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    type = models.CharField(max_length=20, choices=TYPE_CHOICES)
    version = models.CharField(max_length=20)
    accuracy = models.FloatField(validators=[MinValueValidator(0.0), MaxValueValidator(1.0)])
    features = models.JSONField(default=list)
    hyperparameters = models.JSONField(default=dict)
    training_data_info = models.JSONField(default=dict)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='training')
    last_trained = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} v{self.version}"


class MLPrediction(models.Model):
    PREDICTION_TYPES = [
        ('employee_performance', 'Employee Performance'),
        ('turnover_risk', 'Turnover Risk'),
        ('project_success', 'Project Success'),
        ('budget_forecast', 'Budget Forecast'),
        ('resource_demand', 'Resource Demand'),
        ('kpi_forecast', 'KPI Forecast'),
        ('kpi_anomaly', 'KPI Anomaly Detection'),
        ('kpi_optimization', 'KPI Optimization'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    model = models.ForeignKey(MLModel, on_delete=models.CASCADE)
    prediction_type = models.CharField(max_length=30, choices=PREDICTION_TYPES)
    input_data = models.JSONField()
    prediction_result = models.JSONField()
    confidence = models.FloatField(validators=[MinValueValidator(0.0), MaxValueValidator(1.0)])
    factors = models.JSONField(default=list)
    recommendations = models.JSONField(default=list)
    related_object_type = models.CharField(max_length=50, blank=True)
    related_object_id = models.PositiveIntegerField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    metadata = models.JSONField(default=dict)

    def __str__(self):
        return f"{self.prediction_type} - {self.confidence:.2f}"

    class Meta:
        ordering = ['-created_at']


# Automation Models
class AutomationRule(models.Model):
    CATEGORY_CHOICES = [
        ('hr', 'HR'),
        ('finance', 'Finance'),
        ('project', 'Project'),
        ('system', 'System'),
        ('custom', 'Custom'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    description = models.TextField()
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)

    # Trigger configuration
    trigger_config = models.JSONField()

    # Conditions and actions
    conditions = models.JSONField(default=list)
    actions = models.JSONField(default=list)

    is_active = models.BooleanField(default=True)
    execution_count = models.PositiveIntegerField(default=0)
    success_count = models.PositiveIntegerField(default=0)
    last_executed = models.DateTimeField(null=True, blank=True)

    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    @property
    def success_rate(self):
        if self.execution_count == 0:
            return 0.0
        return self.success_count / self.execution_count


class AutomationExecution(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    rule = models.ForeignKey(AutomationRule, on_delete=models.CASCADE, related_name='executions')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    start_time = models.DateTimeField(auto_now_add=True)
    end_time = models.DateTimeField(null=True, blank=True)
    context_data = models.JSONField(default=dict)
    result_data = models.JSONField(default=dict)
    error_message = models.TextField(blank=True)
    logs = models.JSONField(default=list)

    def __str__(self):
        return f"{self.rule.name} - {self.status}"

    class Meta:
        ordering = ['-start_time']


# Compliance Models
class ComplianceFramework(models.Model):
    TYPE_CHOICES = [
        ('data_protection', 'Data Protection'),
        ('financial', 'Financial'),
        ('industry', 'Industry'),
        ('security', 'Security'),
        ('labor', 'Labor'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    description = models.TextField()
    region = models.CharField(max_length=100)
    type = models.CharField(max_length=20, choices=TYPE_CHOICES)
    version = models.CharField(max_length=20, default='1.0')
    is_active = models.BooleanField(default=False)
    compliance_score = models.FloatField(default=0.0, validators=[MinValueValidator(0.0), MaxValueValidator(100.0)])
    last_assessment = models.DateTimeField(null=True, blank=True)
    next_assessment = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name





class DataProtectionRecord(models.Model):
    LEGAL_BASIS_CHOICES = [
        ('consent', 'Consent'),
        ('contract', 'Contract'),
        ('legal_obligation', 'Legal Obligation'),
        ('vital_interests', 'Vital Interests'),
        ('public_task', 'Public Task'),
        ('legitimate_interests', 'Legitimate Interests'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    data_type = models.CharField(max_length=200)
    purpose = models.TextField()
    legal_basis = models.CharField(max_length=30, choices=LEGAL_BASIS_CHOICES)
    data_subjects = models.JSONField(default=list)
    retention_period = models.PositiveIntegerField(help_text="Retention period in years")
    processing_activities = models.JSONField(default=list)
    third_party_sharing = models.BooleanField(default=False)
    cross_border_transfer = models.BooleanField(default=False)
    security_measures = models.JSONField(default=list)
    data_controller = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='controlled_data')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.data_type


class AuditTrail(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    action = models.CharField(max_length=100)
    resource = models.CharField(max_length=100)
    resource_id = models.CharField(max_length=100, blank=True)
    old_values = models.JSONField(null=True, blank=True)
    new_values = models.JSONField(null=True, blank=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    compliance_relevant = models.BooleanField(default=False)
    retention_date = models.DateTimeField()
    metadata = models.JSONField(default=dict)

    def __str__(self):
        return f"{self.user.username} - {self.action} - {self.timestamp}"

    class Meta:
        ordering = ['-timestamp']


class DataSubjectRequest(models.Model):
    TYPE_CHOICES = [
        ('access', 'Data Access'),
        ('rectification', 'Data Rectification'),
        ('erasure', 'Data Erasure'),
        ('portability', 'Data Portability'),
        ('restriction', 'Processing Restriction'),
        ('objection', 'Processing Objection'),
    ]

    STATUS_CHOICES = [
        ('received', 'Received'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    request_type = models.CharField(max_length=20, choices=TYPE_CHOICES)
    requester_name = models.CharField(max_length=200)
    requester_email = models.EmailField()
    description = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='received')
    submitted_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    assigned_to = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)
    response = models.TextField(blank=True)
    documents = models.JSONField(default=list)
    metadata = models.JSONField(default=dict)

    def __str__(self):
        return f"{self.request_type} - {self.requester_email}"

    class Meta:
        ordering = ['-submitted_at']


# KPI Management Models
class KPICategory(models.Model):
    """Categories for organizing KPIs"""
    CATEGORY_TYPES = [
        ('FINANCIAL', 'Financial'),
        ('HR', 'Human Resources'),
        ('OPERATIONS', 'Operations'),
        ('SALES', 'Sales & Marketing'),
        ('CUSTOMER', 'Customer Service'),
        ('QUALITY', 'Quality Management'),
        ('INNOVATION', 'Innovation & Development'),
        ('COMPLIANCE', 'Compliance & Risk'),
        ('SUSTAINABILITY', 'Sustainability'),
        ('STRATEGIC', 'Strategic Goals'),
    ]

    name = models.CharField(max_length=50, choices=CATEGORY_TYPES, unique=True)
    name_ar = models.CharField(max_length=100, help_text="Arabic name")
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True, help_text="Arabic description")
    icon = models.CharField(max_length=50, default='BarChart3')  # Lucide icon name
    color = models.CharField(max_length=50, default='from-blue-500 to-blue-600')  # Tailwind gradient
    is_active = models.BooleanField(default=True)
    sort_order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.get_name_display()

    class Meta:
        ordering = ['sort_order', 'name']
        verbose_name_plural = "KPI Categories"


class KPI(models.Model):
    """Key Performance Indicator definitions"""
    MEASUREMENT_TYPES = [
        ('NUMBER', 'Number'),
        ('PERCENTAGE', 'Percentage'),
        ('CURRENCY', 'Currency'),
        ('RATIO', 'Ratio'),
        ('SCORE', 'Score'),
        ('TIME', 'Time Duration'),
    ]

    FREQUENCY_CHOICES = [
        ('DAILY', 'Daily'),
        ('WEEKLY', 'Weekly'),
        ('MONTHLY', 'Monthly'),
        ('QUARTERLY', 'Quarterly'),
        ('YEARLY', 'Yearly'),
    ]

    TREND_DIRECTION = [
        ('UP', 'Higher is Better'),
        ('DOWN', 'Lower is Better'),
        ('TARGET', 'Target Value'),
    ]

    STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('INACTIVE', 'Inactive'),
        ('DRAFT', 'Draft'),
        ('ARCHIVED', 'Archived'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, help_text="Arabic name")
    description = models.TextField()
    description_ar = models.TextField(help_text="Arabic description")
    category = models.ForeignKey(KPICategory, on_delete=models.CASCADE, related_name='kpis')

    # Measurement details
    measurement_type = models.CharField(max_length=20, choices=MEASUREMENT_TYPES)
    unit = models.CharField(max_length=50, blank=True, help_text="e.g., %, $, hours")
    unit_ar = models.CharField(max_length=50, blank=True, help_text="Arabic unit")
    frequency = models.CharField(max_length=20, choices=FREQUENCY_CHOICES)
    trend_direction = models.CharField(max_length=10, choices=TREND_DIRECTION)

    # Calculation details
    formula = models.TextField(blank=True, help_text="Calculation formula or description")
    data_source = models.CharField(max_length=200, blank=True, help_text="Source of data")
    calculation_method = models.TextField(blank=True)

    # Current value and target
    current_value = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, help_text="Current KPI value")
    target_value = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    warning_threshold = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    critical_threshold = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)

    # Value metadata
    last_updated = models.DateTimeField(null=True, blank=True, help_text="When the current value was last updated")
    updated_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='updated_kpis', help_text="Who last updated the value")

    # Access control
    visible_to_roles = models.ManyToManyField(Role, blank=True, help_text="Roles that can view this KPI")
    owner = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, related_name='owned_kpis')

    # Status and metadata
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='ACTIVE')
    is_automated = models.BooleanField(default=False, help_text="Automatically calculated")
    automation_config = models.JSONField(default=dict, blank=True)
    calculation_method = models.CharField(max_length=50, blank=True, help_text="Method used for automatic calculation")

    # Algorithm and AI features
    use_ml_prediction = models.BooleanField(default=False, help_text="Use ML for KPI prediction")
    ml_model = models.ForeignKey('MLModel', on_delete=models.SET_NULL, null=True, blank=True, help_text="ML model for predictions")
    anomaly_detection = models.BooleanField(default=True, help_text="Enable anomaly detection")
    auto_recommendations = models.BooleanField(default=True, help_text="Generate AI recommendations")
    algorithm_config = models.JSONField(default=dict, blank=True, help_text="Algorithm configuration")

    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='created_kpis')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['category', 'name']
        verbose_name = "KPI"
        verbose_name_plural = "KPIs"


class KPIValue(models.Model):
    """Historical KPI values and measurements"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    kpi = models.ForeignKey(KPI, on_delete=models.CASCADE, related_name='values')
    value = models.DecimalField(max_digits=15, decimal_places=4)
    period_start = models.DateTimeField()
    period_end = models.DateTimeField()

    # Context and metadata
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)
    project = models.ForeignKey(Project, on_delete=models.SET_NULL, null=True, blank=True)
    employee = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)

    # Data quality
    data_quality_score = models.DecimalField(max_digits=5, decimal_places=2, default=100.00)
    confidence_level = models.DecimalField(max_digits=5, decimal_places=2, default=100.00)
    notes = models.TextField(blank=True)

    # Tracking
    recorded_by = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='recorded_kpi_values')
    recorded_at = models.DateTimeField(auto_now_add=True)
    is_estimated = models.BooleanField(default=False)
    source_data = models.JSONField(default=dict, blank=True)

    def __str__(self):
        return f"{self.kpi.name} - {self.value} ({self.period_start.date()})"

    class Meta:
        ordering = ['-period_start']
        unique_together = ['kpi', 'period_start', 'department', 'project', 'employee']


class KPITarget(models.Model):
    """Target values for KPIs across different time periods"""
    TARGET_TYPES = [
        ('ABSOLUTE', 'Absolute Value'),
        ('PERCENTAGE_CHANGE', 'Percentage Change'),
        ('RELATIVE', 'Relative to Baseline'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    kpi = models.ForeignKey(KPI, on_delete=models.CASCADE, related_name='targets')
    target_value = models.DecimalField(max_digits=15, decimal_places=4)
    target_type = models.CharField(max_length=20, choices=TARGET_TYPES, default='ABSOLUTE')

    # Time period
    start_date = models.DateField()
    end_date = models.DateField()

    # Context
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)
    project = models.ForeignKey(Project, on_delete=models.SET_NULL, null=True, blank=True)
    employee = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)

    # Metadata
    description = models.TextField(blank=True)
    is_stretch_goal = models.BooleanField(default=False)
    weight = models.DecimalField(max_digits=5, decimal_places=2, default=1.00)

    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='created_kpi_targets')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.kpi.name} Target: {self.target_value} ({self.start_date} - {self.end_date})"

    class Meta:
        ordering = ['-start_date']


class KPIAlert(models.Model):
    """Alerts for KPI threshold breaches"""
    ALERT_TYPES = [
        ('THRESHOLD_BREACH', 'Threshold Breach'),
        ('TARGET_MISSED', 'Target Missed'),
        ('TREND_CHANGE', 'Trend Change'),
        ('DATA_QUALITY', 'Data Quality Issue'),
        ('MISSING_DATA', 'Missing Data'),
    ]

    SEVERITY_LEVELS = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    ]

    STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('ACKNOWLEDGED', 'Acknowledged'),
        ('RESOLVED', 'Resolved'),
        ('DISMISSED', 'Dismissed'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    kpi = models.ForeignKey(KPI, on_delete=models.CASCADE, related_name='alerts')
    alert_type = models.CharField(max_length=20, choices=ALERT_TYPES)
    severity = models.CharField(max_length=10, choices=SEVERITY_LEVELS)

    # Alert details
    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200)
    message = models.TextField()
    message_ar = models.TextField()

    # Values
    current_value = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    threshold_value = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    target_value = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)

    # Status and resolution
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='ACTIVE')
    acknowledged_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='acknowledged_alerts')
    acknowledged_at = models.DateTimeField(null=True, blank=True)
    resolved_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_alerts')
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolution_notes = models.TextField(blank=True)

    # Notifications
    notified_users = models.ManyToManyField(Employee, blank=True, related_name='kpi_alert_notifications')
    notification_sent = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.kpi.name} - {self.title} ({self.severity})"

    class Meta:
        ordering = ['-created_at']


# Performance Review Models
class PerformanceReview(models.Model):
    REVIEW_TYPES = [
        ('annual', 'Annual Review'),
        ('quarterly', 'Quarterly Review'),
        ('probation', 'Probation Review'),
        ('project', 'Project Review'),
    ]

    RATING_CHOICES = [
        (1, 'Poor'),
        (2, 'Below Average'),
        (3, 'Average'),
        (4, 'Good'),
        (5, 'Excellent'),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='performance_reviews')
    reviewer = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='conducted_reviews')
    review_period_start = models.DateField()
    review_period_end = models.DateField()
    review_type = models.CharField(max_length=20, choices=REVIEW_TYPES, default='annual')
    overall_rating = models.IntegerField(choices=RATING_CHOICES)
    goals_achievement = models.IntegerField(choices=RATING_CHOICES)
    communication_skills = models.IntegerField(choices=RATING_CHOICES)
    teamwork = models.IntegerField(choices=RATING_CHOICES)
    leadership = models.IntegerField(choices=RATING_CHOICES, null=True, blank=True)
    technical_skills = models.IntegerField(choices=RATING_CHOICES)
    strengths = models.TextField()
    areas_for_improvement = models.TextField()
    goals_for_next_period = models.TextField()
    reviewer_comments = models.TextField()
    employee_comments = models.TextField(blank=True)
    hr_comments = models.TextField(blank=True)
    is_final = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.employee.user.first_name} {self.employee.user.last_name} - {self.review_type} ({self.review_period_start})"

    class Meta:
        ordering = ['-review_period_end']


# Payroll Models
class PayrollPeriod(models.Model):
    name = models.CharField(max_length=100)
    start_date = models.DateField()
    end_date = models.DateField()
    is_processed = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.name} ({self.start_date} - {self.end_date})"

    class Meta:
        ordering = ['-start_date']


class PayrollEntry(models.Model):
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE)
    payroll_period = models.ForeignKey(PayrollPeriod, on_delete=models.CASCADE)
    basic_salary = models.DecimalField(max_digits=10, decimal_places=2)
    allowances = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    overtime_hours = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    overtime_rate = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    bonuses = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    deductions = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    tax_deduction = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    insurance_deduction = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    net_salary = models.DecimalField(max_digits=10, decimal_places=2)
    is_paid = models.BooleanField(default=False)
    payment_date = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        # Calculate net salary
        gross_salary = self.basic_salary + self.allowances + (self.overtime_hours * self.overtime_rate) + self.bonuses
        total_deductions = self.deductions + self.tax_deduction + self.insurance_deduction
        self.net_salary = gross_salary - total_deductions
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.employee.user.first_name} {self.employee.user.last_name} - {self.payroll_period.name}"

    class Meta:
        unique_together = ['employee', 'payroll_period']


# Recruitment Models
class JobPosting(models.Model):
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('closed', 'Closed'),
        ('on_hold', 'On Hold'),
    ]

    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    department = models.ForeignKey(Department, on_delete=models.CASCADE)
    description = models.TextField()
    description_ar = models.TextField(blank=True)
    requirements = models.TextField()
    requirements_ar = models.TextField(blank=True)
    salary_min = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    salary_max = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    employment_type = models.CharField(max_length=50, choices=[
        ('full_time', 'Full Time'),
        ('part_time', 'Part Time'),
        ('contract', 'Contract'),
        ('internship', 'Internship'),
    ], default='full_time')
    location = models.CharField(max_length=200)
    location_ar = models.CharField(max_length=200, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    posted_date = models.DateField(auto_now_add=True)
    closing_date = models.DateField(null=True, blank=True)
    posted_by = models.ForeignKey(Employee, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.title} - {self.department.name}"

    class Meta:
        ordering = ['-posted_date']


# Training Models
class TrainingProgram(models.Model):
    STATUS_CHOICES = [
        ('planned', 'Planned'),
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField()
    description_ar = models.TextField(blank=True)
    instructor = models.CharField(max_length=200)
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)
    start_date = models.DateField()
    end_date = models.DateField()
    duration_hours = models.IntegerField()
    max_participants = models.IntegerField()
    cost_per_participant = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    location = models.CharField(max_length=200, blank=True)
    is_mandatory = models.BooleanField(default=False)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='planned')
    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

    class Meta:
        ordering = ['-start_date']


# Invoice Models
class Invoice(models.Model):
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('sent', 'Sent'),
        ('paid', 'Paid'),
        ('overdue', 'Overdue'),
        ('cancelled', 'Cancelled'),
    ]

    invoice_number = models.CharField(max_length=50, unique=True)
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='invoices')
    issue_date = models.DateField()
    due_date = models.DateField()
    subtotal = models.DecimalField(max_digits=12, decimal_places=2)
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    notes = models.TextField(blank=True)
    payment_terms = models.CharField(max_length=200, blank=True)
    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if not self.invoice_number:
            self.invoice_number = self.generate_invoice_number()
        super().save(*args, **kwargs)

    def generate_invoice_number(self):
        from django.utils import timezone
        import random
        date_part = timezone.now().strftime('%Y%m')
        random_part = f"{random.randint(1000, 9999)}"
        return f"INV-{date_part}-{random_part}"

    def __str__(self):
        return f"{self.invoice_number} - {self.customer.get_full_name()}"

    class Meta:
        ordering = ['-created_at']


# Cost Center Models
class CostCenter(models.Model):
    code = models.CharField(max_length=20, unique=True)
    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='cost_centers')
    manager = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)
    budget_allocated = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    budget_spent = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.code} - {self.name}"

    class Meta:
        ordering = ['code']


class APIKey(models.Model):
    """
    API Key management for external integrations
    """
    KEY_TYPES = [
        ('INTERNAL', 'Internal API Key'),
        ('EXTERNAL', 'External Service Key'),
        ('WEBHOOK', 'Webhook Key'),
        ('INTEGRATION', 'Integration Key'),
    ]

    STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('INACTIVE', 'Inactive'),
        ('EXPIRED', 'Expired'),
        ('REVOKED', 'Revoked'),
    ]

    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    key_type = models.CharField(max_length=20, choices=KEY_TYPES)

    # Key management
    key_id = models.CharField(max_length=100, unique=True)
    key_secret = models.CharField(max_length=255)  # Hashed
    key_prefix = models.CharField(max_length=20, default='ems')

    # Permissions and scope
    scopes = models.JSONField(default=list, help_text="List of allowed scopes/permissions")
    allowed_ips = models.JSONField(default=list, help_text="List of allowed IP addresses")
    rate_limit = models.PositiveIntegerField(default=1000, help_text="Requests per hour")

    # Status and lifecycle
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='ACTIVE')
    expires_at = models.DateTimeField(null=True, blank=True)
    last_used_at = models.DateTimeField(null=True, blank=True)
    usage_count = models.PositiveIntegerField(default=0)

    # System fields
    created_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.key_type})"

    def is_valid(self):
        """Check if API key is valid and not expired"""
        if self.status != 'ACTIVE':
            return False
        if self.expires_at and timezone.now() > self.expires_at:
            self.status = 'EXPIRED'
            self.save()
            return False
        return True

    def increment_usage(self):
        """Increment usage counter and update last used timestamp"""
        self.usage_count += 1
        self.last_used_at = timezone.now()
        self.save(update_fields=['usage_count', 'last_used_at'])


class ExternalService(models.Model):
    """
    External service integration configuration
    """
    SERVICE_TYPES = [
        ('PAYMENT', 'Payment Gateway'),
        ('BANK', 'Banking Service'),
        ('ACCOUNTING', 'Accounting Software'),
        ('CRM', 'Customer Relationship Management'),
        ('INVENTORY', 'Inventory Management'),
        ('SHIPPING', 'Shipping Service'),
        ('EMAIL', 'Email Service'),
        ('SMS', 'SMS Service'),
        ('STORAGE', 'Cloud Storage'),
        ('ANALYTICS', 'Analytics Service'),
        ('CUSTOM', 'Custom Integration'),
    ]

    STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('INACTIVE', 'Inactive'),
        ('TESTING', 'Testing'),
        ('ERROR', 'Error'),
        ('MAINTENANCE', 'Maintenance'),
    ]

    name = models.CharField(max_length=200)
    service_type = models.CharField(max_length=20, choices=SERVICE_TYPES)
    description = models.TextField(blank=True)

    # Connection details
    base_url = models.URLField()
    api_version = models.CharField(max_length=20, blank=True)
    authentication_type = models.CharField(max_length=50, default='API_KEY')

    # Configuration
    config = models.JSONField(default=dict, help_text="Service-specific configuration")
    headers = models.JSONField(default=dict, help_text="Default headers for requests")
    timeout = models.PositiveIntegerField(default=30, help_text="Request timeout in seconds")

    # Credentials (encrypted)
    credentials = models.JSONField(default=dict, help_text="Encrypted service credentials")

    # Status and monitoring
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='INACTIVE')
    last_health_check = models.DateTimeField(null=True, blank=True)
    health_check_url = models.URLField(blank=True)
    is_healthy = models.BooleanField(default=False)

    # Usage tracking
    total_requests = models.PositiveIntegerField(default=0)
    successful_requests = models.PositiveIntegerField(default=0)
    failed_requests = models.PositiveIntegerField(default=0)
    last_request_at = models.DateTimeField(null=True, blank=True)

    # System fields
    created_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['service_type', 'name']

    def __str__(self):
        return f"{self.name} ({self.service_type})"

    def get_success_rate(self):
        """Calculate success rate percentage"""
        if self.total_requests == 0:
            return 0
        return (self.successful_requests / self.total_requests) * 100

    def increment_request_count(self, success=True):
        """Increment request counters"""
        self.total_requests += 1
        if success:
            self.successful_requests += 1
        else:
            self.failed_requests += 1
        self.last_request_at = timezone.now()
        self.save(update_fields=['total_requests', 'successful_requests', 'failed_requests', 'last_request_at'])


class WebhookEndpoint(models.Model):
    """
    Webhook endpoint configuration for receiving external events
    """
    EVENT_TYPES = [
        ('PAYMENT_RECEIVED', 'Payment Received'),
        ('PAYMENT_FAILED', 'Payment Failed'),
        ('INVOICE_PAID', 'Invoice Paid'),
        ('ORDER_CREATED', 'Order Created'),
        ('ORDER_UPDATED', 'Order Updated'),
        ('CUSTOMER_CREATED', 'Customer Created'),
        ('ASSET_UPDATED', 'Asset Updated'),
        ('MAINTENANCE_DUE', 'Maintenance Due'),
        ('CUSTOM', 'Custom Event'),
    ]

    STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('INACTIVE', 'Inactive'),
        ('PAUSED', 'Paused'),
        ('ERROR', 'Error'),
    ]

    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    event_types = models.JSONField(default=list, help_text="List of event types to handle")

    # Endpoint configuration
    url = models.URLField()
    method = models.CharField(max_length=10, default='POST')
    headers = models.JSONField(default=dict, help_text="Headers to include in webhook calls")
    secret = models.CharField(max_length=255, blank=True, help_text="Webhook secret for verification")

    # Retry configuration
    max_retries = models.PositiveIntegerField(default=3)
    retry_delay = models.PositiveIntegerField(default=60, help_text="Retry delay in seconds")
    timeout = models.PositiveIntegerField(default=30, help_text="Request timeout in seconds")

    # Status and monitoring
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='ACTIVE')
    is_verified = models.BooleanField(default=False)
    last_triggered_at = models.DateTimeField(null=True, blank=True)

    # Usage statistics
    total_calls = models.PositiveIntegerField(default=0)
    successful_calls = models.PositiveIntegerField(default=0)
    failed_calls = models.PositiveIntegerField(default=0)

    # System fields
    created_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    def get_success_rate(self):
        """Calculate webhook success rate"""
        if self.total_calls == 0:
            return 0
        return (self.successful_calls / self.total_calls) * 100

    def increment_call_count(self, success=True):
        """Increment call counters"""
        self.total_calls += 1
        if success:
            self.successful_calls += 1
        else:
            self.failed_calls += 1
        self.last_triggered_at = timezone.now()
        self.save(update_fields=['total_calls', 'successful_calls', 'failed_calls', 'last_triggered_at'])


class WebhookEvent(models.Model):
    """
    Webhook event log for tracking webhook deliveries
    """
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('DELIVERED', 'Delivered'),
        ('FAILED', 'Failed'),
        ('RETRYING', 'Retrying'),
        ('CANCELLED', 'Cancelled'),
    ]

    webhook_endpoint = models.ForeignKey(WebhookEndpoint, on_delete=models.CASCADE, related_name='events')
    event_type = models.CharField(max_length=50)
    event_id = models.CharField(max_length=100, unique=True)

    # Event data
    payload = models.JSONField(help_text="Event payload data")
    headers = models.JSONField(default=dict, help_text="Headers sent with the webhook")

    # Delivery tracking
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    attempts = models.PositiveIntegerField(default=0)
    max_attempts = models.PositiveIntegerField(default=3)

    # Response tracking
    response_status_code = models.PositiveIntegerField(null=True, blank=True)
    response_body = models.TextField(blank=True)
    response_headers = models.JSONField(default=dict)

    # Timing
    scheduled_at = models.DateTimeField(default=timezone.now)
    first_attempted_at = models.DateTimeField(null=True, blank=True)
    last_attempted_at = models.DateTimeField(null=True, blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)

    # Error tracking
    error_message = models.TextField(blank=True)

    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['event_type', 'status']),
            models.Index(fields=['webhook_endpoint', 'status']),
        ]

    def __str__(self):
        return f"{self.event_type} - {self.event_id}"

    def can_retry(self):
        """Check if event can be retried"""
        return self.status in ['FAILED', 'RETRYING'] and self.attempts < self.max_attempts

    def mark_delivered(self, status_code, response_body='', response_headers=None):
        """Mark event as successfully delivered"""
        self.status = 'DELIVERED'
        self.response_status_code = status_code
        self.response_body = response_body
        self.response_headers = response_headers or {}
        self.delivered_at = timezone.now()
        self.save()

    def mark_failed(self, error_message, status_code=None, response_body=''):
        """Mark event as failed"""
        self.status = 'FAILED' if self.attempts >= self.max_attempts else 'RETRYING'
        self.error_message = error_message
        self.response_status_code = status_code
        self.response_body = response_body
        self.save()


class IntegrationLog(models.Model):
    """
    Integration activity log for monitoring and debugging
    """
    LOG_TYPES = [
        ('API_REQUEST', 'API Request'),
        ('API_RESPONSE', 'API Response'),
        ('WEBHOOK_SENT', 'Webhook Sent'),
        ('WEBHOOK_RECEIVED', 'Webhook Received'),
        ('SYNC_OPERATION', 'Sync Operation'),
        ('ERROR', 'Error'),
        ('INFO', 'Information'),
    ]

    SEVERITY_LEVELS = [
        ('DEBUG', 'Debug'),
        ('INFO', 'Information'),
        ('WARNING', 'Warning'),
        ('ERROR', 'Error'),
        ('CRITICAL', 'Critical'),
    ]

    log_type = models.CharField(max_length=20, choices=LOG_TYPES)
    severity = models.CharField(max_length=20, choices=SEVERITY_LEVELS, default='INFO')

    # Related objects
    external_service = models.ForeignKey(ExternalService, on_delete=models.SET_NULL, null=True, blank=True)
    webhook_endpoint = models.ForeignKey(WebhookEndpoint, on_delete=models.SET_NULL, null=True, blank=True)
    api_key = models.ForeignKey(APIKey, on_delete=models.SET_NULL, null=True, blank=True)

    # Log details
    message = models.TextField()
    details = models.JSONField(default=dict, help_text="Additional log details")

    # Request/Response data
    request_data = models.JSONField(default=dict, blank=True)
    response_data = models.JSONField(default=dict, blank=True)

    # Timing and performance
    duration_ms = models.PositiveIntegerField(null=True, blank=True, help_text="Operation duration in milliseconds")

    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['log_type', 'severity']),
            models.Index(fields=['external_service', 'created_at']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.log_type} - {self.severity} - {self.created_at.strftime('%Y-%m-%d %H:%M:%S')}"


# Extend the existing Role model with security features
# Add security-related fields to the existing permissions JSON field


class UserSecurityProfile(models.Model):
    """
    Enhanced user security profile with advanced security features
    Extends the existing Employee model with security-specific settings
    """
    SECURITY_LEVELS = [
        ('LOW', 'Low Security'),
        ('MEDIUM', 'Medium Security'),
        ('HIGH', 'High Security'),
        ('CRITICAL', 'Critical Security'),
    ]

    MFA_METHODS = [
        ('NONE', 'No MFA'),
        ('SMS', 'SMS Code'),
        ('EMAIL', 'Email Code'),
        ('TOTP', 'Time-based OTP'),
        ('HARDWARE', 'Hardware Token'),
    ]

    # Link to existing Employee model instead of creating separate user system
    employee = models.OneToOneField(Employee, on_delete=models.CASCADE, related_name='security_profile')

    # Security settings
    security_level = models.CharField(max_length=20, choices=SECURITY_LEVELS, default='MEDIUM')
    mfa_enabled = models.BooleanField(default=False)
    mfa_method = models.CharField(max_length=20, choices=MFA_METHODS, default='NONE')
    mfa_secret = models.CharField(max_length=255, blank=True)  # Encrypted

    # Password policy
    password_expires_at = models.DateTimeField(null=True, blank=True)
    password_history = models.JSONField(default=list, help_text="Hashed password history")
    failed_login_attempts = models.PositiveIntegerField(default=0)
    account_locked_until = models.DateTimeField(null=True, blank=True)

    # Session management
    max_concurrent_sessions = models.PositiveIntegerField(default=3)
    current_session_count = models.PositiveIntegerField(default=0)
    last_login_ip = models.GenericIPAddressField(null=True, blank=True)
    last_login_location = models.CharField(max_length=200, blank=True)

    # Security preferences
    login_notifications = models.BooleanField(default=True)
    suspicious_activity_alerts = models.BooleanField(default=True)
    data_access_notifications = models.BooleanField(default=False)

    # Compliance tracking
    security_training_completed = models.DateTimeField(null=True, blank=True)
    privacy_policy_accepted = models.DateTimeField(null=True, blank=True)
    terms_accepted = models.DateTimeField(null=True, blank=True)

    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-security_level', 'employee__user__username']

    def __str__(self):
        return f"{self.employee.user.username} - {self.security_level}"

    def is_account_locked(self):
        """Check if account is currently locked"""
        if self.account_locked_until:
            return timezone.now() < self.account_locked_until
        return False

    def increment_failed_login(self):
        """Increment failed login attempts and lock if necessary"""
        self.failed_login_attempts += 1

        # Lock account after 5 failed attempts for 30 minutes
        if self.failed_login_attempts >= 5:
            self.account_locked_until = timezone.now() + timedelta(minutes=30)

        self.save()

    def reset_failed_login(self):
        """Reset failed login attempts after successful login"""
        self.failed_login_attempts = 0
        self.account_locked_until = None
        self.save()


class AuditTrail(models.Model):
    """
    Comprehensive audit trail for compliance and security monitoring
    """
    ACTION_TYPES = [
        ('CREATE', 'Create'),
        ('READ', 'Read'),
        ('UPDATE', 'Update'),
        ('DELETE', 'Delete'),
        ('LOGIN', 'Login'),
        ('LOGOUT', 'Logout'),
        ('EXPORT', 'Export'),
        ('IMPORT', 'Import'),
        ('APPROVE', 'Approve'),
        ('REJECT', 'Reject'),
        ('CUSTOM', 'Custom Action'),
    ]

    RISK_LEVELS = [
        ('LOW', 'Low Risk'),
        ('MEDIUM', 'Medium Risk'),
        ('HIGH', 'High Risk'),
        ('CRITICAL', 'Critical Risk'),
    ]

    # Action details
    action_type = models.CharField(max_length=20, choices=ACTION_TYPES)
    action_description = models.TextField()
    risk_level = models.CharField(max_length=20, choices=RISK_LEVELS, default='LOW')

    # User and session information
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    employee = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)
    session_id = models.CharField(max_length=100, blank=True)

    # Resource information
    resource_type = models.CharField(max_length=100, help_text="Model name or resource type")
    resource_id = models.CharField(max_length=100, blank=True, help_text="Resource identifier")
    resource_name = models.CharField(max_length=200, blank=True, help_text="Human-readable resource name")

    # Change tracking
    old_values = models.JSONField(default=dict, blank=True, help_text="Previous values")
    new_values = models.JSONField(default=dict, blank=True, help_text="New values")
    changed_fields = models.JSONField(default=list, help_text="List of changed field names")

    # Context information
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    location = models.CharField(max_length=200, blank=True)
    additional_context = models.JSONField(default=dict, help_text="Additional context data")

    # Compliance and categorization
    compliance_category = models.CharField(max_length=100, blank=True, help_text="Compliance framework category")
    business_justification = models.TextField(blank=True)

    # System fields
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['resource_type', 'resource_id']),
            models.Index(fields=['action_type', 'risk_level']),
            models.Index(fields=['timestamp']),
        ]

    def __str__(self):
        return f"{self.action_type} - {self.resource_type} - {self.timestamp.strftime('%Y-%m-%d %H:%M:%S')}"


class SecurityIncident(models.Model):
    """
    Security incident tracking and management
    """
    INCIDENT_TYPES = [
        ('UNAUTHORIZED_ACCESS', 'Unauthorized Access'),
        ('DATA_BREACH', 'Data Breach'),
        ('MALWARE', 'Malware Detection'),
        ('PHISHING', 'Phishing Attempt'),
        ('BRUTE_FORCE', 'Brute Force Attack'),
        ('PRIVILEGE_ESCALATION', 'Privilege Escalation'),
        ('DATA_LOSS', 'Data Loss'),
        ('SYSTEM_COMPROMISE', 'System Compromise'),
        ('POLICY_VIOLATION', 'Policy Violation'),
        ('OTHER', 'Other'),
    ]

    SEVERITY_LEVELS = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    ]

    STATUS_CHOICES = [
        ('OPEN', 'Open'),
        ('INVESTIGATING', 'Investigating'),
        ('CONTAINED', 'Contained'),
        ('RESOLVED', 'Resolved'),
        ('CLOSED', 'Closed'),
    ]

    # Incident identification
    incident_id = models.CharField(max_length=50, unique=True)
    title = models.CharField(max_length=200)
    description = models.TextField()
    incident_type = models.CharField(max_length=30, choices=INCIDENT_TYPES)
    severity = models.CharField(max_length=20, choices=SEVERITY_LEVELS)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='OPEN')

    # Affected resources
    affected_users = models.ManyToManyField(User, blank=True, related_name='security_incidents')
    affected_systems = models.JSONField(default=list, help_text="List of affected systems/resources")
    data_categories_affected = models.JSONField(default=list, help_text="Types of data potentially affected")

    # Timeline
    detected_at = models.DateTimeField()
    reported_at = models.DateTimeField(auto_now_add=True)
    contained_at = models.DateTimeField(null=True, blank=True)
    resolved_at = models.DateTimeField(null=True, blank=True)

    # Investigation details
    root_cause = models.TextField(blank=True)
    impact_assessment = models.TextField(blank=True)
    remediation_actions = models.JSONField(default=list, help_text="List of remediation actions taken")

    # Compliance and reporting
    regulatory_notification_required = models.BooleanField(default=False)
    regulatory_notification_sent = models.DateTimeField(null=True, blank=True)
    customer_notification_required = models.BooleanField(default=False)
    customer_notification_sent = models.DateTimeField(null=True, blank=True)

    # Assignment and tracking
    assigned_to = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_incidents')
    reported_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='reported_incidents')

    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-severity', '-detected_at']

    def __str__(self):
        return f"{self.incident_id} - {self.title}"

    def get_duration(self):
        """Get incident duration from detection to resolution"""
        if self.resolved_at:
            return self.resolved_at - self.detected_at
        return timezone.now() - self.detected_at


class ComplianceFramework(models.Model):
    """
    Compliance framework management (GDPR, SOX, HIPAA, etc.)
    """
    FRAMEWORK_TYPES = [
        ('GDPR', 'General Data Protection Regulation'),
        ('SOX', 'Sarbanes-Oxley Act'),
        ('HIPAA', 'Health Insurance Portability and Accountability Act'),
        ('PCI_DSS', 'Payment Card Industry Data Security Standard'),
        ('ISO_27001', 'ISO/IEC 27001'),
        ('NIST', 'NIST Cybersecurity Framework'),
        ('SAUDI_DPA', 'Saudi Data Protection Act'),
        ('CUSTOM', 'Custom Framework'),
    ]

    name = models.CharField(max_length=100)
    name_ar = models.CharField(max_length=100, blank=True)
    framework_type = models.CharField(max_length=20, choices=FRAMEWORK_TYPES)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)

    # Framework details
    version = models.CharField(max_length=20, blank=True)
    effective_date = models.DateField()
    requirements = models.JSONField(default=list, help_text="List of compliance requirements")

    # Implementation status
    is_active = models.BooleanField(default=True)
    implementation_status = models.CharField(max_length=50, default='PLANNING')
    compliance_score = models.DecimalField(max_digits=5, decimal_places=2, default=0.00, help_text="Compliance percentage")

    # Monitoring and reporting
    last_assessment_date = models.DateField(null=True, blank=True)
    next_assessment_due = models.DateField(null=True, blank=True)
    assessment_frequency_months = models.PositiveIntegerField(default=12)

    # System fields
    created_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['framework_type', 'name']

    def __str__(self):
        return f"{self.name} ({self.framework_type})"

    def is_assessment_due(self):
        """Check if compliance assessment is due"""
        if not self.next_assessment_due:
            return True
        return timezone.now().date() >= self.next_assessment_due


class ComplianceControl(models.Model):
    """
    Individual compliance controls and their implementation status
    """
    CONTROL_TYPES = [
        ('PREVENTIVE', 'Preventive'),
        ('DETECTIVE', 'Detective'),
        ('CORRECTIVE', 'Corrective'),
        ('COMPENSATING', 'Compensating'),
    ]

    STATUS_CHOICES = [
        ('NOT_IMPLEMENTED', 'Not Implemented'),
        ('PARTIALLY_IMPLEMENTED', 'Partially Implemented'),
        ('IMPLEMENTED', 'Implemented'),
        ('NEEDS_IMPROVEMENT', 'Needs Improvement'),
        ('NOT_APPLICABLE', 'Not Applicable'),
    ]

    framework = models.ForeignKey(ComplianceFramework, on_delete=models.CASCADE, related_name='controls')

    # Control identification
    control_id = models.CharField(max_length=50)
    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField()
    description_ar = models.TextField(blank=True)
    control_type = models.CharField(max_length=20, choices=CONTROL_TYPES)

    # Implementation details
    status = models.CharField(max_length=30, choices=STATUS_CHOICES, default='NOT_IMPLEMENTED')
    implementation_notes = models.TextField(blank=True)
    evidence_required = models.JSONField(default=list, help_text="Types of evidence required")
    evidence_collected = models.JSONField(default=list, help_text="Evidence collected")

    # Risk and priority
    risk_level = models.CharField(max_length=20, choices=[
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    ], default='MEDIUM')
    priority = models.PositiveIntegerField(default=5, help_text="Priority 1-10")

    # Testing and validation
    last_tested_date = models.DateField(null=True, blank=True)
    test_frequency_months = models.PositiveIntegerField(default=12)
    test_results = models.TextField(blank=True)

    # Assignment and ownership
    control_owner = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='owned_controls')
    responsible_department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)

    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['framework', 'priority', 'control_id']
        unique_together = ['framework', 'control_id']

    def __str__(self):
        return f"{self.framework.name} - {self.control_id}: {self.name}"

    def is_test_due(self):
        """Check if control testing is due"""
        if not self.last_tested_date:
            return True

        next_test_due = self.last_tested_date + timedelta(days=self.test_frequency_months * 30)
        return timezone.now().date() >= next_test_due


class DataClassification(models.Model):
    """
    Data classification and protection requirements
    """
    CLASSIFICATION_LEVELS = [
        ('PUBLIC', 'Public'),
        ('INTERNAL', 'Internal'),
        ('CONFIDENTIAL', 'Confidential'),
        ('RESTRICTED', 'Restricted'),
        ('TOP_SECRET', 'Top Secret'),
    ]

    RETENTION_UNITS = [
        ('DAYS', 'Days'),
        ('MONTHS', 'Months'),
        ('YEARS', 'Years'),
        ('INDEFINITE', 'Indefinite'),
    ]

    # Classification details
    name = models.CharField(max_length=100, unique=True)
    name_ar = models.CharField(max_length=100, blank=True)
    classification_level = models.CharField(max_length=20, choices=CLASSIFICATION_LEVELS)
    description = models.TextField()
    description_ar = models.TextField(blank=True)

    # Protection requirements
    encryption_required = models.BooleanField(default=False)
    access_logging_required = models.BooleanField(default=True)
    approval_required_for_access = models.BooleanField(default=False)

    # Retention and disposal
    retention_period = models.PositiveIntegerField(null=True, blank=True)
    retention_unit = models.CharField(max_length=20, choices=RETENTION_UNITS, default='YEARS')
    disposal_method = models.CharField(max_length=100, blank=True)

    # Compliance mapping
    applicable_frameworks = models.ManyToManyField(ComplianceFramework, blank=True)
    regulatory_requirements = models.JSONField(default=list, help_text="Specific regulatory requirements")

    # Access controls
    authorized_roles = models.ManyToManyField(Role, blank=True)
    geographic_restrictions = models.JSONField(default=list, help_text="Geographic access restrictions")

    # System fields
    created_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['classification_level', 'name']

    def __str__(self):
        return f"{self.name} ({self.classification_level})"


class SecurityAlert(models.Model):
    """
    Security alerts and notifications system
    """
    ALERT_TYPES = [
        ('LOGIN_ANOMALY', 'Login Anomaly'),
        ('MULTIPLE_FAILED_LOGINS', 'Multiple Failed Logins'),
        ('PRIVILEGE_ESCALATION', 'Privilege Escalation'),
        ('DATA_ACCESS_VIOLATION', 'Data Access Violation'),
        ('UNUSUAL_DATA_EXPORT', 'Unusual Data Export'),
        ('SYSTEM_INTRUSION', 'System Intrusion'),
        ('MALWARE_DETECTION', 'Malware Detection'),
        ('POLICY_VIOLATION', 'Policy Violation'),
        ('COMPLIANCE_BREACH', 'Compliance Breach'),
        ('CUSTOM', 'Custom Alert'),
    ]

    SEVERITY_LEVELS = [
        ('INFO', 'Information'),
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    ]

    STATUS_CHOICES = [
        ('OPEN', 'Open'),
        ('ACKNOWLEDGED', 'Acknowledged'),
        ('INVESTIGATING', 'Investigating'),
        ('RESOLVED', 'Resolved'),
        ('FALSE_POSITIVE', 'False Positive'),
    ]

    # Alert identification
    alert_id = models.CharField(max_length=50, unique=True)
    alert_type = models.CharField(max_length=30, choices=ALERT_TYPES)
    title = models.CharField(max_length=200)
    description = models.TextField()
    severity = models.CharField(max_length=20, choices=SEVERITY_LEVELS)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='OPEN')

    # Source information
    source_system = models.CharField(max_length=100, blank=True)
    source_ip = models.GenericIPAddressField(null=True, blank=True)
    affected_user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    affected_resource = models.CharField(max_length=200, blank=True)

    # Alert details
    detection_rules = models.JSONField(default=list, help_text="Rules that triggered the alert")
    evidence = models.JSONField(default=dict, help_text="Supporting evidence")
    risk_score = models.PositiveIntegerField(default=0, help_text="Risk score 0-100")

    # Response and resolution
    assigned_to = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)
    response_actions = models.JSONField(default=list, help_text="Actions taken in response")
    resolution_notes = models.TextField(blank=True)

    # Timeline
    detected_at = models.DateTimeField()
    acknowledged_at = models.DateTimeField(null=True, blank=True)
    resolved_at = models.DateTimeField(null=True, blank=True)

    # Escalation
    escalated = models.BooleanField(default=False)
    escalated_at = models.DateTimeField(null=True, blank=True)
    escalated_to = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='escalated_alerts')

    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-severity', '-detected_at']
        indexes = [
            models.Index(fields=['alert_type', 'severity']),
            models.Index(fields=['status', 'detected_at']),
            models.Index(fields=['affected_user', 'detected_at']),
        ]

    def __str__(self):
        return f"{self.alert_id} - {self.title}"

    def get_response_time(self):
        """Get time from detection to acknowledgment"""
        if self.acknowledged_at:
            return self.acknowledged_at - self.detected_at
        return None

    def get_resolution_time(self):
        """Get time from detection to resolution"""
        if self.resolved_at:
            return self.resolved_at - self.detected_at
        return None
