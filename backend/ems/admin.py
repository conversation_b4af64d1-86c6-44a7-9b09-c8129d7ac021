from django.contrib import admin
from .models import (
    Department, Employee, Role, UserProfile, Budget, Expense,
    AccountType, ChartOfAccounts, FiscalYear, JournalEntryBatch, JournalEntry,
    Vendor, VendorInvoice, CustomerInvoice, Payment
)

# Register your models here.

# Basic models
@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ['name', 'name_ar', 'manager', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'name_ar']

@admin.register(Employee)
class EmployeeAdmin(admin.ModelAdmin):
    list_display = ['employee_id', 'user', 'department', 'position', 'employment_status', 'hire_date']
    list_filter = ['department', 'employment_status', 'hire_date']
    search_fields = ['employee_id', 'user__first_name', 'user__last_name', 'position']

# Financial models
@admin.register(AccountType)
class AccountTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'name_ar', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']

@admin.register(ChartOfAccounts)
class ChartOfAccountsAdmin(admin.ModelAdmin):
    list_display = ['account_code', 'account_name', 'account_type', 'parent_account', 'level', 'is_active']
    list_filter = ['account_type', 'level', 'is_active', 'is_system_account']
    search_fields = ['account_code', 'account_name', 'account_name_ar']
    ordering = ['account_code']

@admin.register(FiscalYear)
class FiscalYearAdmin(admin.ModelAdmin):
    list_display = ['name', 'start_date', 'end_date', 'is_current', 'is_closed']
    list_filter = ['is_current', 'is_closed']

@admin.register(JournalEntryBatch)
class JournalEntryBatchAdmin(admin.ModelAdmin):
    list_display = ['batch_number', 'description', 'created_by', 'is_posted', 'created_at']
    list_filter = ['is_posted', 'created_at']
    search_fields = ['batch_number', 'description']

@admin.register(JournalEntry)
class JournalEntryAdmin(admin.ModelAdmin):
    list_display = ['entry_number', 'account', 'transaction_date', 'debit_amount', 'credit_amount', 'is_posted']
    list_filter = ['is_posted', 'transaction_date', 'account__account_type']
    search_fields = ['entry_number', 'description', 'account__account_name']

@admin.register(Vendor)
class VendorAdmin(admin.ModelAdmin):
    list_display = ['vendor_code', 'company_name', 'email', 'phone', 'is_active', 'is_approved']
    list_filter = ['is_active', 'is_approved', 'created_at']
    search_fields = ['vendor_code', 'company_name', 'email']

@admin.register(VendorInvoice)
class VendorInvoiceAdmin(admin.ModelAdmin):
    list_display = ['invoice_number', 'vendor', 'invoice_date', 'due_date', 'total_amount', 'status']
    list_filter = ['status', 'invoice_date', 'due_date']
    search_fields = ['invoice_number', 'vendor_invoice_number', 'vendor__company_name']

@admin.register(CustomerInvoice)
class CustomerInvoiceAdmin(admin.ModelAdmin):
    list_display = ['invoice_number', 'customer', 'invoice_date', 'due_date', 'total_amount', 'status']
    list_filter = ['status', 'invoice_date', 'due_date']
    search_fields = ['invoice_number', 'customer__name']

@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ['payment_number', 'payment_type', 'payment_method', 'payment_date', 'amount']
    list_filter = ['payment_type', 'payment_method', 'payment_date']
    search_fields = ['payment_number', 'reference_number']
