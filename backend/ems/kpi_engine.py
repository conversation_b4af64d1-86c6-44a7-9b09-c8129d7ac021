"""
Enterprise KPI Calculation Engine
Automatically calculates KPIs from real operational data following industry best practices.

This engine:
1. Calculates KPIs from real business data (not sample data)
2. Updates KPIs in real-time when underlying data changes
3. Provides scheduled recalculation capabilities
4. Follows enterprise BI patterns from Tableau, Power BI, etc.
"""

from django.db.models import Sum, Count, Avg, Q, F, Case, When, Value
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any
import logging

from .models import (
    KPI, KPIValue, Employee, Project, Task, CustomerInvoice, 
    Expense, Attendance, LeaveRequest, Department, Payment
)

logger = logging.getLogger(__name__)


class KPICalculationEngine:
    """
    Enterprise-grade KPI calculation engine that automatically derives
    business metrics from operational data.
    """
    
    def __init__(self):
        self.calculation_methods = {
            # HR KPIs
            'EMPLOYEE_TURNOVER_RATE': self._calculate_employee_turnover_rate,
            'EMPLOYEE_SATISFACTION_SCORE': self._calculate_employee_satisfaction_score,
            'AVERAGE_ATTENDANCE_RATE': self._calculate_attendance_rate,
            'TIME_TO_FILL_POSITIONS': self._calculate_time_to_fill,
            'EMPLOYEE_PRODUCTIVITY_SCORE': self._calculate_employee_productivity,
            
            # Financial KPIs
            'MONTHLY_REVENUE': self._calculate_monthly_revenue,
            'PROFIT_MARGIN': self._calculate_profit_margin,
            'AVERAGE_INVOICE_VALUE': self._calculate_average_invoice_value,
            'CASH_FLOW': self._calculate_cash_flow,
            'REVENUE_GROWTH_RATE': self._calculate_revenue_growth_rate,
            
            # Operations KPIs
            'PROJECT_COMPLETION_RATE': self._calculate_project_completion_rate,
            'TASK_COMPLETION_RATE': self._calculate_task_completion_rate,
            'AVERAGE_PROJECT_DURATION': self._calculate_average_project_duration,
            'PROJECT_BUDGET_VARIANCE': self._calculate_project_budget_variance,
            'RESOURCE_UTILIZATION_RATE': self._calculate_resource_utilization,
            
            # Customer KPIs
            'CUSTOMER_ACQUISITION_RATE': self._calculate_customer_acquisition_rate,
            'CUSTOMER_RETENTION_RATE': self._calculate_customer_retention_rate,
            'AVERAGE_DEAL_SIZE': self._calculate_average_deal_size,
        }
    
    def calculate_kpi(self, kpi: KPI, period_start: datetime = None, period_end: datetime = None) -> Optional[Decimal]:
        """
        Calculate a single KPI value from real operational data.
        
        Args:
            kpi: The KPI to calculate
            period_start: Start of calculation period (defaults to current month start)
            period_end: End of calculation period (defaults to now)
            
        Returns:
            Calculated KPI value or None if calculation fails
        """
        if not period_start:
            period_start = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        if not period_end:
            period_end = timezone.now()
            
        calculation_method = kpi.calculation_method
        if calculation_method not in self.calculation_methods:
            logger.warning(f"No calculation method found for KPI: {kpi.name} (method: {calculation_method})")
            return None
            
        try:
            value = self.calculation_methods[calculation_method](period_start, period_end)
            logger.info(f"Calculated KPI {kpi.name}: {value}")
            return Decimal(str(value)) if value is not None else None
        except Exception as e:
            logger.error(f"Error calculating KPI {kpi.name}: {str(e)}")
            return None
    
    def calculate_all_kpis(self, period_start: datetime = None, period_end: datetime = None) -> Dict[str, Any]:
        """
        Calculate all active KPIs and update their values.
        
        Returns:
            Dictionary with calculation results and statistics
        """
        if not period_start:
            period_start = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        if not period_end:
            period_end = timezone.now()
            
        results = {
            'calculated': 0,
            'failed': 0,
            'updated_kpis': [],
            'failed_kpis': [],
            'period_start': period_start,
            'period_end': period_end
        }
        
        active_kpis = KPI.objects.filter(status='ACTIVE', calculation_method__isnull=False)
        
        for kpi in active_kpis:
            calculated_value = self.calculate_kpi(kpi, period_start, period_end)
            
            if calculated_value is not None:
                # Create or update KPI value
                kpi_value, created = KPIValue.objects.update_or_create(
                    kpi=kpi,
                    period_start=period_start,
                    period_end=period_end,
                    defaults={
                        'value': calculated_value,
                        'recorded_by': kpi.created_by,
                        'is_estimated': False,
                        'confidence_level': Decimal('100.0'),
                        'data_quality_score': Decimal('100.0'),
                        'notes': f'Automatically calculated from operational data',
                        'source_data': {
                            'calculation_method': kpi.calculation_method,
                            'engine': 'KPICalculationEngine',
                            'timestamp': timezone.now().isoformat()
                        }
                    }
                )
                
                # Update KPI current value
                kpi.current_value = calculated_value
                kpi.last_updated = timezone.now()
                kpi.save(update_fields=['current_value', 'last_updated'])
                
                results['calculated'] += 1
                results['updated_kpis'].append({
                    'kpi_id': kpi.id,
                    'name': kpi.name,
                    'value': float(calculated_value),
                    'created': created
                })
            else:
                results['failed'] += 1
                results['failed_kpis'].append({
                    'kpi_id': kpi.id,
                    'name': kpi.name,
                    'calculation_method': kpi.calculation_method
                })
        
        logger.info(f"KPI calculation completed: {results['calculated']} calculated, {results['failed']} failed")
        return results
    
    # HR KPI Calculations
    def _calculate_employee_turnover_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate employee turnover rate as percentage"""
        # Get employees who left during the period
        employees_left = Employee.objects.filter(
            is_active=False,
            updated_at__range=[start_date, end_date]
        ).count()
        
        # Get average number of employees during the period
        total_employees = Employee.objects.filter(is_active=True).count()
        
        if total_employees == 0:
            return 0.0
            
        return (employees_left / total_employees) * 100
    
    def _calculate_attendance_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate average attendance rate as percentage"""
        attendance_records = Attendance.objects.filter(
            date__range=[start_date.date(), end_date.date()]
        )

        if not attendance_records.exists():
            return 0.0

        total_records = attendance_records.count()
        present_records = attendance_records.filter(is_present=True).count()

        return (present_records / total_records) * 100 if total_records > 0 else 0.0
    
    def _calculate_employee_satisfaction_score(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate employee satisfaction score based on real workplace indicators"""
        total_employees = Employee.objects.filter(is_active=True).count()

        if total_employees == 0:
            return 0.0

        # Calculate satisfaction based on multiple real indicators
        satisfaction_score = 0.0
        factors_count = 0

        # Factor 1: Attendance rate (higher attendance = higher satisfaction)
        attendance_rate = self._calculate_attendance_rate(start_date, end_date)
        if attendance_rate > 0:
            satisfaction_score += min(attendance_rate, 100) / 100 * 25  # Max 25 points
            factors_count += 1

        # Factor 2: Leave request approval rate (higher approval = better management)
        total_leave_requests = LeaveRequest.objects.filter(
            start_date__range=[start_date.date(), end_date.date()]
        ).count()
        approved_leave_requests = LeaveRequest.objects.filter(
            start_date__range=[start_date.date(), end_date.date()],
            status='APPROVED'
        ).count()

        if total_leave_requests > 0:
            approval_rate = (approved_leave_requests / total_leave_requests) * 100
            satisfaction_score += approval_rate / 100 * 25  # Max 25 points
            factors_count += 1

        # Factor 3: Task completion rate (employees feel satisfied when productive)
        task_completion_rate = self._calculate_task_completion_rate(start_date, end_date)
        if task_completion_rate > 0:
            satisfaction_score += min(task_completion_rate, 100) / 100 * 25  # Max 25 points
            factors_count += 1

        # Factor 4: Low turnover indicates satisfaction
        turnover_rate = self._calculate_employee_turnover_rate(start_date, end_date)
        turnover_satisfaction = max(0, 100 - turnover_rate * 10)  # Lower turnover = higher satisfaction
        satisfaction_score += turnover_satisfaction / 100 * 25  # Max 25 points
        factors_count += 1

        # Return average satisfaction score (0-100 scale)
        return satisfaction_score / factors_count if factors_count > 0 else 0.0
    
    def _calculate_time_to_fill(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate average time to fill positions based on actual hiring data"""
        # Calculate based on employee creation dates and department needs
        new_employees = Employee.objects.filter(
            created_at__range=[start_date, end_date],
            is_active=True
        )

        if not new_employees.exists():
            return 0.0

        # Calculate average time from department creation to employee hiring
        # This is a simplified calculation - in real systems you'd track job postings
        total_days = 0
        count = 0

        for employee in new_employees:
            # Estimate time to fill based on when department was created vs employee hired
            if employee.department and employee.department.created_at:
                days_diff = (employee.created_at.date() - employee.department.created_at.date()).days
                if days_diff > 0:  # Only count positive differences
                    total_days += min(days_diff, 180)  # Cap at 6 months max
                    count += 1

        return total_days / count if count > 0 else 0.0
    
    def _calculate_employee_productivity(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate employee productivity score based on task completion efficiency"""
        # Get all tasks that were due or active during the period
        active_tasks = Task.objects.filter(
            Q(due_date__range=[start_date, end_date]) |
            Q(start_date__range=[start_date, end_date]) |
            Q(completion_date__range=[start_date, end_date])
        ).distinct()

        if not active_tasks.exists():
            return 0.0

        total_active_tasks = active_tasks.count()

        # Count tasks completed during the period
        completed_tasks = active_tasks.filter(
            status='COMPLETED',
            completion_date__range=[start_date, end_date]
        ).count()

        # Count tasks completed on time
        completed_on_time = active_tasks.filter(
            status='COMPLETED',
            completion_date__range=[start_date, end_date],
            completion_date__lte=F('due_date')
        ).count()

        # Calculate productivity score
        # 70% weight for completion rate, 30% weight for on-time completion
        completion_rate = (completed_tasks / total_active_tasks) * 100
        on_time_rate = (completed_on_time / total_active_tasks) * 100

        productivity_score = (completion_rate * 0.7) + (on_time_rate * 0.3)

        return productivity_score

    # Financial KPI Calculations
    def _calculate_monthly_revenue(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate total revenue from paid customer invoices"""
        revenue = CustomerInvoice.objects.filter(
            status='PAID',
            invoice_date__range=[start_date.date(), end_date.date()]
        ).aggregate(total=Sum('total_amount'))['total']

        return float(revenue) if revenue else 0.0

    def _calculate_profit_margin(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate profit margin percentage"""
        revenue = self._calculate_monthly_revenue(start_date, end_date)

        expenses = Expense.objects.filter(
            expense_date__range=[start_date.date(), end_date.date()]
        ).aggregate(total=Sum('amount'))['total']

        expenses = float(expenses) if expenses else 0.0

        if revenue == 0:
            return 0.0

        profit = revenue - expenses
        return (profit / revenue) * 100

    def _calculate_average_invoice_value(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate average invoice value"""
        invoices = CustomerInvoice.objects.filter(
            invoice_date__range=[start_date.date(), end_date.date()]
        )

        if not invoices.exists():
            return 0.0

        avg_value = invoices.aggregate(avg=Avg('total_amount'))['avg']
        return float(avg_value) if avg_value else 0.0

    def _calculate_cash_flow(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate net cash flow based on actual payments"""
        # Cash inflows from customer payments
        inflows = Payment.objects.filter(
            payment_date__range=[start_date.date(), end_date.date()],
            payment_type='CUSTOMER_PAYMENT'
        ).aggregate(total=Sum('amount'))['total']

        # Cash outflows from vendor payments
        outflows = Payment.objects.filter(
            payment_date__range=[start_date.date(), end_date.date()],
            payment_type='VENDOR_PAYMENT'
        ).aggregate(total=Sum('amount'))['total']

        # Also include expenses as outflows
        expense_outflows = Expense.objects.filter(
            expense_date__range=[start_date.date(), end_date.date()]
        ).aggregate(total=Sum('amount'))['total']

        inflows = float(inflows) if inflows else 0.0
        outflows = float(outflows) if outflows else 0.0
        expense_outflows = float(expense_outflows) if expense_outflows else 0.0

        return inflows - (outflows + expense_outflows)

    def _calculate_revenue_growth_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate revenue growth rate compared to previous period"""
        current_revenue = self._calculate_monthly_revenue(start_date, end_date)

        # Calculate previous period revenue
        period_length = end_date - start_date
        prev_start = start_date - period_length
        prev_end = start_date

        previous_revenue = self._calculate_monthly_revenue(prev_start, prev_end)

        if previous_revenue == 0:
            return 0.0 if current_revenue == 0 else 100.0

        return ((current_revenue - previous_revenue) / previous_revenue) * 100

    # Operations KPI Calculations
    def _calculate_project_completion_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate percentage of projects completed on time"""
        # Look at projects that were either due or completed during the period
        relevant_projects = Project.objects.filter(
            Q(end_date__range=[start_date.date(), end_date.date()]) |
            Q(actual_end_date__range=[start_date.date(), end_date.date()])
        ).distinct()

        if not relevant_projects.exists():
            return 0.0

        # Count projects completed on time
        completed_on_time = relevant_projects.filter(
            status='COMPLETED',
            actual_end_date__isnull=False,
            actual_end_date__lte=F('end_date')
        ).count()

        # Count all completed projects in the set
        completed_projects = relevant_projects.filter(
            status='COMPLETED',
            actual_end_date__isnull=False
        ).count()

        # If no projects were completed, return 0
        if completed_projects == 0:
            return 0.0

        return (completed_on_time / completed_projects) * 100

    def _calculate_task_completion_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate percentage of tasks completed on time"""
        tasks_due = Task.objects.filter(
            due_date__range=[start_date, end_date]
        )

        if not tasks_due.exists():
            return 0.0

        completed_on_time = tasks_due.filter(
            status='COMPLETED',
            completion_date__lte=F('due_date')
        ).count()

        total_tasks = tasks_due.count()

        return (completed_on_time / total_tasks) * 100

    def _calculate_average_project_duration(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate average project duration in days"""
        completed_projects = Project.objects.filter(
            status='COMPLETED',
            actual_end_date__range=[start_date.date(), end_date.date()],
            actual_start_date__isnull=False,
            actual_end_date__isnull=False
        )

        if not completed_projects.exists():
            return 0.0

        durations = []
        for project in completed_projects:
            duration = (project.actual_end_date - project.actual_start_date).days
            durations.append(duration)

        return sum(durations) / len(durations) if durations else 0.0

    def _calculate_project_budget_variance(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate average project budget variance percentage"""
        completed_projects = Project.objects.filter(
            status='COMPLETED',
            actual_end_date__range=[start_date.date(), end_date.date()],
            budget_amount__isnull=False,
            actual_cost__isnull=False
        )

        if not completed_projects.exists():
            return 0.0

        variances = []
        for project in completed_projects:
            if project.budget_amount > 0:
                variance = ((float(project.actual_cost) - float(project.budget_amount)) / float(project.budget_amount)) * 100
                variances.append(variance)

        return sum(variances) / len(variances) if variances else 0.0

    def _calculate_resource_utilization(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate resource utilization rate based on task assignments"""
        active_employees = Employee.objects.filter(is_active=True).count()

        if active_employees == 0:
            return 0.0

        # Count employees with active tasks in the period
        employees_with_tasks = Task.objects.filter(
            Q(start_date__range=[start_date, end_date]) |
            Q(due_date__range=[start_date, end_date]),
            assigned_to__isnull=False
        ).values('assigned_to').distinct().count()

        return (employees_with_tasks / active_employees) * 100

    # Customer KPI Calculations
    def _calculate_customer_acquisition_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate new customer acquisition rate based on actual customer creation"""
        # Count customers created during the period
        new_customers = Customer.objects.filter(
            created_at__range=[start_date, end_date]
        ).count()

        return float(new_customers)

    def _calculate_customer_retention_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate customer retention rate based on repeat business"""
        # Calculate retention based on customers who had invoices in both periods

        # Get previous period
        period_length = end_date - start_date
        prev_start = start_date - period_length
        prev_end = start_date

        # Customers who had invoices in previous period
        previous_customers = set(CustomerInvoice.objects.filter(
            invoice_date__range=[prev_start.date(), prev_end.date()]
        ).values_list('customer_id', flat=True))

        if not previous_customers:
            return 0.0

        # Customers who had invoices in current period
        current_customers = set(CustomerInvoice.objects.filter(
            invoice_date__range=[start_date.date(), end_date.date()]
        ).values_list('customer_id', flat=True))

        # Calculate retention rate
        retained_customers = previous_customers.intersection(current_customers)
        retention_rate = (len(retained_customers) / len(previous_customers)) * 100

        return retention_rate

    def _calculate_average_deal_size(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate average deal/invoice size"""
        return self._calculate_average_invoice_value(start_date, end_date)
