#!/usr/bin/env python
"""
Show and manage departments through the system
"""

import os
import sys
import django

# Setup Django
sys.path.append('/Users/<USER>/Desktop/EMS/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from ems.models import Department

def show_departments():
    """Display all departments and their information"""
    print('🏢 DEPARTMENT MANAGEMENT SYSTEM')
    print('=' * 50)
    
    departments = Department.objects.all().order_by('name')
    
    print(f'📊 Total Departments: {departments.count()}')
    print('\n🏢 Current Departments:')
    print('-' * 50)
    
    for i, dept in enumerate(departments, 1):
        emp_count = dept.employee_set.count()
        print(f'{i:2d}. {dept.name}')
        print(f'    🌐 Arabic Name: {dept.name_ar}')
        print(f'    📝 Description: {dept.description}')
        print(f'    👥 Employees: {emp_count}')
        print(f'    ✅ Active: {"Yes" if dept.is_active else "No"}')
        print(f'    📅 Created: {dept.created_at.strftime("%Y-%m-%d %H:%M")}')
        print()
    
    # Add a new department
    print('🚀 Adding New Department Through System...')
    print('-' * 50)
    
    new_dept, created = Department.objects.get_or_create(
        name='Legal & Compliance',
        defaults={
            'name_ar': 'القانونية والامتثال',
            'description': 'Handles legal matters, contracts, and regulatory compliance',
            'description_ar': 'يتعامل مع الأمور القانونية والعقود والامتثال التنظيمي',
            'is_active': True
        }
    )
    
    if created:
        print(f'✅ Successfully created new department: {new_dept.name}')
        print(f'   🌐 Arabic Name: {new_dept.name_ar}')
        print(f'   📝 Description: {new_dept.description}')
    else:
        print(f'📋 Department already exists: {new_dept.name}')
    
    # Add another department
    new_dept2, created2 = Department.objects.get_or_create(
        name='Quality Assurance',
        defaults={
            'name_ar': 'ضمان الجودة',
            'description': 'Ensures product and service quality standards',
            'description_ar': 'يضمن معايير جودة المنتجات والخدمات',
            'is_active': True
        }
    )
    
    if created2:
        print(f'✅ Successfully created new department: {new_dept2.name}')
        print(f'   🌐 Arabic Name: {new_dept2.name_ar}')
        print(f'   📝 Description: {new_dept2.description}')
    else:
        print(f'📋 Department already exists: {new_dept2.name}')
    
    print(f'\n📊 Total Departments After Addition: {Department.objects.count()}')
    
    # Show department statistics
    print('\n📈 Department Statistics:')
    print('-' * 50)
    
    total_employees = 0
    for dept in Department.objects.all():
        emp_count = dept.employee_set.count()
        total_employees += emp_count
        if emp_count > 0:
            print(f'• {dept.name}: {emp_count} employees')
    
    print(f'\n👥 Total Employees Across All Departments: {total_employees}')
    
    # Show active vs inactive departments
    active_depts = Department.objects.filter(is_active=True).count()
    inactive_depts = Department.objects.filter(is_active=False).count()
    
    print(f'✅ Active Departments: {active_depts}')
    print(f'❌ Inactive Departments: {inactive_depts}')
    
    print('\n🎉 Department Management System Working Successfully!')

if __name__ == "__main__":
    show_departments()
