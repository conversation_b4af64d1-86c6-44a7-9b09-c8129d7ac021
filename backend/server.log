INFO Watching for file changes with StatReloader
INFO WeasyPrint not available, using ReportLab fallback: OSError
Error: That port is already in use.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             WARNING Unauthorized: /api/personal-messages/
WARNING "GET /api/personal-messages/ HTTP/1.1" 401 172
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO "GET /api/notifications/notifications/ HTTP/1.1" 200 765
WARNING Not Found: /api/messages/recent/
WARNING Not Found: /api/meetings/my-meetings/
WARNING "GET /api/meetings/my-meetings/ HTTP/1.1" 404 23
INFO "GET /api/tasks/my-tasks/ HTTP/1.1" 200 2
WARNING "GET /api/messages/recent/ HTTP/1.1" 404 23
INFO "GET /api/attendance/my-attendance/?date_from=2025-06-30&date_to=2025-07-21 HTTP/1.1" 200 2
INFO "GET /api/leave-requests/my-requests/ HTTP/1.1" 200 2
WARNING Not Found: /api/messages/recent/
WARNING Not Found: /api/meetings/my-meetings/
WARNING "GET /api/messages/recent/ HTTP/1.1" 404 23
/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/pagination.py:200: UnorderedObjectListWarning: Pagination may yield inconsistent results with an unordered object_list: <class 'ems.models.LeaveType'> QuerySet.
  paginator = self.django_paginator_class(queryset, page_size)
WARNING "GET /api/meetings/my-meetings/ HTTP/1.1" 404 23
INFO "GET /api/attendance/my-attendance/?date_from=2025-06-30&date_to=2025-07-21 HTTP/1.1" 200 2
INFO "GET /api/tasks/my-tasks/ HTTP/1.1" 200 2
INFO "GET /api/leave-types/ HTTP/1.1" 200 1264
INFO "GET /api/leave-requests/my-requests/ HTTP/1.1" 200 2
INFO "GET /api/leave-types/ HTTP/1.1" 200 1264
INFO "GET /api/personal-messages/?page=1&page_size=20 HTTP/1.1" 200 52
WARNING Not Found: /api/meetings/my-meetings/
WARNING Not Found: /api/messages/recent/
WARNING "GET /api/meetings/my-meetings/ HTTP/1.1" 404 23
WARNING "GET /api/messages/recent/ HTTP/1.1" 404 23
INFO "GET /api/attendance/my-attendance/?date_from=2025-06-30&date_to=2025-07-21 HTTP/1.1" 200 2
INFO "GET /api/tasks/my-tasks/ HTTP/1.1" 200 2
INFO "GET /api/leave-requests/my-requests/ HTTP/1.1" 200 2
INFO "GET /api/leave-types/ HTTP/1.1" 200 1264
WARNING Not Found: /api/meetings/my-meetings/
WARNING "GET /api/meetings/my-meetings/ HTTP/1.1" 404 23
WARNING Not Found: /api/messages/recent/
WARNING "GET /api/messages/recent/ HTTP/1.1" 404 23
INFO "GET /api/attendance/my-attendance/?date_from=2025-06-30&date_to=2025-07-21 HTTP/1.1" 200 2
INFO "GET /api/tasks/my-tasks/ HTTP/1.1" 200 2
INFO "GET /api/leave-types/ HTTP/1.1" 200 1264
INFO "GET /api/leave-requests/my-requests/ HTTP/1.1" 200 2
INFO "GET /api/employee-leave/?page=1&page_size=20 HTTP/1.1" 200 52
WARNING Unauthorized: /api/meetings/my-meetings/
WARNING "GET /api/meetings/my-meetings/ HTTP/1.1" 401 172
WARNING Unauthorized: /api/messages/recent/
WARNING "GET /api/messages/recent/ HTTP/1.1" 401 172
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO "GET /api/notifications/notifications/ HTTP/1.1" 200 765
INFO "GET /api/employee-leave/?page=1&page_size=20 HTTP/1.1" 200 52
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
WARNING Not Found: /api/meetings/my-meetings/
WARNING Not Found: /api/messages/recent/
INFO "GET /api/attendance/my-attendance/?date_from=2025-06-30&date_to=2025-07-21 HTTP/1.1" 200 2
WARNING "GET /api/messages/recent/ HTTP/1.1" 404 23
WARNING "GET /api/meetings/my-meetings/ HTTP/1.1" 404 23
INFO "GET /api/leave-requests/my-requests/ HTTP/1.1" 200 2
INFO "GET /api/tasks/my-tasks/ HTTP/1.1" 200 2
INFO "GET /api/leave-types/ HTTP/1.1" 200 1264
WARNING Not Found: /api/messages/recent/
WARNING Not Found: /api/meetings/my-meetings/
WARNING "GET /api/meetings/my-meetings/ HTTP/1.1" 404 23
WARNING "GET /api/messages/recent/ HTTP/1.1" 404 23
INFO "GET /api/leave-requests/my-requests/ HTTP/1.1" 200 2
INFO "GET /api/tasks/my-tasks/ HTTP/1.1" 200 2
INFO "GET /api/leave-types/ HTTP/1.1" 200 1264
INFO "GET /api/attendance/my-attendance/?date_from=2025-06-30&date_to=2025-07-21 HTTP/1.1" 200 2
INFO "GET /api/personal-messages/?page=1&page_size=20 HTTP/1.1" 200 52
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO "GET /api/notifications/notifications/ HTTP/1.1" 200 765
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO "GET /api/employee-tasks/?page=1&page_size=20 HTTP/1.1" 200 52
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO "GET /api/notifications/notifications/ HTTP/1.1" 200 765
INFO "GET /api/employee-leave/?page=1&page_size=20 HTTP/1.1" 200 52
INFO "OPTIONS /api/employee-leave/ HTTP/1.1" 200 0
INFO EmployeeLeaveViewSet.create - Request data: {'type': 'annual', 'startDate': '2025-01-25', 'endDate': '2025-01-27', 'duration': 3, 'reason': 'Personal vacation', 'reasonAr': '', 'typeAr': 'إجازة سنوية', 'requestDate': '2025-07-21', 'status': 'pending'}
INFO EmployeeLeaveViewSet.create - User: employee1
INFO EmployeeLeaveSerializer.create - Validated data: {'type': 'annual', 'startDate': '2025-01-25', 'endDate': '2025-01-27', 'duration': 3, 'reason': 'Personal vacation', 'reasonAr': '', 'typeAr': 'إجازة سنوية', 'requestDate': '2025-07-21', 'status': 'pending'}
INFO Push notification marked as sent for notification 793c719a-92d3-414c-9c32-e1aada445a53
INFO Successfully created leave request: EMP001 - John Doe - Annual Leave (2025-01-25 to 2025-01-27)
INFO "POST /api/employee-leave/ HTTP/1.1" 201 335
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO "GET /api/notifications/notifications/ HTTP/1.1" 200 1431
INFO "GET /api/personal-messages/?page=1&page_size=20 HTTP/1.1" 200 52
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO "GET /api/notifications/notifications/ HTTP/1.1" 200 1430
INFO "GET /api/employee-leave/?page=1&page_size=20 HTTP/1.1" 200 387
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO "GET /api/notifications/notifications/ HTTP/1.1" 200 1419
INFO "GET /api/employee-tasks/?page=1&page_size=20 HTTP/1.1" 200 52
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO "GET /api/notifications/notifications/ HTTP/1.1" 200 1430
INFO "GET /api/personal-messages/?page=1&page_size=20 HTTP/1.1" 200 52
INFO "OPTIONS /api/personal-messages/ HTTP/1.1" 200 0
WARNING Bad Request: /api/personal-messages/
WARNING "POST /api/personal-messages/ HTTP/1.1" 400 38
INFO /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
Performing system checks...


-----

WeasyPrint could not import some external libraries. Please carefully follow the installation steps before reporting an issue:
https://doc.courtbouillon.org/weasyprint/stable/first_steps.html#installation
https://doc.courtbouillon.org/weasyprint/stable/first_steps.html#troubleshooting 

-----

System check identified no issues (0 silenced).
July 21, 2025 - 12:41:02
Django version 4.2.7, using settings 'backend.settings'
Starting development server at http://127.0.0.1:8000/
Quit the server with CONTROL-C.

INFO Watching for file changes with StatReloader
INFO WeasyPrint not available, using ReportLab fallback: OSError
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO User profile request from employee1
INFO "GET /api/auth/user/ HTTP/1.1" 200 1482
INFO "GET /api/notifications/notifications/ HTTP/1.1" 200 1432
INFO "GET /api/personal-messages/?page=1&page_size=20 HTTP/1.1" 200 52
WARNING Unauthorized: /api/personal-messages/
WARNING "POST /api/personal-messages/ HTTP/1.1" 401 58
INFO "OPTIONS /api/auth/refresh/ HTTP/1.1" 200 0
INFO Auth attempt from 127.0.0.1 to /api/auth/refresh/
INFO Auth attempt from 127.0.0.1 to /api/auth/refresh/
INFO Token refreshed successfully
INFO "POST /api/auth/refresh/ HTTP/1.1" 200 42
WARNING Bad Request: /api/personal-messages/
WARNING "POST /api/personal-messages/ HTTP/1.1" 400 38
Performing system checks...


-----

WeasyPrint could not import some external libraries. Please carefully follow the installation steps before reporting an issue:
https://doc.courtbouillon.org/weasyprint/stable/first_steps.html#installation
https://doc.courtbouillon.org/weasyprint/stable/first_steps.html#troubleshooting 

-----

System check identified no issues (0 silenced).
July 21, 2025 - 13:14:23
Django version 4.2.7, using settings 'backend.settings'
Starting development server at http://127.0.0.1:8000/
Quit the server with CONTROL-C.

